# Tối ưu Bot API - Loại bỏ Logic Typing Tự động và Tối ưu External URL

## Tổng quan

Tài liệu này mô tả các thay đổi được thực hiện để tối ưu Bot API, loại bỏ logic typing tự động và cải thiện hiệu suất xử lý hình ảnh với external URL.

## Các thay đổi chính

### 1. Loại bỏ Logic Typing Tự động

#### Trước đây:
- `Facebook::SendOnFacebookService` tự động gửi typing indicator trước khi gửi tin nhắn
- Có logic `sleep(0.8)` để đợi typing indicator hiển thị
- Tự động tắt typing indicator sau khi gửi tin nhắn

#### Sau khi tối ưu:
- Loại bỏ hoàn toàn logic typing tự động trong `perform_reply`
- G<PERSON><PERSON> lại các phương thức typing để sử dụng riêng biệt qua API
- Người dùng chủ động gọi API typing khi cần thiết

#### Files đã thay đổi:
- `app/services/facebook/send_on_facebook_service.rb`
- `docs/api/bot_api.md`

### 2. Tối ưu Multiple Attachments Processing

#### Trước đây:
- Gửi từng attachment tuần tự với `sleep(0.3)` giữa các lần gửi
- Với 3 hình ảnh: 3 × (thời gian gửi + 0.3s delay) = ~20s

#### Sau khi tối ưu:
- Sử dụng **parallel processing** với Ruby threads
- Gửi tất cả attachments đồng thời
- Loại bỏ hoàn toàn delay giữa các attachments

#### Kết quả:
- Thời gian gửi 3 hình ảnh: từ ~20s xuống ~2-3s (giảm 85-90%)

### 3. Cập nhật Facebook API Version

#### Trước đây:
- Sử dụng Facebook Graph API v17.0
- Instagram sử dụng v22.0

#### Sau khi tối ưu:
- Cập nhật Facebook Graph API lên **v22.0** (mới nhất)
- Đồng bộ version giữa Facebook và Instagram
- Tận dụng các cải tiến performance trong API mới

### 4. Tối ưu External URL cho Attachments

#### Hiện trạng (đã tối ưu):
- `Bot::MessageService` ưu tiên sử dụng `external_url` khi có
- `Attachment` model hỗ trợ `external_url` và không validate file khi có external_url
- Facebook và Instagram services sử dụng external_url trực tiếp
- Cache buster chỉ áp dụng cho internal URLs, không áp dụng cho external URLs
- Loại bỏ `is_reusable: true` để tối ưu tốc độ

#### Lợi ích:
- Không cần tải hình ảnh về server Chatwoot
- Giảm thời gian gửi tin nhắn từ ~2-3 giây xuống ~0.5 giây
- Tiết kiệm storage và bandwidth
- Tránh duplicate hình ảnh
- Giữ nguyên URL gốc cho external URLs

## API Usage

### Gửi tin nhắn đơn giản (không typing)

```javascript
const response = await axios.post(`${baseUrl}/messages`, {
  conversation_id: conversationId,
  content: "Nội dung tin nhắn",
  attachments: [
    {
      file_type: "image",
      external_url: "https://example.com/image.jpg"
    }
  ]
}, { headers });
```

### Gửi typing indicator riêng biệt (nếu cần)

```javascript
// Bật typing
await axios.post(`${baseUrl}/conversations/${conversationId}/typing_on`, {}, { headers });

// Đợi một chút
await new Promise(resolve => setTimeout(resolve, 1500));

// Gửi tin nhắn
const response = await axios.post(`${baseUrl}/messages`, {
  conversation_id: conversationId,
  content: "Nội dung tin nhắn"
}, { headers });

// Tắt typing
await axios.post(`${baseUrl}/conversations/${conversationId}/typing_off`, {}, { headers });
```

## Lợi ích của việc tối ưu

### 1. Hiệu suất
- **Tin nhắn text**: Từ ~1.3s xuống ~0.5s (giảm 62%)
- **Multiple attachments**: Từ ~20s xuống ~2-3s (giảm 85-90%)
- **External URL**: Gửi trực tiếp mà không cần download
- **Parallel processing**: Gửi nhiều hình ảnh đồng thời

### 2. Kiểm soát tốt hơn
- Bot có thể chủ động quyết định khi nào gửi typing
- Tránh typing indicator hiển thị khi bot không hoạt động (hết credit, lỗi)
- Linh hoạt hơn trong việc quản lý UX

### 3. Tài nguyên
- Giảm tải cho server Chatwoot
- Tiết kiệm storage với external URL
- Giảm bandwidth sử dụng

## Backward Compatibility

- Tất cả API hiện tại vẫn hoạt động bình thường
- API typing riêng biệt vẫn được giữ nguyên
- External URL và file upload đều được hỗ trợ

## Lưu ý quan trọng

1. **Typing Control**: Hệ thống KHÔNG tự động gửi typing. Cần gọi API riêng biệt
2. **External URL Priority**: Luôn ưu tiên external_url nếu có
3. **URL Accessibility**: Đảm bảo external URL có thể truy cập từ Facebook/Instagram
4. **Error Handling**: Kiểm tra response để xử lý lỗi phù hợp

## Testing

Để test các thay đổi:

1. Gửi tin nhắn và kiểm tra không có typing tự động
2. Test external URL với hình ảnh
3. Test API typing riêng biệt
4. Kiểm tra hiệu suất gửi tin nhắn

## Migration Guide

Nếu code hiện tại dựa vào typing tự động:

```javascript
// Cũ: Typing tự động
await sendMessage(conversationId, content);

// Mới: Typing thủ công (nếu cần)
await sendMessageWithTyping(conversationId, content);
```

## Monitoring & Logs

Để theo dõi hiệu suất sau khi tối ưu:

### 1. Log Messages
- `Facebook::SendOnFacebookService: Sent attachment X in Ys` - Thời gian gửi từng attachment
- `Facebook::SendOnFacebookService: Sent N attachments in parallel in Ys` - Tổng thời gian gửi multiple attachments
- `(external: true/false)` - Phân biệt external URL vs internal

### 2. Performance Metrics
- **Single attachment**: < 1s
- **Multiple attachments (3 images)**: < 5s
- **External URL**: Ưu tiên sử dụng, không có download time

### 3. Error Handling
- Parallel processing có error handling riêng cho từng thread
- Timeout và retry logic được giữ nguyên
- Facebook API v22.0 có error response tốt hơn
