GIT
  remote: https://github.com/chatwoot/azure-storage-ruby
  revision: 9957cf899d33a285b5dfe15bdb875292398e392b
  branch: chatwoot
  specs:
    azure-storage-blob (2.0.3)
      azure-storage-common (~> 2.0)
      nokogiri (~> 1, >= 1.10.8)
    azure-storage-common (2.0.4)
      faraday (~> 2.0)
      faraday-follow_redirects (~> 0.3.0)
      faraday-net_http_persistent (~> 2.0)
      net-http-persistent (~> 4.0)
      nokogiri (~> 1, >= 1.10.8)

GIT
  remote: https://github.com/chatwoot/devise-secure_password
  revision: adcc85fe1babfe40feae73dbcae64d14fff86e69
  branch: chatwoot
  specs:
    devise-secure_password (2.0.1)
      devise (>= 4.0.0, < 5.0.0)
      railties (>= 5.0.0, < 8.0.0)

GEM
  remote: https://rubygems.org/
  specs:
    actioncable (*******)
      actionpack (= *******)
      activesupport (= *******)
      nio4r (~> 2.0)
      websocket-driver (>= 0.6.1)
      zeitwerk (~> 2.6)
    actionmailbox (*******)
      actionpack (= *******)
      activejob (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      mail (>= 2.7.1)
      net-imap
      net-pop
      net-smtp
    actionmailer (*******)
      actionpack (= *******)
      actionview (= *******)
      activejob (= *******)
      activesupport (= *******)
      mail (~> 2.5, >= 2.5.4)
      net-imap
      net-pop
      net-smtp
      rails-dom-testing (~> 2.2)
    actionpack (*******)
      actionview (= *******)
      activesupport (= *******)
      nokogiri (>= 1.8.5)
      racc
      rack (>= 2.2.4)
      rack-session (>= 1.0.1)
      rack-test (>= 0.6.3)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
    actiontext (*******)
      actionpack (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      globalid (>= 0.6.0)
      nokogiri (>= 1.8.5)
    actionview (*******)
      activesupport (= *******)
      builder (~> 3.1)
      erubi (~> 1.11)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
    active_record_query_trace (1.8)
    activejob (*******)
      activesupport (= *******)
      globalid (>= 0.3.6)
    activemodel (*******)
      activesupport (= *******)
    activerecord (*******)
      activemodel (= *******)
      activesupport (= *******)
      timeout (>= 0.4.0)
    activerecord-import (2.1.0)
      activerecord (>= 4.2)
    activestorage (*******)
      actionpack (= *******)
      activejob (= *******)
      activerecord (= *******)
      activesupport (= *******)
      marcel (~> 1.0)
    activesupport (*******)
      base64
      benchmark (>= 0.3)
      bigdecimal
      concurrent-ruby (~> 1.0, >= 1.0.2)
      connection_pool (>= 2.2.5)
      drb
      i18n (>= 1.6, < 2)
      logger (>= 1.4.2)
      minitest (>= 5.1)
      mutex_m
      securerandom (>= 0.3)
      tzinfo (~> 2.0)
    acts-as-taggable-on (12.0.0)
      activerecord (>= 7.1, < 8.1)
      zeitwerk (>= 2.4, < 3.0)
    addressable (2.8.7)
      public_suffix (>= 2.0.2, < 7.0)
    administrate (0.20.1)
      actionpack (>= 6.0, < 8.0)
      actionview (>= 6.0, < 8.0)
      activerecord (>= 6.0, < 8.0)
      jquery-rails (~> 4.6.0)
      kaminari (~> 1.2.2)
      sassc-rails (~> 2.1)
      selectize-rails (~> 0.6)
    administrate-field-active_storage (1.0.3)
      administrate (>= 0.2.2)
      rails (>= 7.0)
    administrate-field-belongs_to_search (0.9.0)
      administrate (>= 0.3, < 1.0)
      jbuilder (~> 2)
      rails (>= 4.2, < 7.2)
      selectize-rails (~> 0.6)
    annotate (3.2.0)
      activerecord (>= 3.2, < 8.0)
      rake (>= 10.4, < 14.0)
    ast (2.4.3)
    attr_extras (7.1.0)
    audited (5.4.1)
      activerecord (>= 5.0, < 7.7)
      activesupport (>= 5.0, < 7.7)
    aws-eventstream (1.2.0)
    aws-partitions (1.760.0)
    aws-sdk-core (3.171.1)
      aws-eventstream (~> 1, >= 1.0.2)
      aws-partitions (~> 1, >= 1.651.0)
      aws-sigv4 (~> 1.5)
      jmespath (~> 1, >= 1.6.1)
    aws-sdk-kms (1.64.0)
      aws-sdk-core (~> 3, >= 3.165.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-s3 (1.122.0)
      aws-sdk-core (~> 3, >= 3.165.0)
      aws-sdk-kms (~> 1)
      aws-sigv4 (~> 1.4)
    aws-sigv4 (1.5.2)
      aws-eventstream (~> 1, >= 1.0.2)
    barnes (0.0.9)
      multi_json (~> 1)
      statsd-ruby (~> 1.1)
    base64 (0.2.0)
    bcrypt (3.1.20)
    benchmark (0.4.0)
    bigdecimal (3.1.9)
    bindex (0.8.1)
    bootsnap (1.16.0)
      msgpack (~> 1.2)
    brakeman (5.4.1)
    browser (5.3.1)
    builder (3.3.0)
    bullet (8.0.7)
      activesupport (>= 3.0.0)
      uniform_notifier (~> 1.11)
    bundle-audit (0.1.0)
      bundler-audit
    bundler-audit (0.9.1)
      bundler (>= 1.2.0, < 3)
      thor (~> 1.0)
    byebug (11.1.3)
    climate_control (1.2.0)
    coderay (1.1.3)
    commonmarker (0.23.10)
    concurrent-ruby (1.3.5)
    connection_pool (2.5.3)
    crack (1.0.0)
      bigdecimal
      rexml
    crass (1.0.6)
    csv (3.3.0)
    csv-safe (3.3.1)
      csv (~> 3.0)
    database_cleaner (2.0.2)
      database_cleaner-active_record (>= 2, < 3)
    database_cleaner-active_record (2.1.0)
      activerecord (>= 5.a)
      database_cleaner-core (~> 2.0.0)
    database_cleaner-core (2.0.1)
    date (3.4.1)
    ddtrace (0.48.0)
      ffi (~> 1.0)
      msgpack
    debug (1.8.0)
      irb (>= 1.5.0)
      reline (>= 0.3.1)
    declarative (0.0.20)
    devise (4.9.4)
      bcrypt (~> 3.0)
      orm_adapter (~> 0.1)
      railties (>= 4.1.0)
      responders
      warden (~> 1.2.3)
    devise_token_auth (1.2.5)
      bcrypt (~> 3.0)
      devise (> 3.5.2, < 5)
      rails (>= 4.2.0, < 8.1)
    diff-lcs (1.5.1)
    digest-crc (0.6.5)
      rake (>= 12.0.0, < 14.0.0)
    docile (1.4.0)
    domain_name (0.5.20190701)
      unf (>= 0.0.5, < 1.0.0)
    dotenv (3.1.2)
    dotenv-rails (3.1.2)
      dotenv (= 3.1.2)
      railties (>= 6.1)
    down (5.4.0)
      addressable (~> 2.8)
    drb (2.2.3)
    dry-cli (1.1.0)
    ecma-re-validator (0.4.0)
      regexp_parser (~> 2.2)
    elastic-apm (4.6.2)
      concurrent-ruby (~> 1.0)
      http (>= 3.0)
      ruby2_keywords
    email_reply_trimmer (0.1.13)
    erubi (1.13.0)
    et-orbi (1.2.11)
      tzinfo
    event_stream_parser (1.0.0)
    execjs (2.8.1)
    facebook-messenger (2.0.1)
      httparty (~> 0.13, >= 0.13.7)
      rack (>= 1.4.5)
    factory_bot (6.4.5)
      activesupport (>= 5.0.0)
    factory_bot_rails (6.4.3)
      factory_bot (~> 6.4)
      railties (>= 5.0.0)
    faker (3.2.0)
      i18n (>= 1.8.11, < 2)
    faraday (2.9.0)
      faraday-net_http (>= 2.0, < 3.2)
    faraday-follow_redirects (0.3.0)
      faraday (>= 1, < 3)
    faraday-mashify (0.1.1)
      faraday (~> 2.0)
      hashie
    faraday-multipart (1.0.4)
      multipart-post (~> 2)
    faraday-net_http (3.1.0)
      net-http
    faraday-net_http_persistent (2.1.0)
      faraday (~> 2.5)
      net-http-persistent (~> 4.0)
    faraday-retry (2.2.1)
      faraday (~> 2.0)
    fcm (1.0.8)
      faraday (>= 1.0.0, < 3.0)
      googleauth (~> 1)
    ffi (1.17.2)
    ffi (1.17.2-arm64-darwin)
    ffi (1.17.2-x86_64-darwin)
    ffi (1.17.2-x86_64-linux-gnu)
    ffi-compiler (1.0.1)
      ffi (>= 1.0.0)
      rake
    flag_shih_tzu (0.3.23)
    foreman (0.87.2)
    fugit (1.11.1)
      et-orbi (~> 1, >= 1.2.11)
      raabro (~> 1.4)
    gapic-common (0.20.0)
      faraday (>= 1.9, < 3.a)
      faraday-retry (>= 1.0, < 3.a)
      google-protobuf (~> 3.14)
      googleapis-common-protos (>= 1.3.12, < 2.a)
      googleapis-common-protos-types (>= 1.3.1, < 2.a)
      googleauth (~> 1.0)
      grpc (~> 1.36)
    geocoder (1.8.1)
    gli (2.22.2)
      ostruct
    globalid (1.2.1)
      activesupport (>= 6.1)
    gmail_xoauth (0.4.3)
      oauth (>= 0.3.6)
    google-apis-core (0.15.1)
      addressable (~> 2.5, >= 2.5.1)
      googleauth (~> 1.9)
      httpclient (>= 2.8.3, < 3.a)
      mini_mime (~> 1.0)
      mutex_m
      representable (~> 3.0)
      retriable (>= 2.0, < 4.a)
    google-apis-iamcredentials_v1 (0.22.0)
      google-apis-core (>= 0.15.0, < 2.a)
    google-apis-storage_v1 (0.47.0)
      google-apis-core (>= 0.15.0, < 2.a)
    google-cloud-core (1.7.1)
      google-cloud-env (>= 1.0, < 3.a)
      google-cloud-errors (~> 1.0)
    google-cloud-dialogflow-v2 (0.31.0)
      gapic-common (>= 0.20.0, < 2.a)
      google-cloud-errors (~> 1.0)
      google-cloud-location (>= 0.4, < 2.a)
    google-cloud-env (2.2.1)
      faraday (>= 1.0, < 3.a)
    google-cloud-errors (1.3.1)
    google-cloud-location (0.6.0)
      gapic-common (>= 0.20.0, < 2.a)
      google-cloud-errors (~> 1.0)
    google-cloud-storage (1.52.0)
      addressable (~> 2.8)
      digest-crc (~> 0.4)
      google-apis-core (~> 0.13)
      google-apis-iamcredentials_v1 (~> 0.18)
      google-apis-storage_v1 (~> 0.38)
      google-cloud-core (~> 1.6)
      googleauth (~> 1.9)
      mini_mime (~> 1.0)
    google-cloud-translate-v3 (0.10.0)
      gapic-common (>= 0.20.0, < 2.a)
      google-cloud-errors (~> 1.0)
    google-protobuf (3.25.7)
    googleapis-common-protos (1.6.0)
      google-protobuf (>= 3.18, < 5.a)
      googleapis-common-protos-types (~> 1.7)
      grpc (~> 1.41)
    googleapis-common-protos-types (1.20.0)
      google-protobuf (>= 3.18, < 5.a)
    googleauth (1.11.2)
      faraday (>= 1.0, < 3.a)
      google-cloud-env (~> 2.1)
      jwt (>= 1.4, < 3.0)
      multi_json (~> 1.11)
      os (>= 0.9, < 2.0)
      signet (>= 0.16, < 2.a)
    groupdate (6.2.1)
      activesupport (>= 5.2)
    grpc (1.72.0)
      google-protobuf (>= 3.25, < 5.0)
      googleapis-common-protos-types (~> 1.0)
    grpc (1.72.0-arm64-darwin)
      google-protobuf (>= 3.25, < 5.0)
      googleapis-common-protos-types (~> 1.0)
    grpc (1.72.0-x86_64-darwin)
      google-protobuf (>= 3.25, < 5.0)
      googleapis-common-protos-types (~> 1.0)
    grpc (1.72.0-x86_64-linux)
      google-protobuf (>= 3.25, < 5.0)
      googleapis-common-protos-types (~> 1.0)
    haikunator (1.1.1)
    hairtrigger (1.0.0)
      activerecord (>= 6.0, < 8)
      ruby2ruby (~> 2.4)
      ruby_parser (~> 3.10)
    hana (1.3.7)
    hash_diff (1.1.1)
    hashdiff (1.1.0)
    hashie (5.0.0)
    html2text (0.4.0)
      nokogiri (>= 1.0, < 2.0)
    http (5.1.1)
      addressable (~> 2.8)
      http-cookie (~> 1.0)
      http-form_data (~> 2.2)
      llhttp-ffi (~> 0.4.0)
    http-accept (1.7.0)
    http-cookie (1.0.5)
      domain_name (~> 0.5)
    http-form_data (2.3.0)
    httparty (0.21.0)
      mini_mime (>= 1.0.0)
      multi_xml (>= 0.5.2)
    httpclient (2.8.3)
    i18n (1.14.7)
      concurrent-ruby (~> 1.0)
    image_processing (1.12.2)
      mini_magick (>= 4.9.5, < 5)
      ruby-vips (>= 2.0.17, < 3)
    io-console (0.6.0)
    irb (1.7.2)
      reline (>= 0.3.6)
    iso-639 (0.3.8)
      csv
    jbuilder (2.11.5)
      actionview (>= 5.0.0)
      activesupport (>= 5.0.0)
    jmespath (1.6.2)
    jquery-rails (4.6.0)
      rails-dom-testing (>= 1, < 3)
      railties (>= 4.2.0)
      thor (>= 0.14, < 2.0)
    json (2.12.0)
    json_refs (0.1.8)
      hana
    json_schemer (0.2.24)
      ecma-re-validator (~> 0.3)
      hana (~> 1.3)
      regexp_parser (~> 2.0)
      uri_template (~> 0.7)
    judoscale-rails (1.8.2)
      judoscale-ruby (= 1.8.2)
      railties
    judoscale-ruby (1.8.2)
    judoscale-sidekiq (1.8.2)
      judoscale-ruby (= 1.8.2)
      sidekiq (>= 5.0)
    jwt (2.8.1)
      base64
    kaminari (1.2.2)
      activesupport (>= 4.1.0)
      kaminari-actionview (= 1.2.2)
      kaminari-activerecord (= 1.2.2)
      kaminari-core (= 1.2.2)
    kaminari-actionview (1.2.2)
      actionview
      kaminari-core (= 1.2.2)
    kaminari-activerecord (1.2.2)
      activerecord
      kaminari-core (= 1.2.2)
    kaminari-core (1.2.2)
    koala (3.4.0)
      addressable
      faraday
      faraday-multipart
      json (>= 1.8)
      rexml
    language_server-protocol (********)
    launchy (2.5.2)
      addressable (~> 2.8)
    letter_opener (1.8.1)
      launchy (>= 2.2, < 3)
    line-bot-api (1.28.0)
    lint_roller (1.1.0)
    liquid (5.4.0)
    listen (3.8.0)
      rb-fsevent (~> 0.10, >= 0.10.3)
      rb-inotify (~> 0.9, >= 0.9.10)
    llhttp-ffi (0.4.0)
      ffi-compiler (~> 1.0)
      rake (~> 13.0)
    logger (1.7.0)
    lograge (0.14.0)
      actionpack (>= 4)
      activesupport (>= 4)
      railties (>= 4)
      request_store (~> 1.0)
    loofah (2.23.1)
      crass (~> 1.0.2)
      nokogiri (>= 1.12.0)
    mail (2.8.1)
      mini_mime (>= 0.1.1)
      net-imap
      net-pop
      net-smtp
    marcel (1.0.4)
    maxminddb (0.1.22)
    meta_request (0.8.3)
      rack-contrib (>= 1.1, < 3)
      railties (>= 3.0.0, < 8)
    method_source (1.1.0)
    mime-types (3.4.1)
      mime-types-data (~> 3.2015)
    mime-types-data (3.2023.0218.1)
    mini_magick (4.12.0)
    mini_mime (1.1.5)
    mini_portile2 (2.8.8)
    minitest (5.25.5)
    mock_redis (0.36.0)
      ruby2_keywords
    msgpack (1.8.0)
    multi_json (1.15.0)
    multi_xml (0.6.0)
    multipart-post (2.3.0)
    mutex_m (0.3.0)
    neighbor (0.2.3)
      activerecord (>= 5.2)
    net-http (0.4.1)
      uri
    net-http-persistent (4.0.2)
      connection_pool (~> 2.2)
    net-imap (0.4.20)
      date
      net-protocol
    net-pop (0.1.2)
      net-protocol
    net-protocol (0.2.2)
      timeout
    net-smtp (0.3.4)
      net-protocol
    netrc (0.11.0)
    newrelic-sidekiq-metrics (1.6.2)
      newrelic_rpm (>= 8.0.0)
      sidekiq
    newrelic_rpm (9.6.0)
      base64
    nio4r (2.7.3)
    nokogiri (1.18.8)
      mini_portile2 (~> 2.8.2)
      racc (~> 1.4)
    nokogiri (1.18.8-arm64-darwin)
      racc (~> 1.4)
    nokogiri (1.18.8-x86_64-darwin)
      racc (~> 1.4)
    nokogiri (1.18.8-x86_64-linux-gnu)
      racc (~> 1.4)
    oauth (1.1.0)
      oauth-tty (~> 1.0, >= 1.0.1)
      snaky_hash (~> 2.0)
      version_gem (~> 1.1)
    oauth-tty (1.0.5)
      version_gem (~> 1.1, >= 1.1.1)
    oauth2 (2.0.9)
      faraday (>= 0.17.3, < 3.0)
      jwt (>= 1.0, < 3.0)
      multi_xml (~> 0.5)
      rack (>= 1.2, < 4)
      snaky_hash (~> 2.0)
      version_gem (~> 1.1)
    oj (3.16.10)
      bigdecimal (>= 3.0)
      ostruct (>= 0.2)
    omniauth (2.1.2)
      hashie (>= 3.4.6)
      rack (>= 2.2.3)
      rack-protection
    omniauth-google-oauth2 (1.1.3)
      jwt (>= 2.0)
      oauth2 (~> 2.0)
      omniauth (~> 2.0)
      omniauth-oauth2 (~> 1.8)
    omniauth-oauth2 (1.8.0)
      oauth2 (>= 1.4, < 3)
      omniauth (~> 2.0)
    omniauth-rails_csrf_protection (1.0.2)
      actionpack (>= 4.2)
      omniauth (~> 2.0)
    openssl (3.2.0)
    orm_adapter (0.5.0)
    os (1.1.4)
    ostruct (0.6.1)
    parallel (1.27.0)
    parser (3.3.8.0)
      ast (~> 2.4.1)
      racc
    pg (1.5.3)
    pg_search (2.3.6)
      activerecord (>= 5.2)
      activesupport (>= 5.2)
    pgvector (0.1.1)
    prism (1.4.0)
    procore-sift (1.0.0)
      activerecord (>= 6.1)
    pry (0.14.2)
      coderay (~> 1.1)
      method_source (~> 1.0)
    pry-rails (0.3.9)
      pry (>= 0.10.4)
    public_suffix (6.0.0)
    puma (6.4.3)
      nio4r (~> 2.0)
    pundit (2.3.0)
      activesupport (>= 3.0.0)
    raabro (1.4.0)
    racc (1.8.1)
    rack (2.2.15)
    rack-attack (6.7.0)
      rack (>= 1.0, < 4)
    rack-contrib (2.5.0)
      rack (< 4)
    rack-cors (2.0.0)
      rack (>= 2.0.0)
    rack-mini-profiler (3.2.0)
      rack (>= 1.2.0)
    rack-protection (3.2.0)
      base64 (>= 0.1.0)
      rack (~> 2.2, >= 2.2.4)
    rack-proxy (0.7.7)
      rack
    rack-session (1.0.2)
      rack (< 3)
    rack-test (2.1.0)
      rack (>= 1.3)
    rack-timeout (0.6.3)
    rackup (1.0.1)
      rack (< 3)
      webrick
    rails (*******)
      actioncable (= *******)
      actionmailbox (= *******)
      actionmailer (= *******)
      actionpack (= *******)
      actiontext (= *******)
      actionview (= *******)
      activejob (= *******)
      activemodel (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      bundler (>= 1.15.0)
      railties (= *******)
    rails-dom-testing (2.2.0)
      activesupport (>= 5.0.0)
      minitest
      nokogiri (>= 1.6)
    rails-html-sanitizer (1.6.1)
      loofah (~> 2.21)
      nokogiri (>= 1.15.7, != 1.16.7, != 1.16.6, != 1.16.5, != 1.16.4, != 1.16.3, != 1.16.2, != 1.16.1, != 1.16.0.rc1, != 1.16.0)
    railties (*******)
      actionpack (= *******)
      activesupport (= *******)
      irb
      rackup (>= 1.0.0)
      rake (>= 12.2)
      thor (~> 1.0, >= 1.2.2)
      zeitwerk (~> 2.6)
    rainbow (3.1.1)
    rake (13.2.1)
    rb-fsevent (0.11.2)
    rb-inotify (0.10.1)
      ffi (~> 1.0)
    redis (5.0.6)
      redis-client (>= 0.9.0)
    redis-client (0.22.2)
      connection_pool
    redis-namespace (1.10.0)
      redis (>= 4)
    regexp_parser (2.10.0)
    reline (0.3.6)
      io-console (~> 0.5)
    representable (3.2.0)
      declarative (< 0.1.0)
      trailblazer-option (>= 0.1.1, < 0.2.0)
      uber (< 0.2.0)
    request_store (1.5.1)
      rack (>= 1.4)
    responders (3.1.1)
      actionpack (>= 5.2)
      railties (>= 5.2)
    rest-client (2.1.0)
      http-accept (>= 1.7.0, < 2.0)
      http-cookie (>= 1.0.2, < 2.0)
      mime-types (>= 1.16, < 4.0)
      netrc (~> 0.8)
    retriable (3.1.2)
    reverse_markdown (2.1.1)
      nokogiri
    rexml (3.4.1)
    rspec-core (3.13.0)
      rspec-support (~> 3.13.0)
    rspec-expectations (3.13.2)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-mocks (3.13.1)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-rails (7.0.1)
      actionpack (>= 7.0)
      activesupport (>= 7.0)
      railties (>= 7.0)
      rspec-core (~> 3.13)
      rspec-expectations (~> 3.13)
      rspec-mocks (~> 3.13)
      rspec-support (~> 3.13)
    rspec-support (3.13.1)
    rspec_junit_formatter (0.6.0)
      rspec-core (>= 2, < 4, != 2.12.0)
    rubocop (1.75.6)
      json (~> 2.3)
      language_server-protocol (~> 3.17.0.2)
      lint_roller (~> 1.1.0)
      parallel (~> 1.10)
      parser (>= 3.3.0.2)
      rainbow (>= 2.2.2, < 4.0)
      regexp_parser (>= 2.9.3, < 3.0)
      rubocop-ast (>= 1.44.0, < 2.0)
      ruby-progressbar (~> 1.7)
      unicode-display_width (>= 2.4.0, < 4.0)
    rubocop-ast (1.44.1)
      parser (>= 3.3.7.2)
      prism (~> 1.4)
    rubocop-factory_bot (2.27.1)
      lint_roller (~> 1.1)
      rubocop (~> 1.72, >= 1.72.1)
    rubocop-performance (1.25.0)
      lint_roller (~> 1.1)
      rubocop (>= 1.75.0, < 2.0)
      rubocop-ast (>= 1.38.0, < 2.0)
    rubocop-rails (2.32.0)
      activesupport (>= 4.2.0)
      lint_roller (~> 1.1)
      rack (>= 1.1)
      rubocop (>= 1.75.0, < 2.0)
      rubocop-ast (>= 1.44.0, < 2.0)
    rubocop-rspec (3.6.0)
      lint_roller (~> 1.1)
      rubocop (~> 1.72, >= 1.72.1)
    ruby-openai (7.3.1)
      event_stream_parser (>= 0.3.0, < 2.0.0)
      faraday (>= 1)
      faraday-multipart (>= 1)
    ruby-progressbar (1.13.0)
    ruby-vips (2.1.4)
      ffi (~> 1.12)
    ruby2_keywords (0.0.5)
    ruby2ruby (2.5.0)
      ruby_parser (~> 3.1)
      sexp_processor (~> 4.6)
    ruby_parser (3.20.0)
      sexp_processor (~> 4.16)
    sass (3.7.4)
      sass-listen (~> 4.0.0)
    sass-listen (4.0.0)
      rb-fsevent (~> 0.9, >= 0.9.4)
      rb-inotify (~> 0.9, >= 0.9.7)
    sassc (2.4.0)
      ffi (~> 1.9)
    sassc-rails (2.1.2)
      railties (>= 4.0.0)
      sassc (>= 2.0)
      sprockets (> 3.0)
      sprockets-rails
      tilt
    scout_apm (5.3.3)
      parser
    scss_lint (0.60.0)
      sass (~> 3.5, >= 3.5.5)
    securerandom (0.4.1)
    seed_dump (3.3.1)
      activerecord (>= 4)
      activesupport (>= 4)
    selectize-rails (0.12.6)
    sentry-rails (5.19.0)
      railties (>= 5.0)
      sentry-ruby (~> 5.19.0)
    sentry-ruby (5.19.0)
      bigdecimal
      concurrent-ruby (~> 1.0, >= 1.0.2)
    sentry-sidekiq (5.19.0)
      sentry-ruby (~> 5.19.0)
      sidekiq (>= 3.0)
    sexp_processor (4.17.0)
    shopify_api (14.8.0)
      activesupport
      concurrent-ruby
      hash_diff
      httparty
      jwt
      oj
      openssl
      securerandom
      sorbet-runtime
      zeitwerk (~> 2.5)
    shoulda-matchers (5.3.0)
      activesupport (>= 5.2.0)
    sidekiq (7.3.1)
      concurrent-ruby (< 2)
      connection_pool (>= 2.3.0)
      logger
      rack (>= 2.2.4)
      redis-client (>= 0.22.2)
    sidekiq-cron (1.12.0)
      fugit (~> 1.8)
      globalid (>= 1.0.1)
      sidekiq (>= 6)
    signet (0.17.0)
      addressable (~> 2.8)
      faraday (>= 0.17.5, < 3.a)
      jwt (>= 1.5, < 3.0)
      multi_json (~> 1.10)
    simplecov (0.17.1)
      docile (~> 1.1)
      json (>= 1.8, < 3)
      simplecov-html (~> 0.10.0)
    simplecov-html (0.10.2)
    slack-ruby-client (2.5.2)
      faraday (>= 2.0)
      faraday-mashify
      faraday-multipart
      gli
      hashie
      logger
    snaky_hash (2.0.1)
      hashie
      version_gem (~> 1.1, >= 1.1.1)
    sorbet-runtime (0.5.11934)
    spring (4.1.1)
    spring-watcher-listen (2.1.0)
      listen (>= 2.7, < 4.0)
      spring (>= 4)
    sprockets (4.2.1)
      concurrent-ruby (~> 1.0)
      rack (>= 2.2.4, < 4)
    sprockets-rails (3.4.2)
      actionpack (>= 5.2)
      activesupport (>= 5.2)
      sprockets (>= 3.0.0)
    squasher (0.7.2)
    stackprof (0.2.25)
    statsd-ruby (1.5.0)
    stripe (8.5.0)
    telephone_number (1.4.20)
    test-prof (1.2.1)
    thor (1.3.1)
    tilt (2.3.0)
    time_diff (0.3.0)
      activesupport
      i18n
    timeout (0.4.3)
    trailblazer-option (0.1.2)
    twilio-ruby (5.77.0)
      faraday (>= 0.9, < 3.0)
      jwt (>= 1.5, < 3.0)
      nokogiri (>= 1.6, < 2.0)
    twitty (0.1.5)
      oauth
    tzinfo (2.0.6)
      concurrent-ruby (~> 1.0)
    tzinfo-data (1.2023.3)
      tzinfo (>= 1.0.0)
    uber (0.1.0)
    uglifier (4.2.0)
      execjs (>= 0.3.0, < 3)
    unf (0.1.4)
      unf_ext
    unf_ext (*******)
    unicode-display_width (3.1.4)
      unicode-emoji (~> 4.0, >= 4.0.4)
    unicode-emoji (4.0.4)
    uniform_notifier (1.17.0)
    uri (1.0.3)
    uri_template (0.7.0)
    valid_email2 (5.2.6)
      activemodel (>= 3.2)
      mail (~> 2.5)
    version_gem (1.1.4)
    vite_rails (3.0.17)
      railties (>= 5.1, < 8)
      vite_ruby (~> 3.0, >= 3.2.2)
    vite_ruby (3.8.0)
      dry-cli (>= 0.7, < 2)
      rack-proxy (~> 0.6, >= 0.6.1)
      zeitwerk (~> 2.2)
    warden (1.2.9)
      rack (>= 2.0.9)
    web-console (4.2.1)
      actionview (>= 6.0.0)
      activemodel (>= 6.0.0)
      bindex (>= 0.4.0)
      railties (>= 6.0.0)
    web-push (3.0.1)
      jwt (~> 2.0)
      openssl (~> 3.0)
    webmock (3.23.1)
      addressable (>= 2.8.0)
      crack (>= 0.3.2)
      hashdiff (>= 0.4.0, < 2.0.0)
    webrick (1.9.1)
    websocket-driver (0.7.7)
      base64
      websocket-extensions (>= 0.1.0)
    websocket-extensions (0.1.5)
    wisper (2.0.0)
    working_hours (1.4.1)
      activesupport (>= 3.2)
      tzinfo
    zeitwerk (2.6.17)

PLATFORMS
  arm64-darwin-20
  arm64-darwin-21
  arm64-darwin-22
  ruby
  x86_64-darwin-18
  x86_64-darwin-20
  x86_64-darwin-21
  x86_64-darwin-22
  x86_64-linux

DEPENDENCIES
  active_record_query_trace
  activerecord-import
  acts-as-taggable-on
  administrate (>= 0.20.1)
  administrate-field-active_storage (>= 1.0.3)
  administrate-field-belongs_to_search (>= 0.9.0)
  annotate
  attr_extras
  audited (~> 5.4, >= 5.4.1)
  aws-sdk-s3
  azure-storage-blob!
  barnes
  bootsnap
  brakeman
  browser
  bullet
  bundle-audit
  byebug
  climate_control
  commonmarker
  csv-safe
  database_cleaner
  ddtrace
  debug (~> 1.8)
  devise (>= 4.9.4)
  devise-secure_password!
  devise_token_auth (>= 1.2.3)
  dotenv-rails (>= 3.0.0)
  down
  elastic-apm
  email_reply_trimmer
  facebook-messenger
  factory_bot_rails (>= 6.4.3)
  faker
  fcm
  flag_shih_tzu
  foreman
  geocoder
  gmail_xoauth
  google-cloud-dialogflow-v2 (>= 0.24.0)
  google-cloud-storage (>= 1.48.0)
  google-cloud-translate-v3 (>= 0.7.0)
  groupdate
  grpc
  haikunator
  hairtrigger
  hashie
  html2text
  image_processing
  iso-639
  jbuilder
  json_refs
  json_schemer
  judoscale-rails
  judoscale-sidekiq
  jwt
  kaminari
  koala
  letter_opener
  line-bot-api
  liquid
  listen
  lograge (~> 0.14.0)
  maxminddb
  meta_request (>= 0.8.3)
  mock_redis
  neighbor
  net-smtp (~> 0.3.4)
  newrelic-sidekiq-metrics (>= 1.6.2)
  newrelic_rpm
  omniauth (>= 2.1.2)
  omniauth-google-oauth2 (>= 1.1.3)
  omniauth-oauth2
  omniauth-rails_csrf_protection (~> 1.0, >= 1.0.2)
  pg
  pg_search
  pgvector
  procore-sift
  pry-rails
  puma
  pundit
  rack-attack (>= 6.7.0)
  rack-cors (= 2.0.0)
  rack-mini-profiler (>= 3.2.0)
  rack-timeout
  rails (~> 7.1)
  redis
  redis-namespace
  responders (>= 3.1.1)
  rest-client
  reverse_markdown
  rspec-rails (>= 6.1.5)
  rspec_junit_formatter
  rubocop
  rubocop-factory_bot
  rubocop-performance
  rubocop-rails
  rubocop-rspec
  ruby-openai
  scout_apm
  scss_lint
  seed_dump
  sentry-rails (>= 5.19.0)
  sentry-ruby
  sentry-sidekiq (>= 5.19.0)
  shopify_api
  shoulda-matchers
  sidekiq (>= 7.3.1)
  sidekiq-cron (>= 1.12.0)
  simplecov (= 0.17.1)
  slack-ruby-client (~> 2.5.2)
  spring
  spring-watcher-listen
  squasher
  stackprof
  stripe
  telephone_number
  test-prof
  time_diff
  twilio-ruby (~> 5.66)
  twitty (~> 0.1.5)
  tzinfo-data
  uglifier
  valid_email2
  vite_rails
  web-console (>= 4.2.1)
  web-push (>= 3.0.1)
  webmock
  wisper (= 2.0.0)
  working_hours

RUBY VERSION
   ruby 3.4.4p34

BUNDLED WITH
   2.5.16
