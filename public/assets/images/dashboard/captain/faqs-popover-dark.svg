<svg width="288" height="120" viewBox="0 0 288 120" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_302_7053)">
<rect width="288" height="120" rx="8" fill="#2E3035"/>
<circle cx="8" r="1" fill="#473876"/>
<circle cx="16" r="1" fill="#473876"/>
<circle cx="24" r="1" fill="#473876"/>
<circle cx="32" r="1" fill="#473876"/>
<circle cx="40" r="1" fill="#473876"/>
<circle cx="48" r="1" fill="#473876"/>
<circle cx="56" r="1" fill="#473876"/>
<circle cx="64" r="1" fill="#473876"/>
<circle cx="72" r="1" fill="#473876"/>
<circle cx="80" r="1" fill="#473876"/>
<circle cx="88" r="1" fill="#473876"/>
<circle cx="96" r="1" fill="#473876"/>
<circle cx="104" r="1" fill="#473876"/>
<circle cx="112" r="1" fill="#473876"/>
<circle cx="120" r="1" fill="#473876"/>
<circle cx="128" r="1" fill="#473876"/>
<circle cx="136" r="1" fill="#473876"/>
<circle cx="144" r="1" fill="#473876"/>
<circle cx="152" r="1" fill="#473876"/>
<circle cx="160" r="1" fill="#473876"/>
<circle cx="168" r="1" fill="#473876"/>
<circle cx="176" r="1" fill="#473876"/>
<circle cx="184" r="1" fill="#473876"/>
<circle cx="192" r="1" fill="#473876"/>
<circle cx="200" r="1" fill="#473876"/>
<circle cx="208" r="1" fill="#473876"/>
<circle cx="216" r="1" fill="#473876"/>
<circle cx="224" r="1" fill="#473876"/>
<circle cx="232" r="1" fill="#473876"/>
<circle cx="240" r="1" fill="#473876"/>
<circle cx="248" r="1" fill="#473876"/>
<circle cx="256" r="1" fill="#473876"/>
<circle cx="264" r="1" fill="#473876"/>
<circle cx="272" r="1" fill="#473876"/>
<circle cx="280" r="1" fill="#473876"/>
<circle cx="8" cy="8" r="1" fill="#473876"/>
<circle cx="16" cy="8" r="1" fill="#473876"/>
<circle cx="24" cy="8" r="1" fill="#473876"/>
<circle cx="32" cy="8" r="1" fill="#473876"/>
<circle cx="40" cy="8" r="1" fill="#473876"/>
<circle cx="48" cy="8" r="1" fill="#473876"/>
<circle cx="56" cy="8" r="1" fill="#473876"/>
<circle cx="64" cy="8" r="1" fill="#473876"/>
<circle cx="72" cy="8" r="1" fill="#473876"/>
<circle cx="80" cy="8" r="1" fill="#473876"/>
<circle cx="88" cy="8" r="1" fill="#473876"/>
<circle cx="96" cy="8" r="1" fill="#473876"/>
<circle cx="104" cy="8" r="1" fill="#473876"/>
<circle cx="112" cy="8" r="1" fill="#473876"/>
<circle cx="120" cy="8" r="1" fill="#473876"/>
<circle cx="128" cy="8" r="1" fill="#473876"/>
<circle cx="136" cy="8" r="1" fill="#473876"/>
<circle cx="144" cy="8" r="1" fill="#473876"/>
<circle cx="152" cy="8" r="1" fill="#473876"/>
<circle cx="160" cy="8" r="1" fill="#473876"/>
<circle cx="168" cy="8" r="1" fill="#473876"/>
<circle cx="176" cy="8" r="1" fill="#473876"/>
<circle cx="184" cy="8" r="1" fill="#473876"/>
<circle cx="192" cy="8" r="1" fill="#473876"/>
<circle cx="200" cy="8" r="1" fill="#473876"/>
<circle cx="208" cy="8" r="1" fill="#473876"/>
<circle cx="216" cy="8" r="1" fill="#473876"/>
<circle cx="224" cy="8" r="1" fill="#473876"/>
<circle cx="232" cy="8" r="1" fill="#473876"/>
<circle cx="240" cy="8" r="1" fill="#473876"/>
<circle cx="248" cy="8" r="1" fill="#473876"/>
<circle cx="256" cy="8" r="1" fill="#473876"/>
<circle cx="264" cy="8" r="1" fill="#473876"/>
<circle cx="272" cy="8" r="1" fill="#473876"/>
<circle cx="280" cy="8" r="1" fill="#473876"/>
<circle cx="8" cy="16" r="1" fill="#473876"/>
<circle cx="16" cy="16" r="1" fill="#473876"/>
<circle cx="24" cy="16" r="1" fill="#473876"/>
<circle cx="32" cy="16" r="1" fill="#473876"/>
<circle cx="40" cy="16" r="1" fill="#473876"/>
<circle cx="48" cy="16" r="1" fill="#473876"/>
<circle cx="56" cy="16" r="1" fill="#473876"/>
<circle cx="64" cy="16" r="1" fill="#473876"/>
<circle cx="72" cy="16" r="1" fill="#473876"/>
<circle cx="80" cy="16" r="1" fill="#473876"/>
<circle cx="88" cy="16" r="1" fill="#473876"/>
<circle cx="96" cy="16" r="1" fill="#473876"/>
<circle cx="104" cy="16" r="1" fill="#473876"/>
<circle cx="112" cy="16" r="1" fill="#473876"/>
<circle cx="120" cy="16" r="1" fill="#473876"/>
<circle cx="128" cy="16" r="1" fill="#473876"/>
<circle cx="136" cy="16" r="1" fill="#473876"/>
<circle cx="144" cy="16" r="1" fill="#473876"/>
<circle cx="152" cy="16" r="1" fill="#473876"/>
<circle cx="160" cy="16" r="1" fill="#473876"/>
<circle cx="168" cy="16" r="1" fill="#473876"/>
<circle cx="176" cy="16" r="1" fill="#473876"/>
<circle cx="184" cy="16" r="1" fill="#473876"/>
<circle cx="192" cy="16" r="1" fill="#473876"/>
<circle cx="200" cy="16" r="1" fill="#473876"/>
<circle cx="208" cy="16" r="1" fill="#473876"/>
<circle cx="216" cy="16" r="1" fill="#473876"/>
<circle cx="224" cy="16" r="1" fill="#473876"/>
<circle cx="232" cy="16" r="1" fill="#473876"/>
<circle cx="240" cy="16" r="1" fill="#473876"/>
<circle cx="248" cy="16" r="1" fill="#473876"/>
<circle cx="256" cy="16" r="1" fill="#473876"/>
<circle cx="264" cy="16" r="1" fill="#473876"/>
<circle cx="272" cy="16" r="1" fill="#473876"/>
<circle cx="280" cy="16" r="1" fill="#473876"/>
<circle cx="8" cy="24" r="1" fill="#473876"/>
<circle cx="16" cy="24" r="1" fill="#473876"/>
<circle cx="24" cy="24" r="1" fill="#473876"/>
<circle cx="32" cy="24" r="1" fill="#473876"/>
<circle cx="40" cy="24" r="1" fill="#473876"/>
<circle cx="48" cy="24" r="1" fill="#473876"/>
<circle cx="56" cy="24" r="1" fill="#473876"/>
<circle cx="64" cy="24" r="1" fill="#473876"/>
<circle cx="72" cy="24" r="1" fill="#473876"/>
<circle cx="80" cy="24" r="1" fill="#473876"/>
<circle cx="88" cy="24" r="1" fill="#473876"/>
<circle cx="96" cy="24" r="1" fill="#473876"/>
<circle cx="104" cy="24" r="1" fill="#473876"/>
<circle cx="112" cy="24" r="1" fill="#473876"/>
<circle cx="120" cy="24" r="1" fill="#473876"/>
<circle cx="128" cy="24" r="1" fill="#473876"/>
<circle cx="136" cy="24" r="1" fill="#473876"/>
<circle cx="144" cy="24" r="1" fill="#473876"/>
<circle cx="152" cy="24" r="1" fill="#473876"/>
<circle cx="160" cy="24" r="1" fill="#473876"/>
<circle cx="168" cy="24" r="1" fill="#473876"/>
<circle cx="176" cy="24" r="1" fill="#473876"/>
<circle cx="184" cy="24" r="1" fill="#473876"/>
<circle cx="192" cy="24" r="1" fill="#473876"/>
<circle cx="200" cy="24" r="1" fill="#473876"/>
<circle cx="208" cy="24" r="1" fill="#473876"/>
<circle cx="216" cy="24" r="1" fill="#473876"/>
<circle cx="224" cy="24" r="1" fill="#473876"/>
<circle cx="232" cy="24" r="1" fill="#473876"/>
<circle cx="240" cy="24" r="1" fill="#473876"/>
<circle cx="248" cy="24" r="1" fill="#473876"/>
<circle cx="256" cy="24" r="1" fill="#473876"/>
<circle cx="264" cy="24" r="1" fill="#473876"/>
<circle cx="272" cy="24" r="1" fill="#473876"/>
<circle cx="280" cy="24" r="1" fill="#473876"/>
<circle cx="8" cy="32" r="1" fill="#473876"/>
<circle cx="16" cy="32" r="1" fill="#473876"/>
<circle cx="24" cy="32" r="1" fill="#473876"/>
<circle cx="32" cy="32" r="1" fill="#473876"/>
<circle cx="40" cy="32" r="1" fill="#473876"/>
<circle cx="48" cy="32" r="1" fill="#473876"/>
<circle cx="56" cy="32" r="1" fill="#473876"/>
<circle cx="64" cy="32" r="1" fill="#473876"/>
<circle cx="72" cy="32" r="1" fill="#473876"/>
<circle cx="80" cy="32" r="1" fill="#473876"/>
<circle cx="88" cy="32" r="1" fill="#473876"/>
<circle cx="96" cy="32" r="1" fill="#473876"/>
<circle cx="104" cy="32" r="1" fill="#473876"/>
<circle cx="112" cy="32" r="1" fill="#473876"/>
<circle cx="120" cy="32" r="1" fill="#473876"/>
<circle cx="128" cy="32" r="1" fill="#473876"/>
<circle cx="136" cy="32" r="1" fill="#473876"/>
<circle cx="144" cy="32" r="1" fill="#473876"/>
<circle cx="152" cy="32" r="1" fill="#473876"/>
<circle cx="160" cy="32" r="1" fill="#473876"/>
<circle cx="168" cy="32" r="1" fill="#473876"/>
<circle cx="176" cy="32" r="1" fill="#473876"/>
<circle cx="184" cy="32" r="1" fill="#473876"/>
<circle cx="192" cy="32" r="1" fill="#473876"/>
<circle cx="200" cy="32" r="1" fill="#473876"/>
<circle cx="208" cy="32" r="1" fill="#473876"/>
<circle cx="216" cy="32" r="1" fill="#473876"/>
<circle cx="224" cy="32" r="1" fill="#473876"/>
<circle cx="232" cy="32" r="1" fill="#473876"/>
<circle cx="240" cy="32" r="1" fill="#473876"/>
<circle cx="248" cy="32" r="1" fill="#473876"/>
<circle cx="256" cy="32" r="1" fill="#473876"/>
<circle cx="264" cy="32" r="1" fill="#473876"/>
<circle cx="272" cy="32" r="1" fill="#473876"/>
<circle cx="280" cy="32" r="1" fill="#473876"/>
<circle cx="8" cy="40" r="1" fill="#473876"/>
<circle cx="16" cy="40" r="1" fill="#473876"/>
<circle cx="24" cy="40" r="1" fill="#473876"/>
<circle cx="32" cy="40" r="1" fill="#473876"/>
<circle cx="40" cy="40" r="1" fill="#473876"/>
<circle cx="48" cy="40" r="1" fill="#473876"/>
<circle cx="56" cy="40" r="1" fill="#473876"/>
<circle cx="64" cy="40" r="1" fill="#473876"/>
<circle cx="72" cy="40" r="1" fill="#473876"/>
<circle cx="80" cy="40" r="1" fill="#473876"/>
<circle cx="88" cy="40" r="1" fill="#473876"/>
<circle cx="96" cy="40" r="1" fill="#473876"/>
<circle cx="104" cy="40" r="1" fill="#473876"/>
<circle cx="112" cy="40" r="1" fill="#473876"/>
<circle cx="120" cy="40" r="1" fill="#473876"/>
<circle cx="128" cy="40" r="1" fill="#473876"/>
<circle cx="136" cy="40" r="1" fill="#473876"/>
<circle cx="144" cy="40" r="1" fill="#473876"/>
<circle cx="152" cy="40" r="1" fill="#473876"/>
<circle cx="160" cy="40" r="1" fill="#473876"/>
<circle cx="168" cy="40" r="1" fill="#473876"/>
<circle cx="176" cy="40" r="1" fill="#473876"/>
<circle cx="184" cy="40" r="1" fill="#473876"/>
<circle cx="192" cy="40" r="1" fill="#473876"/>
<circle cx="200" cy="40" r="1" fill="#473876"/>
<circle cx="208" cy="40" r="1" fill="#473876"/>
<circle cx="216" cy="40" r="1" fill="#473876"/>
<circle cx="224" cy="40" r="1" fill="#473876"/>
<circle cx="232" cy="40" r="1" fill="#473876"/>
<circle cx="240" cy="40" r="1" fill="#473876"/>
<circle cx="248" cy="40" r="1" fill="#473876"/>
<circle cx="256" cy="40" r="1" fill="#473876"/>
<circle cx="264" cy="40" r="1" fill="#473876"/>
<circle cx="272" cy="40" r="1" fill="#473876"/>
<circle cx="280" cy="40" r="1" fill="#473876"/>
<circle cx="8" cy="48" r="1" fill="#473876"/>
<circle cx="16" cy="48" r="1" fill="#473876"/>
<circle cx="24" cy="48" r="1" fill="#473876"/>
<circle cx="32" cy="48" r="1" fill="#473876"/>
<circle cx="40" cy="48" r="1" fill="#473876"/>
<circle cx="48" cy="48" r="1" fill="#473876"/>
<circle cx="56" cy="48" r="1" fill="#473876"/>
<circle cx="64" cy="48" r="1" fill="#473876"/>
<circle cx="72" cy="48" r="1" fill="#473876"/>
<circle cx="80" cy="48" r="1" fill="#473876"/>
<circle cx="88" cy="48" r="1" fill="#473876"/>
<circle cx="96" cy="48" r="1" fill="#473876"/>
<circle cx="104" cy="48" r="1" fill="#473876"/>
<circle cx="112" cy="48" r="1" fill="#473876"/>
<circle cx="120" cy="48" r="1" fill="#473876"/>
<circle cx="128" cy="48" r="1" fill="#473876"/>
<circle cx="136" cy="48" r="1" fill="#473876"/>
<circle cx="144" cy="48" r="1" fill="#473876"/>
<circle cx="152" cy="48" r="1" fill="#473876"/>
<circle cx="160" cy="48" r="1" fill="#473876"/>
<circle cx="168" cy="48" r="1" fill="#473876"/>
<circle cx="176" cy="48" r="1" fill="#473876"/>
<circle cx="184" cy="48" r="1" fill="#473876"/>
<circle cx="192" cy="48" r="1" fill="#473876"/>
<circle cx="200" cy="48" r="1" fill="#473876"/>
<circle cx="208" cy="48" r="1" fill="#473876"/>
<circle cx="216" cy="48" r="1" fill="#473876"/>
<circle cx="224" cy="48" r="1" fill="#473876"/>
<circle cx="232" cy="48" r="1" fill="#473876"/>
<circle cx="240" cy="48" r="1" fill="#473876"/>
<circle cx="248" cy="48" r="1" fill="#473876"/>
<circle cx="256" cy="48" r="1" fill="#473876"/>
<circle cx="264" cy="48" r="1" fill="#473876"/>
<circle cx="272" cy="48" r="1" fill="#473876"/>
<circle cx="280" cy="48" r="1" fill="#473876"/>
<circle cx="8" cy="56" r="1" fill="#473876"/>
<circle cx="16" cy="56" r="1" fill="#473876"/>
<circle cx="24" cy="56" r="1" fill="#473876"/>
<circle cx="32" cy="56" r="1" fill="#473876"/>
<circle cx="40" cy="56" r="1" fill="#473876"/>
<circle cx="48" cy="56" r="1" fill="#473876"/>
<circle cx="56" cy="56" r="1" fill="#473876"/>
<circle cx="64" cy="56" r="1" fill="#473876"/>
<circle cx="72" cy="56" r="1" fill="#473876"/>
<circle cx="80" cy="56" r="1" fill="#473876"/>
<circle cx="88" cy="56" r="1" fill="#473876"/>
<circle cx="96" cy="56" r="1" fill="#473876"/>
<circle cx="104" cy="56" r="1" fill="#473876"/>
<circle cx="112" cy="56" r="1" fill="#473876"/>
<circle cx="120" cy="56" r="1" fill="#473876"/>
<circle cx="128" cy="56" r="1" fill="#473876"/>
<circle cx="136" cy="56" r="1" fill="#473876"/>
<circle cx="144" cy="56" r="1" fill="#473876"/>
<circle cx="152" cy="56" r="1" fill="#473876"/>
<circle cx="160" cy="56" r="1" fill="#473876"/>
<circle cx="168" cy="56" r="1" fill="#473876"/>
<circle cx="176" cy="56" r="1" fill="#473876"/>
<circle cx="184" cy="56" r="1" fill="#473876"/>
<circle cx="192" cy="56" r="1" fill="#473876"/>
<circle cx="200" cy="56" r="1" fill="#473876"/>
<circle cx="208" cy="56" r="1" fill="#473876"/>
<circle cx="216" cy="56" r="1" fill="#473876"/>
<circle cx="224" cy="56" r="1" fill="#473876"/>
<circle cx="232" cy="56" r="1" fill="#473876"/>
<circle cx="240" cy="56" r="1" fill="#473876"/>
<circle cx="248" cy="56" r="1" fill="#473876"/>
<circle cx="256" cy="56" r="1" fill="#473876"/>
<circle cx="264" cy="56" r="1" fill="#473876"/>
<circle cx="272" cy="56" r="1" fill="#473876"/>
<circle cx="280" cy="56" r="1" fill="#473876"/>
<circle cx="8" cy="64" r="1" fill="#473876"/>
<circle cx="16" cy="64" r="1" fill="#473876"/>
<circle cx="24" cy="64" r="1" fill="#473876"/>
<circle cx="32" cy="64" r="1" fill="#473876"/>
<circle cx="40" cy="64" r="1" fill="#473876"/>
<circle cx="48" cy="64" r="1" fill="#473876"/>
<circle cx="56" cy="64" r="1" fill="#473876"/>
<circle cx="64" cy="64" r="1" fill="#473876"/>
<circle cx="72" cy="64" r="1" fill="#473876"/>
<circle cx="80" cy="64" r="1" fill="#473876"/>
<circle cx="88" cy="64" r="1" fill="#473876"/>
<circle cx="96" cy="64" r="1" fill="#473876"/>
<circle cx="104" cy="64" r="1" fill="#473876"/>
<circle cx="112" cy="64" r="1" fill="#473876"/>
<circle cx="120" cy="64" r="1" fill="#473876"/>
<circle cx="128" cy="64" r="1" fill="#473876"/>
<circle cx="136" cy="64" r="1" fill="#473876"/>
<circle cx="144" cy="64" r="1" fill="#473876"/>
<circle cx="152" cy="64" r="1" fill="#473876"/>
<circle cx="160" cy="64" r="1" fill="#473876"/>
<circle cx="168" cy="64" r="1" fill="#473876"/>
<circle cx="176" cy="64" r="1" fill="#473876"/>
<circle cx="184" cy="64" r="1" fill="#473876"/>
<circle cx="192" cy="64" r="1" fill="#473876"/>
<circle cx="200" cy="64" r="1" fill="#473876"/>
<circle cx="208" cy="64" r="1" fill="#473876"/>
<circle cx="216" cy="64" r="1" fill="#473876"/>
<circle cx="224" cy="64" r="1" fill="#473876"/>
<circle cx="232" cy="64" r="1" fill="#473876"/>
<circle cx="240" cy="64" r="1" fill="#473876"/>
<circle cx="248" cy="64" r="1" fill="#473876"/>
<circle cx="256" cy="64" r="1" fill="#473876"/>
<circle cx="264" cy="64" r="1" fill="#473876"/>
<circle cx="272" cy="64" r="1" fill="#473876"/>
<circle cx="280" cy="64" r="1" fill="#473876"/>
<circle cx="8" cy="72" r="1" fill="#473876"/>
<circle cx="16" cy="72" r="1" fill="#473876"/>
<circle cx="24" cy="72" r="1" fill="#473876"/>
<circle cx="32" cy="72" r="1" fill="#473876"/>
<circle cx="40" cy="72" r="1" fill="#473876"/>
<circle cx="48" cy="72" r="1" fill="#473876"/>
<circle cx="56" cy="72" r="1" fill="#473876"/>
<circle cx="64" cy="72" r="1" fill="#473876"/>
<circle cx="72" cy="72" r="1" fill="#473876"/>
<circle cx="80" cy="72" r="1" fill="#473876"/>
<circle cx="88" cy="72" r="1" fill="#473876"/>
<circle cx="96" cy="72" r="1" fill="#473876"/>
<circle cx="104" cy="72" r="1" fill="#473876"/>
<circle cx="112" cy="72" r="1" fill="#473876"/>
<circle cx="120" cy="72" r="1" fill="#473876"/>
<circle cx="128" cy="72" r="1" fill="#473876"/>
<circle cx="136" cy="72" r="1" fill="#473876"/>
<circle cx="144" cy="72" r="1" fill="#473876"/>
<circle cx="152" cy="72" r="1" fill="#473876"/>
<circle cx="160" cy="72" r="1" fill="#473876"/>
<circle cx="168" cy="72" r="1" fill="#473876"/>
<circle cx="176" cy="72" r="1" fill="#473876"/>
<circle cx="184" cy="72" r="1" fill="#473876"/>
<circle cx="192" cy="72" r="1" fill="#473876"/>
<circle cx="200" cy="72" r="1" fill="#473876"/>
<circle cx="208" cy="72" r="1" fill="#473876"/>
<circle cx="216" cy="72" r="1" fill="#473876"/>
<circle cx="224" cy="72" r="1" fill="#473876"/>
<circle cx="232" cy="72" r="1" fill="#473876"/>
<circle cx="240" cy="72" r="1" fill="#473876"/>
<circle cx="248" cy="72" r="1" fill="#473876"/>
<circle cx="256" cy="72" r="1" fill="#473876"/>
<circle cx="264" cy="72" r="1" fill="#473876"/>
<circle cx="272" cy="72" r="1" fill="#473876"/>
<circle cx="280" cy="72" r="1" fill="#473876"/>
<circle cx="8" cy="80" r="1" fill="#473876"/>
<circle cx="16" cy="80" r="1" fill="#473876"/>
<circle cx="24" cy="80" r="1" fill="#473876"/>
<circle cx="32" cy="80" r="1" fill="#473876"/>
<circle cx="40" cy="80" r="1" fill="#473876"/>
<circle cx="48" cy="80" r="1" fill="#473876"/>
<circle cx="56" cy="80" r="1" fill="#473876"/>
<circle cx="64" cy="80" r="1" fill="#473876"/>
<circle cx="72" cy="80" r="1" fill="#473876"/>
<circle cx="80" cy="80" r="1" fill="#473876"/>
<circle cx="88" cy="80" r="1" fill="#473876"/>
<circle cx="96" cy="80" r="1" fill="#473876"/>
<circle cx="104" cy="80" r="1" fill="#473876"/>
<circle cx="112" cy="80" r="1" fill="#473876"/>
<circle cx="120" cy="80" r="1" fill="#473876"/>
<circle cx="128" cy="80" r="1" fill="#473876"/>
<circle cx="136" cy="80" r="1" fill="#473876"/>
<circle cx="144" cy="80" r="1" fill="#473876"/>
<circle cx="152" cy="80" r="1" fill="#473876"/>
<circle cx="160" cy="80" r="1" fill="#473876"/>
<circle cx="168" cy="80" r="1" fill="#473876"/>
<circle cx="176" cy="80" r="1" fill="#473876"/>
<circle cx="184" cy="80" r="1" fill="#473876"/>
<circle cx="192" cy="80" r="1" fill="#473876"/>
<circle cx="200" cy="80" r="1" fill="#473876"/>
<circle cx="208" cy="80" r="1" fill="#473876"/>
<circle cx="216" cy="80" r="1" fill="#473876"/>
<circle cx="224" cy="80" r="1" fill="#473876"/>
<circle cx="232" cy="80" r="1" fill="#473876"/>
<circle cx="240" cy="80" r="1" fill="#473876"/>
<circle cx="248" cy="80" r="1" fill="#473876"/>
<circle cx="256" cy="80" r="1" fill="#473876"/>
<circle cx="264" cy="80" r="1" fill="#473876"/>
<circle cx="272" cy="80" r="1" fill="#473876"/>
<circle cx="280" cy="80" r="1" fill="#473876"/>
<circle cx="8" cy="88" r="1" fill="#473876"/>
<circle cx="16" cy="88" r="1" fill="#473876"/>
<circle cx="24" cy="88" r="1" fill="#473876"/>
<circle cx="32" cy="88" r="1" fill="#473876"/>
<circle cx="40" cy="88" r="1" fill="#473876"/>
<circle cx="48" cy="88" r="1" fill="#473876"/>
<circle cx="56" cy="88" r="1" fill="#473876"/>
<circle cx="64" cy="88" r="1" fill="#473876"/>
<circle cx="72" cy="88" r="1" fill="#473876"/>
<circle cx="80" cy="88" r="1" fill="#473876"/>
<circle cx="88" cy="88" r="1" fill="#473876"/>
<circle cx="96" cy="88" r="1" fill="#473876"/>
<circle cx="104" cy="88" r="1" fill="#473876"/>
<circle cx="112" cy="88" r="1" fill="#473876"/>
<circle cx="120" cy="88" r="1" fill="#473876"/>
<circle cx="128" cy="88" r="1" fill="#473876"/>
<circle cx="136" cy="88" r="1" fill="#473876"/>
<circle cx="144" cy="88" r="1" fill="#473876"/>
<circle cx="152" cy="88" r="1" fill="#473876"/>
<circle cx="160" cy="88" r="1" fill="#473876"/>
<circle cx="168" cy="88" r="1" fill="#473876"/>
<circle cx="176" cy="88" r="1" fill="#473876"/>
<circle cx="184" cy="88" r="1" fill="#473876"/>
<circle cx="192" cy="88" r="1" fill="#473876"/>
<circle cx="200" cy="88" r="1" fill="#473876"/>
<circle cx="208" cy="88" r="1" fill="#473876"/>
<circle cx="216" cy="88" r="1" fill="#473876"/>
<circle cx="224" cy="88" r="1" fill="#473876"/>
<circle cx="232" cy="88" r="1" fill="#473876"/>
<circle cx="240" cy="88" r="1" fill="#473876"/>
<circle cx="248" cy="88" r="1" fill="#473876"/>
<circle cx="256" cy="88" r="1" fill="#473876"/>
<circle cx="264" cy="88" r="1" fill="#473876"/>
<circle cx="272" cy="88" r="1" fill="#473876"/>
<circle cx="280" cy="88" r="1" fill="#473876"/>
<circle cx="8" cy="96" r="1" fill="#473876"/>
<circle cx="16" cy="96" r="1" fill="#473876"/>
<circle cx="24" cy="96" r="1" fill="#473876"/>
<circle cx="32" cy="96" r="1" fill="#473876"/>
<circle cx="40" cy="96" r="1" fill="#473876"/>
<circle cx="48" cy="96" r="1" fill="#473876"/>
<circle cx="56" cy="96" r="1" fill="#473876"/>
<circle cx="64" cy="96" r="1" fill="#473876"/>
<circle cx="72" cy="96" r="1" fill="#473876"/>
<circle cx="80" cy="96" r="1" fill="#473876"/>
<circle cx="88" cy="96" r="1" fill="#473876"/>
<circle cx="96" cy="96" r="1" fill="#473876"/>
<circle cx="104" cy="96" r="1" fill="#473876"/>
<circle cx="112" cy="96" r="1" fill="#473876"/>
<circle cx="120" cy="96" r="1" fill="#473876"/>
<circle cx="128" cy="96" r="1" fill="#473876"/>
<circle cx="136" cy="96" r="1" fill="#473876"/>
<circle cx="144" cy="96" r="1" fill="#473876"/>
<circle cx="152" cy="96" r="1" fill="#473876"/>
<circle cx="160" cy="96" r="1" fill="#473876"/>
<circle cx="168" cy="96" r="1" fill="#473876"/>
<circle cx="176" cy="96" r="1" fill="#473876"/>
<circle cx="184" cy="96" r="1" fill="#473876"/>
<circle cx="192" cy="96" r="1" fill="#473876"/>
<circle cx="200" cy="96" r="1" fill="#473876"/>
<circle cx="208" cy="96" r="1" fill="#473876"/>
<circle cx="216" cy="96" r="1" fill="#473876"/>
<circle cx="224" cy="96" r="1" fill="#473876"/>
<circle cx="232" cy="96" r="1" fill="#473876"/>
<circle cx="240" cy="96" r="1" fill="#473876"/>
<circle cx="248" cy="96" r="1" fill="#473876"/>
<circle cx="256" cy="96" r="1" fill="#473876"/>
<circle cx="264" cy="96" r="1" fill="#473876"/>
<circle cx="272" cy="96" r="1" fill="#473876"/>
<circle cx="280" cy="96" r="1" fill="#473876"/>
<circle cx="8" cy="104" r="1" fill="#473876"/>
<circle cx="16" cy="104" r="1" fill="#473876"/>
<circle cx="24" cy="104" r="1" fill="#473876"/>
<circle cx="32" cy="104" r="1" fill="#473876"/>
<circle cx="40" cy="104" r="1" fill="#473876"/>
<circle cx="48" cy="104" r="1" fill="#473876"/>
<circle cx="56" cy="104" r="1" fill="#473876"/>
<circle cx="64" cy="104" r="1" fill="#473876"/>
<circle cx="72" cy="104" r="1" fill="#473876"/>
<circle cx="80" cy="104" r="1" fill="#473876"/>
<circle cx="88" cy="104" r="1" fill="#473876"/>
<circle cx="96" cy="104" r="1" fill="#473876"/>
<circle cx="104" cy="104" r="1" fill="#473876"/>
<circle cx="112" cy="104" r="1" fill="#473876"/>
<circle cx="120" cy="104" r="1" fill="#473876"/>
<circle cx="128" cy="104" r="1" fill="#473876"/>
<circle cx="136" cy="104" r="1" fill="#473876"/>
<circle cx="144" cy="104" r="1" fill="#473876"/>
<circle cx="152" cy="104" r="1" fill="#473876"/>
<circle cx="160" cy="104" r="1" fill="#473876"/>
<circle cx="168" cy="104" r="1" fill="#473876"/>
<circle cx="176" cy="104" r="1" fill="#473876"/>
<circle cx="184" cy="104" r="1" fill="#473876"/>
<circle cx="192" cy="104" r="1" fill="#473876"/>
<circle cx="200" cy="104" r="1" fill="#473876"/>
<circle cx="208" cy="104" r="1" fill="#473876"/>
<circle cx="216" cy="104" r="1" fill="#473876"/>
<circle cx="224" cy="104" r="1" fill="#473876"/>
<circle cx="232" cy="104" r="1" fill="#473876"/>
<circle cx="240" cy="104" r="1" fill="#473876"/>
<circle cx="248" cy="104" r="1" fill="#473876"/>
<circle cx="256" cy="104" r="1" fill="#473876"/>
<circle cx="264" cy="104" r="1" fill="#473876"/>
<circle cx="272" cy="104" r="1" fill="#473876"/>
<circle cx="280" cy="104" r="1" fill="#473876"/>
<circle cx="8" cy="112" r="1" fill="#473876"/>
<circle cx="16" cy="112" r="1" fill="#473876"/>
<circle cx="24" cy="112" r="1" fill="#473876"/>
<circle cx="32" cy="112" r="1" fill="#473876"/>
<circle cx="40" cy="112" r="1" fill="#473876"/>
<circle cx="48" cy="112" r="1" fill="#473876"/>
<circle cx="56" cy="112" r="1" fill="#473876"/>
<circle cx="64" cy="112" r="1" fill="#473876"/>
<circle cx="72" cy="112" r="1" fill="#473876"/>
<circle cx="80" cy="112" r="1" fill="#473876"/>
<circle cx="88" cy="112" r="1" fill="#473876"/>
<circle cx="96" cy="112" r="1" fill="#473876"/>
<circle cx="104" cy="112" r="1" fill="#473876"/>
<circle cx="112" cy="112" r="1" fill="#473876"/>
<circle cx="120" cy="112" r="1" fill="#473876"/>
<circle cx="128" cy="112" r="1" fill="#473876"/>
<circle cx="136" cy="112" r="1" fill="#473876"/>
<circle cx="144" cy="112" r="1" fill="#473876"/>
<circle cx="152" cy="112" r="1" fill="#473876"/>
<circle cx="160" cy="112" r="1" fill="#473876"/>
<circle cx="168" cy="112" r="1" fill="#473876"/>
<circle cx="176" cy="112" r="1" fill="#473876"/>
<circle cx="184" cy="112" r="1" fill="#473876"/>
<circle cx="192" cy="112" r="1" fill="#473876"/>
<circle cx="200" cy="112" r="1" fill="#473876"/>
<circle cx="208" cy="112" r="1" fill="#473876"/>
<circle cx="216" cy="112" r="1" fill="#473876"/>
<circle cx="224" cy="112" r="1" fill="#473876"/>
<circle cx="232" cy="112" r="1" fill="#473876"/>
<circle cx="240" cy="112" r="1" fill="#473876"/>
<circle cx="248" cy="112" r="1" fill="#473876"/>
<circle cx="256" cy="112" r="1" fill="#473876"/>
<circle cx="264" cy="112" r="1" fill="#473876"/>
<circle cx="272" cy="112" r="1" fill="#473876"/>
<circle cx="280" cy="112" r="1" fill="#473876"/>
<circle cx="120" cy="120" r="1" fill="#473876"/>
<circle cx="128" cy="120" r="1" fill="#473876"/>
<circle cx="136" cy="120" r="1" fill="#473876"/>
<circle cx="144" cy="120" r="1" fill="#473876"/>
<circle cx="152" cy="120" r="1" fill="#473876"/>
<circle cx="160" cy="120" r="1" fill="#473876"/>
<circle cx="168" cy="120" r="1" fill="#473876"/>
<rect width="288" height="120" fill="url(#paint0_radial_302_7053)"/>
<path d="M119.888 53.93C119.993 52.8088 120.987 51.9847 122.108 52.0894C123.229 52.194 124.053 53.1878 123.949 54.309L123.511 59.0009C123.406 60.1222 122.412 60.9462 121.291 60.8416C120.17 60.7369 119.346 59.7431 119.451 58.6219L119.888 53.93Z" fill="#E2DDFE"/>
<path d="M126.384 54.5364C126.489 53.4152 127.483 52.5911 128.604 52.6957C129.725 52.8004 130.549 53.7942 130.444 54.9154L130.006 59.6073C129.902 60.7285 128.908 61.5526 127.787 61.4479C126.666 61.3433 125.841 60.3495 125.946 59.2283L126.384 54.5364Z" fill="#E2DDFE"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M131.943 45.1464C124.483 43.3741 118.324 42.7567 110.491 43.1392C108.511 43.2359 107.201 43.3038 106.188 43.4998C105.249 43.6816 104.7 43.9516 104.225 44.392C103.242 45.3039 103.019 46.4183 102.532 50.3318C102.052 54.1779 101.966 57.657 102.05 61.5884C102.095 63.6834 102.129 65.0975 102.291 66.1854C102.446 67.2174 102.695 67.7878 103.074 68.242C103.456 68.6999 103.968 69.0453 104.936 69.3773C105.96 69.7285 107.32 70.01 109.342 70.4235C115.881 71.7609 120.826 72.2189 127.523 72.1245C129.611 72.0951 131.022 72.0722 132.109 71.9198C133.141 71.775 133.712 71.5326 134.165 71.1647C134.608 70.8047 134.97 70.2846 135.338 69.2507C135.723 68.172 136.05 66.7279 136.528 64.606C137.379 60.82 138.049 57.4879 138.407 53.8025C138.789 49.8622 138.813 48.7331 138.01 47.6088C137.622 47.0658 137.124 46.686 136.215 46.3237C135.233 45.9323 133.924 45.6169 131.943 45.1464ZM110.328 39.7902C118.487 39.3918 124.962 40.0416 132.718 41.8842L132.832 41.9112C134.668 42.3474 136.22 42.7159 137.457 43.209C138.792 43.7411 139.882 44.4616 140.738 45.6596C142.353 47.9196 142.11 50.3994 141.786 53.6941C141.772 53.8367 141.758 53.9809 141.744 54.1266C141.365 58.0233 140.659 61.5178 139.799 65.3416L139.776 65.4428C139.327 67.4383 138.958 69.0827 138.497 70.3757C138.013 71.7354 137.372 72.8801 136.278 73.768C135.194 74.6482 133.972 75.0442 132.575 75.2402C131.255 75.4254 129.632 75.4482 127.675 75.4757L127.57 75.4772C120.635 75.5749 115.449 75.0949 108.67 73.7085L108.565 73.6871C106.675 73.3005 105.101 72.9786 103.848 72.5489C102.517 72.0922 101.399 71.4675 100.5 70.3898C99.5976 69.3086 99.185 68.0852 98.9751 66.6813C98.7771 65.3571 98.7422 63.7281 98.7002 61.7665L98.6979 61.6603C98.6114 57.6284 98.6984 53.9764 99.2043 49.9171C99.2227 49.7699 99.2407 49.6242 99.2585 49.4801C99.6617 46.2178 99.9642 43.7706 101.945 41.9336C102.996 40.959 104.175 40.4743 105.551 40.208C106.826 39.961 108.378 39.8853 110.214 39.7957C110.252 39.7939 110.29 39.792 110.328 39.7902Z" fill="#E2DDFE"/>
<path d="M172.351 54.1886C169.848 52.8972 166.772 53.8794 165.481 56.3826C164.949 57.4133 165.353 58.6799 166.384 59.2117L189.49 71.1328C190.521 71.6645 191.788 71.2601 192.319 70.2294C193.611 67.7262 192.628 64.6501 190.125 63.3587L172.351 54.1886Z" fill="#3B334A" stroke="#C9C7DD" stroke-width="2.2"/>
<path d="M184.599 52.2402C182.096 50.9487 179.02 51.931 177.729 54.4341C177.197 55.4648 177.601 56.7315 178.632 57.2633L184.745 60.4169C185.775 60.9486 187.042 60.5442 187.574 59.5135C188.865 57.0103 187.883 53.9342 185.38 52.6428L184.599 52.2402Z" fill="#3B334A" stroke="#C9C7DD" stroke-width="2.2"/>
<path d="M171.38 33.2415C172.263 33.3009 172.829 32.7874 172.875 32.101C172.878 32.0617 172.881 32.0225 172.883 31.9833C172.939 31.1596 173.504 30.6657 174.57 30.0479C176.104 29.2055 177.322 28.3812 177.452 26.4396C177.635 23.7332 175.315 22.1981 172.647 22.0187C169.941 21.8367 168.075 22.9325 167.513 24.2541C167.396 24.5221 167.339 24.794 167.32 25.0685C167.269 25.8334 167.849 26.2861 168.418 26.3244C169.006 26.364 169.396 26.1341 169.757 25.7447L170.117 25.3748C170.819 24.5947 171.548 24.3088 172.391 24.3655C173.666 24.4513 174.479 25.2546 174.403 26.3724C174.333 27.4118 173.615 27.8363 172.241 28.6501C171.151 29.3254 170.194 30.0884 170.088 31.6574C170.087 31.677 170.083 31.7358 170.081 31.7554C170.019 32.6773 170.478 33.1808 171.38 33.2415ZM171.024 37.9457C171.985 38.0103 172.88 37.3023 172.945 36.3411C173.011 35.3606 172.237 34.56 171.257 34.494C170.276 34.4281 169.401 35.1375 169.336 36.0984C169.273 37.0399 170.063 37.8811 171.024 37.9457Z" fill="#C171FF"/>
<path d="M169.5 43.5V43.5C168.978 46.0605 166.726 47.9 164.113 47.9L158.189 47.9C156.148 47.9 154.192 48.7162 152.757 50.1668V50.1668C149.147 53.8152 150.088 59.9144 154.63 62.305L155 62.5" stroke="#C171FF" stroke-width="2.4" stroke-linecap="round"/>
<path d="M162 65L167.282 66.1318C168.414 66.3744 169.48 66.8597 170.406 67.5543L170.555 67.6661C172.129 68.8471 173.213 70.5671 173.599 72.4972L174 74.5" stroke="#C171FF" stroke-width="2.4" stroke-linecap="round"/>
<path d="M169.65 78.4531L169.836 78.8248L170.192 78.611C171.313 77.9385 172.732 77.9745 173.913 78.4746C175.105 78.9794 175.939 79.903 176.02 80.8889C176.189 82.9307 174.281 84.8177 171.408 85.055L159.964 86.0003C158.714 86.1037 157.677 85.8776 156.949 85.4334C156.23 84.9947 155.799 84.3391 155.732 83.5239C155.676 82.8454 155.81 82.0406 156.308 81.47C156.788 80.9204 157.684 80.5017 159.349 80.7397L159.78 80.8011L159.788 80.3667C159.836 78.0588 161.654 76.0798 164.031 75.8834C166.508 75.6788 168.743 76.6406 169.65 78.4531Z" fill="#3B334A" stroke="#C9C7DD" stroke-width="0.769498"/>
<circle cx="160" cy="72" r="1.5" fill="#3B334A" stroke="#C9C7DD"/>
<circle cx="156" cy="77" r="0.5" fill="#3B334A" stroke="#C9C7DD"/>
<path d="M156.902 79.7159C155.196 79.8576 153.928 81.3555 154.069 83.0617L154.896 93.0275C155.312 98.036 159.709 101.759 164.718 101.344L180.663 100.02C185.672 99.6045 189.395 95.2073 188.979 90.1987L188.152 80.233C188.01 78.5268 186.512 77.2584 184.806 77.4L156.902 79.7159Z" fill="#3B334A" stroke="#C9C7DD" stroke-width="2.2"/>
<path d="M188 80.3305L190.79 80.0992C191.451 80.0444 192.031 80.5354 192.085 81.1959C192.14 81.8564 191.649 82.4362 190.989 82.491L188.198 82.7223L188 80.3305Z" fill="#C9C7DD"/>
<path d="M150.099 85.5264C150.044 84.866 150.535 84.2861 151.196 84.2314L154.983 83.9174L155.181 86.3092L151.394 86.6232C150.734 86.6779 150.154 86.1869 150.099 85.5264Z" fill="#C9C7DD"/>
<path d="M110.766 34.3752C111.019 34.2955 111.233 34.1295 111.362 33.9137C111.491 33.6979 111.523 33.45 111.452 33.2245L110.735 30.9504L110.019 28.6763C109.896 28.2878 110.094 27.8638 110.433 27.5728C111.136 26.9719 111.599 26.1779 111.74 25.3309C111.882 24.4839 111.692 23.6383 111.206 22.9434C110.719 22.2485 109.967 21.7489 109.081 21.5329C108.195 21.3169 107.233 21.3982 106.365 21.7627C105.756 21.1155 104.939 20.6579 104.017 20.4463C103.094 20.2348 102.106 20.2786 101.174 20.5724C100.242 20.8662 99.4066 21.397 98.772 22.0993C98.1375 22.8016 97.7311 23.6445 97.6033 24.5238C96.6834 24.7236 95.8493 25.2086 95.248 25.8933C94.6467 26.578 94.3168 27.4184 94.3165 28.2662C94.3162 29.1141 94.6456 29.9151 95.2465 30.5279C95.8473 31.1408 96.6812 31.5261 97.601 31.616C98.046 31.6608 98.4511 31.8948 98.5733 32.2825L100.007 36.8315C100.078 37.057 100.247 37.2415 100.476 37.3446C100.705 37.4476 100.976 37.4608 101.229 37.381C101.229 37.381 103.992 36.0429 105.863 35.4531C107.735 34.8632 110.766 34.3752 110.766 34.3752Z" stroke="#C171FF" stroke-width="2.4" stroke-linecap="round" stroke-linejoin="round"/>
</g>
<defs>
<radialGradient id="paint0_radial_302_7053" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(144.5 67) rotate(90.5405) scale(53.0024 127.206)">
<stop stop-color="#2E3035" stop-opacity="0.95"/>
<stop offset="0.566518" stop-color="#2E3035" stop-opacity="0"/>
<stop offset="1" stop-color="#2E3035" stop-opacity="0.7"/>
</radialGradient>
<clipPath id="clip0_302_7053">
<rect width="288" height="120" rx="8" fill="white"/>
</clipPath>
</defs>
</svg>
