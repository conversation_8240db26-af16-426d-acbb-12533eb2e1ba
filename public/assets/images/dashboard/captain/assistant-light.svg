<svg width="120" height="104" viewBox="0 0 120 104" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_160_16406)">
<rect width="120" height="104" rx="8" fill="#FCFCFD"/>
<circle cx="8" cy="4" r="1" fill="#B9BBC6"/>
<circle cx="16" cy="4" r="1" fill="#B9BBC6"/>
<circle cx="24" cy="4" r="1" fill="#B9BBC6"/>
<circle cx="32" cy="4" r="1" fill="#B9BBC6"/>
<circle cx="40" cy="4" r="1" fill="#B9BBC6"/>
<circle cx="48" cy="4" r="1" fill="#B9BBC6"/>
<circle cx="56" cy="4" r="1" fill="#B9BBC6"/>
<circle cx="64" cy="4" r="1" fill="#B9BBC6"/>
<circle cx="72" cy="4" r="1" fill="#B9BBC6"/>
<circle cx="80" cy="4" r="1" fill="#B9BBC6"/>
<circle cx="88" cy="4" r="1" fill="#B9BBC6"/>
<circle cx="96" cy="4" r="1" fill="#B9BBC6"/>
<circle cx="104" cy="4" r="1" fill="#B9BBC6"/>
<circle cx="112" cy="4" r="1" fill="#B9BBC6"/>
<circle cx="8" cy="12" r="1" fill="#B9BBC6"/>
<circle cx="16" cy="12" r="1" fill="#B9BBC6"/>
<circle cx="24" cy="12" r="1" fill="#B9BBC6"/>
<circle cx="32" cy="12" r="1" fill="#B9BBC6"/>
<circle cx="40" cy="12" r="1" fill="#B9BBC6"/>
<circle cx="48" cy="12" r="1" fill="#B9BBC6"/>
<circle cx="56" cy="12" r="1" fill="#B9BBC6"/>
<circle cx="64" cy="12" r="1" fill="#B9BBC6"/>
<circle cx="72" cy="12" r="1" fill="#B9BBC6"/>
<circle cx="80" cy="12" r="1" fill="#B9BBC6"/>
<circle cx="88" cy="12" r="1" fill="#B9BBC6"/>
<circle cx="96" cy="12" r="1" fill="#B9BBC6"/>
<circle cx="104" cy="12" r="1" fill="#B9BBC6"/>
<circle cx="112" cy="12" r="1" fill="#B9BBC6"/>
<circle cx="8" cy="20" r="1" fill="#B9BBC6"/>
<circle cx="16" cy="20" r="1" fill="#B9BBC6"/>
<circle cx="24" cy="20" r="1" fill="#B9BBC6"/>
<circle cx="32" cy="20" r="1" fill="#B9BBC6"/>
<circle cx="40" cy="20" r="1" fill="#B9BBC6"/>
<circle cx="48" cy="20" r="1" fill="#B9BBC6"/>
<circle cx="56" cy="20" r="1" fill="#B9BBC6"/>
<circle cx="64" cy="20" r="1" fill="#B9BBC6"/>
<circle cx="72" cy="20" r="1" fill="#B9BBC6"/>
<circle cx="80" cy="20" r="1" fill="#B9BBC6"/>
<circle cx="88" cy="20" r="1" fill="#B9BBC6"/>
<circle cx="96" cy="20" r="1" fill="#B9BBC6"/>
<circle cx="104" cy="20" r="1" fill="#B9BBC6"/>
<circle cx="112" cy="20" r="1" fill="#B9BBC6"/>
<circle cx="8" cy="28" r="1" fill="#B9BBC6"/>
<circle cx="16" cy="28" r="1" fill="#B9BBC6"/>
<circle cx="24" cy="28" r="1" fill="#B9BBC6"/>
<circle cx="32" cy="28" r="1" fill="#B9BBC6"/>
<circle cx="40" cy="28" r="1" fill="#B9BBC6"/>
<circle cx="48" cy="28" r="1" fill="#B9BBC6"/>
<circle cx="56" cy="28" r="1" fill="#B9BBC6"/>
<circle cx="64" cy="28" r="1" fill="#B9BBC6"/>
<circle cx="72" cy="28" r="1" fill="#B9BBC6"/>
<circle cx="80" cy="28" r="1" fill="#B9BBC6"/>
<circle cx="88" cy="28" r="1" fill="#B9BBC6"/>
<circle cx="96" cy="28" r="1" fill="#B9BBC6"/>
<circle cx="104" cy="28" r="1" fill="#B9BBC6"/>
<circle cx="112" cy="28" r="1" fill="#B9BBC6"/>
<circle cx="8" cy="36" r="1" fill="#B9BBC6"/>
<circle cx="16" cy="36" r="1" fill="#B9BBC6"/>
<circle cx="24" cy="36" r="1" fill="#B9BBC6"/>
<circle cx="32" cy="36" r="1" fill="#B9BBC6"/>
<circle cx="40" cy="36" r="1" fill="#B9BBC6"/>
<circle cx="48" cy="36" r="1" fill="#B9BBC6"/>
<circle cx="56" cy="36" r="1" fill="#B9BBC6"/>
<circle cx="64" cy="36" r="1" fill="#B9BBC6"/>
<circle cx="72" cy="36" r="1" fill="#B9BBC6"/>
<circle cx="80" cy="36" r="1" fill="#B9BBC6"/>
<circle cx="88" cy="36" r="1" fill="#B9BBC6"/>
<circle cx="96" cy="36" r="1" fill="#B9BBC6"/>
<circle cx="104" cy="36" r="1" fill="#B9BBC6"/>
<circle cx="112" cy="36" r="1" fill="#B9BBC6"/>
<circle cx="8" cy="44" r="1" fill="#B9BBC6"/>
<circle cx="16" cy="44" r="1" fill="#B9BBC6"/>
<circle cx="24" cy="44" r="1" fill="#B9BBC6"/>
<circle cx="32" cy="44" r="1" fill="#B9BBC6"/>
<circle cx="40" cy="44" r="1" fill="#B9BBC6"/>
<circle cx="48" cy="44" r="1" fill="#B9BBC6"/>
<circle cx="56" cy="44" r="1" fill="#B9BBC6"/>
<circle cx="64" cy="44" r="1" fill="#B9BBC6"/>
<circle cx="72" cy="44" r="1" fill="#B9BBC6"/>
<circle cx="80" cy="44" r="1" fill="#B9BBC6"/>
<circle cx="88" cy="44" r="1" fill="#B9BBC6"/>
<circle cx="96" cy="44" r="1" fill="#B9BBC6"/>
<circle cx="104" cy="44" r="1" fill="#B9BBC6"/>
<circle cx="112" cy="44" r="1" fill="#B9BBC6"/>
<circle cx="8" cy="52" r="1" fill="#B9BBC6"/>
<circle cx="16" cy="52" r="1" fill="#B9BBC6"/>
<circle cx="24" cy="52" r="1" fill="#B9BBC6"/>
<circle cx="32" cy="52" r="1" fill="#B9BBC6"/>
<circle cx="40" cy="52" r="1" fill="#B9BBC6"/>
<circle cx="48" cy="52" r="1" fill="#B9BBC6"/>
<circle cx="56" cy="52" r="1" fill="#B9BBC6"/>
<circle cx="64" cy="52" r="1" fill="#B9BBC6"/>
<circle cx="72" cy="52" r="1" fill="#B9BBC6"/>
<circle cx="80" cy="52" r="1" fill="#B9BBC6"/>
<circle cx="88" cy="52" r="1" fill="#B9BBC6"/>
<circle cx="96" cy="52" r="1" fill="#B9BBC6"/>
<circle cx="104" cy="52" r="1" fill="#B9BBC6"/>
<circle cx="112" cy="52" r="1" fill="#B9BBC6"/>
<circle cx="8" cy="60" r="1" fill="#B9BBC6"/>
<circle cx="16" cy="60" r="1" fill="#B9BBC6"/>
<circle cx="24" cy="60" r="1" fill="#B9BBC6"/>
<circle cx="32" cy="60" r="1" fill="#B9BBC6"/>
<circle cx="40" cy="60" r="1" fill="#B9BBC6"/>
<circle cx="48" cy="60" r="1" fill="#B9BBC6"/>
<circle cx="56" cy="60" r="1" fill="#B9BBC6"/>
<circle cx="64" cy="60" r="1" fill="#B9BBC6"/>
<circle cx="72" cy="60" r="1" fill="#B9BBC6"/>
<circle cx="80" cy="60" r="1" fill="#B9BBC6"/>
<circle cx="88" cy="60" r="1" fill="#B9BBC6"/>
<circle cx="96" cy="60" r="1" fill="#B9BBC6"/>
<circle cx="104" cy="60" r="1" fill="#B9BBC6"/>
<circle cx="112" cy="60" r="1" fill="#B9BBC6"/>
<circle cx="8" cy="68" r="1" fill="#B9BBC6"/>
<circle cx="16" cy="68" r="1" fill="#B9BBC6"/>
<circle cx="24" cy="68" r="1" fill="#B9BBC6"/>
<circle cx="32" cy="68" r="1" fill="#B9BBC6"/>
<circle cx="40" cy="68" r="1" fill="#B9BBC6"/>
<circle cx="48" cy="68" r="1" fill="#B9BBC6"/>
<circle cx="56" cy="68" r="1" fill="#B9BBC6"/>
<circle cx="64" cy="68" r="1" fill="#B9BBC6"/>
<circle cx="72" cy="68" r="1" fill="#B9BBC6"/>
<circle cx="80" cy="68" r="1" fill="#B9BBC6"/>
<circle cx="88" cy="68" r="1" fill="#B9BBC6"/>
<circle cx="96" cy="68" r="1" fill="#B9BBC6"/>
<circle cx="104" cy="68" r="1" fill="#B9BBC6"/>
<circle cx="112" cy="68" r="1" fill="#B9BBC6"/>
<circle cx="8" cy="76" r="1" fill="#B9BBC6"/>
<circle cx="16" cy="76" r="1" fill="#B9BBC6"/>
<circle cx="24" cy="76" r="1" fill="#B9BBC6"/>
<circle cx="32" cy="76" r="1" fill="#B9BBC6"/>
<circle cx="40" cy="76" r="1" fill="#B9BBC6"/>
<circle cx="48" cy="76" r="1" fill="#B9BBC6"/>
<circle cx="56" cy="76" r="1" fill="#B9BBC6"/>
<circle cx="64" cy="76" r="1" fill="#B9BBC6"/>
<circle cx="72" cy="76" r="1" fill="#B9BBC6"/>
<circle cx="80" cy="76" r="1" fill="#B9BBC6"/>
<circle cx="88" cy="76" r="1" fill="#B9BBC6"/>
<circle cx="96" cy="76" r="1" fill="#B9BBC6"/>
<circle cx="104" cy="76" r="1" fill="#B9BBC6"/>
<circle cx="112" cy="76" r="1" fill="#B9BBC6"/>
<circle cx="8" cy="84" r="1" fill="#B9BBC6"/>
<circle cx="16" cy="84" r="1" fill="#B9BBC6"/>
<circle cx="24" cy="84" r="1" fill="#B9BBC6"/>
<circle cx="32" cy="84" r="1" fill="#B9BBC6"/>
<circle cx="40" cy="84" r="1" fill="#B9BBC6"/>
<circle cx="48" cy="84" r="1" fill="#B9BBC6"/>
<circle cx="56" cy="84" r="1" fill="#B9BBC6"/>
<circle cx="64" cy="84" r="1" fill="#B9BBC6"/>
<circle cx="72" cy="84" r="1" fill="#B9BBC6"/>
<circle cx="80" cy="84" r="1" fill="#B9BBC6"/>
<circle cx="88" cy="84" r="1" fill="#B9BBC6"/>
<circle cx="96" cy="84" r="1" fill="#B9BBC6"/>
<circle cx="104" cy="84" r="1" fill="#B9BBC6"/>
<circle cx="112" cy="84" r="1" fill="#B9BBC6"/>
<circle cx="8" cy="92" r="1" fill="#B9BBC6"/>
<circle cx="16" cy="92" r="1" fill="#B9BBC6"/>
<circle cx="24" cy="92" r="1" fill="#B9BBC6"/>
<circle cx="32" cy="92" r="1" fill="#B9BBC6"/>
<circle cx="40" cy="92" r="1" fill="#B9BBC6"/>
<circle cx="48" cy="92" r="1" fill="#B9BBC6"/>
<circle cx="56" cy="92" r="1" fill="#B9BBC6"/>
<circle cx="64" cy="92" r="1" fill="#B9BBC6"/>
<circle cx="72" cy="92" r="1" fill="#B9BBC6"/>
<circle cx="80" cy="92" r="1" fill="#B9BBC6"/>
<circle cx="88" cy="92" r="1" fill="#B9BBC6"/>
<circle cx="96" cy="92" r="1" fill="#B9BBC6"/>
<circle cx="104" cy="92" r="1" fill="#B9BBC6"/>
<circle cx="112" cy="92" r="1" fill="#B9BBC6"/>
<circle cx="8" cy="100" r="1" fill="#B9BBC6"/>
<circle cx="16" cy="100" r="1" fill="#B9BBC6"/>
<circle cx="24" cy="100" r="1" fill="#B9BBC6"/>
<circle cx="32" cy="100" r="1" fill="#B9BBC6"/>
<circle cx="40" cy="100" r="1" fill="#B9BBC6"/>
<circle cx="48" cy="100" r="1" fill="#B9BBC6"/>
<circle cx="56" cy="100" r="1" fill="#B9BBC6"/>
<circle cx="64" cy="100" r="1" fill="#B9BBC6"/>
<circle cx="72" cy="100" r="1" fill="#B9BBC6"/>
<circle cx="80" cy="100" r="1" fill="#B9BBC6"/>
<circle cx="88" cy="100" r="1" fill="#B9BBC6"/>
<circle cx="96" cy="100" r="1" fill="#B9BBC6"/>
<circle cx="104" cy="100" r="1" fill="#B9BBC6"/>
<circle cx="112" cy="100" r="1" fill="#B9BBC6"/>
<rect width="120" height="104" fill="url(#paint0_radial_160_16406)"/>
<path d="M32 54.1818C32 50.7677 34.8844 48 38.4424 48H40.053V63.3917C40.053 64.2799 39.3329 65 38.4447 65C34.8854 65 32 62.2313 32 58.816V54.1818Z" fill="#B77FFF"/>
<path d="M41.991 54.1818C41.991 50.7677 42.3351 48 38.7771 48H37.1665V65H38.7748C42.3341 65 41.991 62.2313 41.991 58.816V54.1818Z" fill="#B77FFF"/>
<path d="M86 53C86 47.1652 83.3661 41.5695 78.6777 37.4437C73.9893 33.3179 67.6304 31 61 31C54.3696 31 48.0107 33.3178 43.3223 37.4436C38.6339 41.5694 36 47.1652 36 53L39.594 53C39.594 48.0041 41.8493 43.2127 45.8637 39.6801C49.8781 36.1474 55.3228 34.1628 61 34.1628C66.6772 34.1628 72.1219 36.1474 76.1363 39.6801C80.1507 43.2127 82.406 48.0041 82.406 53H86Z" fill="#B77FFF"/>
<path d="M49.2493 52.4696C49.2493 51.3435 50.1621 50.4307 51.2882 50.4307C52.4143 50.4307 53.3272 51.3435 53.3272 52.4696V57.1819C53.3272 58.308 52.4143 59.2209 51.2882 59.2209C50.1621 59.2209 49.2493 58.308 49.2493 57.1819V52.4696Z" fill="black"/>
<path d="M55.7737 52.4696C55.7737 51.3435 56.6866 50.4307 57.8127 50.4307C58.9387 50.4307 59.8516 51.3435 59.8516 52.4696V57.1819C59.8516 58.308 58.9387 59.2209 57.8127 59.2209C56.6866 59.2209 55.7737 58.308 55.7737 57.1819V52.4696Z" fill="black"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M71.6052 44.8367C64.0123 43.7654 57.8229 43.7231 50.0594 44.832C48.097 45.1122 46.7985 45.3017 45.8086 45.591C44.8906 45.8593 44.3685 46.1792 43.9367 46.6618C43.0425 47.6611 42.9247 48.7913 42.8028 52.7332C42.683 56.6073 42.9202 60.0793 43.3696 63.9859C43.6091 66.0676 43.7739 67.4725 44.0369 68.5406C44.2865 69.5537 44.5883 70.0984 45.0078 70.5155C45.4307 70.9358 45.9726 71.2322 46.9671 71.4728C48.0192 71.7273 49.3995 71.8812 51.451 72.105C58.0857 72.8289 63.0522 72.8252 69.7116 72.1088C71.788 71.8855 73.1908 71.7314 74.2586 71.4787C75.273 71.2386 75.8184 70.9442 76.2354 70.5359C76.6436 70.1361 76.9555 69.5847 77.2261 68.5211C77.5084 67.4113 77.7004 65.943 77.9784 63.7859C78.4742 59.9372 78.8313 56.5572 78.8452 52.8545C78.86 48.8957 78.779 47.7693 77.8747 46.7245C77.438 46.2199 76.907 45.8881 75.9681 45.6118C74.9541 45.3134 73.6206 45.121 71.6052 44.8367ZM49.5853 41.5127C57.6726 40.3576 64.1796 40.4028 72.0736 41.5166L72.1891 41.5329C74.0582 41.7965 75.6372 42.0192 76.9147 42.3952C78.2934 42.8009 79.4464 43.4169 80.41 44.5302C82.2277 46.6303 82.2158 49.122 82.2 52.4325C82.1994 52.5758 82.1987 52.7207 82.1981 52.8671C82.1835 56.7821 81.8047 60.3271 81.3038 64.2143L81.2906 64.3172C81.0293 66.3457 80.814 68.0174 80.4756 69.3476C80.1198 70.7464 79.588 71.9457 78.5813 72.9315C77.5835 73.9087 76.4043 74.4165 75.0309 74.7415C73.7339 75.0485 72.12 75.2221 70.1748 75.4313L70.0703 75.4426C63.1741 76.1844 57.9654 76.1886 51.0873 75.4382L50.9812 75.4266C49.0631 75.2174 47.466 75.0432 46.1788 74.7318C44.8104 74.4008 43.6391 73.8827 42.6439 72.8933C41.6453 71.9006 41.1208 70.7208 40.7813 69.3425C40.4611 68.0425 40.2749 66.4238 40.0507 64.4746L40.0386 64.369C39.5777 60.3626 39.325 56.7183 39.4514 52.6296C39.456 52.4813 39.4604 52.3345 39.4647 52.1894C39.563 48.9037 39.6367 46.4391 41.438 44.426C42.3936 43.3579 43.5232 42.7657 44.868 42.3726C46.1151 42.0081 47.6529 41.7886 49.473 41.5287C49.5103 41.5234 49.5478 41.518 49.5853 41.5127Z" fill="black"/>
<path d="M79.5975 55.3508C79.5975 51.6744 82.5778 48.6941 86.2542 48.6941H87.3575V67H86.2566C82.5789 67 79.5975 64.0186 79.5975 60.3409V55.3508Z" fill="#B77FFF"/>
<path d="M92.3359 55.3508C92.3359 51.6744 89.3556 48.6941 85.6793 48.6941H84.576V67H85.6769C89.3546 67 92.3359 64.0186 92.3359 60.3409V55.3508Z" fill="#B77FFF"/>
<path d="M85.8456 58.6705C85.8456 58.6705 85.9608 65.4999 84.5619 70.4999C83.1631 75.4999 79.5629 76.9042 75.7024 77.9999C71.8419 79.0957 69.1743 78.9999 69.1743 78.9999" stroke="#B77FFF" stroke-width="2.49412" stroke-linecap="round"/>
</g>
<rect x="0.5" y="0.5" width="119" height="103" rx="7.5" stroke="#ECECEC"/>
<defs>
<radialGradient id="paint0_radial_160_16406" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(60 52) rotate(90) scale(52 60)">
<stop offset="0.434745" stop-color="#FCFCFD"/>
<stop offset="0.77" stop-color="#FCFCFD" stop-opacity="0.7"/>
<stop offset="0.99" stop-color="#FCFCFD" stop-opacity="0.9"/>
</radialGradient>
<clipPath id="clip0_160_16406">
<rect width="120" height="104" rx="8" fill="white"/>
</clipPath>
</defs>
</svg>
