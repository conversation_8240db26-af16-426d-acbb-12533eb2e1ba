type: object
properties:
  attribute_display_name:
    type: string
    description: Attribute display name
  attribute_display_type:
    type: integer
    description: Attribute display type (text- 0, number- 1, currency- 2, percent- 3, link- 4, date- 5, list- 6, checkbox- 7)
  attribute_description:
    type: string
    description: Attribute description
  attribute_key:
    type: string
    description: Attribute unique key value
  attribute_values:
    type: array
    description: Attribute values
    items:
      type: string
  attribute_model:
    type: integer
    description: Attribute type(conversation_attribute- 0, contact_attribute- 1)
