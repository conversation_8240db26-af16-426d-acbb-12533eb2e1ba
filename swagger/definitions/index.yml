##  ---------- ERRORS ------------- ##
## -------------------------------- ##
bad_request_error:
  $ref: ./error/bad_request.yml
request_error:
  $ref: ./error/request.yml



##  ---------- RESOURCE ------------- ##
## -------------------------------- ##

generic_id:
  $ref: ./resource/extension/generic.yml
canned_response:
  $ref: ./resource/canned_response.yml
custom_attribute:
  $ref: ./resource/custom_attribute.yml
automation_rule:
  $ref: ./resource/automation_rule.yml
portal:
  $ref: ./resource/portal.yml
category:
  $ref: ./resource/category.yml
article:
  $ref: ./resource/article.yml
category:
  $ref: ./resource/category.yml
article:
  $ref: ./resource/article.yml
contact:
  $ref: ./resource/contact.yml
conversation:
  $ref: ./resource/conversation.yml
message:
  $ref: ./resource/message.yml
user:
  $ref: ./resource/user.yml
agent:
  $ref: ./resource/agent.yml
inbox:
  $ref: ./resource/inbox.yml
agent_bot:
  $ref: ./resource/agent_bot.yml
contact_inboxes:
  $ref: ./resource/contact_inboxes.yml
contactable_inboxes:
  $ref: ./resource/contactable_inboxes.yml
custom_filter:
  $ref: ./resource/custom_filter.yml
webhook:
  $ref: ./resource/webhook.yml
account:
  $ref: ./resource/account.yml
platform_account:
  $ref: ./resource/platform_account.yml
team:
  $ref: ./resource/team.yml
integrations_app:
  $ref: ./resource/integrations/app.yml
integrations_hook:
  $ref: ./resource/integrations/hook.yml

## public resources
public_contact:
  $ref: ./resource/public/contact.yml
public_conversation:
  $ref: ./resource/public/conversation.yml
public_message:
  $ref: ./resource/public/message.yml
public_inbox:
  $ref: ./resource/public/inbox.yml


##  ---------- REQUEST------------- ##
## -------------------------------- ##

account_create_update_payload:
  $ref: ./request/account/create_update_payload.yml

agent_bot_create_update_payload:
  $ref: ./request/agent_bot/create_update_payload.yml

user_create_update_payload:
  $ref: ./request/user/create_update_payload.yml

canned_response_create_update_payload:
  $ref: ./request/canned_response/create_update_payload.yml

custom_attribute_create_update_payload:
  $ref: ./request/custom_attribute/create_update_payload.yml
## contact
contact_create:
  $ref: ./request/contact/create.yml
contact_update:
  $ref: ./request/contact/update.yml

## conversation
conversation_message_create:
  $ref: ./request/conversation/create_message.yml
conversation_message_create_with_external_url:
  $ref: ./request/conversation/create_message_with_external_url.yml

# Team request Payload
team_create_update_payload:
  $ref: ./request/team/create_update_payload.yml

# Custom Filter request Payload
custom_filter_create_update_payload:
  $ref: ./request/custom_filter/create_update_payload.yml

webhook_create_update_payload:
  $ref: ./request/webhooks/create_update_payload.yml

integrations_hook_create_payload:
  $ref: ./request/integrations/hook_create_payload.yml

integrations_hook_update_payload:
  $ref: ./request/integrations/hook_update_payload.yml

# Automation Rule
automation_rule_create_update_payload:
  $ref: ./request/automation_rule/create_update_payload.yml

# Help Center
portal_create_update_payload:
  $ref: ./request/portal/portal_create_update_payload.yml
category_create_update_payload:
  $ref: ./request/portal/category_create_update_payload.yml
article_create_update_payload:
  $ref: ./request/portal/article_create_update_payload.yml


## public requests
public_contact_create_update_payload:
  $ref: ./request/public/contact/create_update_payload.yml

public_message_create_payload:
  $ref: ./request/public/message/create_payload.yml
public_message_update_payload:
  $ref: ./request/public/message/update_payload.yml

public_conversation_create_payload:
  $ref: ./request/public/conversation/create_payload.yml

##  ---------- RESPONSE ------------- ##
## -------------------------------- ##

## contact
extended_contact:
  allOf:
    - $ref: '#/definitions/contact'
    - $ref: ./resource/extension/contact/show.yml
contact_base:
  allOf:
    - $ref: '#/definitions/generic_id'
    - $ref: '#/definitions/contact'
contact_list:
  type: array
  description: 'array of contacts'
  items:
    allOf:
      - $ref: '#/definitions/generic_id'
      - $ref: '#/definitions/contact'
contact_conversations:
  type: array
  description: 'array of conversations'
  items:
    allOf:
      - $ref: '#/definitions/conversation'
      - $ref: ./resource/extension/contact/conversation.yml
      - $ref: ./resource/extension/conversation/with_display_id.yml
contact_labels:
  $ref: ./resource/extension/contact/labels.yml

## conversation
conversation_list:
  $ref: ./resource/extension/conversation/list.yml
conversation_show:
  $ref: ./resource/extension/conversation/show.yml
conversation_status_toggle:
  $ref: ./resource/extension/conversation/status_toggle.yml
conversation_labels:
  $ref: ./resource/extension/conversation/labels.yml


## report
account_summary:
  $ref: './resource/reports/summary.yml'
agent_conversation_metrics:
  $ref: './resource/reports/conversation/agent.yml'
