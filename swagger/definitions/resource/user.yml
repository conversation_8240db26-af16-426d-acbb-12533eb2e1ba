type: object
properties:
  id:
    type: number
  uid:
    type: string
  name:
    type: string
  available_name:
    type: string
  display_name:
    type: string
  email:
    type: string
  account_id:
    type: number
  role:
    type: string
    enum: ['agent', 'administrator']
  confirmed:
    type: boolean
  custom_attributes: 
    type: object
    description: Available for users who are created through platform APIs and has custom attributes associated.
  accounts:
    type: array
    items:
      $ref: '#/definitions/account'
