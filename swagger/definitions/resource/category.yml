type: object
properties:
  id:
    type: integer
  description:
    type: string
    description: The text content.
  locale:
    type: string
  name:
    type: string
  slug:
    type: string
  position:
    type: integer
  portal_id:
    type: integer
  account_id:
    type: integer
  associated_category_id:
    type: integer
    description: To associate similar categories to each other, e.g same category of product documentation in different languages
  parent_category_id:
    type: integer
    description: To define parent category, e.g product documentation has multiple level features in sales category or in engineering category.
