type: object
properties:
  content:
    type: string
    description: The text content of the message
  content_type:
    type: string
    enum: ["text", "input_select", "cards", "form"]
    description: The type of the template message
  content_attributes:
    type: object
    description: The content attributes for each content_type
  message_type:
    type: string
    enum: ["incoming", "outgoing", "activity", "template"]
    description: The type of the message
  created_at:
    type: integer
    description: The time at which message was created
  private:
    type: boolean
    description: The flags which shows whether the message is private or not
  attachment:
    type: object
    description: The file object attached to the image
  sender:
    type: object
    description: User/Agent/AgentBot object
  conversation_id:
    type: number
    description: ID of the conversation
