tags:
  - Custom Attributes
operationId: add-new-custom-attribute-to-account
summary: Add a new custom attribute
description: Add a new custom attribute to account
security:
  - userApiKey: []
parameters:
  - name: data
    in: body
    required: true
    schema:
      $ref: '#/definitions/custom_attribute_create_update_payload'
responses:
  200:
    description: Success
    schema:
       $ref: '#/definitions/custom_attribute'
  403:
    description: Access denied
