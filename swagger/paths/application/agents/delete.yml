tags:
  - Agents
operationId: delete-agent-from-account
summary: Remove an Agent from Account
description: Remove an Agent from Account
security:
  - userApiKey: []
parameters:
  - in: path
    name: id
    type: integer
    required: true
    description: The ID of the agent to be deleted
responses:
  200:
    description: Success
  404:
    description: Agent not found
  403:
    description: Access denied
