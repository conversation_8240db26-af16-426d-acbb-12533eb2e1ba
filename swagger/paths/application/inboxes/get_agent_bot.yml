get:
  tags:
    - Inboxes
  operationId: getInboxAgentBot
  summary: Show Inbox Agent Bot
  description: See if an agent bot is associated to the Inbox
  parameters:
    - $ref: '#/parameters/account_id'
    - name: id
      in: path
      type: number
      description: ID of the inbox
      required: true
  responses:
    204:
      description: Success
      schema:
        $ref: '#/definitions/agent_bot'
    404:
      description: Inbox not found, Agent bot not found
    403:
      description: Access denied
