patch:
  tags:
    - Inboxes
  operationId: updateInbox
  summary: Update Inbox
  description: Add avatar and disable auto assignment for an inbox
  parameters:
    - $ref: '#/parameters/account_id'
    - name: id
      in: path
      type: number
      description: ID of the inbox
      required: true
    - name: data
      in: body
      required: true
      schema:
        type: object
        required:
          - enable_auto_assignment
        properties:
          name:
            type: string
            description: The name of the inbox
          enable_auto_assignment:
            type: boolean
            description: 'Enable Auto Assignment'
          avatar:
            type: string
            format: binary
            description: 'Image file for avatar'
          channel:
            type: object
            properties:
              website_url:
                type: string
                description: URL at which the widget will be loaded
              welcome_title:
                type: string
                description: Welcome title to be displayed on the widget
              welcome_tagline:
                type: string
                description: Welcome tagline to be displayed on the widget
              agent_away_message:
                type: string
                description: A message which will be sent if there is not agent available. This is not available if agentbot is connected
              widget_color:
                type: string
                description: A Hex-color string used to customize the widget
  responses:
    200:
      description: Success
      schema:
        type: object
        description: 'Updated inbox object'
        $ref: '#/definitions/inbox'
    404:
      description: Inbox not found
    403:
      description: Access denied
