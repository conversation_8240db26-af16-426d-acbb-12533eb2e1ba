post:
  tags:
    - Inboxes
  operationId: inboxCreation
  summary: Create an inbox
  description: You can create more than one website inbox in each account
  parameters:
    - $ref: '#/parameters/account_id'
    - name: data
      in: body
      required: true
      schema:
        type: object
        properties:
          name:
            type: string
            description: The name of the inbox
          avatar:
            type: string
            format: binary
            description: File for avatar image
          channel:
            type: object
            properties:
              type:
                type: string
                enum: ['web_widget']
              website_url:
                type: string
                description: URL at which the widget will be loaded
              welcome_title:
                type: string
                description: Welcome title to be displayed on the widget
              welcome_tagline:
                type: string
                description: Welcome tagline to be displayed on the widget
              agent_away_message:
                type: string
                description: A message which will be sent if there is not agent available. This is not available if agentbot is connected
              widget_color:
                type: string
                description: A Hex-color string used to customize the widget
  responses:
    200:
      description: Success
      schema:
        $ref: '#/definitions/inbox'
    404:
      description: Inbox not found
    403:
      description: Access denied
