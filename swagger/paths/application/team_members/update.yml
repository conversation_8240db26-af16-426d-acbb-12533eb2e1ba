tags:
  - Teams
operationId: update-agents-in-team
summary: Update Agents in Team
description: All agents except the one passed in params will be removed
security:
  - userApiKey: []
parameters:
  - name: data
    in: body
    required: true
    schema:
      type: object
      required:
        - user_ids
      properties:
        user_ids:
          type: array
          items:
            type: integer
          description: IDs of users to be added to the team
responses:
  200:
    description: Success
    schema:
        type: array
        description: 'Array of all agents in the team'
        items:
          $ref: '#/definitions/agent'
  404:
    description: Team not found
  403:
    description: Access denied
  422:
    description: User must exist
