tags:
  - Teams
operationId: delete-agent-in-team
summary: Remove an Agent from Team
description: Remove an Agent from Team
security:
  - userApiKey: []
parameters:
  - name: data
    in: body
    required: true
    schema:
      type: object
      required:
        - team_id
        - user_ids
      properties:
        user_ids:
          type: array
          items:
            type: integer
          description: IDs of users to be deleted from the team
responses:
  200:
    description: Success
  404:
    description: Team not found
  403:
    description: Access denied
  422:
    description: User must exist
