tags:
  - Teams
operationId: get-team-members
summary: List Agents in Team
description: Get Details of Agents in an Team
security:
  - userApiKey: []
parameters:
  - $ref: '#/parameters/account_id'
  - $ref: '#/parameters/team_id'
responses:
  200:
    description: Success
    schema:
        type: array
        description: 'Array of all agents in the team'
        items:
          $ref: '#/definitions/agent'
  404:
    description: Inbox not found
  403:
    description: Access denied
