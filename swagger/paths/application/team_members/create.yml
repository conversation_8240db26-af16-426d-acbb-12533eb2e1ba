tags:
  - Teams
operationId: add-new-agent-to-team
summary: Add a New Agent
description: Add a new Agent to Team
security:
  - userApiKey: []
parameters:
  - name: data
    in: body
    required: true
    schema:
      type: object
      required:
          - user_ids
      properties:
        user_ids:
          type: array
          items:
            type: integer
          description: IDs of users to be added to the team
responses:
  200:
    description: Success
    schema:
        type: array
        description: 'Array of all active agents'
        items:
          $ref: '#/definitions/agent'
  404:
    description: Team not found
  403:
    description: Access denied
  422:
    description: User must exist
