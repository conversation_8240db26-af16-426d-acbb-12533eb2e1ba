tags:
  - Contacts
operationId: contactFilter
description: Filter contacts with custom filter options and pagination
summary: Contact Filter
security:
  - userApiKey: []
  - agentBotApiKey: []
parameters:
  - name: page
    in: query
    type: integer
  - name: body
    in: body
    required: true
    schema:
      type: object
      properties:
        payload:
          type: array
          items:
            type: object
            properties:
              attribute_key:
                type: string
                description: filter attribute name
              filter_operator:
                type: string
                description: filter operator name
                enum: [ equal_to, not_equal_to, contains, does_not_contain ]
              values:
                type: array
                items:
                  type: string
                description: array of the attribute values to filter
              query_operator:
                type: string
                description: query operator name
                enum: [ AND, OR ]
          example:
            - attribute_key: 'name'
              filter_operator: 'equal_to'
              values: ['en']
              query_operator: 'AND'
            - attribute_key: 'country_code'
              filter_operator: 'equal_to'
              values: ['us']
              query_operator: null

responses:
  200:
    description: Success
    schema:
      $ref: '#/definitions/contact_list'
  400:
    description: Bad Request Error
    schema:
      $ref: '#/definitions/bad_request_error'
