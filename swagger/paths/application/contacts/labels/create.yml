tags:
  - Contact Labels
operationId: contact-add-labels
summary: Add Labels
description: Add labels to a contact. Note that this API would overwrite the existing list of labels associated to the conversation.
parameters:
  - name: data
    in: body
    required: true
    schema:
      type: object
      properties:
        labels:
          type: array
          description: Array of labels (comma-separated strings)
          items:
            type: string
responses:
  200:
    description: Success
    schema:
      $ref: '#/definitions/contact_labels'
  404:
    description: Contact not found
  401:
    description: Unauthorized
