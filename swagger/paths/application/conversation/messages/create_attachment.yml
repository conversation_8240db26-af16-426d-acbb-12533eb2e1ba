post:
  tags:
    - Messages
  operationId: conversationNewMessageAttachment
  summary: Create New Message Attachment
  description: Create an attachment message. Refer to this discussion if you have any further doubts. https://github.com/chatwoot/chatwoot/discussions/1809#discussioncomment-1211845
  consumes:
    - multipart/form-data
  security:
    - userApiKey: []
    - agentBot<PERSON>pi<PERSON>ey: []
  parameters:
    - $ref: '#/parameters/account_id'
    - $ref: '#/parameters/conversation_id'
      required: true
    - in: formData
      name: content
      type: string
      description: The content of the message
      required: true
    - in: formData
      name: message_type
      type: string
      enum: ['outgoing', 'incoming']
    - in: formData
      name: private
      type: boolean
      description: Flag to identify if it is a private note
    - in: formData
      name: attachments[]
      type: array
      description: The files to be uploaded.
      items:
        type: string
        format: binary


  responses:
    200:
      description: Success
      schema:
        allOf:
          - $ref: '#/definitions/generic_id'
          - $ref: '#/definitions/message'
    404:
      description: Conversation not found
    403:
      description: Access denied
