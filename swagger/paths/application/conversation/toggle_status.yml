tags:
  - Conversations
operationId: toggle-status-of-a-conversation
summary: Toggle Status
description: Toggles the status of the conversation between open and resolved
security:
  - userApiKey: []
  - agentBotApiKey: []
parameters:
  - name: data
    in: body
    required: true
    schema:
      type: object
      required:
        - status
      properties:
        status:
          type: string
          enum: ["open", "resolved", "pending"]
          description: The status of the conversation
responses:
  200:
    description: Success
    schema:
      $ref: '#/definitions/conversation_status_toggle'
  404:
    description: Conversation not found
  401:
    description: Unauthorized
