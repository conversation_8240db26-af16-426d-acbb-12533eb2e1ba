tags:
  - Conversations
operationId: toggle-priority-of-a-conversation
summary: Toggle Priority
description: Toggles the priority of conversation
security:
  - userApiKey: []
  - agentBotApiKey: []
parameters:
  - name: data
    in: body
    required: true
    schema:
      type: object
      required:
        - priority
      properties:
        priority:
          type: string
          enum:  ["urgent", "high", "medium", "low", "none"]
          description: "The priority of the conversation"
responses:
  200:
    description: Success
  404:
    description: Conversation not found
  401:
    description: Unauthorized
