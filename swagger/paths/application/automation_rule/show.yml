tags:
  - Automation Rule
operationId: get-details-of-a-single-automation-rule
summary: Get a automation rule details
description: Get the details of a automation rule in the account
parameters:
  - in: path
    name: id
    type: integer
    required: true
    description: The ID of the automation rule to be updated.
responses:
  '200':
    description: Success
    schema:
      $ref: '#/definitions/automation_rule'
  '401':
    description: Unauthorized
  '404':
    description: The given rule ID does not exist in the account
