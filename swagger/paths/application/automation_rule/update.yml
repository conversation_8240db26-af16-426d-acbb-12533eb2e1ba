tags:
  - Automation Rule
operationId: update-automation-rule-in-account
summary: Update automation rule in Account
description: Update a automation rule in account
security:
  - userApiKey: []
parameters:
  - in: path
    name: id
    type: integer
    required: true
    description: The ID of the automation rule to be updated.
  - name: data
    in: body
    required: true
    schema:
      $ref: '#/definitions/automation_rule_create_update_payload'
responses:
  '200':
    description: Success
    schema:
      $ref: '#/definitions/automation_rule'
  '403':
    description: Access denied
  '404':
    description: Rule not found
