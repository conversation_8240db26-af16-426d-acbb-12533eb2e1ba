tags:
  - Canned Responses
operationId: add-new-canned-response-to-account
summary: Add a New Canned Response
description: Add a new Canned Response to Account
security:
  - userApiKey: []
parameters:
  - name: data
    in: body
    required: true
    schema:
      $ref: '#/definitions/canned_response_create_update_payload'
responses:
  200:
    description: Success
    schema:
        description: 'Newly Created Canned Response'
        $ref: '#/definitions/canned_response'
  403:
    description: Access denied
