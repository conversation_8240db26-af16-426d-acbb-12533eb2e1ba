tags:
  - Accounts
operationId: create-an-account
summary: Create an Account
description: Create an Account
security:
  - platformAppApiKey: []
parameters:
  - name: data
    in: body
    required: true
    schema:
      $ref: '#/definitions/account_create_update_payload'
responses:
  200:
    description: Success
    schema:
      $ref: '#/definitions/platform_account'
  401:
    description: Unauthorized
