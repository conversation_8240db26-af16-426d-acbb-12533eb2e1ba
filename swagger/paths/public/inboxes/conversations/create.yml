tags:
  - Conversations API
operationId: create-a-conversation
summary: Create a conversation
description: Create a conversation
security: []
parameters:
  - name: data
    in: body
    required: true
    schema:
      $ref: '#/definitions/public_conversation_create_payload'
responses:
  200:
    description: Success
    schema:
      $ref: '#/definitions/public_conversation'
  401:
    description: Unauthorized
