{"swagger": "2.0", "info": {"description": "This is the API documentation for Chatwoot server.", "version": "1.0.0", "title": "Chatwoot", "termsOfService": "https://www.chatwoot.com/terms-of-service/", "contact": {"email": "<EMAIL>"}, "license": {"name": "MIT License", "url": "https://opensource.org/licenses/MIT"}}, "host": "app.chatwoot.com", "basePath": "/", "schemes": ["https"], "produces": ["application/json; charset=utf-8"], "consumes": ["application/json; charset=utf-8"], "securityDefinitions": {"userApiKey": {"type": "<PERSON><PERSON><PERSON><PERSON>", "in": "header", "name": "api_access_token", "description": "This token can be obtained by visiting the profile page or via rails console. Provides access to  endpoints based on the user permissions levels. This token can be saved by an external system when user is created via API, to perform activities on behalf of the user."}, "agentBotApiKey": {"type": "<PERSON><PERSON><PERSON><PERSON>", "in": "header", "name": "api_access_token", "description": "This token should be provided by system admin or obtained via rails console. This token can be used to build bot integrations and can only access limited apis."}, "platformAppApiKey": {"type": "<PERSON><PERSON><PERSON><PERSON>", "in": "header", "name": "api_access_token", "description": "This token can be obtained by the system admin after creating a platformApp. This token should be used to provision agent bots, accounts, users and their roles."}}, "security": [{"userApiKey": []}], "paths": {"/platform/api/v1/accounts": {"post": {"tags": ["Accounts"], "operationId": "create-an-account", "summary": "Create an Account", "description": "Create an Account", "security": [{"platformAppApiKey": []}], "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/account_create_update_payload"}}], "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/platform_account"}}, "401": {"description": "Unauthorized"}}}}, "/platform/api/v1/accounts/{account_id}": {"parameters": [{"$ref": "#/parameters/account_id"}], "get": {"tags": ["Accounts"], "operationId": "get-details-of-an-account", "summary": "Get an account details", "description": "Get the details of an account", "security": [{"platformAppApiKey": []}], "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/platform_account"}}, "401": {"description": "Unauthorized"}, "404": {"description": "The given account does not exist"}}}, "patch": {"tags": ["Accounts"], "operationId": "update-an-account", "summary": "Update an account", "description": "Update an account's attributes", "security": [{"platformAppApiKey": []}], "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/account_create_update_payload"}}], "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/platform_account"}}, "401": {"description": "Unauthorized"}}}, "delete": {"tags": ["Accounts"], "operationId": "delete-an-account", "summary": "Delete an Account", "description": "Delete an Account", "security": [{"platformAppApiKey": []}], "responses": {"200": {"description": "Success"}, "401": {"description": "Unauthorized"}, "404": {"description": "The account does not exist"}}}}, "/platform/api/v1/accounts/{account_id}/account_users": {"parameters": [{"$ref": "#/parameters/account_id"}], "get": {"tags": ["Account Users"], "operationId": "list-all-account-users", "summary": "List all Account Users", "description": "List all account users", "security": [{"platformAppApiKey": []}], "responses": {"200": {"description": "Success", "schema": {"type": "array", "description": "Array of account users", "items": {"type": "object", "properties": {"account_id": {"type": "integer", "description": "The ID of the user"}, "user_id": {"type": "integer", "description": "The ID of the user"}, "role": {"type": "string", "description": "whether user is an administrator or agent"}}}}}, "401": {"description": "Unauthorized"}}}, "post": {"tags": ["Account Users"], "operationId": "create-an-account-user", "summary": "Create an Account User", "description": "Create an Account User", "security": [{"platformAppApiKey": []}], "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"type": "object", "required": ["user_id", "role"], "properties": {"user_id": {"type": "integer", "description": "The ID of the user"}, "role": {"type": "string", "description": "whether user is an administrator or agent"}}}}], "responses": {"200": {"description": "Success", "schema": {"properties": {"account_id": {"type": "integer", "description": "The ID of the user"}, "user_id": {"type": "integer", "description": "The ID of the user"}, "role": {"type": "string", "description": "whether user is an administrator or agent"}}}}, "401": {"description": "Unauthorized"}}}, "delete": {"tags": ["Account Users"], "operationId": "delete-an-account-user", "summary": "Delete an Account User", "description": "Delete an Account User", "security": [{"platformAppApiKey": []}], "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"type": "object", "required": ["user_id"], "properties": {"user_id": {"type": "integer", "description": "The ID of the user"}}}}], "responses": {"200": {"description": "Success"}, "401": {"description": "Unauthorized"}, "404": {"description": "The account does not exist"}}}}, "/platform/api/v1/agent_bots": {"get": {"tags": ["AgentBots"], "operationId": "list-all-agent-bots", "summary": "List all AgentBots", "description": "List all agent bots available", "security": [{"platformAppApiKey": []}], "responses": {"200": {"description": "Success", "schema": {"type": "array", "description": "Array of agent bots", "items": {"$ref": "#/definitions/agent_bot"}}}, "401": {"description": "Unauthorized"}}}, "post": {"tags": ["AgentBots"], "operationId": "create-an-agent-bot", "summary": "Create an Agent <PERSON>", "description": "Create an agent bot", "security": [{"platformAppApiKey": []}], "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/agent_bot_create_update_payload"}}], "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/agent_bot"}}, "401": {"description": "Unauthorized"}}}}, "/platform/api/v1/agent_bots/{id}": {"parameters": [{"$ref": "#/parameters/agent_bot_id"}], "get": {"tags": ["AgentBots"], "operationId": "get-details-of-a-single-agent-bot", "summary": "Get an agent bot details", "description": "Get the details of an agent bot", "security": [{"platformAppApiKey": []}], "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/agent_bot"}}, "401": {"description": "Unauthorized"}, "404": {"description": "The given agent bot ID does not exist"}}}, "patch": {"tags": ["AgentBots"], "operationId": "update-an-agent-bot", "summary": "Update an agent bot", "description": "Update an agent bot's attributes", "security": [{"platformAppApiKey": []}], "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/agent_bot_create_update_payload"}}], "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/agent_bot"}}, "401": {"description": "Unauthorized"}}}, "delete": {"tags": ["AgentBots"], "operationId": "delete-an-agent-bot", "summary": "Delete an AgentBot", "description": "Delete an AgentBot", "security": [{"platformAppApiKey": []}], "responses": {"200": {"description": "Success"}, "401": {"description": "Unauthorized"}, "404": {"description": "The agent bot does not exist"}}}}, "/platform/api/v1/users": {"post": {"tags": ["Users"], "operationId": "create-a-user", "summary": "Create a User", "description": "Create a User", "security": [{"platformAppApiKey": []}], "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/user_create_update_payload"}}], "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/user"}}, "401": {"description": "Unauthorized"}}}}, "/platform/api/v1/users/{id}": {"parameters": [{"$ref": "#/parameters/platform_user_id"}], "get": {"tags": ["Users"], "operationId": "get-details-of-a-user", "summary": "Get an user details", "description": "Get the details of an user", "security": [{"platformAppApiKey": []}], "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/user"}}, "401": {"description": "Unauthorized"}, "404": {"description": "The given user does not exist"}}}, "patch": {"tags": ["Users"], "operationId": "update-a-user", "summary": "Update a user", "description": "Update a user's attributes", "security": [{"platformAppApiKey": []}], "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/user_create_update_payload"}}], "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/user"}}, "401": {"description": "Unauthorized"}}}, "delete": {"tags": ["Users"], "operationId": "delete-a-user", "summary": "Delete a User", "description": "Delete a User", "security": [{"platformAppApiKey": []}], "responses": {"200": {"description": "Success"}, "401": {"description": "Unauthorized"}, "404": {"description": "The user does not exist"}}}}, "/platform/api/v1/users/{id}/login": {"parameters": [{"$ref": "#/parameters/platform_user_id"}], "get": {"tags": ["Users"], "operationId": "get-sso-url-of-a-user", "summary": "Get User SSO Link", "description": "Get the sso link of a user", "security": [{"platformAppApiKey": []}], "responses": {"200": {"description": "Success", "schema": {"type": "object", "properties": {"url": {"type": "string", "description": "SSO url to autenticate the user"}}}}, "401": {"description": "Unauthorized"}, "404": {"description": "The given user does not exist"}}}}, "/platform/api/v1/users/{id}/token": {"parameters": [{"$ref": "#/parameters/platform_user_id"}], "post": {"tags": ["Users"], "operationId": "post-user-token", "summary": "Get User Access Token", "description": "Get the access token of a user", "security": [{"platformAppApiKey": []}], "responses": {"200": {"description": "Success", "schema": {"type": "object", "properties": {"access_token": {"type": "string", "description": "Access token of the user"}, "expiry": {"type": ["integer", "null"], "description": "Expiry timestamp"}, "user": {"type": "object", "properties": {"id": {"type": "integer", "description": "User ID"}, "name": {"type": "string", "description": "User's full name"}, "display_name": {"type": "string", "description": "User's display name"}, "email": {"type": "string", "description": "User's email address"}, "pubsub_token": {"type": "string", "description": "User's pubsub token"}}}}}}, "401": {"description": "Unauthorized"}, "404": {"description": "The given user does not exist"}}}}, "/public/api/v1/inboxes/{inbox_identifier}": {"parameters": [{"$ref": "#/parameters/public_inbox_identifier"}], "get": {"tags": ["Inbox API"], "operationId": "get-details-of-a-inbox", "summary": "Inbox details", "description": "Get the details of an inbox", "security": [], "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/public_inbox"}}, "401": {"description": "Unauthorized"}, "404": {"description": "The given inbox does not exist"}}}}, "/public/api/v1/inboxes/{inbox_identifier}/contacts": {"parameters": [{"$ref": "#/parameters/public_inbox_identifier"}], "post": {"tags": ["Contacts API"], "operationId": "create-a-contact", "summary": "Create a contact", "description": "Create a contact", "security": [], "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/public_contact_create_update_payload"}}], "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/public_contact"}}, "401": {"description": "Unauthorized"}}}}, "/public/api/v1/inboxes/{inbox_identifier}/contacts/{contact_identifier}": {"parameters": [{"$ref": "#/parameters/public_inbox_identifier"}, {"$ref": "#/parameters/public_contact_identifier"}], "get": {"tags": ["Contacts API"], "operationId": "get-details-of-a-contact", "summary": "Get a contact", "description": "Get the details of a contact", "security": [], "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/public_contact"}}, "401": {"description": "Unauthorized"}, "404": {"description": "The given contact does not exist"}}}, "patch": {"tags": ["Contacts API"], "operationId": "update-a-contact", "summary": "Update a contact", "description": "Update a contact's attributes", "security": [], "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/public_contact_create_update_payload"}}], "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/public_contact"}}, "401": {"description": "Unauthorized"}}}}, "/public/api/v1/inboxes/{inbox_identifier}/contacts/{contact_identifier}/conversations": {"parameters": [{"$ref": "#/parameters/public_inbox_identifier"}, {"$ref": "#/parameters/public_contact_identifier"}], "post": {"tags": ["Conversations API"], "operationId": "create-a-conversation", "summary": "Create a conversation", "description": "Create a conversation", "security": [], "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/public_conversation_create_payload"}}], "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/public_conversation"}}, "401": {"description": "Unauthorized"}}}, "get": {"tags": ["Conversations API"], "operationId": "list-all-contact-conversations", "summary": "List all conversations", "description": "List all conversations for the contact", "responses": {"200": {"description": "Success", "schema": {"type": "array", "description": "Array of conversations", "items": {"$ref": "#/definitions/public_conversation"}}}, "401": {"description": "Unauthorized"}}}}, "/public/api/v1/inboxes/{inbox_identifier}/contacts/{contact_identifier}/conversations/{conversation_id}": {"parameters": [{"$ref": "#/parameters/public_inbox_identifier"}, {"$ref": "#/parameters/public_contact_identifier"}, {"$ref": "#/parameters/conversation_id"}], "get": {"tags": ["Conversations API"], "operationId": "get-single-conversation", "summary": "Get a single conversation", "description": "Retrieves the details of a specific conversation", "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/public_conversation"}}, "401": {"description": "Unauthorized"}, "404": {"description": "Conversation not found"}}}}, "/public/api/v1/inboxes/{inbox_identifier}/contacts/{contact_identifier}/conversations/{conversation_id}/toggle_status": {"parameters": [{"$ref": "#/parameters/public_inbox_identifier"}, {"$ref": "#/parameters/public_contact_identifier"}, {"$ref": "#/parameters/conversation_id"}], "post": {"tags": ["Conversations API"], "operationId": "resolve-conversation", "summary": "Resolve a conversation", "description": "Marks a conversation as resolved", "responses": {"200": {"description": "Conversation resolved successfully", "schema": {"$ref": "#/definitions/public_conversation"}}, "401": {"description": "Unauthorized"}, "404": {"description": "Conversation not found"}}}}, "/public/api/v1/inboxes/{inbox_identifier}/contacts/{contact_identifier}/conversations/{conversation_id}/toggle_typing": {"parameters": [{"$ref": "#/parameters/public_inbox_identifier"}, {"$ref": "#/parameters/public_contact_identifier"}, {"$ref": "#/parameters/conversation_id"}], "post": {"tags": ["Conversations API"], "operationId": "toggle-typing-status", "summary": "Toggle typing status", "description": "Toggles the typing status in a conversation", "parameters": [{"name": "typing_status", "in": "query", "required": true, "type": "string", "description": "Typing status, either 'on' or 'off'"}], "responses": {"200": {"description": "Typing status toggled successfully"}, "401": {"description": "Unauthorized"}, "404": {"description": "Conversation not found"}}}}, "/public/api/v1/inboxes/{inbox_identifier}/contacts/{contact_identifier}/conversations/{conversation_id}/update_last_seen": {"parameters": [{"$ref": "#/parameters/public_inbox_identifier"}, {"$ref": "#/parameters/public_contact_identifier"}, {"$ref": "#/parameters/conversation_id"}], "post": {"tags": ["Conversations API"], "operationId": "update-last-seen", "summary": "Update last seen", "description": "Updates the last seen time of the contact in a conversation", "responses": {"200": {"description": "Last seen updated successfully"}, "401": {"description": "Unauthorized"}, "404": {"description": "Conversation not found"}}}}, "/public/api/v1/inboxes/{inbox_identifier}/contacts/{contact_identifier}/conversations/{conversation_id}/messages": {"parameters": [{"$ref": "#/parameters/public_inbox_identifier"}, {"$ref": "#/parameters/public_contact_identifier"}, {"$ref": "#/parameters/conversation_id"}], "post": {"tags": ["Messages API"], "operationId": "create-a-message", "summary": "Create a message", "description": "Create a message", "security": [], "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/public_message_create_payload"}}], "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/public_message"}}, "401": {"description": "Unauthorized"}}}, "get": {"tags": ["Messages API"], "operationId": "list-all-converation-messages", "summary": "List all messages", "description": "List all messages in the conversation", "responses": {"200": {"description": "Success", "schema": {"type": "array", "description": "Array of messages", "items": {"$ref": "#/definitions/public_message"}}}, "401": {"description": "Unauthorized"}}}}, "/public/api/v1/inboxes/{inbox_identifier}/contacts/{contact_identifier}/conversations/{conversation_id}/messages/{message_id}": {"parameters": [{"$ref": "#/parameters/public_inbox_identifier"}, {"$ref": "#/parameters/public_contact_identifier"}, {"$ref": "#/parameters/conversation_id"}, {"$ref": "#/parameters/message_id"}], "patch": {"tags": ["Messages API"], "operationId": "update-a-message", "summary": "Update a message", "description": "Update a message", "security": [], "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/public_message_update_payload"}}], "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/public_message"}}, "401": {"description": "Unauthorized"}}}}, "/api/v1/accounts/{account_id}/contacts/{contact_identifier}/labels": {"parameters": [{"$ref": "#/parameters/account_id"}, {"$ref": "#/parameters/public_contact_identifier"}], "get": {"tags": ["Contact Labels"], "operationId": "list-all-labels-of-a-contact", "summary": "List Labels", "description": "Lists all the labels of a contact", "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/contact_labels"}}, "404": {"description": "Contact not found"}, "401": {"description": "Unauthorized"}}}, "post": {"tags": ["Contact Labels"], "operationId": "contact-add-labels", "summary": "Add Labels", "description": "Add labels to a contact. Note that this API would overwrite the existing list of labels associated to the conversation.", "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"type": "object", "properties": {"labels": {"type": "array", "description": "Array of labels (comma-separated strings)", "items": {"type": "string"}}}}}], "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/contact_labels"}}, "404": {"description": "Contact not found"}, "401": {"description": "Unauthorized"}}}}, "/survey/responses/{conversation_uuid}": {"parameters": [{"$ref": "#/parameters/conversation_uuid"}], "get": {"tags": ["CSAT Survey Page"], "operationId": "get-csat-survey-page", "summary": "Get CSAT survey page", "description": "You can redirect the client to this URL, instead of implementing the CSAT survey component yourself.", "security": [], "responses": {"200": {"description": "Success"}}}}, "/api/v1/accounts/{account_id}/agent_bots": {"parameters": [{"$ref": "#/parameters/account_id"}], "get": {"tags": ["Account AgentBots"], "operationId": "list-all-account-agent-bots", "summary": "List all AgentBots", "description": "List all agent bots available for the current account", "responses": {"200": {"description": "Success", "schema": {"type": "array", "description": "Array of agent bots", "items": {"$ref": "#/definitions/agent_bot"}}}, "401": {"description": "Unauthorized"}}}, "post": {"tags": ["Account AgentBots"], "operationId": "create-an-account-agent-bot", "summary": "Create an Agent <PERSON>", "description": "Create an agent bot in the account", "parameters": [{"$ref": "#/parameters/account_id"}, {"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/agent_bot_create_update_payload"}}], "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/agent_bot"}}, "401": {"description": "Unauthorized"}}}}, "/api/v1/accounts/{account_id}/agent_bots/{id}": {"parameters": [{"$ref": "#/parameters/account_id"}, {"$ref": "#/parameters/agent_bot_id"}], "get": {"tags": ["Account AgentBots"], "operationId": "get-details-of-a-single-account-agent-bot", "summary": "Get an agent bot details", "description": "Get the details of an agent bot in the account", "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/agent_bot"}}, "401": {"description": "Unauthorized"}, "404": {"description": "The given agent bot ID does not exist in the account"}}}, "patch": {"tags": ["Account AgentBots"], "operationId": "update-an-account-agent-bot", "summary": "Update an agent bot", "description": "Update an agent bot's attributes", "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/agent_bot_create_update_payload"}}], "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/agent_bot"}}, "401": {"description": "Unauthorized"}}}, "delete": {"tags": ["Account AgentBots"], "operationId": "delete-an-account-agent-bot", "summary": "Delete an AgentBot", "description": "Delete an AgentBot from the account", "responses": {"200": {"description": "Success"}, "401": {"description": "Unauthorized"}, "404": {"description": "The agent bot does not exist in the account"}}}}, "/api/v1/accounts/{account_id}/agents": {"parameters": [{"$ref": "#/parameters/account_id"}], "get": {"tags": ["Agents"], "operationId": "get-account-agents", "summary": "List Agents in Account", "description": "Get Details of Agents in an Account", "security": [{"userApiKey": []}], "responses": {"200": {"description": "Success", "schema": {"type": "array", "description": "Array of all active agents", "items": {"$ref": "#/definitions/agent"}}}, "403": {"description": "Access denied"}}}, "post": {"tags": ["Agents"], "operationId": "add-new-agent-to-account", "summary": "Add a New Agent", "description": "Add a new Agent to Account", "security": [{"userApiKey": []}], "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"type": "object", "required": ["name", "email", "role"], "properties": {"name": {"type": "string", "description": "Full Name of the agent"}, "email": {"type": "string", "description": "Email of the Agent"}, "role": {"type": "string", "enum": ["agent", "administrator"], "description": "Whether its administrator or agent"}, "availability_status": {"type": "string", "enum": ["available", "busy", "offline"], "description": "The availability setting of the agent."}, "auto_offline": {"type": "boolean", "description": "Whether the availability status of agent is configured to go offline automatically when away."}}}}], "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/agent"}}, "403": {"description": "Access denied"}}}}, "/api/v1/accounts/{account_id}/agents/{id}": {"parameters": [{"$ref": "#/parameters/account_id"}], "patch": {"tags": ["Agents"], "operationId": "update-agent-in-account", "summary": "Update Agent in Account", "description": "Update an Agent in Account", "security": [{"userApiKey": []}], "parameters": [{"in": "path", "name": "id", "type": "integer", "required": true, "description": "The ID of the agent to be updated."}, {"name": "data", "in": "body", "required": true, "schema": {"type": "object", "required": ["role"], "properties": {"role": {"type": "string", "enum": ["agent", "administrator"], "description": "Whether its administrator or agent"}, "availability": {"type": "string", "enum": ["available", "busy", "offline"], "description": "The availability setting of the agent."}, "auto_offline": {"type": "boolean", "description": "Whether the availability status of agent is configured to go offline automatically when away."}}}}], "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/agent"}}, "404": {"description": "Agent not found"}, "403": {"description": "Access denied"}}}, "delete": {"tags": ["Agents"], "operationId": "delete-agent-from-account", "summary": "Remove an Agent from Account", "description": "Remove an Agent from Account", "security": [{"userApiKey": []}], "parameters": [{"in": "path", "name": "id", "type": "integer", "required": true, "description": "The ID of the agent to be deleted"}], "responses": {"200": {"description": "Success"}, "404": {"description": "Agent not found"}, "403": {"description": "Access denied"}}}}, "/api/v1/accounts/{account_id}/canned_responses": {"parameters": [{"$ref": "#/parameters/account_id"}], "get": {"tags": ["Canned Responses"], "operationId": "get-account-canned-response", "summary": "List all Canned Responses in an Account", "description": "Get Details of Canned Responses in an Account", "security": [{"userApiKey": []}], "responses": {"200": {"description": "Success", "schema": {"type": "array", "description": "Array of all canned responses", "items": {"$ref": "#/definitions/canned_response"}}}, "403": {"description": "Access denied"}}}, "post": {"tags": ["Canned Responses"], "operationId": "add-new-canned-response-to-account", "summary": "Add a New Canned Response", "description": "Add a new Canned Response to Account", "security": [{"userApiKey": []}], "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/canned_response_create_update_payload"}}], "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/canned_response"}}, "403": {"description": "Access denied"}}}}, "/api/v1/accounts/{account_id}/canned_responses/{id}": {"parameters": [{"$ref": "#/parameters/account_id"}], "patch": {"tags": ["Canned Response"], "operationId": "update-canned-response-in-account", "summary": "Update Canned Response in Account", "description": "Update a Canned Response in Account", "security": [{"userApiKey": []}], "parameters": [{"in": "path", "name": "id", "type": "integer", "required": true, "description": "The ID of the canned response to be updated."}, {"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/canned_response_create_update_payload"}}], "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/canned_response"}}, "404": {"description": "Agent not found"}, "403": {"description": "Access denied"}}}, "delete": {"tags": ["Canned Responses"], "operationId": "delete-canned-response-from-account", "summary": "Remove a Canned Response from Account", "description": "Remove a Canned Response from Account", "security": [{"userApiKey": []}], "parameters": [{"in": "path", "name": "id", "type": "integer", "required": true, "description": "The ID of the canned response to be deleted"}], "responses": {"200": {"description": "Success"}, "404": {"description": "Canned Response not found"}, "403": {"description": "Access denied"}}}}, "/api/v1/accounts/{account_id}/custom_attribute_definitions": {"parameters": [{"$ref": "#/parameters/account_id"}], "get": {"tags": ["Custom Attributes"], "operationId": "get-account-custom-attribute", "summary": "List all custom attributes in an account", "parameters": [{"name": "attribute_model", "in": "query", "type": "string", "enum": ["0", "1"], "description": "conversation_attribute(0)/contact_attribute(1)", "required": true}], "description": "Get details of custom attributes in an Account", "security": [{"userApiKey": []}], "responses": {"200": {"description": "Success", "schema": {"type": "array", "description": "Array of all custom attributes", "items": {"$ref": "#/definitions/custom_attribute"}}}, "403": {"description": "Access denied"}}}, "post": {"tags": ["Custom Attributes"], "operationId": "add-new-custom-attribute-to-account", "summary": "Add a new custom attribute", "description": "Add a new custom attribute to account", "security": [{"userApiKey": []}], "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/custom_attribute_create_update_payload"}}], "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/custom_attribute"}}, "403": {"description": "Access denied"}}}}, "/api/v1/accounts/{account_id}/custom_attribute_definitions/{id}": {"parameters": [{"$ref": "#/parameters/account_id"}, {"name": "id", "in": "path", "type": "number", "description": "ID of the custom attribute", "required": true}], "get": {"tags": ["Custom Attributes"], "operationId": "get-details-of-a-single-custom-attribute", "summary": "Get a custom attribute details", "description": "Get the details of a custom attribute in the account", "parameters": [{"$ref": "#/parameters/account_id"}, {"in": "path", "name": "id", "type": "integer", "required": true, "description": "The ID of the custom attribute to be updated."}], "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/custom_attribute"}}, "401": {"description": "Unauthorized"}, "404": {"description": "The given attribute ID does not exist in the account"}}}, "patch": {"tags": ["Custom Attributes"], "operationId": "update-custom-attribute-in-account", "summary": "Update custom attribute in Account", "description": "Update a custom attribute in account", "security": [{"userApiKey": []}], "parameters": [{"in": "path", "name": "id", "type": "integer", "required": true, "description": "The ID of the custom attribute to be updated."}, {"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/custom_attribute_create_update_payload"}}], "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/custom_attribute"}}, "404": {"description": "Agent not found"}, "403": {"description": "Access denied"}}}, "delete": {"tags": ["Custom Attributes"], "operationId": "delete-custom-attribute-from-account", "summary": "Remove a custom attribute from account", "description": "Remove a custom attribute from account", "security": [{"userApiKey": []}], "parameters": [{"$ref": "#/parameters/account_id"}, {"in": "path", "name": "id", "type": "integer", "required": true, "description": "The ID of the custom attribute to be deleted"}], "responses": {"200": {"description": "Success"}, "404": {"description": "Custom attribute not found"}, "403": {"description": "Access denied"}}}}, "/api/v1/accounts/{account_id}/contacts": {"get": {"tags": ["Contacts"], "operationId": "contactList", "description": "Listing all the resolved contacts with pagination (Page size = 15) . Resolved contacts are the ones with a value for identifier, email or phone number", "summary": "List Contacts", "parameters": [{"$ref": "#/parameters/account_id"}, {"$ref": "#/parameters/contact_sort_param"}, {"$ref": "#/parameters/page"}], "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/contact_list"}}, "400": {"description": "Bad Request Error", "schema": {"$ref": "#/definitions/bad_request_error"}}}}, "post": {"tags": ["Contacts"], "operationId": "contactCreate", "description": "Create a new Contact", "summary": "Create Contact", "parameters": [{"$ref": "#/parameters/account_id"}, {"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/contact_create"}}], "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/extended_contact"}}, "400": {"description": "Bad Request Error", "schema": {"$ref": "#/definitions/bad_request_error"}}}}}, "/api/v1/accounts/{account_id}/contacts/{id}": {"parameters": [{"$ref": "#/parameters/account_id"}, {"name": "id", "in": "path", "type": "number", "description": "ID of the contact", "required": true}], "get": {"tags": ["Contacts"], "operationId": "contactDetails", "summary": "Show Contact", "description": "Get a contact belonging to the account using ID", "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/extended_contact"}}, "404": {"description": "Contact not found"}, "403": {"description": "Access denied"}}}, "put": {"tags": ["Contacts"], "operationId": "contactUpdate", "summary": "Update Contact", "description": "Update a contact belonging to the account using ID", "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/contact_update"}}], "responses": {"204": {"description": "Success", "schema": {"$ref": "#/definitions/contact_base"}}, "404": {"description": "Contact not found"}, "403": {"description": "Access denied"}}}, "delete": {"tags": ["Contacts"], "operationId": "contactDelete", "summary": "Delete Contact", "responses": {"200": {"description": "Success"}, "401": {"description": "Unauthorized"}, "404": {"description": "Contact not found"}}}}, "/api/v1/accounts/{account_id}/contacts/{id}/conversations": {"get": {"tags": ["Contacts"], "operationId": "contactConversations", "summary": "Contact Conversations", "description": "Get conversations associated to that contact", "parameters": [{"$ref": "#/parameters/account_id"}, {"name": "id", "in": "path", "type": "number", "description": "ID of the contact", "required": true}], "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/contact_conversations"}}, "404": {"description": "Contact not found"}, "403": {"description": "Access denied"}}}}, "/api/v1/accounts/{account_id}/contacts/search": {"get": {"tags": ["Contacts"], "operationId": "contactSearch", "description": "Search the resolved contacts using a search key, currently supports email search (Page size = 15). Resolved contacts are the ones with a value for identifier, email or phone number", "summary": "Search Contacts", "parameters": [{"$ref": "#/parameters/account_id"}, {"name": "q", "in": "query", "type": "string", "description": "Search using contact `name`, `identifier`, `email` or `phone number`"}, {"$ref": "#/parameters/contact_sort_param"}, {"$ref": "#/parameters/page"}], "responses": {"200": {"description": "Success", "schema": {"type": "object", "properties": {"payload": {"$ref": "#/definitions/contact_list"}}}}, "401": {"description": "Authentication error", "schema": {"$ref": "#/definitions/bad_request_error"}}}}}, "/api/v1/accounts/{account_id}/contacts/filter": {"parameters": [{"$ref": "#/parameters/account_id"}], "post": {"tags": ["Contacts"], "operationId": "contactFilter", "description": "Filter contacts with custom filter options and pagination", "summary": "Contact Filter", "security": [{"userApiKey": []}, {"agentBotApiKey": []}], "parameters": [{"name": "page", "in": "query", "type": "integer"}, {"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {"payload": {"type": "array", "items": {"type": "object", "properties": {"attribute_key": {"type": "string", "description": "filter attribute name"}, "filter_operator": {"type": "string", "description": "filter operator name", "enum": ["equal_to", "not_equal_to", "contains", "does_not_contain"]}, "values": {"type": "array", "items": {"type": "string"}, "description": "array of the attribute values to filter"}, "query_operator": {"type": "string", "description": "query operator name", "enum": ["AND", "OR"]}}}, "example": [{"attribute_key": "name", "filter_operator": "equal_to", "values": ["en"], "query_operator": "AND"}, {"attribute_key": "country_code", "filter_operator": "equal_to", "values": ["us"], "query_operator": null}]}}}}], "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/contact_list"}}, "400": {"description": "Bad Request Error", "schema": {"$ref": "#/definitions/bad_request_error"}}}}}, "/api/v1/accounts/{account_id}/contacts/{id}/contact_inboxes": {"post": {"tags": ["Contact"], "operationId": "contactInboxCreation", "description": "Create a contact inbox record for an inbox", "summary": "Create contact inbox", "parameters": [{"$ref": "#/parameters/account_id"}, {"name": "id", "in": "path", "type": "number", "description": "ID of the contact", "required": true}, {"name": "data", "in": "body", "required": true, "schema": {"type": "object", "required": ["inbox_id"], "properties": {"inbox_id": {"type": "number", "description": "The ID of the inbox"}, "source_id": {"type": "string", "description": "Contact Inbox Source Id"}}}}], "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/contact_inboxes"}}, "401": {"description": "Authentication error", "schema": {"$ref": "#/definitions/bad_request_error"}}, "422": {"description": "Incorrect payload"}}}}, "/api/v1/accounts/{account_id}/contacts/{id}/contactable_inboxes": {"get": {"tags": ["Contact"], "operationId": "contactableInboxesGet", "description": "Get List of contactable Inboxes", "summary": "Get Contactable Inboxes", "parameters": [{"$ref": "#/parameters/account_id"}, {"name": "id", "in": "path", "type": "number", "description": "ID of the contact", "required": true}], "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/contactable_inboxes"}}, "401": {"description": "Authentication error", "schema": {"$ref": "#/definitions/bad_request_error"}}, "422": {"description": "Incorrect payload"}}}}, "/api/v1/accounts/{account_id}/automation_rules": {"parameters": [{"$ref": "#/parameters/account_id"}], "get": {"tags": ["Automation Rule"], "operationId": "get-account-automation-rule", "summary": "List all automation rules in an account", "parameters": [{"$ref": "#/parameters/account_id"}, {"$ref": "#/parameters/page"}], "description": "Get details of automation rules in an Account", "security": [{"userApiKey": []}], "responses": {"200": {"description": "Success", "schema": {"type": "array", "description": "Array of all automation rules", "items": {"$ref": "#/definitions/automation_rule"}}}, "403": {"description": "Access denied"}}}, "post": {"tags": ["Automation Rule"], "operationId": "add-new-automation-rule-to-account", "summary": "Add a new automation rule", "description": "Add a new automation rule to account", "security": [{"userApiKey": []}], "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/automation_rule_create_update_payload"}}], "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/automation_rule"}}, "403": {"description": "Access denied"}}}}, "/api/v1/accounts/{account_id}/automation_rules/{id}": {"parameters": [{"$ref": "#/parameters/account_id"}, {"name": "id", "in": "path", "type": "number", "description": "ID of the Automation Rule", "required": true}], "get": {"tags": ["Automation Rule"], "operationId": "get-details-of-a-single-automation-rule", "summary": "Get a automation rule details", "description": "Get the details of a automation rule in the account", "parameters": [{"in": "path", "name": "id", "type": "integer", "required": true, "description": "The ID of the automation rule to be updated."}], "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/automation_rule"}}, "401": {"description": "Unauthorized"}, "404": {"description": "The given rule ID does not exist in the account"}}}, "patch": {"tags": ["Automation Rule"], "operationId": "update-automation-rule-in-account", "summary": "Update automation rule in Account", "description": "Update a automation rule in account", "security": [{"userApiKey": []}], "parameters": [{"in": "path", "name": "id", "type": "integer", "required": true, "description": "The ID of the automation rule to be updated."}, {"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/automation_rule_create_update_payload"}}], "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/automation_rule"}}, "403": {"description": "Access denied"}, "404": {"description": "Rule not found"}}}, "delete": {"tags": ["Automation Rule"], "operationId": "delete-automation-rule-from-account", "summary": "Remove a automation rule from account", "description": "Remove a automation rule from account", "security": [{"userApiKey": []}], "parameters": [{"in": "path", "name": "id", "type": "integer", "required": true, "description": "The ID of the automation rule to be deleted"}], "responses": {"200": {"description": "Success"}, "403": {"description": "Access denied"}, "404": {"description": "automation rule not found"}}}}, "/api/v1/accounts/{account_id}/portals": {"parameters": [{"$ref": "#/parameters/account_id"}], "post": {"tags": ["Help Center"], "operationId": "add-new-portal-to-account", "summary": "Add a new portal", "description": "Add a new portal to account", "security": [{"userApiKey": []}], "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/portal_create_update_payload"}}], "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/portal"}}, "403": {"description": "Access denied"}}}, "get": {"tags": ["Help Center"], "operationId": "get-portal", "summary": "List all portals in an account", "parameters": [{"$ref": "#/parameters/account_id"}], "description": "Get details of portals in an Account", "security": [{"userApiKey": []}], "responses": {"200": {"description": "Success", "schema": {"type": "array", "description": "Array of all portals", "items": {"$ref": "#/definitions/portal"}}}, "403": {"description": "Access denied"}}}, "patch": {"tags": ["Help Center"], "operationId": "update-new-portal-to-account", "summary": "update a new portal", "description": "update a new portal to account", "security": [{"userApiKey": []}], "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/portal_create_update_payload"}}], "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/portal"}}, "403": {"description": "Access denied"}}}}, "/api/v1/accounts/{account_id}/portals/{portal_id}/categories": {"parameters": [{"$ref": "#/parameters/account_id"}, {"$ref": "#/parameters/portal_id"}], "post": {"tags": ["Help Center"], "operationId": "add-new-category-to-account", "summary": "Add a new category", "description": "Add a new category to portal", "security": [{"userApiKey": []}], "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/category_create_update_payload"}}], "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/category"}}, "403": {"description": "Access denied"}}}}, "/api/v1/accounts/{account_id}/portals/{portal_id}/articles": {"parameters": [{"$ref": "#/parameters/account_id"}, {"$ref": "#/parameters/portal_id"}], "post": {"tags": ["Help Center"], "operationId": "add-new-article-to-account", "summary": "Add a new article", "description": "Add a new article to portal", "security": [{"userApiKey": []}], "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/article_create_update_payload"}}], "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/article"}}, "403": {"description": "Access denied"}}}}, "/api/v1/accounts/{account_id}/conversations/meta": {"parameters": [{"$ref": "#/parameters/account_id"}], "get": {"tags": ["Conversations"], "operationId": "conversationListMeta", "description": "Get open, unassigned and all Conversation counts", "summary": "Get Conversation Counts", "parameters": [{"name": "status", "in": "query", "type": "string", "enum": ["all", "open", "resolved", "pending", "snoozed"], "default": "open", "description": "Filter by conversation status."}, {"name": "q", "in": "query", "type": "string", "description": "Filters conversations with messages containing the search term"}, {"name": "inbox_id", "in": "query", "type": "integer"}, {"name": "team_id", "in": "query", "type": "integer"}, {"name": "labels", "in": "query", "type": "array", "items": {"type": "string"}}], "responses": {"200": {"description": "Success", "schema": {"type": "object", "properties": {"meta": {"type": "object", "properties": {"mine_count": {"type": "number"}, "unassigned_count": {"type": "number"}, "assigned_count": {"type": "number"}, "all_count": {"type": "number"}}}}}}, "400": {"description": "Bad Request Error", "schema": {"$ref": "#/definitions/bad_request_error"}}}}}, "/api/v1/accounts/{account_id}/conversations": {"parameters": [{"$ref": "#/parameters/account_id"}], "get": {"tags": ["Conversations"], "operationId": "conversationList", "description": "List all the conversations with pagination", "summary": "Conversations List", "parameters": [{"name": "assignee_type", "in": "query", "type": "string", "enum": ["me", "unassigned", "all", "assigned"], "default": "all", "description": "Filter conversations by assignee type."}, {"name": "status", "in": "query", "type": "string", "enum": ["all", "open", "resolved", "pending", "snoozed"], "default": "open", "description": "Filter by conversation status."}, {"name": "q", "in": "query", "type": "string", "description": "Filters conversations with messages containing the search term"}, {"name": "inbox_id", "in": "query", "type": "integer"}, {"name": "team_id", "in": "query", "type": "integer"}, {"name": "labels", "in": "query", "type": "array", "items": {"type": "string"}}, {"name": "page", "in": "query", "type": "integer", "default": 1, "description": "paginate through conversations"}], "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/conversation_list"}}, "400": {"description": "Bad Request Error", "schema": {"$ref": "#/definitions/bad_request_error"}}}}, "post": {"tags": ["Conversations"], "operationId": "newConversation", "summary": "Create New Conversation", "description": "Creating a conversation in chatwoot requires a source id. \n\n Learn more about source_id: https://github.com/chatwoot/chatwoot/wiki/Building-on-Top-of-Chatwoot:-Importing-Existing-Contacts-and-Creating-Conversations", "security": [{"userApiKey": []}, {"agentBotApiKey": []}], "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"type": "object", "required": ["source_id", "inbox_id"], "properties": {"source_id": {"type": "string", "description": "Conversation source id"}, "inbox_id": {"type": "string", "description": "Id of inbox in which the conversation is created <br/> Allowed Inbox Types: Website, Phone, Api, Email "}, "contact_id": {"type": "string", "description": "Contact Id for which conversation is created"}, "additional_attributes": {"type": "object", "description": "Lets you specify attributes like browser information"}, "custom_attributes": {"type": "object", "description": "The object to save custom attributes for conversation, accepts custom attributes key and value", "example": {"attribute_key": "attribute_value", "priority_conversation_number": 3}}, "status": {"type": "string", "enum": ["open", "resolved", "pending"], "description": "Specify the conversation whether it's pending, open, closed"}, "assignee_id": {"type": "string", "description": "Agent Id for assigning a conversation to an agent"}, "team_id": {"type": "string", "description": "Team Id for assigning a conversation to a team"}, "message": {"type": "object", "description": "The initial message to be sent to the conversation", "required": ["content"], "properties": {"content": {"type": "string", "description": "The content of the message"}, "template_params": {"type": "object", "description": "The template params for the message in case of whatsapp Channel", "properties": {"name": {"type": "string", "description": "Name of the template", "example": "sample_issue_resolution"}, "category": {"type": "string", "description": "Category of the template", "example": "UTILITY"}, "language": {"type": "string", "description": "Language of the template", "example": "en_US"}, "processed_params": {"type": "object", "description": "The processed param values for template variables in template", "example": {"1": "Chatwoot"}}}}}}}}}], "responses": {"200": {"description": "Success", "schema": {"type": "object", "properties": {"id": {"type": "number", "description": "ID of the conversation"}, "account_id": {"type": "number", "description": "Account Id"}, "inbox_id": {"type": "number", "description": "ID of the inbox"}}}}, "403": {"description": "Access denied"}}}}, "/api/v1/accounts/{account_id}/conversations/filter": {"parameters": [{"$ref": "#/parameters/account_id"}], "post": {"tags": ["Conversations"], "operationId": "conversationFilter", "description": "Filter conversations with custom filter options and pagination", "summary": "Conversations Filter", "security": [{"userApiKey": []}, {"agentBotApiKey": []}], "parameters": [{"name": "page", "in": "query", "type": "integer"}, {"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {"payload": {"type": "array", "items": {"type": "object", "properties": {"attribute_key": {"type": "string", "description": "filter attribute name"}, "filter_operator": {"type": "string", "description": "filter operator name", "enum": ["equal_to", "not_equal_to", "contains", "does_not_contain"]}, "values": {"type": "array", "items": {"type": "string"}, "description": "array of the attribute values to filter"}, "query_operator": {"type": "string", "description": "query operator name", "enum": ["AND", "OR"]}}}, "example": [{"attribute_key": "browser_language", "filter_operator": "not_eq", "values": ["en"], "query_operator": "AND"}, {"attribute_key": "status", "filter_operator": "eq", "values": ["pending"], "query_operator": null}]}}}}], "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/conversation_list"}}, "400": {"description": "Bad Request Error", "schema": {"$ref": "#/definitions/bad_request_error"}}}}}, "/api/v1/accounts/{account_id}/conversations/{conversation_id}": {"parameters": [{"$ref": "#/parameters/account_id"}, {"$ref": "#/parameters/conversation_id"}], "get": {"tags": ["Conversations"], "operationId": "get-details-of-a-conversation", "summary": "Conversation Details", "description": "Get all details regarding a conversation with all messages in the conversation", "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/conversation_show"}}, "404": {"description": "Conversation not found"}, "403": {"description": "Access denied"}}}, "patch": {"tags": ["Conversations"], "operationId": "update-conversation", "summary": "Update Conversation", "description": "Update Conversation Attributes", "security": [{"userApiKey": []}, {"agentBotApiKey": []}], "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"type": "object", "properties": {"priority": {"type": "string", "enum": ["urgent", "high", "medium", "low", "none"], "description": "The priority of the conversation"}, "sla_policy_id": {"type": "number", "description": "The ID of the SLA policy (Available only in Enterprise edition)"}}}}], "responses": {"200": {"description": "Success"}, "404": {"description": "Conversation not found"}, "401": {"description": "Unauthorized"}}}}, "/api/v1/accounts/{account_id}/conversations/{conversation_id}/toggle_status": {"parameters": [{"$ref": "#/parameters/account_id"}, {"$ref": "#/parameters/conversation_id"}], "post": {"tags": ["Conversations"], "operationId": "toggle-status-of-a-conversation", "summary": "Toggle Status", "description": "Toggles the status of the conversation between open and resolved", "security": [{"userApiKey": []}, {"agentBotApiKey": []}], "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"type": "object", "required": ["status"], "properties": {"status": {"type": "string", "enum": ["open", "resolved", "pending"], "description": "The status of the conversation"}}}}], "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/conversation_status_toggle"}}, "404": {"description": "Conversation not found"}, "401": {"description": "Unauthorized"}}}}, "/api/v1/accounts/{account_id}/conversations/{conversation_id}/toggle_priority": {"parameters": [{"$ref": "#/parameters/account_id"}, {"$ref": "#/parameters/conversation_id"}], "post": {"tags": ["Conversations"], "operationId": "toggle-priority-of-a-conversation", "summary": "Toggle Priority", "description": "Toggles the priority of conversation", "security": [{"userApiKey": []}, {"agentBotApiKey": []}], "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"type": "object", "required": ["priority"], "properties": {"priority": {"type": "string", "enum": ["urgent", "high", "medium", "low", "none"], "description": "The priority of the conversation"}}}}], "responses": {"200": {"description": "Success"}, "404": {"description": "Conversation not found"}, "401": {"description": "Unauthorized"}}}}, "/api/v1/accounts/{account_id}/conversations/{conversation_id}/custom_attributes": {"parameters": [{"$ref": "#/parameters/account_id"}, {"$ref": "#/parameters/conversation_id"}], "post": {"tags": ["Conversations"], "operationId": "update-custom-attributes-of-a-conversation", "summary": "Update Custom Attributes", "description": "Updates the custom attributes of a conversation", "security": [{"userApiKey": []}, {"agentBotApiKey": []}], "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"type": "object", "required": ["custom_attributes"], "properties": {"custom_attributes": {"type": "object", "description": "The custom attributes to be set for the conversation", "example": {"order_id": "12345", "previous_conversation": "67890"}}}}}], "responses": {"200": {"description": "Success", "schema": {"type": "object", "properties": {"custom_attributes": {"type": "object", "description": "The custom attributes of the conversation"}}}}, "404": {"description": "Conversation not found"}, "401": {"description": "Unauthorized"}}}}, "/api/v1/accounts/{account_id}/conversations/{conversation_id}/assignments": {"parameters": [{"$ref": "#/parameters/account_id"}, {"$ref": "#/parameters/conversation_id"}], "post": {"tags": ["Conversation Assignment"], "operationId": "assign-a-conversation", "summary": "Assign Conversation", "description": "Assign a conversation to an agent or a team", "security": [{"userApiKey": []}, {"agentBotApiKey": []}], "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"type": "object", "properties": {"assignee_id": {"type": "number", "description": "Id of the assignee user"}, "team_id": {"type": "number", "description": "Id of the team. If the assignee_id is present, this param would be ignored"}}}}], "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/user"}}, "404": {"description": "Conversation not found"}, "401": {"description": "Unauthorized"}}}}, "/api/v1/accounts/{account_id}/conversations/{conversation_id}/labels": {"parameters": [{"$ref": "#/parameters/account_id"}, {"$ref": "#/parameters/conversation_id"}], "get": {"tags": ["Conversation Labels"], "operationId": "list-all-labels-of-a-conversation", "summary": "List Labels", "description": "Lists all the labels of a conversation", "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/conversation_labels"}}, "404": {"description": "Conversation not found"}, "401": {"description": "Unauthorized"}}}, "post": {"tags": ["Conversation Labels"], "operationId": "conversation-add-labels", "summary": "Add Labels", "description": "Add labels to a conversation. Note that this API would overwrite the existing list of labels associated to the conversation.", "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"type": "object", "properties": {"labels": {"type": "array", "description": "Array of labels (comma-separated strings)", "items": {"type": "string"}}}}}], "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/conversation_labels"}}, "404": {"description": "Conversation not found"}, "401": {"description": "Unauthorized"}}}}, "/api/v1/accounts/{account_id}/inboxes": {"get": {"tags": ["Inboxes"], "operationId": "listAllInboxes", "summary": "List all inboxes", "description": "List all inboxes available in the current account", "parameters": [{"$ref": "#/parameters/account_id"}], "responses": {"200": {"description": "Success", "schema": {"type": "array", "description": "Array of inboxes", "items": {"$ref": "#/definitions/inbox"}}}, "404": {"description": "Inbox not found"}, "403": {"description": "Access denied"}}}}, "/api/v1/accounts/{account_id}/inboxes/{id}/": {"get": {"tags": ["Inboxes"], "operationId": "GetInbox", "summary": "Get an inbox", "description": "Get an inbox available in the current account", "parameters": [{"$ref": "#/parameters/account_id"}, {"name": "id", "in": "path", "type": "number", "description": "ID of the inbox", "required": true}], "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/inbox"}}, "404": {"description": "Inbox not found"}, "403": {"description": "Access denied"}}}}, "/api/v1/accounts/{account_id}/inboxes/": {"post": {"tags": ["Inboxes"], "operationId": "inboxCreation", "summary": "Create an inbox", "description": "You can create more than one website inbox in each account", "parameters": [{"$ref": "#/parameters/account_id"}, {"name": "data", "in": "body", "required": true, "schema": {"type": "object", "properties": {"name": {"type": "string", "description": "The name of the inbox"}, "avatar": {"type": "string", "format": "binary", "description": "File for avatar image"}, "channel": {"type": "object", "properties": {"type": {"type": "string", "enum": ["web_widget"]}, "website_url": {"type": "string", "description": "URL at which the widget will be loaded"}, "welcome_title": {"type": "string", "description": "Welcome title to be displayed on the widget"}, "welcome_tagline": {"type": "string", "description": "Welcome tagline to be displayed on the widget"}, "agent_away_message": {"type": "string", "description": "A message which will be sent if there is not agent available. This is not available if agent<PERSON> is connected"}, "widget_color": {"type": "string", "description": "A Hex-color string used to customize the widget"}}}}}}], "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/inbox"}}, "404": {"description": "Inbox not found"}, "403": {"description": "Access denied"}}}}, "/api/v1/accounts/{account_id}/inboxes/{id}": {"patch": {"tags": ["Inboxes"], "operationId": "updateInbox", "summary": "Update Inbox", "description": "Add avatar and disable auto assignment for an inbox", "parameters": [{"$ref": "#/parameters/account_id"}, {"name": "id", "in": "path", "type": "number", "description": "ID of the inbox", "required": true}, {"name": "data", "in": "body", "required": true, "schema": {"type": "object", "required": ["enable_auto_assignment"], "properties": {"name": {"type": "string", "description": "The name of the inbox"}, "enable_auto_assignment": {"type": "boolean", "description": "Enable Auto Assignment"}, "avatar": {"type": "string", "format": "binary", "description": "Image file for avatar"}, "channel": {"type": "object", "properties": {"website_url": {"type": "string", "description": "URL at which the widget will be loaded"}, "welcome_title": {"type": "string", "description": "Welcome title to be displayed on the widget"}, "welcome_tagline": {"type": "string", "description": "Welcome tagline to be displayed on the widget"}, "agent_away_message": {"type": "string", "description": "A message which will be sent if there is not agent available. This is not available if agent<PERSON> is connected"}, "widget_color": {"type": "string", "description": "A Hex-color string used to customize the widget"}}}}}}], "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/inbox"}}, "404": {"description": "Inbox not found"}, "403": {"description": "Access denied"}}}}, "/api/v1/accounts/{account_id}/inboxes/{id}/agent_bot": {"get": {"tags": ["Inboxes"], "operationId": "getInboxAgentBot", "summary": "Show Inbox Agent <PERSON>", "description": "See if an agent bot is associated to the Inbox", "parameters": [{"$ref": "#/parameters/account_id"}, {"name": "id", "in": "path", "type": "number", "description": "ID of the inbox", "required": true}], "responses": {"204": {"description": "Success", "schema": {"$ref": "#/definitions/agent_bot"}}, "404": {"description": "Inbox not found, Agent bot not found"}, "403": {"description": "Access denied"}}}}, "/api/v1/accounts/{account_id}/inboxes/{id}/set_agent_bot": {"post": {"tags": ["Inboxes"], "operationId": "updateAgentBot", "summary": "Add or remove agent bot", "description": "To add an agent bot pass agent_bot id, to remove agent bot from an inbox pass null", "parameters": [{"$ref": "#/parameters/account_id"}, {"name": "id", "in": "path", "type": "number", "description": "ID of the inbox", "required": true}, {"name": "data", "in": "body", "required": true, "schema": {"type": "object", "required": ["agent_bot"], "properties": {"agent_bot": {"type": "number", "description": "Agent bot ID"}}}}], "responses": {"204": {"description": "Success"}, "404": {"description": "Inbox not found, Agent bot not found"}, "403": {"description": "Access denied"}}}}, "/api/v1/accounts/{account_id}/inbox_members/{inbox_id}": {"parameters": [{"$ref": "#/parameters/account_id"}, {"$ref": "#/parameters/inbox_id"}], "get": {"tags": ["Inboxes"], "operationId": "get-inbox-members", "summary": "List Agents in Inbox", "description": "Get Details of Agents in an Inbox", "security": [{"userApiKey": []}], "parameters": [{"$ref": "#/parameters/inbox_id"}], "responses": {"200": {"description": "Success", "schema": {"type": "array", "description": "Array of all active agents", "items": {"$ref": "#/definitions/agent"}}}, "404": {"description": "Inbox not found"}, "403": {"description": "Access denied"}}}}, "/api/v1/accounts/{account_id}/inbox_members": {"parameters": [{"$ref": "#/parameters/account_id"}], "post": {"tags": ["Inboxes"], "operationId": "add-new-agent-to-inbox", "summary": "Add a New Agent", "description": "Add a new Agent to Inbox", "security": [{"userApiKey": []}], "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"type": "object", "required": ["inbox_id", "user_ids"], "properties": {"inbox_id": {"type": "string", "description": "The ID of the inbox"}, "user_ids": {"type": "array", "items": {"type": "integer"}, "description": "IDs of users to be added to the inbox"}}}}], "responses": {"200": {"description": "Success", "schema": {"type": "array", "description": "Array of all active agents", "items": {"$ref": "#/definitions/agent"}}}, "404": {"description": "Inbox not found"}, "403": {"description": "Access denied"}, "422": {"description": "User must exist"}}}, "patch": {"tags": ["Inboxes"], "operationId": "update-agents-in-inbox", "summary": "Update Agents in Inbox", "description": "All agents except the one passed in params will be removed", "security": [{"userApiKey": []}], "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"type": "object", "required": ["inbox_id", "user_ids"], "properties": {"inbox_id": {"type": "string", "description": "The ID of the inbox"}, "user_ids": {"type": "array", "items": {"type": "integer"}, "description": "IDs of users to be added to the inbox"}}}}], "responses": {"200": {"description": "Success", "schema": {"type": "array", "description": "Array of all active agents", "items": {"$ref": "#/definitions/agent"}}}, "404": {"description": "Inbox not found"}, "403": {"description": "Access denied"}, "422": {"description": "User must exist"}}}, "delete": {"tags": ["Inboxes"], "operationId": "delete-agent-in-inbox", "summary": "Remove an Agent from Inbox", "description": "Remove an Agent from Inbox", "security": [{"userApiKey": []}], "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"type": "object", "required": ["inbox_id", "user_ids"], "properties": {"inbox_id": {"type": "string", "description": "The ID of the inbox"}, "user_ids": {"type": "array", "items": {"type": "integer"}, "description": "IDs of users to be deleted from the inbox"}}}}], "responses": {"200": {"description": "Success"}, "404": {"description": "Inbox not found"}, "403": {"description": "Access denied"}, "422": {"description": "User must exist"}}}}, "/api/v1/accounts/{account_id}/conversations/{conversation_id}/messages": {"parameters": [{"$ref": "#/parameters/account_id"}, {"$ref": "#/parameters/conversation_id"}], "get": {"tags": ["Messages"], "operationId": "list-all-messages", "summary": "Get messages", "description": "List all messages of a conversation", "responses": {"200": {"description": "Success", "schema": {"type": "array", "description": "Array of messages", "items": {"allOf": [{"$ref": "#/definitions/generic_id"}, {"$ref": "#/definitions/message"}]}}}, "404": {"description": "Conversation not found"}, "401": {"description": "Unauthorized"}}}, "post": {"tags": ["Messages"], "operationId": "create-a-new-message-in-a-conversation", "summary": "Create New Message", "description": "Create a new message in the conversation", "security": [{"userApiKey": []}, {"agentBotApiKey": []}], "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/conversation_message_create"}}], "responses": {"200": {"description": "Success", "schema": {"allOf": [{"$ref": "#/definitions/generic_id"}, {"$ref": "#/definitions/message"}]}}, "404": {"description": "Conversation not found"}, "403": {"description": "Access denied"}}}}, "/api/v1/accounts/{account_id}/conversations/{conversation_id}/messages/{message_id}": {"parameters": [{"$ref": "#/parameters/account_id"}, {"$ref": "#/parameters/conversation_id"}, {"$ref": "#/parameters/message_id"}], "delete": {"tags": ["Messages"], "operationId": "delete-a-message", "summary": "Delete a message", "description": "Delete a message and it's attachments from the conversation.", "responses": {"200": {"description": "Success"}, "401": {"description": "Unauthorized"}, "404": {"description": "The message or conversation does not exist in the account"}}}}, "/api/v1/accounts/{account_id}/integrations/apps": {"parameters": [{"$ref": "#/parameters/account_id"}], "get": {"tags": ["Integrations"], "operationId": "get-details-of-all-integrations", "summary": "List all the Integrations", "description": "Get the details of all Integrations available for the account", "responses": {"200": {"description": "Success", "schema": {"type": "array", "description": "Array of Integration apps", "items": {"$ref": "#/definitions/integrations_app"}}}, "401": {"description": "Unauthorized"}, "404": {"description": "Url not found"}}}}, "/api/v1/accounts/{account_id}/integrations/hooks": {"post": {"tags": ["Integrations"], "operationId": "create-an-integration-hook", "summary": "Create an integration hook", "description": "Create an integration hook", "parameters": [{"$ref": "#/parameters/account_id"}, {"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/integrations_hook_create_payload"}}], "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/integrations_hook"}}, "401": {"description": "Unauthorized"}}}}, "/api/v1/accounts/{account_id}/integrations/hooks/{hook_id}": {"patch": {"tags": ["Integrations"], "operationId": "update-an-integrations-hook", "summary": "Update an Integration Hook", "description": "Update an Integration Hook", "parameters": [{"$ref": "#/parameters/account_id"}, {"$ref": "#/parameters/hook_id"}, {"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/integrations_hook_update_payload"}}], "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/integrations_hook"}}, "401": {"description": "Unauthorized"}}}, "delete": {"tags": ["Integrations"], "operationId": "delete-an-integration-hook", "summary": "Delete an Integration Hook", "description": "Delete an Integration Hook", "parameters": [{"$ref": "#/parameters/account_id"}, {"$ref": "#/parameters/hook_id"}], "responses": {"200": {"description": "Success"}, "401": {"description": "Unauthorized"}, "404": {"description": "The hook does not exist in the account"}}}}, "/api/v1/profile": {"get": {"tags": ["Profile"], "operationId": "fetchProfile", "summary": "Fetch user profile", "description": "Get the user profile details", "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/user"}}, "401": {"description": "Unauthorized"}}}}, "/api/v1/accounts/{account_id}/teams": {"parameters": [{"$ref": "#/parameters/account_id"}], "get": {"tags": ["Teams"], "operationId": "list-all-teams", "summary": "List all teams", "description": "List all teams available in the current account", "responses": {"200": {"description": "Success", "schema": {"type": "array", "description": "Array of teams", "items": {"$ref": "#/definitions/team"}}}, "401": {"description": "Unauthorized"}}}, "post": {"tags": ["Teams"], "operationId": "create-a-team", "summary": "Create a team", "description": "Create a team in the account", "parameters": [{"$ref": "#/parameters/account_id"}, {"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/team_create_update_payload"}}], "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/team"}}, "401": {"description": "Unauthorized"}}}}, "/api/v1/accounts/{account_id}/teams/{team_id}": {"parameters": [{"$ref": "#/parameters/account_id"}, {"$ref": "#/parameters/team_id"}], "get": {"tags": ["Teams"], "operationId": "get-details-of-a-single-team", "summary": "Get a team details", "description": "Get the details of a team in the account", "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/team"}}, "401": {"description": "Unauthorized"}, "404": {"description": "The given team ID does not exist in the account"}}}, "patch": {"tags": ["Teams"], "operationId": "update-a-team", "summary": "Update a team", "description": "Update a team's attributes", "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/team_create_update_payload"}}], "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/team"}}, "401": {"description": "Unauthorized"}}}, "delete": {"tags": ["Teams"], "operationId": "delete-a-team", "summary": "Delete a team", "description": "Delete a team from the account", "responses": {"200": {"description": "Success"}, "401": {"description": "Unauthorized"}, "404": {"description": "The team does not exist in the account"}}}}, "/api/v1/accounts/{account_id}/teams/{team_id}/team_members": {"parameters": [{"$ref": "#/parameters/account_id"}, {"$ref": "#/parameters/team_id"}], "get": {"tags": ["Teams"], "operationId": "get-team-members", "summary": "List Agents in Team", "description": "Get Details of Agents in an Team", "security": [{"userApiKey": []}], "parameters": [{"$ref": "#/parameters/account_id"}, {"$ref": "#/parameters/team_id"}], "responses": {"200": {"description": "Success", "schema": {"type": "array", "description": "Array of all agents in the team", "items": {"$ref": "#/definitions/agent"}}}, "404": {"description": "Inbox not found"}, "403": {"description": "Access denied"}}}, "post": {"tags": ["Teams"], "operationId": "add-new-agent-to-team", "summary": "Add a New Agent", "description": "Add a new Agent to Team", "security": [{"userApiKey": []}], "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"type": "object", "required": ["user_ids"], "properties": {"user_ids": {"type": "array", "items": {"type": "integer"}, "description": "IDs of users to be added to the team"}}}}], "responses": {"200": {"description": "Success", "schema": {"type": "array", "description": "Array of all active agents", "items": {"$ref": "#/definitions/agent"}}}, "404": {"description": "Team not found"}, "403": {"description": "Access denied"}, "422": {"description": "User must exist"}}}, "patch": {"tags": ["Teams"], "operationId": "update-agents-in-team", "summary": "Update Agents in Team", "description": "All agents except the one passed in params will be removed", "security": [{"userApiKey": []}], "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"type": "object", "required": ["user_ids"], "properties": {"user_ids": {"type": "array", "items": {"type": "integer"}, "description": "IDs of users to be added to the team"}}}}], "responses": {"200": {"description": "Success", "schema": {"type": "array", "description": "Array of all agents in the team", "items": {"$ref": "#/definitions/agent"}}}, "404": {"description": "Team not found"}, "403": {"description": "Access denied"}, "422": {"description": "User must exist"}}}, "delete": {"tags": ["Teams"], "operationId": "delete-agent-in-team", "summary": "Remove an Agent from Team", "description": "Remove an Agent from Team", "security": [{"userApiKey": []}], "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"type": "object", "required": ["team_id", "user_ids"], "properties": {"user_ids": {"type": "array", "items": {"type": "integer"}, "description": "IDs of users to be deleted from the team"}}}}], "responses": {"200": {"description": "Success"}, "404": {"description": "Team not found"}, "403": {"description": "Access denied"}, "422": {"description": "User must exist"}}}}, "/api/v1/accounts/{account_id}/custom_filters": {"parameters": [{"$ref": "#/parameters/account_id"}, {"in": "query", "name": "filter_type", "type": "string", "enum": ["conversation", "contact", "report"], "required": false, "description": "The type of custom filter"}], "get": {"tags": ["Custom Filters"], "operationId": "list-all-filters", "summary": "List all custom filters", "description": "List all custom filters in a category of a user", "responses": {"200": {"description": "Success", "schema": {"type": "array", "description": "Array of custom filters", "items": {"$ref": "#/definitions/custom_filter"}}}, "401": {"description": "Unauthorized"}}}, "post": {"tags": ["Custom Filters"], "operationId": "create-a-custom-filter", "summary": "Create a custom filter", "description": "Create a custom filter in the account", "parameters": [{"$ref": "#/parameters/account_id"}, {"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/custom_filter_create_update_payload"}}], "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/custom_filter"}}, "401": {"description": "Unauthorized"}}}}, "/api/v1/accounts/{account_id}/custom_filters/{custom_filter_id}": {"parameters": [{"$ref": "#/parameters/account_id"}, {"$ref": "#/parameters/custom_filter_id"}], "get": {"tags": ["Custom Filters"], "operationId": "get-details-of-a-single-custom-filter", "summary": "Get a custom filter details", "description": "Get the details of a custom filter in the account", "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/custom_filter"}}, "401": {"description": "Unauthorized"}, "404": {"description": "The given team ID does not exist in the account"}}}, "patch": {"tags": ["Custom Filters"], "operationId": "update-a-custom-filter", "summary": "Update a custom filter", "description": "Update a custom filter's attributes", "parameters": [{"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/custom_filter_create_update_payload"}}], "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/custom_filter"}}, "401": {"description": "Unauthorized"}}}, "delete": {"tags": ["Custom Filters"], "operationId": "delete-a-custom-filter", "summary": "Delete a custom filter", "description": "Delete a custom filter from the account", "responses": {"200": {"description": "Success"}, "401": {"description": "Unauthorized"}, "404": {"description": "The custom filter does not exist in the account"}}}}, "/api/v1/accounts/{account_id}/webhooks": {"parameters": [{"$ref": "#/parameters/account_id"}], "get": {"tags": ["Webhooks"], "operationId": "list-all-webhooks", "summary": "List all webhooks", "description": "List all webhooks in the account", "responses": {"200": {"description": "Success", "schema": {"type": "array", "description": "Array of webhook objects", "items": {"$ref": "#/definitions/webhook"}}}, "401": {"description": "Unauthorized"}}}, "post": {"tags": ["Webhooks"], "operationId": "create-a-webhook", "summary": "Add a webhook", "description": "Add a webhook subscription to the account", "parameters": [{"$ref": "#/parameters/account_id"}, {"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/webhook_create_update_payload"}}], "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/webhook"}}, "401": {"description": "Unauthorized"}}}}, "/api/v1/accounts/{account_id}/webhooks/{webhook_id}": {"parameters": [{"$ref": "#/parameters/account_id"}, {"$ref": "#/parameters/webhook_id"}], "patch": {"tags": ["Webhooks"], "operationId": "update-a-webhook", "summary": "Update a webhook object", "description": "Update a webhook object in the account", "parameters": [{"$ref": "#/parameters/account_id"}, {"name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/webhook_create_update_payload"}}], "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/webhook"}}, "401": {"description": "Unauthorized"}}}, "delete": {"tags": ["Webhooks"], "operationId": "delete-a-webhook", "summary": "Delete a webhook", "description": "Delete a webhook from the account", "responses": {"200": {"description": "Success"}, "401": {"description": "Unauthorized"}, "404": {"description": "The webhook does not exist in the account"}}}}, "/api/v2/accounts/{account_id}/reports": {"parameters": [{"$ref": "#/parameters/account_id"}, {"$ref": "#/parameters/report_metric"}, {"$ref": "#/parameters/report_type"}, {"in": "query", "name": "id", "type": "string", "description": "The Id of specific object in case of agent/inbox/label"}, {"in": "query", "name": "since", "type": "string", "description": "The timestamp from where report should start."}, {"in": "query", "name": "until", "type": "string", "description": "The timestamp from where report should stop."}], "get": {"tags": ["Reports"], "operationId": "list-all-conversation-statistics", "summary": "Get Account reports", "description": "Get Account reports for a specific type, metric and date range", "responses": {"200": {"description": "Success", "schema": {"type": "array", "description": "Array of date based conversation statistics", "items": {"type": "object", "properties": {"value": {"type": "string"}, "timestamp": {"type": "number"}}}}}, "404": {"description": "reports not found"}, "403": {"description": "Access denied"}}}}, "/api/v2/accounts/{account_id}/reports/summary": {"parameters": [{"$ref": "#/parameters/account_id"}, {"$ref": "#/parameters/report_type"}, {"in": "query", "name": "id", "type": "string", "description": "The Id of specific object in case of agent/inbox/label"}, {"in": "query", "name": "since", "type": "string", "description": "The timestamp from where report should start."}, {"in": "query", "name": "until", "type": "string", "description": "The timestamp from where report should stop."}], "get": {"tags": ["Reports"], "operationId": "list-all-conversation-statistics-summary", "summary": "Get Account reports summary", "description": "Get Account reports summary for a specific type and date range", "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/account_summary"}}, "404": {"description": "reports not found"}, "403": {"description": "Access denied"}}}}, "/api/v2/accounts/{account_id}/reports/conversations": {"parameters": [{"$ref": "#/parameters/account_id"}, {"in": "query", "name": "type", "type": "string", "enum": ["account"], "required": true, "description": "Type of report"}], "get": {"tags": ["Reports"], "operationId": "get-account-conversation-metrics", "summary": "Account Conversation Metrics", "description": "Get conversation metrics for Account", "responses": {"200": {"description": "Success", "schema": {"type": "object", "description": "Object of account conversation metrics", "properties": {"open": {"type": "number"}, "unattended": {"type": "number"}, "unassigned": {"type": "number"}}}}, "404": {"description": "reports not found"}, "403": {"description": "Access denied"}}}}, "/api/v2/accounts/{account_id}/reports/conversations/": {"parameters": [{"$ref": "#/parameters/account_id"}, {"in": "query", "name": "type", "type": "string", "enum": ["agent"], "required": true, "description": "Type of report"}, {"in": "query", "name": "user_id", "type": "string", "description": "The numeric ID of the user"}], "get": {"tags": ["Reports"], "operationId": "get-agent-conversation-metrics", "summary": "Agent Conversation Metrics", "description": "Get conversation metrics for Agent", "responses": {"200": {"description": "Success", "schema": {"type": "array", "description": "Array of agent based conversation metrics", "items": {"$ref": "#/definitions/agent_conversation_metrics"}}}, "404": {"description": "reports not found"}, "403": {"description": "Access denied"}}}}}, "definitions": {"bad_request_error": {"title": "data", "type": "object", "properties": {"description": {"type": "string"}, "errors": {"type": "array", "items": {"$ref": "#/definitions/request_error"}}}}, "request_error": {"type": "object", "properties": {"field": {"type": "string"}, "message": {"type": "string"}, "code": {"type": "string"}}}, "generic_id": {"type": "object", "properties": {"id": {"type": "number"}}}, "canned_response": {"type": "object", "properties": {"id": {"type": "integer", "description": "ID of the canned response"}, "content": {"type": "string", "description": "Message content for canned response"}, "short_code": {"type": "string", "description": "Short Code for quick access of the canned response"}, "account_id": {"type": "integer", "description": "Account Id"}}}, "custom_attribute": {"type": "object", "properties": {"id": {"type": "integer", "description": "Identifier"}, "attribute_display_name": {"type": "string", "description": "Attribute display name"}, "attribute_display_type": {"type": "string", "description": "Attribute display type (text, number, currency, percent, link, date, list, checkbox)"}, "attribute_description": {"type": "string", "description": "Attribute description"}, "attribute_key": {"type": "string", "description": "Attribute unique key value"}, "attribute_values": {"type": "string", "description": "Attribute values"}, "default_value": {"type": "string", "description": "Attribute default value"}, "attribute_model": {"type": "string", "description": "Attribute type(conversation_attribute/contact_attribute)"}, "account_id": {"type": "integer", "description": "Account Id"}}}, "automation_rule": {"type": "object", "properties": {"event_name": {"type": "string", "description": "Automation Rule event, on which we call the actions(conversation_created, conversation_updated, message_created)", "enum": ["conversation_created", "conversation_updated", "message_created"], "example": "message_created"}, "name": {"type": "string", "description": "The name of the rule", "example": "Add label on message create event"}, "description": {"type": "string", "description": "Description to give more context about the rule", "example": "Add label support and sales on message create event if incoming message content contains text help"}, "active": {"type": "boolean", "description": "Enable/disable automation rule"}, "actions": {"type": "array", "description": "Array of actions which we perform when condition matches", "items": {"type": "object", "example": {"action_name": "add_label", "action_params": ["support", "sales"]}}}, "conditions": {"type": "array", "description": "Array of conditions on which conversation/message filter would work", "items": {"type": "object", "example": {"attribute_key": "content", "filter_operator": "contains", "values": ["help"], "query_operator": "nil"}}}, "account_id": {"type": "integer", "description": "Account Id"}}}, "portal": {"type": "object", "properties": {"id": {"type": "integer"}, "archived": {"type": "boolean"}, "color": {"type": "string"}, "config": {"type": "object", "description": "Save information about locales, allowed_locales and default portal/help-center locale"}, "custom_domain": {"type": "string"}, "header_text": {"type": "string", "description": "The text content."}, "homepage_link": {"type": "string"}, "name": {"type": "string"}, "slug": {"type": "string"}, "page_title": {"type": "string"}, "account_id": {"type": "integer"}, "categories": {"type": "array", "items": {"$ref": "#/definitions/category"}}, "articles": {"type": "array", "items": {"$ref": "#/definitions/article"}}}}, "category": {"type": "object", "properties": {"id": {"type": "integer"}, "description": {"type": "string", "description": "The text content."}, "locale": {"type": "string"}, "name": {"type": "string"}, "slug": {"type": "string"}, "position": {"type": "integer"}, "portal_id": {"type": "integer"}, "account_id": {"type": "integer"}, "associated_category_id": {"type": "integer", "description": "To associate similar categories to each other, e.g same category of product documentation in different languages"}, "parent_category_id": {"type": "integer", "description": "To define parent category, e.g product documentation has multiple level features in sales category or in engineering category."}}}, "article": {"type": "object", "properties": {"id": {"type": "integer"}, "content": {"type": "string", "description": "The text content."}, "meta": {"type": "object"}, "position": {"type": "integer"}, "status": {"type": "integer", "enum": ["draft", "published", "archived"]}, "title": {"type": "string"}, "slug": {"type": "string"}, "views": {"type": "integer"}, "portal_id": {"type": "integer"}, "account_id": {"type": "integer"}, "author_id": {"type": "integer"}, "category_id": {"type": "integer"}, "folder_id": {"type": "integer"}, "associated_article_id": {"type": "integer", "description": "To associate similar articles to each other, e.g to provide the link for the reference."}}}, "contact": {"type": "object", "properties": {"payload": {"type": "object", "properties": {"contact": {"type": "object", "properties": {"email": {"type": "string", "description": "Email address of the contact"}, "name": {"type": "string", "description": "The name of the contact"}, "phone_number": {"type": "string", "description": "Phone number of the contact"}, "thumbnail": {"type": "string", "description": "Avatar URL of the contact"}, "additional_attributes": {"type": "object", "description": "The object containing additional attributes related to the contact"}, "custom_attributes": {"type": "object", "description": "The object to save custom attributes for contact, accepts custom attributes key and value", "example": {"attribute_key": "attribute_value", "signed_up_at": "dd/mm/yyyy"}}, "contact_inboxes": {"type": "array", "items": {"$ref": "#/definitions/contact_inboxes"}}}}}}}}, "conversation": {"type": "object", "properties": {"id": {"type": "number", "description": "ID of the conversation"}, "messages": {"type": "array", "items": {"$ref": "#/definitions/message"}}, "account_id": {"type": "number", "description": "Account Id"}, "inbox_id": {"type": "number", "description": "ID of the inbox"}, "status": {"type": "string", "enum": ["open", "resolved", "pending"], "description": "The status of the conversation"}, "timestamp": {"type": "string", "description": "The time at which conversation was created"}, "contact_last_seen_at": {"type": "string"}, "agent_last_seen_at": {"type": "string"}, "unread_count": {"type": "number", "description": "The number of unread messages"}, "additional_attributes": {"type": "object", "description": "The object containing additional attributes related to the conversation"}, "custom_attributes": {"type": "object", "description": "The object to save custom attributes for conversation, accepts custom attributes key and value", "example": {"attribute_key": "attribute_value", "priority_conversation_number": 3}}}}, "message": {"type": "object", "properties": {"content": {"type": "string", "description": "The text content of the message"}, "content_type": {"type": "string", "enum": ["text", "input_select", "cards", "form"], "description": "The type of the template message"}, "content_attributes": {"type": "object", "description": "The content attributes for each content_type"}, "message_type": {"type": "string", "enum": ["incoming", "outgoing", "activity", "template"], "description": "The type of the message"}, "created_at": {"type": "integer", "description": "The time at which message was created"}, "private": {"type": "boolean", "description": "The flags which shows whether the message is private or not"}, "attachment": {"type": "object", "description": "The file object attached to the image"}, "sender": {"type": "object", "description": "User/Agent/AgentBot object"}, "conversation_id": {"type": "number", "description": "ID of the conversation"}}}, "user": {"type": "object", "properties": {"id": {"type": "number"}, "uid": {"type": "string"}, "name": {"type": "string"}, "available_name": {"type": "string"}, "display_name": {"type": "string"}, "email": {"type": "string"}, "account_id": {"type": "number"}, "role": {"type": "string", "enum": ["agent", "administrator"]}, "confirmed": {"type": "boolean"}, "custom_attributes": {"type": "object", "description": "Available for users who are created through platform APIs and has custom attributes associated."}, "accounts": {"type": "array", "items": {"$ref": "#/definitions/account"}}}}, "agent": {"type": "object", "properties": {"id": {"type": "integer"}, "uid": {"type": "string"}, "name": {"type": "string"}, "available_name": {"type": "string"}, "display_name": {"type": "string"}, "email": {"type": "string"}, "account_id": {"type": "integer"}, "role": {"type": "string", "enum": ["agent", "administrator"]}, "confirmed": {"type": "boolean"}, "availability_status": {"type": "string", "enum": ["available", "busy", "offline"], "description": "The availability status of the agent computed by <PERSON><PERSON><PERSON>ot."}, "auto_offline": {"type": "boolean", "description": "Whether the availability status of agent is configured to go offline automatically when away."}, "custom_attributes": {"type": "object", "description": "Available for users who are created through platform APIs and has custom attributes associated."}}}, "inbox": {"type": "object", "properties": {"id": {"type": "number", "description": "ID of the inbox"}, "name": {"type": "string", "description": "The name of the inbox"}, "website_url": {"type": "string", "description": "Website URL"}, "channel_type": {"type": "string", "description": "The type of the inbox"}, "avatar_url": {"type": "string", "description": "The avatar image of the inbox"}, "widget_color": {"type": "string", "description": "Widget Color used for customization of the widget"}, "website_token": {"type": "string", "description": "Website Token"}, "enable_auto_assignment": {"type": "boolean", "description": "The flag which shows whether Auto Assignment is enabled or not"}, "web_widget_script": {"type": "string", "description": "<PERSON><PERSON>t used to load the website widget"}, "welcome_title": {"type": "string", "description": "Welcome title to be displayed on the widget"}, "welcome_tagline": {"type": "string", "description": "Welcome tagline to be displayed on the widget"}, "greeting_enabled": {"type": "boolean", "description": "The flag which shows whether greeting is enabled"}, "greeting_message": {"type": "string", "description": "A greeting message when the user starts the conversation"}}}, "agent_bot": {"type": "object", "properties": {"id": {"type": "number", "description": "ID of the agent bot"}, "name": {"type": "string", "description": "The name of the agent bot"}, "description": {"type": "string", "description": "The description about the agent bot"}, "account_id": {"type": "number", "description": "Account ID if it's an account specific bot"}, "outgoing_url": {"type": "string", "description": "The webhook URL for the bot"}}}, "contact_inboxes": {"type": "object", "properties": {"source_id": {"type": "string", "description": "Contact Inbox Source Id"}, "inbox": {"$ref": "#/definitions/inbox"}}}, "contactable_inboxes": {"type": "object", "properties": {"source_id": {"type": "string", "description": "Contact Inbox Source Id"}, "inbox": {"$ref": "#/definitions/inbox"}}}, "custom_filter": {"type": "object", "properties": {"id": {"type": "number", "description": "The ID of the custom filter"}, "name": {"type": "string", "description": "The name of the custom filter"}, "type": {"type": "string", "enum": ["conversation", "contact", "report"], "description": "The description about the custom filter"}, "query": {"type": "object", "description": "A query that needs to be saved as a custom filter"}, "created_at": {"type": "string", "format": "date-time", "description": "The time at which the custom filter was created"}, "updated_at": {"type": "string", "format": "date-time", "description": "The time at which the custom filter was updated"}}}, "webhook": {"type": "object", "properties": {"id": {"type": "number", "description": "The ID of the webhook"}, "url": {"type": "string", "description": "The url to which the events will be send"}, "subscriptions": {"type": "array", "items": {"type": "string", "enum": ["conversation_created", "conversation_status_changed", "conversation_updated", "contact_created", "contact_updated", "message_created", "message_updated", "webwidget_triggered"]}, "description": "The list of subscribed events"}, "account_id": {"type": "number", "description": "The id of the account which the webhook object belongs to"}}}, "account": {"type": "object", "properties": {"id": {"type": "number", "description": "Account ID"}, "name": {"type": "string", "description": "Name of the account"}, "role": {"type": "string", "enum": ["administrator", "agent"], "description": "The user role in the account"}}}, "platform_account": {"type": "object", "properties": {"id": {"type": "number", "description": "Account ID"}, "name": {"type": "string", "description": "Name of the account"}}}, "team": {"type": "object", "properties": {"id": {"type": "number", "description": "The ID of the team"}, "name": {"type": "string", "description": "The name of the team"}, "description": {"type": "string", "description": "The description about the team"}, "allow_auto_assign": {"type": "boolean", "description": "If this setting is turned on, the system would automatically assign the conversation to an agent in the team while assigning the conversation to a team"}, "account_id": {"type": "number", "description": "The ID of the account with the team is a part of"}, "is_member": {"type": "boolean", "description": "This field shows whether the current user is a part of the team"}}}, "integrations_app": {"type": "object", "properties": {"id": {"type": "string", "description": "The ID of the integration"}, "name": {"type": "string", "description": "The name of the integration"}, "description": {"type": "string", "description": "The description about the team"}, "hook_type": {"type": "string", "description": "Whether the integration is an account or inbox integration"}, "enabled": {"type": "boolean", "description": "Whether the integration is enabled for the account"}, "allow_multiple_hooks": {"type": "boolean", "description": "Whether multiple hooks can be created for the integration"}, "hooks": {"type": "array", "items": {"type": "object"}, "description": "If there are any hooks created for this integration"}}}, "integrations_hook": {"type": "object", "properties": {"id": {"type": "string", "description": "The ID of the integration hook"}, "app_id": {"type": "string", "description": "The ID of the integration app"}, "inbox_id": {"type": "string", "description": "Inbox ID if its an Inbox integration"}, "account_id": {"type": "string", "description": "Account ID of the integration"}, "status": {"type": "boolean", "description": "Whether the integration hook is enabled for the account"}, "hook_type": {"type": "boolean", "description": "Whether its an account or inbox integration hook"}, "settings": {"type": "object", "description": "The associated settings for the integration"}}}, "public_contact": {"type": "object", "properties": {"id": {"type": "integer", "description": "Id of the contact"}, "source_id": {"type": "string", "description": "The session identifier of the contact"}, "name": {"type": "string", "description": "Name of the contact"}, "email": {"type": "string", "description": "Email of the contact"}, "pubsub_token": {"type": "string", "description": "The token to be used to connect to chatwoot websocket"}}}, "public_conversation": {"type": "object", "properties": {"id": {"type": "integer", "description": "Id of the conversation"}, "inbox_id": {"type": "string", "description": "The inbox id of the conversation"}, "messages": {"type": "array", "items": {"$ref": "#/definitions/message"}, "description": "Messages in the conversation"}, "contact": {"type": "object", "description": "The contact information associated to the conversation"}}}, "public_message": {"type": "object", "properties": {"id": {"type": "string", "description": "Id of the message"}, "content": {"type": "string", "description": "Text content of the message"}, "message_type": {"type": "string", "description": "Denotes the message type"}, "content_type": {"type": "string", "description": "Content type of the message"}, "content_attributes": {"type": "string", "description": "Additional content attributes of the message"}, "created_at": {"type": "string", "description": "Created at time stamp of the message"}, "conversation_id": {"type": "string", "description": "Conversation Id of the message"}, "attachments": {"type": "array", "items": {"type": "object"}, "description": "Attachments if any"}, "sender": {"type": "object", "description": "Details of the sender"}}}, "public_inbox": {"type": "object", "properties": {"identifier": {"type": "string", "description": "Inbox identifier"}, "name": {"type": "string", "description": "Name of the inbox"}, "timezone": {"type": "string", "description": "The timezone defined on the inbox"}, "working_hours": {"type": "array", "description": "The working hours defined on the inbox", "items": {"type": "object", "properties": {"day_of_week": {"type": "integer", "description": "Day of the week as a number. Sunday -> 0, Saturday -> 6"}, "open_all_day": {"type": "boolean", "description": "Whether or not the business is open the whole day"}, "closed_all_day": {"type": "boolean", "description": "Whether or not the business is closed the whole day"}, "open_hour": {"type": "integer", "description": "Opening hour. Can be null if closed all day"}, "open_minutes": {"type": "integer", "description": "Opening minute. Can be null if closed all day"}, "close_hour": {"type": "integer", "description": "Closing hour. Can be null if closed all day"}, "close_minutes": {"type": "integer", "description": "Closing minute. Can be null if closed all day"}}}}, "working_hours_enabled": {"type": "boolean", "description": "Whether of not the working hours are enabled on the inbox"}, "csat_survey_enabled": {"type": "boolean", "description": "Whether of not the Customer Satisfaction survey is enabled on the inbox"}, "greeting_enabled": {"type": "boolean", "description": "Whether of not the Greeting Message is enabled on the inbox"}, "identity_validation_enabled": {"type": "boolean", "description": "Whether of not the User Identity Validation is enforced on the inbox"}}}, "account_create_update_payload": {"type": "object", "properties": {"name": {"type": "string", "description": "Name of the account"}}}, "agent_bot_create_update_payload": {"type": "object", "properties": {"name": {"type": "string", "description": "The name of the agent bot"}, "description": {"type": "string", "description": "The description about the agent bot"}, "outgoing_url": {"type": "string", "description": "The webhook URL for the bot"}}}, "user_create_update_payload": {"type": "object", "properties": {"name": {"type": "string", "description": "Name of the user"}, "email": {"type": "string", "description": "Email of the user"}, "password": {"type": "string", "description": "Password must contain uppercase, lowercase letters, number and a special character"}, "custom_attributes": {"type": "object", "description": "Custom attributes you want to associate with the user"}}}, "canned_response_create_update_payload": {"type": "object", "properties": {"content": {"type": "string", "description": "Message content for canned response"}, "short_code": {"type": "string", "description": "Short Code for quick access of the canned response"}}}, "custom_attribute_create_update_payload": {"type": "object", "properties": {"attribute_display_name": {"type": "string", "description": "Attribute display name"}, "attribute_display_type": {"type": "integer", "description": "Attribute display type (text- 0, number- 1, currency- 2, percent- 3, link- 4, date- 5, list- 6, checkbox- 7)"}, "attribute_description": {"type": "string", "description": "Attribute description"}, "attribute_key": {"type": "string", "description": "Attribute unique key value"}, "attribute_values": {"type": "array", "description": "Attribute values", "items": {"type": "string"}}, "attribute_model": {"type": "integer", "description": "Attribute type(conversation_attribute- 0, contact_attribute- 1)"}}}, "contact_create": {"type": "object", "required": ["inbox_id"], "properties": {"inbox_id": {"type": "number"}, "name": {"type": "string", "description": "name of the contact"}, "email": {"type": "string", "description": "email of the contact"}, "phone_number": {"type": "string", "description": "phone number of the contact"}, "avatar": {"type": "string", "format": "binary", "description": "Send the form data with the avatar image binary or use the avatar_url"}, "avatar_url": {"type": "string", "description": "The url to a jpeg, png file for the contact avatar"}, "identifier": {"type": "string", "description": "A unique identifier for the contact in external system"}, "custom_attributes": {"type": "object", "description": "An object where you can store custom attributes for contact. example {\"type\":\"customer\", \"age\":30}"}}}, "contact_update": {"type": "object", "properties": {"name": {"type": "string", "description": "name of the contact"}, "email": {"type": "string", "description": "email of the contact"}, "phone_number": {"type": "string", "description": "phone number of the contact"}, "avatar": {"type": "string", "format": "binary", "description": "Send the form data with the avatar image binary or use the avatar_url"}, "avatar_url": {"type": "string", "description": "The url to a jpeg, png file for the contact avatar"}, "identifier": {"type": "string", "description": "A unique identifier for the contact in external system"}, "custom_attributes": {"type": "object", "description": "An object where you can store custom attributes for contact. example {\"type\":\"customer\", \"age\":30}"}}}, "conversation_message_create": {"type": "object", "required": ["content"], "properties": {"content": {"type": "string", "description": "The content of the message"}, "message_type": {"type": "string", "enum": ["outgoing", "incoming"]}, "private": {"type": "boolean", "description": "Flag to identify if it is a private note"}, "content_type": {"type": "string", "enum": ["text", "input_email", "cards", "input_select", "form", "article"], "example": "cards", "description": "if you want to create custom message types"}, "content_attributes": {"type": "object", "description": "attributes based on your content type"}, "template_params": {"type": "object", "description": "The template params for the message in case of whatsapp Channel", "properties": {"name": {"type": "string", "description": "Name of the template", "example": "sample_issue_resolution"}, "category": {"type": "string", "description": "Category of the template", "example": "UTILITY"}, "language": {"type": "string", "description": "Language of the template", "example": "en_US"}, "processed_params": {"type": "object", "description": "The processed param values for template variables in template", "example": {"1": "Chatwoot"}}}}}}, "team_create_update_payload": {"type": "object", "properties": {"name": {"type": "string", "description": "The name of the team"}, "description": {"type": "string", "description": "The description of the team"}, "allow_auto_assign": {"type": "boolean", "description": "If this setting is turned on, the system would automatically assign the conversation to an agent in the team while assigning the conversation to a team"}}}, "custom_filter_create_update_payload": {"type": "object", "properties": {"name": {"type": "string", "description": "The name of the custom filter"}, "type": {"type": "string", "enum": ["conversation", "contact", "report"], "description": "The description about the custom filter"}, "query": {"type": "object", "description": "A query that needs to be saved as a custom filter"}}}, "webhook_create_update_payload": {"type": "object", "properties": {"url": {"type": "string", "description": "The url where the events should be sent"}, "subscriptions": {"type": "array", "items": {"type": "string", "enum": ["conversation_created", "conversation_status_changed", "conversation_updated", "message_created", "message_updated", "webwidget_triggered"]}, "description": "The events you want to subscribe to."}}}, "integrations_hook_create_payload": {"type": "object", "properties": {"app_id": {"type": "string", "description": "The ID of app for which integration hook is being created"}, "inbox_id": {"type": "string", "description": "The inbox ID, if the hook is an inbox hook"}, "settings": {"type": "object", "description": "The settings required by the integration"}}}, "integrations_hook_update_payload": {"type": "object", "properties": {"settings": {"type": "object", "description": "The settings required by the integration"}}}, "automation_rule_create_update_payload": {"type": "object", "properties": {"name": {"type": "string", "description": "Rule name", "example": "Add label on message create event"}, "description": {"type": "string", "description": "The description about the automation and actions", "example": "Add label support and sales on message create event if incoming message content contains text help"}, "event_name": {"type": "string", "enum": ["conversation_created", "conversation_updated", "message_created"], "example": "message_created", "description": "The event when you want to execute the automation actions"}, "active": {"type": "boolean", "description": "Enable/disable automation rule"}, "actions": {"type": "array", "description": "Array of actions which you want to perform when condition matches, e.g add label support if message contains content help.", "items": {"type": "object", "example": {"action_name": "add_label", "action_params": ["support"]}}}, "conditions": {"type": "array", "description": "Array of conditions on which conversation filter would work, e.g message content contains text help.", "items": {"type": "object", "example": {"attribute_key": "content", "filter_operator": "contains", "query_operator": "nil", "values": ["help"]}}}}}, "portal_create_update_payload": {"type": "object", "properties": {"archived": {"type": "boolean", "description": "Status to check if portal is live"}, "color": {"type": "string", "description": "Header color for help-center", "example": "add color HEX string, \"#fffff\""}, "config": {"type": "object", "description": "Configuration about supporting locales", "example": {"allowed_locales": ["en", "es"], "default_locale": "en"}}, "custom_domain": {"type": "string", "description": "Custom domain to  display help center.", "example": "https://chatwoot.help/."}, "header_text": {"type": "string", "description": "Help center header", "example": "Handbook"}, "homepage_link": {"type": "string", "description": "link to main dashboard", "example": "https://www.chatwoot.com/"}, "name": {"type": "string", "description": "Name for the portal"}, "slug": {"type": "string", "description": "Slug for the portal to display in link"}, "page_title": {"type": "string", "description": "Page title for the portal"}, "account_id": {"type": "integer"}}}, "category_create_update_payload": {"type": "object", "properties": {"description": {"type": "string", "description": "Category description"}, "locale": {"type": "string", "description": "Category locale", "example": "en/es"}, "name": {"type": "string", "description": "Category name"}, "slug": {"type": "string", "description": "Category slug"}, "position": {"type": "integer", "description": "Category position in the portal list to sort"}, "portal_id": {"type": "integer"}, "account_id": {"type": "integer"}, "associated_category_id": {"type": "integer", "description": "To associate similar categories to each other, e.g same category of product documentation in different languages"}, "parent_category_id": {"type": "integer", "description": "To define parent category, e.g product documentation has multiple level features in sales category or in engineering category."}}}, "article_create_update_payload": {"type": "object", "properties": {"content": {"type": "string", "description": "The text content."}, "meta": {"type": "object", "description": "Use for search", "example": {"tags": ["article_name"], "title": "article title", "description": "article description"}}, "position": {"type": "integer", "description": "article position in category"}, "status": {"type": "integer", "example": ["draft", "published", "archived"]}, "title": {"type": "string"}, "slug": {"type": "string"}, "views": {"type": "integer"}, "portal_id": {"type": "integer"}, "account_id": {"type": "integer"}, "author_id": {"type": "integer"}, "category_id": {"type": "integer"}, "folder_id": {"type": "integer"}, "associated_article_id": {"type": "integer", "description": "To associate similar articles to each other, e.g to provide the link for the reference."}}}, "public_contact_create_update_payload": {"type": "object", "properties": {"identifier": {"type": "string", "description": "External identifier of the contact"}, "identifier_hash": {"type": "string", "description": "Identifier hash prepared for HMAC authentication"}, "email": {"type": "string", "description": "Email of the contact"}, "name": {"type": "string", "description": "Name of the contact"}, "phone_number": {"type": "string", "description": "Phone number of the contact"}, "avatar_url": {"type": "string", "description": "The url to a jpeg, png file for the user avatar"}, "custom_attributes": {"type": "object", "description": "Custom attributes of the customer"}}}, "public_message_create_payload": {"type": "object", "properties": {"content": {"type": "string", "description": "Content for the message"}, "echo_id": {"type": "string", "description": "Temporary identifier which will be passed back via websockets"}}}, "public_message_update_payload": {"type": "object", "properties": {"submitted_values": {"type": "object", "description": "Replies to the Bot Message Types"}}}, "public_conversation_create_payload": {"type": "object", "properties": {"custom_attributes": {"type": "object", "description": "Custom attributes of the conversation"}}}, "extended_contact": {"allOf": [{"$ref": "#/definitions/contact"}, {"type": "object", "properties": {"id": {"type": "number", "description": "Id of the user"}, "availability_status": {"type": "string", "enum": ["online", "offline"], "description": "Availability status of the user"}}}]}, "contact_base": {"allOf": [{"$ref": "#/definitions/generic_id"}, {"$ref": "#/definitions/contact"}]}, "contact_list": {"type": "array", "description": "array of contacts", "items": {"allOf": [{"$ref": "#/definitions/generic_id"}, {"$ref": "#/definitions/contact"}]}}, "contact_conversations": {"type": "array", "description": "array of conversations", "items": {"allOf": [{"$ref": "#/definitions/conversation"}, {"type": "object", "properties": {"meta": {"type": "object", "properties": {"sender": {"type": "object", "properties": {"id": {"type": "number", "description": "ID fo the sender"}, "name": {"type": "string", "description": "The name of the sender"}, "thumbnail": {"type": "string", "description": "Avatar URL of the contact"}, "channel": {"type": "string", "description": "Channel Type"}}}, "assignee": {"$ref": "#/definitions/user"}}}}}, {"type": "object", "properties": {"display_id": {"type": "number"}}}]}}, "contact_labels": {"type": "object", "properties": {"payload": {"type": "array", "description": "Array of labels", "items": {"type": "string"}}}}, "conversation_list": {"type": "object", "properties": {"data": {"type": "object", "properties": {"meta": {"type": "object", "properties": {"mine_count": {"type": "number"}, "unassigned_count": {"type": "number"}, "assigned_count": {"type": "number"}, "all_count": {"type": "number"}}}, "payload": {"type": "array", "description": "array of conversations", "items": {"allOf": [{"$ref": "#/definitions/generic_id"}, {"$ref": "#/definitions/conversation"}, {"type": "object", "properties": {"meta": {"type": "object", "properties": {"sender": {"type": "object", "properties": {"id": {"type": "number", "description": "ID fo the sender"}, "name": {"type": "string", "description": "The name of the sender"}, "thumbnail": {"type": "string", "description": "Avatar URL of the contact"}, "channel": {"type": "string", "description": "Channel Type"}}}, "assignee": {"$ref": "#/definitions/user"}}}}}]}}}}}}, "conversation_show": {"type": "object", "allOf": [{"$ref": "#/definitions/conversation"}, {"type": "object", "properties": {"meta": {"type": "object", "properties": {"sender": {"type": "object", "properties": {"id": {"type": "number", "description": "ID fo the sender"}, "name": {"type": "string", "description": "The name of the sender"}, "thumbnail": {"type": "string", "description": "Avatar URL of the contact"}, "channel": {"type": "string", "description": "Channel Type"}}}, "assignee": {"$ref": "#/definitions/user"}}}}}]}, "conversation_status_toggle": {"type": "object", "properties": {"meta": {"type": "object"}, "payload": {"type": "object", "properties": {"success": {"type": "boolean"}, "current_status": {"type": "string", "enum": ["open", "resolved"]}, "conversation_id": {"type": "number"}}}}}, "conversation_labels": {"type": "object", "properties": {"payload": {"type": "array", "description": "Array of labels", "items": {"type": "string"}}}}, "account_summary": {"type": "object", "properties": {"avg_first_response_time": {"type": "string"}, "avg_resolution_time": {"type": "string"}, "conversations_count": {"type": "number"}, "incoming_messages_count": {"type": "number"}, "outgoing_messages_count": {"type": "number"}, "resolutions_count": {"type": "number"}, "previous": {"type": "object", "properties": {"avg_first_response_time": {"type": "string"}, "avg_resolution_time": {"type": "string"}, "conversations_count": {"type": "number"}, "incoming_messages_count": {"type": "number"}, "outgoing_messages_count": {"type": "number"}, "resolutions_count": {"type": "number"}}}}}, "agent_conversation_metrics": {"type": "object", "properties": {"id": {"type": "number"}, "name": {"type": "string"}, "email": {"type": "string"}, "thumbnail": {"type": "string"}, "availability": {"type": "string"}, "metric": {"type": "object", "properties": {"open": {"type": "number"}, "unattended": {"type": "number"}}}}}}, "parameters": {"account_id": {"in": "path", "name": "account_id", "type": "integer", "required": true, "description": "The numeric ID of the account"}, "agent_bot_id": {"in": "path", "name": "id", "type": "integer", "required": true, "description": "The ID of the agentbot to be updated"}, "team_id": {"in": "path", "name": "team_id", "type": "integer", "required": true, "description": "The ID of the team to be updated"}, "inbox_id": {"in": "path", "name": "inbox_id", "type": "integer", "required": true, "description": "The ID of the Inbox"}, "hook_id": {"in": "path", "name": "hook_id", "type": "integer", "required": true, "description": "The numeric ID of the integration hook"}, "source_id": {"in": "path", "name": "source_id", "required": true, "type": "string", "description": "Id of the session for which the conversation is created.\n\n\n\n Source Ids can be obtained through contactable inboxes API or via generated.<br/><br/>Website: Chatwoot generated string which can be obtained from webhook events. <br/> Phone Channels(Twilio): Phone number in e164 format <br/> Email Channels: Contact Email address <br/> API Channel: Any Random String"}, "contact_sort_param": {"in": "query", "name": "sort", "type": "string", "enum": ["name", "email", "phone_number", "last_activity_at", "-name", "-email", "-phone_number", "-last_activity_at"], "required": false, "description": "The attribute by which list should be sorted"}, "conversation_id": {"in": "path", "name": "conversation_id", "type": "integer", "required": true, "description": "The numeric ID of the conversation"}, "conversation_uuid": {"in": "path", "name": "conversation_uuid", "type": "integer", "required": true, "description": "The uuid of the conversation"}, "custom_filter_id": {"in": "path", "name": "custom_filter_id", "type": "integer", "required": true, "description": "The numeric ID of the custom filter"}, "webhook_id": {"in": "path", "name": "webhook_id", "type": "integer", "required": true, "description": "The numeric ID of the webhook"}, "message_id": {"in": "path", "name": "message_id", "type": "integer", "required": true, "description": "The numeric ID of the message"}, "page": {"in": "query", "name": "page", "type": "integer", "default": 1, "required": false, "description": "The page parameter"}, "platform_user_id": {"in": "path", "name": "id", "type": "integer", "required": true, "description": "The numeric ID of the user on the platform"}, "report_type": {"in": "query", "name": "type", "type": "string", "enum": ["account", "agent", "inbox", "label", "team"], "required": true, "description": "Type of report"}, "report_metric": {"in": "query", "name": "metric", "type": "string", "enum": ["conversations_count", "incoming_messages_count", "outgoing_messages_count", "avg_first_response_time", "avg_resolution_time", "resolutions_count"], "required": true, "description": "The type of metric"}, "public_inbox_identifier": {"in": "path", "name": "inbox_identifier", "type": "string", "required": true, "description": "The identifier obtained from API inbox channel"}, "public_contact_identifier": {"in": "path", "name": "contact_identifier", "type": "string", "required": true, "description": "The source id of contact obtained on contact create"}, "portal_id": {"in": "path", "name": "portal_id", "type": "integer", "required": true, "description": "The numeric ID of the portal"}}, "x-tagGroups": [{"name": "Platform", "tags": ["Accounts", "Account Users", "AgentBots", "Users"]}, {"name": "Application", "tags": ["Account AgentBots", "Agents", "Canned Responses", "Contacts", "Contact Labels", "Conversation Assignment", "Conversation Labels", "Conversations", "Custom Attributes", "Custom Filters", "Inboxes", "Integrations", "Messages", "Profile", "Reports", "Teams", "Webhooks", "Automation Rule", "Help Center"]}, {"name": "Client", "tags": ["Contacts API", "Conversations API", "Messages API"]}, {"name": "Others", "tags": ["CSAT Survey Page"]}]}