test:
  service: Disk
  root: <%= Rails.root.join("tmp/storage") %>

local:
  service: Disk
  root: <%= Rails.root.join("storage") %>

# Use rails credentials:edit to set the AWS secrets (as aws:access_key_id|secret_access_key)
amazon:
  service: S3
  access_key_id: <%= ENV.fetch('AWS_ACCESS_KEY_ID', '') %>
  secret_access_key: <%= ENV.fetch('AWS_SECRET_ACCESS_KEY', '') %>
  region: <%= ENV.fetch('AWS_REGION', '') %>
  bucket: <%= ENV.fetch('S3_BUCKET_NAME', '') %>

# Remember not to checkin your GCS keyfile to a repository
google:
  service: GCS
  project: <%= ENV.fetch('GCS_PROJECT', '') %>
  credentials: <%= ENV.fetch('GCS_CREDENTIALS', '') %>
  bucket: <%= ENV.fetch('GCS_BUCKET', '') %>

# Use rails credentials:edit to set the Azure Storage secret (as azure_storage:storage_access_key)
microsoft:
  service: AzureStorage
  storage_account_name: <%= ENV.fetch('AZURE_STORAGE_ACCOUNT_NAME', '') %>
  storage_access_key: <%= ENV.fetch('AZURE_STORAGE_ACCESS_KEY', '') %>
  container: <%= ENV.fetch('AZURE_STORAGE_CONTAINER', '') %>

# s3 compatible service such as DigitalOcean Spaces, Minio.
s3_compatible:
  service: S3
  access_key_id: <%= ENV.fetch('STORAGE_ACCESS_KEY_ID', '') %>
  secret_access_key: <%= ENV.fetch('STORAGE_SECRET_ACCESS_KEY', '') %>
  region: <%= ENV.fetch('STORAGE_REGION', '') %>
  bucket: <%= ENV.fetch('STORAGE_BUCKET_NAME', '') %>
  endpoint: <%= ENV.fetch('STORAGE_ENDPOINT', '') %>
  force_path_style: <%= ENV.fetch('STORAGE_FORCE_PATH_STYLE', false) %>

# mirror:
#   service: Mirror
#   primary: local
#   mirrors: [ amazon, google, microsoft ]
