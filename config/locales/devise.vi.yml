#Additional translations at https://github.com/plataformatec/devise/wiki/I18n
vi:
  devise:
    confirmations:
      confirmed: "Email của bạn đã được xác nhận thành công."
      send_instructions: "Bạn sẽ nhận được một email có hướng dẫn về cách xác nhận địa chỉ email của bạn sau vài phút."
      send_paranoid_instructions: "Nếu địa chỉ email của bạn tồn tại trong cơ sở dữ liệu của chúng tôi, bạn sẽ nhận được một email có hướng dẫn cách xác nhận địa chỉ email của bạn trong vài phút."
    failure:
      already_authenticated: "Bạn đã đăng nhập."
      inactive: "Tài khoản của bạn chưa được kích hoạt."
      invalid: "%{authentication_keys}/mật khẩu không hợp lệ hoặc tài khoản chưa được xác thực."
      locked: "Tài khoản của bạn đã bị khoá."
      last_attempt: "Bạn còn một lần thử nữa trước khi tài khoản của bạn bị khóa."
      not_found_in_database: "%{authentication_keys} hoặc mật khẩu không hợp lệ."
      timeout: "Phiên của bạn đã hết hạn. Vui lòng đăng nhập lại để tiếp tục."
      unauthenticated: "Bạn cần đăng nhập hoặc đăng ký trước khi tiếp tục."
      unconfirmed: "Bạn phải xác nhận địa chỉ email của mình trước khi tiếp tục."
    mailer:
      confirmation_instructions:
        subject: "Hướng Dẫn Xác Nhận"
      reset_password_instructions:
        subject: "Hướng dẫn thay đổi mật khẩu"
      unlock_instructions:
        subject: "Hướng dẫn mở khoá"
      password_change:
        subject: "Mật khẩu đã được thay đổi"
    omniauth_callbacks:
      failure: "Không thể xác thực bạn từ %{kind} vì \"%{reason}\"."
      success: "Xác thực thành công từ %{kind} tài khoản."
    passwords:
      no_token: "Bạn không thể truy cập trang này nếu không nhận được email đặt lại mật khẩu. Nếu bạn đến từ một email đặt lại mật khẩu, hãy đảm bảo rằng bạn đã sử dụng URL đầy đủ được cung cấp."
      send_instructions: "Bạn sẽ nhận được email hướng dẫn cách đặt lại mật khẩu sau vài phút."
      send_paranoid_instructions: "Nếu địa chỉ email của bạn tồn tại trong cơ sở dữ liệu của chúng tôi, bạn sẽ nhận được liên kết khôi phục mật khẩu tại địa chỉ email của mình sau vài phút."
      updated: "Mật khẩu của bạn đã được thay đổi thành công. Bây giờ bạn đã đăng nhập."
      updated_not_active: "Mật khẩu của bạn đã được thay đổi thành công."
    registrations:
      destroyed: "Tạm biệt! Tài khoản của bạn đã được hủy thành công. Mong rằng chúng tôi sẽ sớm gặp lại bạn."
      signed_up: "Chào mừng! Bạn đã đăng ký thành công."
      signed_up_but_inactive: "Bạn đã đăng ký thành công. Tuy nhiên, chúng tôi không thể đăng nhập cho bạn vì tài khoản của bạn chưa được kích hoạt."
      signed_up_but_locked: "Bạn đã đăng ký thành công. Tuy nhiên, chúng tôi không thể đăng nhập cho bạn vì tài khoản của bạn bị khóa."
      signed_up_but_unconfirmed: "Một tin nhắn với một liên kết xác nhận đã được gửi đến địa chỉ email của bạn. Vui lòng nhấp vào liên kết để kích hoạt tài khoản của bạn."
      update_needs_confirmation: "Bạn đã cập nhật tài khoản của mình thành công, nhưng chúng tôi cần xác minh địa chỉ email mới của bạn. Vui lòng kiểm tra email của bạn và nhấp vào liên kết xác nhận để xác nhận địa chỉ email mới của bạn."
      updated: "Tài khoản của bạn đã được cập nhật thành công."
    sessions:
      signed_in: "Đã đăng nhập thành công."
      signed_out: "Đã đăng xuất thành công."
      already_signed_out: "Đã đăng xuất thành công."
    unlocks:
      send_instructions: "Bạn sẽ nhận được một email có hướng dẫn về cách mở khóa tài khoản của mình sau vài phút."
      send_paranoid_instructions: "Nếu tài khoản của bạn tồn tại, bạn sẽ nhận được email hướng dẫn cách mở khóa tài khoản sau vài phút."
      unlocked: "Tài khoản của bạn đã được mở khóa thành công. Vui lòng đăng nhập để tiếp tục."
  errors:
    messages:
      already_confirmed: "đã được xác nhận, vui lòng thử đăng nhập"
      confirmation_period_expired: "cần được xác nhận trong %{period}, vui lòng yêu cầu một cái mới"
      expired: "đã hết hạn, vui lòng yêu cầu một cái mới"
      not_found: "không tìm thấy"
      not_locked: "không được khoá"
      not_saved:
        other: "Có %{count} lỗi được tìm thấy từ %{resource}:"
