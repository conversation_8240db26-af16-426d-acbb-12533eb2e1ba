#Files in the config/locales directory are used for internationalization
#and are automatically loaded by Rails. If you want to use locales other
#than English, add the necessary files in this directory.
#To use the locales, use `I18n.t`:
#I18n.t 'hello'
#In views, this is aliased to just `t`:
#<%= t('hello') %>
#To use a different locale, set it with `I18n.locale`:
#I18n.locale = :es
#This would use the information in config/locales/es.yml.
#The following keys must be escaped otherwise they will not be retrieved by
#the default I18n backend:
#true, false, on, off, yes, no
#Instead, surround them with single quotes.
#en:
#'true': 'foo'
#To learn more, please read the Rails Internationalization guide
#available at https://guides.rubyonrails.org/i18n.html.
id:
  hello: 'Hello world'
  messages:
    reset_password_success: Woot! Permintaan pengaturan ulang kata sandi berhasil. Periksa email Anda untuk mendapatkan petunjuk.
    reset_password_failure: Aduh! Kami tidak dapat menemukan pengguna dengan email yang dimasukkan.
    inbox_deletetion_response: Permintaan penghapusan kotak masuk Anda akan diproses dalam beberapa waktu.
  errors:
    validations:
      presence: tidak boleh kosong
    webhook:
      invalid: Acara tidak valid
    signup:
      disposable_email: Kami tidak mengizinkan email sekali pakai
      blocked_domain: This domain is not allowed. If you believe this is a mistake, please contact support.
      invalid_email: Anda telah memasukkan email yang tidak valid
      email_already_exists: 'Anda telah mendaftar untuk sebuah akun dengan %{email}'
      invalid_params: 'Invalid, please check the signup paramters and try again'
      failed: Pendaftaran gagal
    data_import:
      data_type:
        invalid: Jenis data tidak valid
    contacts:
      import:
        failed: File kosong
      export:
        success: Kami akan memberi tahu Anda setelah file ekspor kontak siap dilihat.
      email:
        invalid: Email tidak valid
      phone_number:
        invalid: harus dalam format e164
    categories:
      locale:
        unique: harus unik dalam kategori dan portal
    dyte:
      invalid_message_type: 'Jenis pesan tidak valid. Tindakan tidak diizinkan'
    slack:
      invalid_channel_id: 'Invalid slack channel. Please try again'
    inboxes:
      imap:
        socket_error: Periksa sambungan jaringan, alamat IMAP, dan coba lagi.
        no_response_error: Periksa kembali kredensial IMAP dan coba lagi.
        host_unreachable_error: Host tidak dapat dijangkau, Harap periksa alamat IMAP, port IMAP, dan coba lagi.
        connection_timed_out_error: Waktu koneksi habis untuk %{address}:%{port}
        connection_closed_error: Koneksi terputus.
      validations:
        name: tidak boleh dimulai atau diakhiri dengan simbol, dan tidak boleh memiliki karakter < > / \ @.
    custom_filters:
      number_of_records: Batas tercapai. Jumlah maksimum filter ubahsuaian yang diizinkan untuk satu pengguna per akun adalah 50.
      invalid_attribute: Invalid attribute key - [%{key}]. The key should be one of [%{allowed_keys}] or a custom attribute defined in the account.
      invalid_operator: Invalid operator. The allowed operators for %{attribute_name} are [%{allowed_keys}].
      invalid_query_operator: Query operator must be either "AND" or "OR".
      invalid_value: Invalid value. The values provided for %{attribute_name} are invalid
    custom_attribute_definition:
      key_conflict: The provided key is not allowed as it might conflict with default attributes.
  reports:
    period: Periode pelaporan %{since} hingga %{until}
    utc_warning: Laporan yang dihasilkan berada dalam zona waktu UTC
    agent_csv:
      agent_name: Nama agen
      conversations_count: Assigned conversations
      avg_first_response_time: Avg first response time
      avg_resolution_time: Avg resolution time
      resolution_count: Jumlah Terselesaikan
      avg_customer_waiting_time: Avg customer waiting time
    inbox_csv:
      inbox_name: Nama kotak masuk
      inbox_type: Jenis kotak masuk
      conversations_count: Jumlah percakapan
      avg_first_response_time: Avg first response time
      avg_resolution_time: Avg resolution time
    label_csv:
      label_title: Label
      conversations_count: Jumlah percakapan
      avg_first_response_time: Avg first response time
      avg_resolution_time: Avg resolution time
    team_csv:
      team_name: Nama Tim
      conversations_count: Jumlah percakapan
      avg_first_response_time: Avg first response time
      avg_resolution_time: Avg resolution time
      resolution_count: Jumlah Terselesaikan
      avg_customer_waiting_time: Avg customer waiting time
    conversation_traffic_csv:
      timezone: Zona Waktu
    sla_csv:
      conversation_id: Conversation ID
      sla_policy_breached: SLA Policy
      assignee: Assignee
      team: Tim
      inbox: Kotak masuk
      labels: Label
      conversation_link: Link to the Conversation
      breached_events: Breached Events
    default_group_by: hari
    csat:
      headers:
        contact_name: Nama Kontak
        contact_email_address: Hubungi Alamat Surel
        contact_phone_number: Kontak Nomor Telepon
        link_to_the_conversation: Tautan ke percakapan
        agent_name: Nama Agen
        rating: Peringkat
        feedback: Komentar Umpan Balik
        recorded_at: Tanggal rekaman
  notifications:
    notification_title:
      conversation_creation: 'A conversation (#%{display_id}) has been created in %{inbox_name}'
      conversation_assignment: 'A conversation (#%{display_id}) has been assigned to you'
      assigned_conversation_new_message: 'A new message is created in conversation (#%{display_id})'
      conversation_mention: 'You have been mentioned in conversation (#%{display_id})'
      sla_missed_first_response: 'SLA target first response missed for conversation (#%{display_id})'
      sla_missed_next_response: 'SLA target next response missed for conversation (#%{display_id})'
      sla_missed_resolution: 'SLA target resolution missed for conversation (#%{display_id})'
    attachment: 'Attachment'
    no_content: 'No content'
  conversations:
    captain:
      handoff: 'Transferring to another agent for further assistance.'
    messages:
      instagram_story_content: '%{story_sender} menyebutmu dalam story: '
      instagram_deleted_story_content: Story ini tidak lagi tersedia.
      deleted: Pesan ini telah terhapus
      delivery_status:
        error_code: 'Error code: %{error_code}'
    activity:
      captain:
        resolved: 'Conversation was marked resolved by %{user_name} due to inactivity'
        open: 'Conversation was marked open by %{user_name}'
      status:
        resolved: 'Percakapan ditandai selesai oleh %{user_name}'
        contact_resolved: 'Percakapan diselesaikan oleh %{contact_name}'
        open: 'Percakapan telah dibuka kembali oleh %{user_name}'
        pending: 'Percakapan telah ditandai sebagai tertunda oleh %{user_name}'
        snoozed: 'Percakapan telah ditunda oleh %{user_name}'
        auto_resolved_days: 'Percakapan ditandai terselesaikan oleh sistem karena tidak ada aktifitas dalam %{count} hari'
        auto_resolved_hours: 'Conversation was marked resolved by system due to %{count} hours of inactivity'
        auto_resolved_minutes: 'Conversation was marked resolved by system due to %{count} minutes of inactivity'
        system_auto_open: Sistem membuka kembali percakapan karena pesan masuk baru.
      priority:
        added: '%{user_name} tetapkan prioritas untuk %{new_priority}'
        updated: '%{user_name} mengubah prioritas dari %{old_priority} ke %{new_priority}'
        removed: '%{user_name} menghapus prioritasnya'
      assignee:
        self_assigned: '%{user_name} menetapkan sendiri percakapan ini'
        assigned: 'Ditugaskan ke %{assignee_name} oleh %{user_name}'
        removed: 'Percakapan batal ditetapkan oleh %{user_name}'
      team:
        assigned: 'Ditugaskan ke %{team_name} oleh %{user_name}'
        assigned_with_assignee: 'Ditugaskan ke %{assignee_name} melalui %{team_name} oleh %{user_name}'
        removed: 'Dibebastugaskan dari %{team_name} oleh %{user_name}'
      labels:
        added: '%{user_name} menambahkan %{labels}'
        removed: '%{user_name} menghapus %{labels}'
      sla:
        added: '%{user_name} added SLA policy %{sla_name}'
        removed: '%{user_name} removed SLA policy %{sla_name}'
      muted: '%{user_name} me-mute percakapan'
      unmuted: '%{user_name} telah un-mute percakapan'
      auto_resolution_message: 'Resolving the conversation as it has been inactive for a while. Please start a new conversation if you need further assistance.'
    templates:
      greeting_message_body: '%{account_name} biasanya membalas dalam beberapa jam.'
      ways_to_reach_you_message_body: 'Beri tim cara untuk menghubungi Anda.'
      email_input_box_message_body: 'Dapatkan pemberitahuan melalui email'
      csat_input_message_body: 'Silakan beri peringkat percakapan ini'
    reply:
      email:
        header:
          from_with_name: '%{assignee_name} dari %{inbox_name} <%{from_email}>'
          reply_with_name: '%{assignee_name} dari %{inbox_name} <reply+%{reply_email}>'
          friendly_name: '%{sender_name} dari %{business_name} <%{from_email}>'
          professional_name: '%{business_name} <%{from_email}>'
      channel_email:
        header:
          reply_with_name: '%{assignee_name} dari %{inbox_name} <%{from_email}>'
          reply_with_inbox_name: '%{inbox_name} <%{from_email}>'
      email_subject: 'Pesan baru pada percakapan ini'
      transcript_subject: 'Transkrip Percakapan'
    survey:
      response: 'Silakan beri peringkat percakapan ini, %{link}'
  contacts:
    online:
      delete: '%{contact_name} sedang Online, silakan coba lagi nanti'
  integration_apps:
    dashboard_apps:
      name: 'Aplikasi Dasbor'
      description: 'Dashboard Apps allow you to create and embed applications that display user information, orders, or payment history, providing more context to your customer support agents.'
    dyte:
      name: 'Dyte'
      description: 'Dyte is a product that integrates audio and video functionalities into your application. With this integration, your agents can start video/voice calls with your customers directly from Chatwoot.'
      meeting_name: '%{agent_name} memulai percakapan'
    slack:
      name: 'Slack'
      description: "Integrate Chatwoot with Slack to keep your team in sync. This integration allows you to receive notifications for new conversations and respond to them directly within Slack's interface."
    webhooks:
      name: 'Webhooks'
      description: 'Webhook events provide real-time updates about activities in your Chatwoot account. You can subscribe to your preferred events, and Chatwoot will send you HTTP callbacks with the updates.'
    dialogflow:
      name: 'Dialogflow'
      description: 'Build chatbots with Dialogflow and easily integrate them into your inbox. These bots can handle initial queries before transferring them to a customer service agent.'
    google_translate:
      name: 'Google Terjemahan'
      description: "Integrate Google Translate to help agents easily translate customer messages. This integration automatically detects the language and converts it to the agent's or admin's preferred language."
    openai:
      name: 'OpenAI'
      description: 'Leverage the power of large language models from OpenAI with the features such as reply suggestions, summarization, message rephrasing, spell-checking, and label classification.'
    linear:
      name: 'Linear'
      description: 'Create issues in Linear directly from your conversation window. Alternatively, link existing Linear issues for a more streamlined and efficient issue tracking process.'
    shopify:
      name: 'Shopify'
      description: 'Connect your Shopify store to access order details, customer information, and product data directly within your conversations and helps your support team provide faster, more contextual assistance to your customers.'
    leadsquared:
      name: 'LeadSquared'
      short_description: 'Sync your contacts and conversations with LeadSquared CRM.'
      description: 'Sync your contacts and conversations with LeadSquared CRM. This integration automatically creates leads in LeadSquared when new contacts are added, and logs conversation activity to provide your sales team with complete context.'
  captain:
    copilot_error: 'Please connect an assistant to this inbox to use Copilot'
    copilot_limit: 'You are out of Copilot credits. You can buy more credits from the billing section.'
  public_portal:
    search:
      search_placeholder: Telusuri artikel menurut judul atau isi...
      empty_placeholder: Tidak ada hasil ditemukan.
      loading_placeholder: Sedang mencari...
      results_title: Hasil pencarian
    toc_header: 'Di halaman ini'
    hero:
      sub_title: Cari artikel di sini atau jelajahi kategori di bawah ini.
    common:
      home: Beranda
      last_updated_on: Terakhir diperbarui saat %{last_updated_on}
      view_all_articles: View all
      article: artikel
      articles: artikel
      author: penulis
      authors: authors
      other: other
      others: others
      by: By
      no_articles: Tidak ada artikel di sini
    footer:
      made_with: Dibuat oleh
    header:
      go_to_homepage: Website
      appearance:
        system: Sistem
        light: Light
        dark: Dark
      featured_articles: Featured Articles
      uncategorized: Tanpa Kategori
    404:
      title: Page not found
      description: We couldn't find the page you were looking for.
      back_to_home: Go to home page
  slack_unfurl:
    fields:
      name: Nama
      email: Email
      phone_number: Phone
      company_name: Perusahaan
      inbox_name: Kotak masuk
      inbox_type: Inbox Type
    button: Buka percakapan
  time_units:
    days:
      other: '%{count} days'
    hours:
      other: '%{count} hours'
    minutes:
      other: '%{count} minutes'
    seconds:
      other: '%{count} seconds'
  automation:
    system_name: 'Automation System'
  crm:
    no_message: 'No messages in conversation'
    attachment: '[Attachment: %{type}]'
    no_content: '[No content]'
    created_activity: |
      New conversation started on %{brand_name}

      Channel: %{channel_info}
      Created: %{formatted_creation_time}
      Conversation ID: %{display_id}
      View in %{brand_name}: %{url}
    transcript_activity: |
      Conversation Transcript from %{brand_name}

      Channel: %{channel_info}
      Conversation ID: %{display_id}
      View in %{brand_name}: %{url}

      Transcript:
      %{format_messages}
