#Files in the config/locales directory are used for internationalization
#and are automatically loaded by Rails. If you want to use locales other
#than English, add the necessary files in this directory.
#To use the locales, use `I18n.t`:
#I18n.t 'hello'
#In views, this is aliased to just `t`:
#<%= t('hello') %>
#To use a different locale, set it with `I18n.locale`:
#I18n.locale = :es
#This would use the information in config/locales/es.yml.
#The following keys must be escaped otherwise they will not be retrieved by
#the default I18n backend:
#true, false, on, off, yes, no
#Instead, surround them with single quotes.
#en:
#'true': 'foo'
#To learn more, please read the Rails Internationalization guide
#available at https://guides.rubyonrails.org/i18n.html.
pl:
  hello: 'Witaj świecie'
  messages:
    reset_password_success: Woot! Prośba o zresetowanie hasła zakończona pomyślnie. Sprawdź swoją pocztę, aby u<PERSON><PERSON> instrukcje.
    reset_password_failure: Ups! Nie mogliśmy znaleźć żadnego użytkownika z podanym adresem e-mail.
    inbox_deletetion_response: Żądanie usunięcia skrzynki odbiorczej zostanie rozpatrzone za jakiś czas.
  errors:
    validations:
      presence: nie może być puste
    webhook:
      invalid: Nieprawidłowe zdarzenia
    signup:
      disposable_email: Nie zezwalamy na jednorazowe konta e-mail
      blocked_domain: This domain is not allowed. If you believe this is a mistake, please contact support.
      invalid_email: Wprowadzono nieprawidłowy adres e-mail
      email_already_exists: 'Już zarejestrowałeś się na konto %{email}'
      invalid_params: 'Invalid, please check the signup paramters and try again'
      failed: Rejestracja nie powiodła się
    data_import:
      data_type:
        invalid: Nieprawidłowy typ danych
    contacts:
      import:
        failed: Plik jest pusty
      export:
        success: Powiadomimy Cię, gdy plik eksportu kontaktów będzie gotowy do wyświetlenia.
      email:
        invalid: Nieprawidłowy adres e-mail
      phone_number:
        invalid: powinno być w formacie e164
    categories:
      locale:
        unique: powinno być unikalne w kategorii i portalu
    dyte:
      invalid_message_type: 'Nieprawidłowy typ wiadomości. Niedozwolone działanie.'
    slack:
      invalid_channel_id: 'Invalid slack channel. Please try again'
    inboxes:
      imap:
        socket_error: Sprawdź połączenie sieciowe, adres IMAP i spróbuj ponownie.
        no_response_error: Sprawdź poświadczenia IMAP i spróbuj ponownie.
        host_unreachable_error: Host nieosiągalny, sprawdź adres IMAP, port IMAP i spróbuj ponownie.
        connection_timed_out_error: Limit czasu połączenia dla %{address}:%{port}
        connection_closed_error: Połączenie zakończone.
      validations:
        name: nie powinno zaczynać się ani kończyć symbolami i nie powinno zawierać znaków < > / \ @.
    custom_filters:
      number_of_records: Osiągnięto limit. Maksymalna liczba dozwolonych filtrów niestandardowych dla użytkownika na konto wynosi 50.
      invalid_attribute: Invalid attribute key - [%{key}]. The key should be one of [%{allowed_keys}] or a custom attribute defined in the account.
      invalid_operator: Invalid operator. The allowed operators for %{attribute_name} are [%{allowed_keys}].
      invalid_query_operator: Query operator must be either "AND" or "OR".
      invalid_value: Invalid value. The values provided for %{attribute_name} are invalid
    custom_attribute_definition:
      key_conflict: The provided key is not allowed as it might conflict with default attributes.
  reports:
    period: Okres raportowania od %{since} do %{until}
    utc_warning: Generowany raport jest w strefie czasowej UTC
    agent_csv:
      agent_name: Nazwa agenta
      conversations_count: Assigned conversations
      avg_first_response_time: Avg first response time
      avg_resolution_time: Avg resolution time
      resolution_count: Liczba rozwiązań
      avg_customer_waiting_time: Avg customer waiting time
    inbox_csv:
      inbox_name: Nazwa skrzynki odbiorczej
      inbox_type: Typ skrzynki odbiorczej
      conversations_count: Ilość rozmów
      avg_first_response_time: Avg first response time
      avg_resolution_time: Avg resolution time
    label_csv:
      label_title: Etykieta
      conversations_count: Ilość rozmów
      avg_first_response_time: Avg first response time
      avg_resolution_time: Avg resolution time
    team_csv:
      team_name: Nazwa zespołu
      conversations_count: Liczba rozmów
      avg_first_response_time: Avg first response time
      avg_resolution_time: Avg resolution time
      resolution_count: Liczba rozwiązań
      avg_customer_waiting_time: Avg customer waiting time
    conversation_traffic_csv:
      timezone: Strefa czasowa
    sla_csv:
      conversation_id: Conversation ID
      sla_policy_breached: SLA Policy
      assignee: Assignee
      team: Zespół
      inbox: Skrzynka odbiorcza
      labels: Etykiety
      conversation_link: Link to the Conversation
      breached_events: Breached Events
    default_group_by: dnia
    csat:
      headers:
        contact_name: Nazwa kontaktu
        contact_email_address: Adres email kontaktu
        contact_phone_number: Numer telefonu kontaktu
        link_to_the_conversation: Link do rozmowy
        agent_name: Nazwa agenta
        rating: Ocena
        feedback: Komentarz zwrotny
        recorded_at: Data nagrania
  notifications:
    notification_title:
      conversation_creation: 'A conversation (#%{display_id}) has been created in %{inbox_name}'
      conversation_assignment: 'A conversation (#%{display_id}) has been assigned to you'
      assigned_conversation_new_message: 'A new message is created in conversation (#%{display_id})'
      conversation_mention: 'You have been mentioned in conversation (#%{display_id})'
      sla_missed_first_response: 'SLA target first response missed for conversation (#%{display_id})'
      sla_missed_next_response: 'SLA target next response missed for conversation (#%{display_id})'
      sla_missed_resolution: 'SLA target resolution missed for conversation (#%{display_id})'
    attachment: 'Attachment'
    no_content: 'No content'
  conversations:
    captain:
      handoff: 'Transferring to another agent for further assistance.'
    messages:
      instagram_story_content: '%{story_sender} wspomniał o Tobie w historii: '
      instagram_deleted_story_content: Ta historia już nie jest dostępna.
      deleted: Ta wiadomość została usunięta
      delivery_status:
        error_code: 'Error code: %{error_code}'
    activity:
      captain:
        resolved: 'Conversation was marked resolved by %{user_name} due to inactivity'
        open: 'Conversation was marked open by %{user_name}'
      status:
        resolved: 'Rozmowa została oznaczona przez %{user_name}'
        contact_resolved: 'Rozmowa została rozwiązana przez %{contact_name}'
        open: 'Rozmowa została ponownie otwarta przez %{user_name}'
        pending: 'Rozmowa została oznaczona jako oczekująca przez %{user_name}'
        snoozed: 'Rozmowa została uśpiona przez %{user_name}'
        auto_resolved_days: 'Rozmowa została oznaczona przez system jako rozwiązana z powodu %{count} dni bezczynności'
        auto_resolved_hours: 'Conversation was marked resolved by system due to %{count} hours of inactivity'
        auto_resolved_minutes: 'Conversation was marked resolved by system due to %{count} minutes of inactivity'
        system_auto_open: System otworzył ponownie rozmowę z powodu nowej wiadomości przychodzącej.
      priority:
        added: '%{user_name} ustawił priorytet na %{new_priority}'
        updated: '%{user_name} zmienił priorytet z %{old_priority} na %{new_priority}'
        removed: '%{user_name} usunął priorytet'
      assignee:
        self_assigned: '%{user_name} przypisał się do tej rozmowy'
        assigned: 'Przypisane do %{assignee_name} przez %{user_name}'
        removed: 'Rozmowa nieprzypisana przez %{user_name}'
      team:
        assigned: 'Przydzielone do %{team_name} przez %{user_name}'
        assigned_with_assignee: 'Przypisano do %{assignee_name} przez %{team_name} przez %{user_name}'
        removed: 'Usunięto przydzielenie do %{team_name} przez %{user_name}'
      labels:
        added: '%{user_name} dodał/a %{labels}'
        removed: '%{user_name} usunął/a %{labels}'
      sla:
        added: '%{user_name} added SLA policy %{sla_name}'
        removed: '%{user_name} removed SLA policy %{sla_name}'
      muted: '%{user_name} wyciszył/a rozmowę'
      unmuted: '%{user_name} cofnął wyciszenie rozmowy'
      auto_resolution_message: 'Resolving the conversation as it has been inactive for a while. Please start a new conversation if you need further assistance.'
    templates:
      greeting_message_body: '%{account_name} zazwyczaj odpowiada w ciągu kilku godzin.'
      ways_to_reach_you_message_body: 'Daj zespołowi możliwość dotarcia do Ciebie.'
      email_input_box_message_body: 'Otrzymuj powiadomienia przez e-mail'
      csat_input_message_body: 'Oceń rozmowę'
    reply:
      email:
        header:
          from_with_name: '%{assignee_name} z %{inbox_name} <%{from_email}>'
          reply_with_name: '%{assignee_name} z %{inbox_name} <reply+%{reply_email}>'
          friendly_name: '%{sender_name} z %{business_name} <%{from_email}>'
          professional_name: '%{business_name} <%{from_email}>'
      channel_email:
        header:
          reply_with_name: '%{assignee_name} z %{inbox_name} <%{from_email}>'
          reply_with_inbox_name: '%{inbox_name} <%{from_email}>'
      email_subject: 'Nowe wiadomości w tej dyskusji'
      transcript_subject: 'Transkrypcja rozmowy'
    survey:
      response: 'Oceń tę rozmowę, %{link}'
  contacts:
    online:
      delete: '%{contact_name} jest online, spróbuj ponownie później'
  integration_apps:
    dashboard_apps:
      name: 'Aplikacje na pulpicie'
      description: 'Dashboard Apps allow you to create and embed applications that display user information, orders, or payment history, providing more context to your customer support agents.'
    dyte:
      name: 'Dyte'
      description: 'Dyte is a product that integrates audio and video functionalities into your application. With this integration, your agents can start video/voice calls with your customers directly from Chatwoot.'
      meeting_name: '%{agent_name} rozpoczął spotkanie'
    slack:
      name: 'Slack'
      description: "Integrate Chatwoot with Slack to keep your team in sync. This integration allows you to receive notifications for new conversations and respond to them directly within Slack's interface."
    webhooks:
      name: 'Webhooki'
      description: 'Webhook events provide real-time updates about activities in your Chatwoot account. You can subscribe to your preferred events, and Chatwoot will send you HTTP callbacks with the updates.'
    dialogflow:
      name: 'Dialogflow'
      description: 'Build chatbots with Dialogflow and easily integrate them into your inbox. These bots can handle initial queries before transferring them to a customer service agent.'
    google_translate:
      name: 'Tłumacz Google'
      description: "Integrate Google Translate to help agents easily translate customer messages. This integration automatically detects the language and converts it to the agent's or admin's preferred language."
    openai:
      name: 'OpenAI'
      description: 'Leverage the power of large language models from OpenAI with the features such as reply suggestions, summarization, message rephrasing, spell-checking, and label classification.'
    linear:
      name: 'Linear'
      description: 'Create issues in Linear directly from your conversation window. Alternatively, link existing Linear issues for a more streamlined and efficient issue tracking process.'
    shopify:
      name: 'Shopify'
      description: 'Connect your Shopify store to access order details, customer information, and product data directly within your conversations and helps your support team provide faster, more contextual assistance to your customers.'
    leadsquared:
      name: 'LeadSquared'
      short_description: 'Sync your contacts and conversations with LeadSquared CRM.'
      description: 'Sync your contacts and conversations with LeadSquared CRM. This integration automatically creates leads in LeadSquared when new contacts are added, and logs conversation activity to provide your sales team with complete context.'
  captain:
    copilot_error: 'Please connect an assistant to this inbox to use Copilot'
    copilot_limit: 'You are out of Copilot credits. You can buy more credits from the billing section.'
  public_portal:
    search:
      search_placeholder: Wyszukaj artykuł według tytułu lub treści...
      empty_placeholder: Brak wyników.
      loading_placeholder: Wyszukiwanie...
      results_title: Wyniki wyszukiwania
    toc_header: 'Na tej stronie'
    hero:
      sub_title: Szukaj artykułów tutaj lub przeglądaj kategorie poniżej.
    common:
      home: Strona główna
      last_updated_on: Ostatnia aktualizacja %{last_updated_on}
      view_all_articles: View all
      article: artykuł
      articles: artykuły
      author: autor
      authors: authors
      other: other
      others: others
      by: By
      no_articles: Nie ma tu żadnych artykułów
    footer:
      made_with: Wykonane z
    header:
      go_to_homepage: Website
      appearance:
        system: System
        light: Light
        dark: Dark
      featured_articles: Featured Articles
      uncategorized: Bez kategorii
    404:
      title: Page not found
      description: We couldn't find the page you were looking for.
      back_to_home: Go to home page
  slack_unfurl:
    fields:
      name: Imię
      email: E-mail
      phone_number: Phone
      company_name: Firma
      inbox_name: Skrzynka odbiorcza
      inbox_type: Inbox Type
    button: Otwórz rozmowę
  time_units:
    days:
      one: '%{count} day'
      few: '%{count} days'
      many: '%{count} days'
      other: '%{count} days'
    hours:
      one: '%{count} hour'
      few: '%{count} hours'
      many: '%{count} hours'
      other: '%{count} hours'
    minutes:
      one: '%{count} minute'
      few: '%{count} minutes'
      many: '%{count} minutes'
      other: '%{count} minutes'
    seconds:
      one: '%{count} second'
      few: '%{count} seconds'
      many: '%{count} seconds'
      other: '%{count} seconds'
  automation:
    system_name: 'Automation System'
  crm:
    no_message: 'No messages in conversation'
    attachment: '[Attachment: %{type}]'
    no_content: '[No content]'
    created_activity: |
      New conversation started on %{brand_name}

      Channel: %{channel_info}
      Created: %{formatted_creation_time}
      Conversation ID: %{display_id}
      View in %{brand_name}: %{url}
    transcript_activity: |
      Conversation Transcript from %{brand_name}

      Channel: %{channel_info}
      Conversation ID: %{display_id}
      View in %{brand_name}: %{url}

      Transcript:
      %{format_messages}
