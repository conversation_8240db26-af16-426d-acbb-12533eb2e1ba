#Files in the config/locales directory are used for internationalization
#and are automatically loaded by Rails. If you want to use locales other
#than English, add the necessary files in this directory.
#To use the locales, use `I18n.t`:
#I18n.t 'hello'
#In views, this is aliased to just `t`:
#<%= t('hello') %>
#To use a different locale, set it with `I18n.locale`:
#I18n.locale = :es
#This would use the information in config/locales/es.yml.
#The following keys must be escaped otherwise they will not be retrieved by
#the default I18n backend:
#true, false, on, off, yes, no
#Instead, surround them with single quotes.
#en:
#'true': 'foo'
#To learn more, please read the Rails Internationalization guide
#available at https://guides.rubyonrails.org/i18n.html.
he:
  hello: 'שלום עולם'
  messages:
    reset_password_success: יאס! בקשה לאיפוס ססמה נשלחה בהצלחה. בדוק תיבת מייל להוראות.
    reset_password_failure: אופס! לא מצאנו משתמש עם המייל שצוין.
    inbox_deletetion_response: Your inbox deletion request will be processed in some time.
  errors:
    validations:
      presence: must not be blank
    webhook:
      invalid: Invalid events
    signup:
      disposable_email: אנחנו לא מאפשרים מיילים חד פעמיים
      blocked_domain: This domain is not allowed. If you believe this is a mistake, please contact support.
      invalid_email: הכנסת מייל לא תקין
      email_already_exists: 'כבר נרשמת לחשבון עם %{email}'
      invalid_params: 'Invalid, please check the signup paramters and try again'
      failed: הרשמה נכשלה
    data_import:
      data_type:
        invalid: Invalid data type
    contacts:
      import:
        failed: File is blank
      export:
        success: We will notify you once contacts export file is ready to view.
      email:
        invalid: Invalid email
      phone_number:
        invalid: should be in e164 format
    categories:
      locale:
        unique: should be unique in the category and portal
    dyte:
      invalid_message_type: 'סוג הודעה לא חוקי. פעולה אסורה'
    slack:
      invalid_channel_id: 'Invalid slack channel. Please try again'
    inboxes:
      imap:
        socket_error: Please check the network connection, IMAP address and try again.
        no_response_error: Please check the IMAP credentials and try again.
        host_unreachable_error: Host unreachable, Please check the IMAP address, IMAP port and try again.
        connection_timed_out_error: Connection timed out for %{address}:%{port}
        connection_closed_error: Connection closed.
      validations:
        name: should not start or end with symbols, and it should not have < > / \ @ characters.
    custom_filters:
      number_of_records: Limit reached. The maximum number of allowed custom filters for a user per account is 50.
      invalid_attribute: Invalid attribute key - [%{key}]. The key should be one of [%{allowed_keys}] or a custom attribute defined in the account.
      invalid_operator: Invalid operator. The allowed operators for %{attribute_name} are [%{allowed_keys}].
      invalid_query_operator: Query operator must be either "AND" or "OR".
      invalid_value: Invalid value. The values provided for %{attribute_name} are invalid
    custom_attribute_definition:
      key_conflict: The provided key is not allowed as it might conflict with default attributes.
  reports:
    period: Reporting period %{since} to %{until}
    utc_warning: The report generated is in UTC timezone
    agent_csv:
      agent_name: Agent name
      conversations_count: Assigned conversations
      avg_first_response_time: Avg first response time
      avg_resolution_time: Avg resolution time
      resolution_count: ספירת רזולוציות
      avg_customer_waiting_time: Avg customer waiting time
    inbox_csv:
      inbox_name: Inbox name
      inbox_type: Inbox type
      conversations_count: No. of conversations
      avg_first_response_time: Avg first response time
      avg_resolution_time: Avg resolution time
    label_csv:
      label_title: תווית
      conversations_count: No. of conversations
      avg_first_response_time: Avg first response time
      avg_resolution_time: Avg resolution time
    team_csv:
      team_name: שם קבוצה
      conversations_count: Conversations count
      avg_first_response_time: Avg first response time
      avg_resolution_time: Avg resolution time
      resolution_count: ספירת רזולוציות
      avg_customer_waiting_time: Avg customer waiting time
    conversation_traffic_csv:
      timezone: Timezone
    sla_csv:
      conversation_id: Conversation ID
      sla_policy_breached: SLA Policy
      assignee: Assignee
      team: צוות
      inbox: תיבת הדואר הנכנס
      labels: תוויות
      conversation_link: Link to the Conversation
      breached_events: Breached Events
    default_group_by: day
    csat:
      headers:
        contact_name: Contact Name
        contact_email_address: Contact Email Address
        contact_phone_number: Contact Phone Number
        link_to_the_conversation: Link to the conversation
        agent_name: שם סוכן
        rating: דירוג
        feedback: Feedback Comment
        recorded_at: Recorded date
  notifications:
    notification_title:
      conversation_creation: 'A conversation (#%{display_id}) has been created in %{inbox_name}'
      conversation_assignment: 'A conversation (#%{display_id}) has been assigned to you'
      assigned_conversation_new_message: 'הודעה חדשה נוצרה בשיחה (#%{display_id})'
      conversation_mention: 'You have been mentioned in conversation (#%{display_id})'
      sla_missed_first_response: 'SLA target first response missed for conversation (#%{display_id})'
      sla_missed_next_response: 'SLA target next response missed for conversation (#%{display_id})'
      sla_missed_resolution: 'SLA target resolution missed for conversation (#%{display_id})'
    attachment: 'Attachment'
    no_content: 'No content'
  conversations:
    captain:
      handoff: 'Transferring to another agent for further assistance.'
    messages:
      instagram_story_content: '%{story_sender} mentioned you in the story: '
      instagram_deleted_story_content: This story is no longer available.
      deleted: This message was deleted
      delivery_status:
        error_code: 'Error code: %{error_code}'
    activity:
      captain:
        resolved: 'Conversation was marked resolved by %{user_name} due to inactivity'
        open: 'Conversation was marked open by %{user_name}'
      status:
        resolved: 'השיחה סומנה כפתורה על ידי %{user_name}'
        contact_resolved: 'השיחה נפתרה על ידי %{contact_name}'
        open: 'השיחה נפתחה מחדש על ידי %{user_name}'
        pending: 'השיחה סומנה כממתינה על ידי %{user_name}'
        snoozed: 'השיחה הושהה על ידי %{user_name}'
        auto_resolved_days: 'השיחה סומנה כפתורה על ידי המערכת עקב חוסר פעילות של %{count} ימים'
        auto_resolved_hours: 'Conversation was marked resolved by system due to %{count} hours of inactivity'
        auto_resolved_minutes: 'Conversation was marked resolved by system due to %{count} minutes of inactivity'
        system_auto_open: System reopened the conversation due to a new incoming message.
      priority:
        added: '%{user_name} set the priority to %{new_priority}'
        updated: '%{user_name} changed the priority from %{old_priority} to %{new_priority}'
        removed: '%{user_name} removed the priority'
      assignee:
        self_assigned: '%{user_name} self-assigned this conversation'
        assigned: 'הוקצה ל-%{assignee_name} על ידי %{user_name}'
        removed: 'ביטול הקצאה של שיחה על ידי %{user_name}'
      team:
        assigned: 'הוקצה ל-%{team_name} על ידי %{user_name}'
        assigned_with_assignee: 'הוקצה ל-%{assignee_name} דרך %{team_name} על ידי %{user_name}'
        removed: 'לא הוקצה מ-%{team_name} על ידי %{user_name}'
      labels:
        added: '%{user_name} added %{labels}'
        removed: '%{user_name} removed %{labels}'
      sla:
        added: '%{user_name} added SLA policy %{sla_name}'
        removed: '%{user_name} removed SLA policy %{sla_name}'
      muted: '%{user_name} has muted the conversation'
      unmuted: '%{user_name} has unmuted the conversation'
      auto_resolution_message: 'Resolving the conversation as it has been inactive for a while. Please start a new conversation if you need further assistance.'
    templates:
      greeting_message_body: '%{account_name} typically replies in a few hours.'
      ways_to_reach_you_message_body: '"תן לצוות דרך להגיע אליך".'
      email_input_box_message_body: 'קבל הודעה במייל'
      csat_input_message_body: 'אנא דרג את השיחה'
    reply:
      email:
        header:
          from_with_name: '%{assignee_name} from %{inbox_name} <%{from_email}>'
          reply_with_name: '%{assignee_name} from %{inbox_name} <reply+%{reply_email}>'
          friendly_name: '%{sender_name} from %{business_name} <%{from_email}>'
          professional_name: '%{business_name} <%{from_email}>'
      channel_email:
        header:
          reply_with_name: '%{assignee_name} from %{inbox_name} <%{from_email}>'
          reply_with_inbox_name: '%{inbox_name} <%{from_email}>'
      email_subject: 'הודעות חדשות בשיחה זו'
      transcript_subject: 'תמלול שיחה'
    survey:
      response: 'אנא דרג שיחה זו, %{link}'
  contacts:
    online:
      delete: '%{contact_name} is Online, please try again later'
  integration_apps:
    dashboard_apps:
      name: 'אפליקציות לוח מחוונים'
      description: 'Dashboard Apps allow you to create and embed applications that display user information, orders, or payment history, providing more context to your customer support agents.'
    dyte:
      name: 'דיטה'
      description: 'Dyte is a product that integrates audio and video functionalities into your application. With this integration, your agents can start video/voice calls with your customers directly from Chatwoot.'
      meeting_name: '%{agent_name} has started a meeting'
    slack:
      name: 'רָפוּי'
      description: "Integrate Chatwoot with Slack to keep your team in sync. This integration allows you to receive notifications for new conversations and respond to them directly within Slack's interface."
    webhooks:
      name: 'Webhooks'
      description: 'Webhook events provide real-time updates about activities in your Chatwoot account. You can subscribe to your preferred events, and Chatwoot will send you HTTP callbacks with the updates.'
    dialogflow:
      name: 'זרימת דיאלוג'
      description: 'Build chatbots with Dialogflow and easily integrate them into your inbox. These bots can handle initial queries before transferring them to a customer service agent.'
    google_translate:
      name: 'גוגל תרגום'
      description: "Integrate Google Translate to help agents easily translate customer messages. This integration automatically detects the language and converts it to the agent's or admin's preferred language."
    openai:
      name: 'OpenAI'
      description: 'Leverage the power of large language models from OpenAI with the features such as reply suggestions, summarization, message rephrasing, spell-checking, and label classification.'
    linear:
      name: 'Linear'
      description: 'Create issues in Linear directly from your conversation window. Alternatively, link existing Linear issues for a more streamlined and efficient issue tracking process.'
    shopify:
      name: 'Shopify'
      description: 'Connect your Shopify store to access order details, customer information, and product data directly within your conversations and helps your support team provide faster, more contextual assistance to your customers.'
    leadsquared:
      name: 'LeadSquared'
      short_description: 'Sync your contacts and conversations with LeadSquared CRM.'
      description: 'Sync your contacts and conversations with LeadSquared CRM. This integration automatically creates leads in LeadSquared when new contacts are added, and logs conversation activity to provide your sales team with complete context.'
  captain:
    copilot_error: 'Please connect an assistant to this inbox to use Copilot'
    copilot_limit: 'You are out of Copilot credits. You can buy more credits from the billing section.'
  public_portal:
    search:
      search_placeholder: Search for article by title or body...
      empty_placeholder: לא נמצאו תוצאות.
      loading_placeholder: Searching...
      results_title: Search results
    toc_header: 'On this page'
    hero:
      sub_title: Search for the articles here or browse the categories below.
    common:
      home: בית
      last_updated_on: Last updated on %{last_updated_on}
      view_all_articles: View all
      article: article
      articles: מאמרים
      author: מחבר
      authors: authors
      other: other
      others: others
      by: By
      no_articles: There are no articles here
    footer:
      made_with: Made with
    header:
      go_to_homepage: Website
      appearance:
        system: System
        light: Light
        dark: Dark
      featured_articles: Featured Articles
      uncategorized: Uncategorized
    404:
      title: Page not found
      description: We couldn't find the page you were looking for.
      back_to_home: Go to home page
  slack_unfurl:
    fields:
      name: שם
      email: אימייל
      phone_number: Phone
      company_name: חברה
      inbox_name: תיבת הדואר הנכנס
      inbox_type: Inbox Type
    button: שיחה פתוחה
  time_units:
    days:
      one: '%{count} day'
      two: '%{count} days'
      many: '%{count} days'
      other: '%{count} days'
    hours:
      one: '%{count} hour'
      two: '%{count} hours'
      many: '%{count} hours'
      other: '%{count} hours'
    minutes:
      one: '%{count} minute'
      two: '%{count} minutes'
      many: '%{count} minutes'
      other: '%{count} minutes'
    seconds:
      one: '%{count} second'
      two: '%{count} seconds'
      many: '%{count} seconds'
      other: '%{count} seconds'
  automation:
    system_name: 'Automation System'
  crm:
    no_message: 'No messages in conversation'
    attachment: '[Attachment: %{type}]'
    no_content: '[No content]'
    created_activity: |
      New conversation started on %{brand_name}

      Channel: %{channel_info}
      Created: %{formatted_creation_time}
      Conversation ID: %{display_id}
      View in %{brand_name}: %{url}
    transcript_activity: |
      Conversation Transcript from %{brand_name}

      Channel: %{channel_info}
      Conversation ID: %{display_id}
      View in %{brand_name}: %{url}

      Transcript:
      %{format_messages}
