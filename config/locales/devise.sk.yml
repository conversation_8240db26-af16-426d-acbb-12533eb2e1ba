#Additional translations at https://github.com/plataformatec/devise/wiki/I18n
sk:
  devise:
    confirmations:
      confirmed: "<PERSON>a<PERSON> e-mailová adresa bola úspešne potvrdená."
      send_instructions: "O niekoľko minút dostanete e-mail s pokynmi, ako potvrdiť svoju e-mailovú adresu."
      send_paranoid_instructions: "Ak vaša e-mailová adresa existuje v našej databáze, do niekoľkých minút dostanete e-mail s pokynmi, ako potvrdiť svoju e-mailovú adresu."
    failure:
      already_authenticated: "<PERSON>ž ste prihlásení."
      inactive: "Vaše konto ešte nie je aktivované."
      invalid: "Neplatné %{authentication_keys}/heslo alebo konto ešte nie je overené."
      locked: "Vaše konto je zablokované."
      last_attempt: "Pred zablokovaním konta máte ešte jeden pokus."
      not_found_in_database: "Neplatné %{authentication_keys} alebo heslo."
      timeout: "Vaše sedenie vypršalo. Ak chcete pokrač<PERSON>ť, prihláste sa znova."
      unauthenticated: "Pred pokračovaním sa musíte prihlásiť alebo zaregistrovať."
      unconfirmed: "Pred pokračovaním musíte potvrdiť svoju e-mailovú adresu."
    mailer:
      confirmation_instructions:
        subject: "Pokyny na potvrdenie"
      reset_password_instructions:
        subject: "Pokyny na obnovenie hesla"
      unlock_instructions:
        subject: "Pokyny na odomknutie"
      password_change:
        subject: "Heslo bolo zmenené"
    omniauth_callbacks:
      failure: "Nepodarilo sa vás overiť z %{kind}, pretože \"%{reason}\"."
      success: "Úspešne overené z konta %{kind}."
    passwords:
      no_token: "Na túto stránku sa nedostanete bez toho, aby ste prišli z e-mailovej správy o obnovení hesla. Ak prichádzate z e-mailu s obnovením hesla, uistite sa, že ste použili celú uvedenú adresu URL."
      send_instructions: "Do niekoľkých minút dostanete e-mail s pokynmi na obnovenie hesla."
      send_paranoid_instructions: "If your email address exists in our database, you will receive a password recovery link at your email address in a few minutes."
      updated: "Your password has been changed successfully. You are now signed in."
      updated_not_active: "Your password has been changed successfully."
    registrations:
      destroyed: "Bye! Your account has been successfully cancelled. We hope to see you again soon."
      signed_up: "Welcome! You have signed up successfully."
      signed_up_but_inactive: "You have signed up successfully. However, we could not sign you in because your account is not yet activated."
      signed_up_but_locked: "You have signed up successfully. However, we could not sign you in because your account is locked."
      signed_up_but_unconfirmed: "A message with a confirmation link has been sent to your email address. Please follow the link to activate your account."
      update_needs_confirmation: "You updated your account successfully, but we need to verify your new email address. Please check your email and follow the confirm link to confirm your new email address."
      updated: "Your account has been updated successfully."
    sessions:
      signed_in: "Signed in successfully."
      signed_out: "Signed out successfully."
      already_signed_out: "Signed out successfully."
    unlocks:
      send_instructions: "You will receive an email with instructions for how to unlock your account in a few minutes."
      send_paranoid_instructions: "If your account exists, you will receive an email with instructions for how to unlock it in a few minutes."
      unlocked: "Your account has been unlocked successfully. Please sign in to continue."
  errors:
    messages:
      already_confirmed: "was already confirmed, please try signing in"
      confirmation_period_expired: "needs to be confirmed within %{period}, please request a new one"
      expired: "has expired, please request a new one"
      not_found: "not found"
      not_locked: "was not locked"
      not_saved:
        one: "1 error prohibited this %{resource} from being saved:"
        few: "%{count} errors prohibited this %{resource} from being saved:"
        many: "%{count} errors prohibited this %{resource} from being saved:"
        other: "%{count} errors prohibited this %{resource} from being saved:"
