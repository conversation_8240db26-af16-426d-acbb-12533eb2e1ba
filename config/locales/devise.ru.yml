#Additional translations at https://github.com/plataformatec/devise/wiki/I18n
ru:
  devise:
    confirmations:
      confirmed: "Ваш email был успешно подтвержден."
      send_instructions: "Через несколько минут вы получите письмо с инструкциями по подтверждению адреса электронной почты."
      send_paranoid_instructions: "Если ваш адрес электронной почты существует в нашей базе данных, вы получите письмо с инструкциями по подтверждению email в течение нескольких минут."
    failure:
      already_authenticated: "Вы уже вошли."
      inactive: "Ваша учетная запись еще не активирована."
      invalid: "Недопустимый %{authentication_keys}/пароль или учетная запись еще не активирована."
      locked: "Ваш аккаунт заблокирован."
      last_attempt: "У вас есть еще одна попытка, прежде чем ваш аккаунт будет заблокирован."
      not_found_in_database: "Неверный %{authentication_keys} или пароль."
      timeout: "Ваша сессия истекла. Пожалуйста, войдите снова, чтобы продолжить."
      unauthenticated: "Вам нужно войти или зарегистрироваться, чтобы продолжить."
      unconfirmed: "Вы должны подтвердить свой email, прежде чем продолжить."
    mailer:
      confirmation_instructions:
        subject: "Инструкции по подтверждению"
      reset_password_instructions:
        subject: "Инструкции по восстановлению пароля"
      unlock_instructions:
        subject: "Инструкции по разблокировке"
      password_change:
        subject: "Пароль изменен"
    omniauth_callbacks:
      failure: "Не удалось авторизовать вас с помощью %{kind}, потому что \"%{reason}\"."
      success: "Успешная авторизация через %{kind}."
    passwords:
      no_token: "Вы не можете получить доступ к этой странице без отправки письма для сброса пароля. Если вы перешли из письма для сброса пароля, пожалуйста, убедитесь, что вы использовали полный URL-адрес."
      send_instructions: "Через несколько минут вы получите письмо с инструкциями по подтверждению адреса электронной почты."
      send_paranoid_instructions: "Если ваш адрес электронной почты существует в нашей базе данных, вы получите письмо с инструкциями по подтверждению email в течение нескольких минут."
      updated: "Ваш пароль успешно изменён. Вы авторизованы."
      updated_not_active: "Ваш пароль успешно изменен."
    registrations:
      destroyed: "До свидания! Ваша учетная запись была успешно отменена. Мы надеемся вскоре увидеть вас снова."
      signed_up: "Добро пожаловать! Регистрация прошла успешно."
      signed_up_but_inactive: "Вы зарегистрировались. Но вы пока не можете войти, потому что ваша учетная запись еще не активирована."
      signed_up_but_locked: "Вы успешно зарегистрировались. Но вы не можете войти, потому что ваша учетная запись заблокирована."
      signed_up_but_unconfirmed: "На ваш адрес электронной почты было отправлено сообщение со ссылкой для подтверждения. Перейдите по ссылке для активации вашей учетной записи."
      update_needs_confirmation: "Вы успешно обновили свой аккаунт, но нам нужно подтвердить ваш email. Пожалуйста, проверьте свою почту и пройдите по ссылке для подтверждения нового адреса."
      updated: "Ваш аккаунт сохранен."
    sessions:
      signed_in: "Вход выполнен."
      signed_out: "Вы вышли из системы."
      already_signed_out: "Вы вышли из системы."
    unlocks:
      send_instructions: "Вы получите письмо с инструкциями по разблокировке аккаунта в течение нескольких минут."
      send_paranoid_instructions: "Если ваш аккаунт существует, вы получите письмо с инструкциями по его разблокировке через несколько минут."
      unlocked: "Ваша учетная запись успешно разблокирована. Пожалуйста, войдите для продолжения."
  errors:
    messages:
      already_confirmed: "уже подтверждён, попробуйте войти"
      confirmation_period_expired: "должен был быть подтвержден в течение %{period}, пожалуйста, запросите новый"
      expired: "истек, пожалуйста, запросите новый"
      not_found: "не найден"
      not_locked: "не был заблокирован"
      not_saved:
        one: "1 ошибка не позволяют сохранить %{resource}:"
        few: "%{count} ошибок не позволяют сохранить %{resource}:"
        many: "%{count} ошибок не позволяют сохранить %{resource}:"
        other: "%{count} ошибок не позволяют сохранить %{resource}:"
