#Files in the config/locales directory are used for internationalization
#and are automatically loaded by Rails. If you want to use locales other
#than English, add the necessary files in this directory.
#To use the locales, use `I18n.t`:
#I18n.t 'hello'
#In views, this is aliased to just `t`:
#<%= t('hello') %>
#To use a different locale, set it with `I18n.locale`:
#I18n.locale = :es
#This would use the information in config/locales/es.yml.
#The following keys must be escaped otherwise they will not be retrieved by
#the default I18n backend:
#true, false, on, off, yes, no
#Instead, surround them with single quotes.
#en:
#'true': 'foo'
#To learn more, please read the Rails Internationalization guide
#available at https://guides.rubyonrails.org/i18n.html.
tr:
  hello: 'Merhaba Dünya'
  messages:
    reset_password_success: Parola sıfırlama isteği başarılı. Talimatlar için postanızı kontrol edin.
    reset_password_failure: Belirtilen e-postaya sahip herhangi bir kullanıcı bulamadık.
    inbox_deletetion_response: <PERSON><PERSON>n kutusu silme isteğiniz bir süre sonra işleme alınacaktır.
  errors:
    validations:
      presence: boş bırakılmamalı
    webhook:
      invalid: Hatalı işlem
    signup:
      disposable_email: Tek kullanımlık e-postalara izin vermiyoruz
      blocked_domain: This domain is not allowed. If you believe this is a mistake, please contact support.
      invalid_email: Geçersiz bir e-posta girdiniz
      email_already_exists: '%{email} ile zaten bir hesaba kaydoldunuz'
      invalid_params: 'Hata, lütfen kayıt olma parametrelerini kontrol edin ve tekrar deneyin'
      failed: Kayıt başarısız oldu
    data_import:
      data_type:
        invalid: Hatalı veri türü
    contacts:
      import:
        failed: Dosya boş
      export:
        success: İletişim dışa aktarma dosyası görüntüleme için hazır olduğunda sizi bilgilendireceğiz.
      email:
        invalid: Hatalı e-posta
      phone_number:
        invalid: e164 formatında olmalı
    categories:
      locale:
        unique: kategori ve portalde tekil olmalı
    dyte:
      invalid_message_type: 'Geçersiz mesaj türü. İşlem izin verilmiyor'
    slack:
      invalid_channel_id: 'Geçersiz Slack kanalı. Lütfen tekrar deneyin'
    inboxes:
      imap:
        socket_error: Lütfen ağ bağlantınızı, IMAP adresini kontrol edin ve tekrar deneyin.
        no_response_error: Lütfen IMAP erişim bilgilerinizi kontrol edip tekrar deneyin.
        host_unreachable_error: Host ulaşılamaz, Lütfen IMAP adresini, IMAP portunu kontrol edin ve tekrar deneyin.
        connection_timed_out_error: '%{address}:%{port} için bağlantı zaman aşımına uğradı.'
        connection_closed_error: Bağlantı kapalı.
      validations:
        name: sembollerle başlamamalı veya bitmemeli, < > / \ @ karakterlerini içermemeli.
    custom_filters:
      number_of_records: Limit aşıldı. Bir kullanıcının bir hesap için izin verilen özel filtre sayısı 50'dir.
      invalid_attribute: Invalid attribute key - [%{key}]. The key should be one of [%{allowed_keys}] or a custom attribute defined in the account.
      invalid_operator: Invalid operator. The allowed operators for %{attribute_name} are [%{allowed_keys}].
      invalid_query_operator: Query operator must be either "AND" or "OR".
      invalid_value: Invalid value. The values provided for %{attribute_name} are invalid
    custom_attribute_definition:
      key_conflict: The provided key is not allowed as it might conflict with default attributes.
  reports:
    period: Raporlama aralığı %{since}'dan %{until}'a
    utc_warning: Oluşturulan rapor UTC zaman dilimindedir.
    agent_csv:
      agent_name: Temsilci adı
      conversations_count: Assigned conversations
      avg_first_response_time: Avg first response time
      avg_resolution_time: Avg resolution time
      resolution_count: Çözünürlük Sayısı
      avg_customer_waiting_time: Avg customer waiting time
    inbox_csv:
      inbox_name: Inbox adı
      inbox_type: Inbox türü
      conversations_count: Konuşmaların sayısı
      avg_first_response_time: Avg first response time
      avg_resolution_time: Avg resolution time
    label_csv:
      label_title: Etiket
      conversations_count: Konuşma sayısı
      avg_first_response_time: Avg first response time
      avg_resolution_time: Avg resolution time
    team_csv:
      team_name: Ekip adı
      conversations_count: Konuşma sayısı
      avg_first_response_time: Avg first response time
      avg_resolution_time: Avg resolution time
      resolution_count: Çözünürlük Sayısı
      avg_customer_waiting_time: Avg customer waiting time
    conversation_traffic_csv:
      timezone: Zaman dilimi
    sla_csv:
      conversation_id: Sohbet Kodu
      sla_policy_breached: SLA Policy
      assignee: Assignee
      team: Ekip
      inbox: Gelen Kutusu
      labels: Etiketler
      conversation_link: Link to the Conversation
      breached_events: Breached Events
    default_group_by: gün
    csat:
      headers:
        contact_name: İletişim Adı
        contact_email_address: İletişim E-posta Adresi
        contact_phone_number: İletişim Telefon Numarası
        link_to_the_conversation: Görüşmenin bağlantısı
        agent_name: Temsilci Adı
        rating: Derecelendirme
        feedback: Derecelendirme Yorumu
        recorded_at: Kaydedilen tarih
  notifications:
    notification_title:
      conversation_creation: 'A conversation (#%{display_id}) has been created in %{inbox_name}'
      conversation_assignment: 'A conversation (#%{display_id}) has been assigned to you'
      assigned_conversation_new_message: 'A new message is created in conversation (#%{display_id})'
      conversation_mention: 'You have been mentioned in conversation (#%{display_id})'
      sla_missed_first_response: 'SLA target first response missed for conversation (#%{display_id})'
      sla_missed_next_response: 'SLA target next response missed for conversation (#%{display_id})'
      sla_missed_resolution: 'SLA target resolution missed for conversation (#%{display_id})'
    attachment: 'Attachment'
    no_content: 'No content'
  conversations:
    captain:
      handoff: 'Transferring to another agent for further assistance.'
    messages:
      instagram_story_content: '%{story_sender} hikayesinde senden bahsetti: '
      instagram_deleted_story_content: Bu hikaye artık mevcut değil.
      deleted: Bu mesaj silinmiş
      delivery_status:
        error_code: 'Hata kodu: %{error_code}'
    activity:
      captain:
        resolved: 'Conversation was marked resolved by %{user_name} due to inactivity'
        open: 'Conversation was marked open by %{user_name}'
      status:
        resolved: 'Konuşma %{user_name} tarafından çözümlendi olarak işaretlendi'
        contact_resolved: 'Konuşma %{contact_name} tarafından çözümlendi olarak işaretlendi'
        open: 'Konuşma %{user_name} tarafından açık olarak işaretlendi'
        pending: 'Konuşma, %{user_name} tarafından bekleyen olarak işaretlendi'
        snoozed: 'Konuşma, %{user_name} tarafından erteledi olarak işaretlendi'
        auto_resolved_days: ' %{count} günlük hareketsizlik nedeniyle konuşma, sistem tarafından çözümlendi olarak işaretlendi'
        auto_resolved_hours: 'Conversation was marked resolved by system due to %{count} hours of inactivity'
        auto_resolved_minutes: 'Conversation was marked resolved by system due to %{count} minutes of inactivity'
        system_auto_open: Sistem, yeni gelen bir mesaj nedeniyle konuşmayı tekrar açtı.
      priority:
        added: '%{user_name} önceliği %{new_priority} olarak ayarladı'
        updated: '%{user_name} önceliği %{old_priority} dan %{new_priority} olarak değiştirdi'
        removed: '%{user_name} önceliği kaldırdı'
      assignee:
        self_assigned: '%{user_name} bu konuşmayı kendisine atadı'
        assigned: '%{user_name} tarafından %{assignee_name} adına atandı'
        removed: '%{user_name} tarafından konuşmanın atanması kaldırıldı'
      team:
        assigned: '%{user_name} tarafından %{team_name} adına atandı'
        assigned_with_assignee: '%{user_name} tarafından %{team_name} aracılığıyla %{assignee_name} adına atandı'
        removed: '%{user_name} tarafından %{team_name} ataması kaldırıldı'
      labels:
        added: '%{user_name}, %{labels} ekledi'
        removed: '%{user_name}, %{labels} kaldırdı'
      sla:
        added: '%{user_name} added SLA policy %{sla_name}'
        removed: '%{user_name} removed SLA policy %{sla_name}'
      muted: '%{user_name}, sohbeti sessize aldı'
      unmuted: '%{user_name} konuşmanın sesini açtı'
      auto_resolution_message: 'Resolving the conversation as it has been inactive for a while. Please start a new conversation if you need further assistance.'
    templates:
      greeting_message_body: '%{account_name} genellikle birkaç saat içinde yanıt verir.'
      ways_to_reach_you_message_body: 'Ekibin size ulaşması için bir bilgi verin.'
      email_input_box_message_body: 'E-posta ile haberdar olun'
      csat_input_message_body: 'Lütfen görüşmeyi değerlendirin'
    reply:
      email:
        header:
          from_with_name: '"%{inbox_name} <%{from_email}> adresinden %{assignee_name}''e gönderildi,'
          reply_with_name: '%{assignee_name} tarafından %{inbox_name} <reply+%{reply_email}>'
          friendly_name: '%{sender_name} tarafından %{business_name} <%{from_email}>'
          professional_name: '%{business_name} <%{from_email}>'
      channel_email:
        header:
          reply_with_name: '"%{inbox_name} <%{from_email}> adresinden %{assignee_name}''e gönderildi,'
          reply_with_inbox_name: '%{inbox_name} <%{from_email}>'
      email_subject: 'Bu konuşmadaki yeni mesajlar'
      transcript_subject: 'Konuşma Metni'
    survey:
      response: 'Lütfen bu konuşmayı değerlendirin, %{link}'
  contacts:
    online:
      delete: '%{contact_name} çevrimiçi, lütfen daha sonra tekrar deneyin'
  integration_apps:
    dashboard_apps:
      name: 'Panel Uygulamaları'
      description: 'Dashboard Apps allow you to create and embed applications that display user information, orders, or payment history, providing more context to your customer support agents.'
    dyte:
      name: 'Dyte'
      description: 'Dyte is a product that integrates audio and video functionalities into your application. With this integration, your agents can start video/voice calls with your customers directly from Chatwoot.'
      meeting_name: '%{agent_name} bir toplantı başlattı'
    slack:
      name: 'Slack'
      description: "Integrate Chatwoot with Slack to keep your team in sync. This integration allows you to receive notifications for new conversations and respond to them directly within Slack's interface."
    webhooks:
      name: 'Webhooks'
      description: 'Webhook events provide real-time updates about activities in your Chatwoot account. You can subscribe to your preferred events, and Chatwoot will send you HTTP callbacks with the updates.'
    dialogflow:
      name: 'Dialogflow botu'
      description: 'Build chatbots with Dialogflow and easily integrate them into your inbox. These bots can handle initial queries before transferring them to a customer service agent.'
    google_translate:
      name: 'Google Çeviri'
      description: "Integrate Google Translate to help agents easily translate customer messages. This integration automatically detects the language and converts it to the agent's or admin's preferred language."
    openai:
      name: 'OpenAI'
      description: 'Leverage the power of large language models from OpenAI with the features such as reply suggestions, summarization, message rephrasing, spell-checking, and label classification.'
    linear:
      name: 'Linear'
      description: 'Create issues in Linear directly from your conversation window. Alternatively, link existing Linear issues for a more streamlined and efficient issue tracking process.'
    shopify:
      name: 'Shopify'
      description: 'Connect your Shopify store to access order details, customer information, and product data directly within your conversations and helps your support team provide faster, more contextual assistance to your customers.'
    leadsquared:
      name: 'LeadSquared'
      short_description: 'Sync your contacts and conversations with LeadSquared CRM.'
      description: 'Sync your contacts and conversations with LeadSquared CRM. This integration automatically creates leads in LeadSquared when new contacts are added, and logs conversation activity to provide your sales team with complete context.'
  captain:
    copilot_error: 'Please connect an assistant to this inbox to use Copilot'
    copilot_limit: 'You are out of Copilot credits. You can buy more credits from the billing section.'
  public_portal:
    search:
      search_placeholder: Başlık veya içerikle makale arayın...
      empty_placeholder: Sonuç bulunamadı.
      loading_placeholder: Aranıyor...
      results_title: Arama sonuçları
    toc_header: 'Bu sayfada'
    hero:
      sub_title: Makaleleri buradan arayın veya aşağıdaki kategorilere göz atın.
    common:
      home: Anasayfa
      last_updated_on: Son güncelleme tarihi %{last_updated_on}
      view_all_articles: Tümünü Görüntüle
      article: makale
      articles: makaleler
      author: yazar
      authors: yazarlar
      other: diğer
      others: diğerleri
      by: Tarafından
      no_articles: Burada makale bulunmuyor
    footer:
      made_with: İle yapılmıştır
    header:
      go_to_homepage: Website
      appearance:
        system: Sistem
        light: Açık Mod
        dark: Koyu Mod
      featured_articles: Öne Çıkan Makaleler
      uncategorized: Kategorize Edilmemiş
    404:
      title: Sayfa bulunamadı
      description: Aradığınız sayfayı bulamadık.
      back_to_home: Ana sayfaya dön
  slack_unfurl:
    fields:
      name: İsim
      email: E-Posta
      phone_number: Telefon
      company_name: Şirket
      inbox_name: Gelen Kutusu
      inbox_type: Gelen Kutusu Türü
    button: Görüşmeyi aç
  time_units:
    days:
      one: '%{count} day'
      other: '%{count} days'
    hours:
      one: '%{count} hour'
      other: '%{count} hours'
    minutes:
      one: '%{count} minute'
      other: '%{count} minutes'
    seconds:
      one: '%{count} second'
      other: '%{count} seconds'
  automation:
    system_name: 'Automation System'
  crm:
    no_message: 'No messages in conversation'
    attachment: '[Attachment: %{type}]'
    no_content: '[No content]'
    created_activity: |
      New conversation started on %{brand_name}

      Channel: %{channel_info}
      Created: %{formatted_creation_time}
      Conversation ID: %{display_id}
      View in %{brand_name}: %{url}
    transcript_activity: |
      Conversation Transcript from %{brand_name}

      Channel: %{channel_info}
      Conversation ID: %{display_id}
      View in %{brand_name}: %{url}

      Transcript:
      %{format_messages}
