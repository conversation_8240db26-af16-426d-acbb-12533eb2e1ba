default: &default
  adapter: postgresql
  encoding: unicode
  host: <%= ENV.fetch('POSTGRES_HOST', 'localhost') %>
  port: <%= ENV.fetch('POSTGRES_PORT', '5432') %>
   # ref: https://github.com/mperham/sidekiq/issues/2985#issuecomment-531097962
  pool: <%= Sidekiq.server? ? ENV.fetch('SIDEKIQ_CONCURRENCY', 10) : ENV.fetch('RAILS_MAX_THREADS', 5) %>
  # frequency in seconds to periodically run the Reaper, which attempts
  # to find and recover connections from dead threads
  reaping_frequency: <%= ENV.fetch('DB_POOL_REAPING_FREQUENCY', 30) %>
  variables:
    # we are setting this value to be close to the racktimeout value. we will iterate and reduce this value going forward
    statement_timeout: <%= ENV["POSTGRES_STATEMENT_TIMEOUT"] || "14s" %>

development:
  <<: *default
  database: "<%= ENV.fetch('POSTGRES_DATABASE', 'chatwoot_dev') %>"
  username: "<%= ENV.fetch('POSTGRES_USERNAME', 'postgres') %>"
  password: "<%= ENV.fetch('POSTGRES_PASSWORD', '') %>"

test:
  <<: *default
  database: "<%= ENV.fetch('POSTGRES_DATABASE', 'chatwoot_test') %>"
  username: "<%= ENV.fetch('POSTGRES_USERNAME', 'postgres') %>"
  password: "<%= ENV.fetch('POSTGRES_PASSWORD', '') %>"

production:
  <<: *default
  database: "<%= ENV.fetch('POSTGRES_DATABASE', 'chatwoot_production') %>"
  username: "<%= ENV.fetch('POSTGRES_USERNAME', 'chatwoot_prod') %>"
  password: "<%= ENV.fetch('POSTGRES_PASSWORD', 'chatwoot_prod') %>"
