######  Attributes Supported by Integration Apps #######
# id: Internal Id for the integrations, used by the hooks
# logo: place the image in /public/dashboard/images/integrations and reference here
# i18n_key: the key under which translations for the integration is placed in en.yml
# action: if integration requires external redirect url
# hook_type: ( account / inbox )
# feature_flag: (string) feature flag to enable/disable the integration
# allow_multiple_hooks: whether multiple hooks can be created for the integration
# settings_json_schema: the json schema used to validate the settings hash (https://json-schema.org/)
# settings_form_schema: the formulate schema used in frontend to render settings form (https://vueformulate.com/)
########################################################
webhooks:
  id: webhook
  logo: webhooks.png
  i18n_key: webhooks
  action: /webhook
  hook_type: account
  allow_multiple_hooks: true
dashboard_apps:
  id: dashboard_apps
  logo: dashboard_apps.png
  i18n_key: dashboard_apps
  hook_type: account
  allow_multiple_hooks: true
openai:
  id: openai
  logo: openai.png
  i18n_key: openai
  action: /openai
  hook_type: account
  allow_multiple_hooks: false
  settings_json_schema:
    {
      'type': 'object',
      'properties':
        {
          'api_key': { 'type': 'string' },
          'label_suggestion': { 'type': 'boolean' },
        },
      'required': ['api_key'],
      'additionalProperties': false,
    }
  settings_form_schema:
    [
      {
        'label': 'API Key',
        'type': 'text',
        'name': 'api_key',
        'validation': 'required',
      },
      {
        'label': 'Show label suggestions',
        'type': 'checkbox',
        'name': 'label_suggestion',
        'validation': '',
      },
    ]
  visible_properties: ['api_key', 'label_suggestion']
linear:
  id: linear
  logo: linear.png
  i18n_key: linear
  action: https://linear.app/oauth/authorize
  hook_type: account
  allow_multiple_hooks: false
slack:
  id: slack
  logo: slack.png
  i18n_key: slack
  action: https://slack.com/oauth/v2/authorize?scope=commands,chat:write,channels:read,channels:manage,channels:join,groups:read,groups:write,im:write,mpim:write,users:read,users:read.email,chat:write.customize,channels:history,groups:history,mpim:history,im:history,files:read,files:write
  hook_type: account
  allow_multiple_hooks: false
dialogflow:
  id: dialogflow
  logo: dialogflow.png
  i18n_key: dialogflow
  action: /dialogflow
  hook_type: inbox
  allow_multiple_hooks: true
  settings_json_schema:
    {
      'type': 'object',
      'properties':
        {
          'project_id': { 'type': 'string' },
          'credentials': { 'type': 'object' },
        },
      'required': ['project_id', 'credentials'],
      'additionalProperties': false,
    }
  settings_form_schema:
    [
      {
        'label': 'Dialogflow Project ID',
        'type': 'text',
        'name': 'project_id',
        'validation': 'required',
        'validationName': 'Project Id',
      },
      {
        'label': 'Dialogflow Project Key File',
        'type': 'textarea',
        'name': 'credentials',
        'validation': 'required|JSON',
        'validationName': 'Credentials',
        'validation-messages':
          { 'JSON': 'Invalid JSON', 'required': 'Credentials is required' },
      },
    ]
  visible_properties: ['project_id']
google_translate:
  id: google_translate
  logo: google-translate.png
  i18n_key: google_translate
  action: /google_translate
  hook_type: account
  allow_multiple_hooks: false
  settings_json_schema:
    {
      'type': 'object',
      'properties':
        {
          'project_id': { 'type': 'string' },
          'credentials': { 'type': 'object' },
        },
      'required': ['project_id', 'credentials'],
      'additionalProperties': false,
    }
  settings_form_schema:
    [
      {
        'label': 'Google Cloud Project ID',
        'type': 'text',
        'name': 'project_id',
        'validation': 'required',
        'validationName': 'Project Id',
      },
      {
        'label': 'Google Cloud Project Key File',
        'type': 'textarea',
        'name': 'credentials',
        'validation': 'required|JSON',
        'validationName': 'Credentials',
        'validation-messages':
          { 'JSON': 'Invalid JSON', 'required': 'Credentials is required' },
      },
    ]
  visible_properties: ['project_id']
dyte:
  id: dyte
  logo: dyte.png
  i18n_key: dyte
  action: /dyte
  hook_type: account
  allow_multiple_hooks: false
  settings_json_schema:
    {
      'type': 'object',
      'properties':
        {
          'api_key': { 'type': 'string' },
          'organization_id': { 'type': 'string' },
        },
      'required': ['api_key', 'organization_id'],
      'additionalProperties': false,
    }
  settings_form_schema:
    [
      {
        'label': 'Organization ID',
        'type': 'text',
        'name': 'organization_id',
        'validation': 'required',
      },
      {
        'label': 'API Key',
        'type': 'text',
        'name': 'api_key',
        'validation': 'required',
      },
    ]
  visible_properties: ['organization_id']

shopify:
  id: shopify
  logo: shopify.png
  i18n_key: shopify
  hook_type: account
  allow_multiple_hooks: false

leadsquared:
  id: leadsquared
  feature_flag: crm_integration
  logo: leadsquared.png
  i18n_key: leadsquared
  action: /leadsquared
  hook_type: account
  allow_multiple_hooks: false
  settings_json_schema:
    {
      'type': 'object',
      'properties':
        {
          'access_key': { 'type': 'string' },
          'secret_key': { 'type': 'string' },
          'endpoint_url': { 'type': 'string' },
          'app_url': { 'type': 'string' },
          'enable_conversation_activity': { 'type': 'boolean' },
          'enable_transcript_activity': { 'type': 'boolean' },
          'conversation_activity_score': { 'type': 'string' },
          'transcript_activity_score': { 'type': 'string' },
          'conversation_activity_code': { 'type': 'integer' },
          'transcript_activity_code': { 'type': 'integer' },
        },
      'required': ['access_key', 'secret_key'],
      'additionalProperties': false,
    }
  settings_form_schema:
    [
      {
        'label': 'Access Key',
        'type': 'text',
        'name': 'access_key',
        'validation': 'required',
      },
      {
        'label': 'Secret Key',
        'type': 'text',
        'name': 'secret_key',
        'validation': 'required',
      },
      {
        'label': 'Push Conversation Activity',
        'type': 'checkbox',
        'name': 'enable_conversation_activity',
        'help': 'Enable this option to push an activity when a conversation is created',
      },
      {
        'label': 'Conversation Activity Score',
        'type': 'number',
        'name': 'conversation_activity_score',
        'help': 'Score to assign to the conversation created activity, default is 0',
      },
      {
        'label': 'Push Transcript Activity',
        'type': 'checkbox',
        'name': 'enable_transcript_activity',
        'help': 'Enable this option to push an activity when a transcript is created',
      },
      {
        'label': 'Transcript Activity Score',
        'type': 'number',
        'name': 'transcript_activity_score',
        'help': 'Score to assign to the conversation transcript activity, default is 0',
      },
    ]
  visible_properties:
    [
      'access_key',
      'endpoint_url',
      'enable_conversation_activity',
      'enable_transcript_activity',
      'conversation_activity_score',
      'transcript_activity_score',
    ]
