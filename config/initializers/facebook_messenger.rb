# ref: https://github.com/jgorset/facebook-messenger#make-a-configuration-provider
class ChatwootFbProvider < Facebook::Messenger::Configuration::Providers::Base
  def valid_verify_token?(_verify_token)
    GlobalConfigService.load('FB_VERIFY_TOKEN', '')
  end

  def app_secret_for(_page_id)
    GlobalConfigService.load('FB_APP_SECRET', '')
  end

  def access_token_for(page_id)
    Channel::FacebookPage.where(page_id: page_id).last.page_access_token
  end

  private

  def bot
    Chatwoot::Bot
  end
end

Rails.application.reloader.to_prepare do
  Facebook::Messenger.configure do |config|
    config.provider = ChatwootFbProvider.new
  end

  Facebook::Messenger::Bot.on :message do |message|
    Webhooks::FacebookEventsJob.perform_later(message.to_json)
  end

  Facebook::Messenger::Bot.on :delivery do |delivery|
    Rails.logger.info "Recieved delivery status #{delivery.to_json}"
    Webhooks::FacebookDeliveryJob.perform_later(delivery.to_json)
  end

  Facebook::Messenger::Bot.on :read do |read|
    Rails.logger.info "Recieved read status  #{read.to_json}"
    Webhooks::FacebookDeliveryJob.perform_later(read.to_json)
  end

  Facebook::Messenger::Bot.on :message_echo do |message|
    Webhooks::FacebookEventsJob.perform_later(message.to_json)
  end

  # Thêm xử lý postback message
  Facebook::Messenger::Bot.on :postback do |postback|
    Rails.logger.info "Received postback #{postback.to_json}"
    Webhooks::FacebookEventsJob.perform_later(postback.to_json)
  end

  # Thêm xử lý messaging_referrals để track nguồn từ ads
  Facebook::Messenger::Bot.on :referral do |referral|
    Rails.logger.info "Received referral from ads #{referral.to_json}"
    Webhooks::FacebookEventsJob.perform_later(referral.to_json)
  end
end
