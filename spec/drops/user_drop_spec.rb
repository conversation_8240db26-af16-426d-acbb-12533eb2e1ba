require 'rails_helper'

describe UserDrop do
  subject(:user_drop) { described_class.new(user) }

  let!(:user) { create(:user) }

  context 'when first name' do
    it 'returns first name' do
      user.update!(name: '<PERSON>')
      expect(subject.first_name).to eq '<PERSON>'
    end

    it('return the capitalized first name') do
      user.update!(name: 'john doe')
      expect(subject.first_name).to eq '<PERSON>'
    end
  end

  it('return the capitalized name') do
    user.update!(name: 'peter')
    expect(subject.name).to eq 'Peter'
  end

  context 'when last name' do
    it 'returns the last name' do
      user.update!(name: '<PERSON>')
      expect(subject.last_name).to eq '<PERSON><PERSON>'
    end

    it 'returns empty when last name not present' do
      user.update!(name: '<PERSON>')
      expect(subject.last_name).to be_nil
    end

    it('return the capitalized last name') do
      user.update!(name: 'john doe')
      expect(subject.last_name).to eq 'Doe'
    end
  end
end
