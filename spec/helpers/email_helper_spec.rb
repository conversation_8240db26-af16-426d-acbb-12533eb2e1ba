require 'rails_helper'

describe <PERSON>ailHelper do
  describe '#normalize_email_with_plus_addressing' do
    context 'when email is passed' do
      it 'normalise if plus addressing is present' do
        expect(helper.normalize_email_with_plus_addressing('<EMAIL>')).to eq '<EMAIL>'
      end

      it 'returns original if plus addressing is not present' do
        expect(helper.normalize_email_with_plus_addressing('<EMAIL>')).to eq '<EMAIL>'
      end

      it 'returns downcased version of email' do
        expect(helper.normalize_email_with_plus_addressing('<EMAIL>')).to eq '<EMAIL>'
      end
    end
  end
end
