MIME-Version: 1.0
Date: Wed, 31 May 2023 15:27:36 +0530
Message-ID: <CAFkiBVzURp=<EMAIL>>
Subject: New inline image test email conversation
From: <PERSON> Mathew <<EMAIL>>
To: "Replies" <<EMAIL>>
Content-Type: multipart/related; boundary="0000000000007a538405fcfa5967"

--0000000000007a538405fcfa5967
Content-Type: multipart/alternative; boundary="0000000000007a538205fcfa5966"

--0000000000007a538205fcfa5966
Content-Type: text/plain; charset="UTF-8"

[image: poster (8).jpg][image: poster (7).jpg][image: poster (1).jpg]

Let's add no HTML content here, just plain text and images

--
*Tejaswini Chile.*
Software developer
*Mob:8485827731 | *@tejaswini_chile <https://twitter.com/tejaswini_chile>


--0000000000007a538405fcfa5967
Content-Type: image/jpeg; name="poster (8).jpg"
Content-Disposition: inline; filename="poster (8).jpg"
Content-Transfer-Encoding: base64
X-Attachment-Id: ii_lm7fuura0
Content-ID: <ii_lm7fuura0>


--0000000000007a538405fcfa5967
Content-Type: image/jpeg; name="poster (7).jpg"
Content-Disposition: inline; filename="poster (7).jpg"
Content-Transfer-Encoding: base64
X-Attachment-Id: ii_lm7fuuvm1
Content-ID: <ii_lm7fuuvm1>


--0000000000007a538405fcfa5967
Content-Type: image/jpeg; name="poster (1).jpg"
Content-Disposition: inline; filename="poster (1).jpg"
Content-Transfer-Encoding: base64
X-Attachment-Id: ii_lm7fuuwn2
Content-ID: <ii_lm7fuuwn2>


--0000000000007a538405fcfa5967--
