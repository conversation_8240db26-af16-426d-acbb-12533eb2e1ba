MIME-Version: 1.0
Date: Thu, 19 Aug 2021 14:14:31 +0530
References: <<EMAIL>> <<EMAIL>>
In-Reply-To: <<EMAIL>>
Message-ID: <CAFkiBVwJjO_k_e-LpiKi7MAQAKbHX5nkEPcf0y1R=<EMAIL>>
Subject: Re: Checking mail forwarding to cw inbox
From: Sony Mathew <<EMAIL>>
To: Tejaswini <<EMAIL>>
Content-Type: multipart/alternative; boundary="0000000000004af64505c9e58f03"

--0000000000004af64505c9e58f03
Content-Type: text/plain; charset="UTF-8"

Yes, I am providing you step how to reproduce this issue

On Thu, Aug 19, 2021 at 2:07 PM Tejaswini from Email sender test <
<EMAIL>> wrote:

> Any update on this?
>
>

--
* Sony Mathew*
Software developer
*Mob:9999999999

--0000000000004af64505c9e58f03
Content-Type: text/html; charset="UTF-8"
Content-Transfer-Encoding: quoted-printable

<div dir=3D"ltr">Yes, I am providing you step how to reproduce this issue</=
div><br>class=3D"gmail_quote"><div dir=3D"ltr" class=3D"gmail_attr">On=
 Thu, Aug 19, 2021 at 2:07 PM Tejaswini from Email sender test &l=
t;<a href=3D"mailto:<EMAIL>"><EMAIL></a>&gt; wrot=
e:<br></div><blockquote class=3D"gmail_quote" style=3D"margin:0px 0px 0px 0=
.8ex;border-left:1px solid rgb(204,204,204);padding-left:1ex">  <p>
      </p>Any update on this?</p>

  <p>
</blockquote></div><br clear=3D"all"><div><br></div>-- <br><div dir=3D"ltr"=
 class=3D"gmail_signature"><div dir=3D"ltr"><div><div dir=3D"ltr"><div><div=
><b>Sony Mathew.</b><br></div><span style=3D"font-family:&quot;times ne=
w roman&quot;,serif"><span></span><span></span>Software developer</span><br=
></div><b>Mob:9999999999</b></div></div></div></div>

--0000000000004af64505c9e58f03--
