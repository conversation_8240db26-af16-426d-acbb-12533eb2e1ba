[Unit]
Requires=network.target
PartOf=chatwoot.target

[Service]
Type=simple
User=chatwoot
WorkingDirectory=/home/<USER>/chatwoot

ExecStart=/bin/bash -lc 'bin/rails server -p $PORT -e $RAILS_ENV'

Restart=always
RestartSec=1
TimeoutStopSec=30
KillMode=mixed
StandardInput=null
SyslogIdentifier=%p

Environment="PATH=/home/<USER>/.rvm/gems/ruby-3.4.4/bin:/home/<USER>/.rvm/gems/ruby-3.4.4@global/bin:/home/<USER>/.rvm/rubies/ruby-3.4.4/bin:/home/<USER>/.rvm/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/snap/bin:/home/<USER>/.rvm/bin:/home/<USER>/.rvm/bin"
Environment="PORT=3000"
Environment="RAILS_ENV=production"
Environment="NODE_ENV=production"
Environment="RAILS_LOG_TO_STDOUT=true"
Environment="GEM_HOME=/home/<USER>/.rvm/gems/ruby-3.4.4"
Environment="GEM_PATH=/home/<USER>/.rvm/gems/ruby-3.4.4:/home/<USER>/.rvm/gems/ruby-3.4.4@global"
