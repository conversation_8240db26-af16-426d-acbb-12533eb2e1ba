## github action to check deployment success
## curl the deployment url and check for 200 status
## deployment url will be of the form chatwoot-pr-<pr_number>.herokuapp.com
name: Deploy Check

on:
  pull_request:

jobs:
  deployment_check:
    name: Check Deployment
    runs-on: ubuntu-latest
    steps:
      - name: Install jq
        run: sudo apt-get install -y jq
      - name: Print Deployment URL
        run: echo "https://chatwoot-pr-${{ github.event.pull_request.number }}.herokuapp.com"
      - name: Check Deployment Status
        run: |
          max_attempts=10
          attempt=1
          status_code=0
          echo "Waiting for review app to be deployed/redeployed, trying in  10 minutes..."
          sleep 600
          while [ $attempt -le $max_attempts ]; do
            response=$(curl -s -o /dev/null -w "%{http_code}" https://chatwoot-pr-${{ github.event.pull_request.number }}.herokuapp.com/api)
            status_code=$(echo $response | head -n   1)
            if [ $status_code -eq   200 ]; then
              body=$(curl -s https://chatwoot-pr-${{ github.event.pull_request.number }}.herokuapp.com/api)
              if echo "$body" | jq -e '.version and .timestamp and .queue_services == "ok" and .data_services == "ok"' > /dev/null; then
                echo "Deployment successful"
                exit 0
              else
                echo "Deployment status unknown, retrying in   3 minutes..."
                sleep 180
              fi
            else
              echo "Waiting for review app to be ready, retrying in  3 minutes..."
              sleep 180
              attempt=$((attempt +  1))
            fi
          done
          echo "Deployment failed after $max_attempts attempts"
          exit  1
          fi
