pull_request_title: "chore: Update translations"
commit_message: "[ci skip]"
files:
  - source: /app/javascript/dashboard/i18n/locale/en/*.json
    translation: >-
      /app/javascript/dashboard/i18n/locale/%two_letters_code%/%original_file_name%
  - source: /config/locales/en.yml
    translation: /config/locales/%two_letters_code%.yml
  - source: /config/locales/devise.en.yml
    translation: /config/locales/devise.%two_letters_code%.yml
  - source: /app/javascript/widget/i18n/locale/en.json
    translation: /app/javascript/widget/i18n/locale/%two_letters_code%.json
  - source: /app/javascript/survey/i18n/locale/en.json
    translation: /app/javascript/survey/i18n/locale/%two_letters_code%.json
