version: '3'

services:
  base: &base
    build:
      context: .
      dockerfile: ./docker/Dockerfile
      args:
        BUNDLE_WITHOUT: ''
        EXECJS_RUNTIME: 'Node'
        RAILS_ENV: 'development'
        RAILS_SERVE_STATIC_FILES: 'false'
    tty: true
    stdin_open: true
    image: chatwoot:development
    env_file: .env

  rails:
    <<: *base
    build:
      context: .
      dockerfile: ./docker/dockerfiles/rails.Dockerfile
    image: chatwoot-rails:development
    volumes:
      - ./:/app:delegated
      - node_modules:/app/node_modules
      - packs:/app/public/packs
      - cache:/app/tmp/cache
      - bundle:/usr/local/bundle
    depends_on:
      - postgres
      - redis
      - vite
      - mailhog
      - sidekiq
    ports:
      - 3000:3000
    env_file: .env
    environment:
      - VITE_DEV_SERVER_HOST=vite
      - NODE_ENV=development
      - RAILS_ENV=development
    entrypoint: docker/entrypoints/rails.sh
    command: ["bundle", "exec", "rails", "s", "-p", "3000", "-b", "0.0.0.0"]

  sidekiq:
    <<: *base
    image: chatwoot-rails:development
    volumes:
      - ./:/app:delegated
      - node_modules:/app/node_modules
      - packs:/app/public/packs
      - cache:/app/tmp/cache
      - bundle:/usr/local/bundle
    depends_on:
      - postgres
      - redis
      - mailhog
    environment:
      - NODE_ENV=development
      - RAILS_ENV=development
    command: ["bundle", "exec", "sidekiq", "-C", "config/sidekiq.yml"]

  vite:
    <<: *base
    build:
      context: .
      dockerfile: ./docker/dockerfiles/vite.Dockerfile
    image: chatwoot-vite:development
    volumes:
      - ./:/app:delegated
      - node_modules:/app/node_modules
      - packs:/app/public/packs
      - cache:/app/tmp/cache
      - bundle:/usr/local/bundle
    ports:
      - "3036:3036" # Vite dev server
    environment:
      - VITE_DEV_SERVER_HOST=0.0.0.0
      - NODE_ENV=development
      - RAILS_ENV=development
    entrypoint: docker/entrypoints/vite.sh
    command: bin/vite dev

  postgres:
    image: pgvector/pgvector:pg16
    restart: always
    ports:
      - '5432:5432'
    volumes:
      - postgres:/data/postgres
    environment:
      - POSTGRES_DB=chatwoot
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=

  redis:
    image: redis:alpine
    restart: always
    command: ["sh", "-c", "redis-server --requirepass \"$REDIS_PASSWORD\""]
    env_file: .env
    volumes:
      - redis:/data/redis
    ports:
      - '6379:6379'

  mailhog:
    image: mailhog/mailhog
    ports:
      - 1025:1025
      - 8025:8025

volumes:
  postgres:
  redis:
  packs:
  node_modules:
  cache:
  bundle:
