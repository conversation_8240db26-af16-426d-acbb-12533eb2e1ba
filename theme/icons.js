export const icons = {
  'logic-or': {
    body: `<rect x="14" y="5" width="2" height="13" rx="1" fill="currentColor"/><rect x="8" y="5" width="2" height="13" rx="1" fill="currentColor"/>`,
    width: 24,
    height: 24,
  },
  alert: {
    body: `<path d="M1.81348 0.9375L1.69727 7.95117H0.302734L0.179688 0.9375H1.81348ZM1 11.1025C0.494141 11.1025 0.0908203 10.7061 0.0976562 10.2207C0.0908203 9.72852 0.494141 9.33203 1 9.33203C1.49219 9.33203 1.89551 9.72852 1.90234 10.2207C1.89551 10.7061 1.49219 11.1025 1 11.1025Z" fill="currentColor" />`,
    width: 2,
    height: 12,
  },
  captain: {
    body: `<path d="M150.485 213.282C150.485 200.856 160.559 190.782 172.985 190.782C185.411 190.782 195.485 200.856 195.485 213.282V265.282C195.485 277.709 185.411 287.782 172.985 287.782C160.559 287.782 150.485 277.709 150.485 265.282V213.282Z" fill="currentColor"/>
    <path d="M222.485 213.282C222.485 200.856 232.559 190.782 244.985 190.782C257.411 190.782 267.485 200.856 267.485 213.282V265.282C267.485 277.709 257.411 287.782 244.985 287.782C232.559 287.782 222.485 277.709 222.485 265.282V213.282Z" fill="currentColor"/>
    <path fill-rule="evenodd" clip-rule="evenodd" d="M412.222 109.961C317.808 96.6217 240.845 96.0953 144.309 109.902C119.908 113.392 103.762 115.751 91.4521 119.354C80.0374 122.694 73.5457 126.678 68.1762 132.687C57.0576 145.13 55.592 159.204 54.0765 208.287C52.587 256.526 55.5372 299.759 61.1249 348.403C64.1025 374.324 66.1515 391.817 69.4229 405.117C72.526 417.732 76.2792 424.515 81.4954 429.708C86.7533 434.942 93.4917 438.633 105.859 441.629C118.94 444.797 136.104 446.713 161.613 449.5C244.114 458.514 305.869 458.469 388.677 449.548C414.495 446.767 431.939 444.849 445.216 441.702C457.83 438.712 464.612 435.047 469.797 429.962C474.873 424.985 478.752 418.118 482.116 404.874C485.626 391.056 488.014 372.772 491.47 345.913C497.636 297.99 502.076 255.903 502.248 209.798C502.433 160.503 501.426 146.477 490.181 133.468C484.75 127.185 478.148 123.053 466.473 119.612C453.865 115.897 437.283 113.502 412.222 109.961ZM138.414 68.5711C238.977 54.1882 319.888 54.7514 418.047 68.6199L419.483 68.8227C442.724 72.1054 462.359 74.8786 478.244 79.5601C495.387 84.6124 509.724 92.2821 521.706 106.145C544.308 132.295 544.161 163.321 543.965 204.542C543.956 206.327 543.948 208.131 543.941 209.954C543.758 258.703 539.048 302.844 532.821 351.247L532.656 352.528C529.407 377.787 526.729 398.602 522.522 415.166C518.098 432.584 511.485 447.517 498.968 459.792C486.56 471.959 471.897 478.282 454.819 482.33C438.691 486.153 418.624 488.314 394.436 490.919L393.136 491.059C307.385 500.297 242.618 500.349 157.091 491.004L155.772 490.86C131.921 488.255 112.062 486.086 96.056 482.209C79.0408 478.087 64.4759 471.637 52.1005 459.316C39.6835 446.955 33.1618 432.265 28.94 415.102C24.9582 398.915 22.6435 378.759 19.8561 354.488L19.7052 353.174C13.9746 303.287 10.8315 257.908 12.4035 206.997C12.4606 205.15 12.5151 203.323 12.5691 201.516C13.7911 160.603 14.7077 129.914 37.1055 104.847C48.989 91.5477 63.035 84.1731 79.7563 79.2794C95.2643 74.7408 114.386 72.0068 137.018 68.7707C137.482 68.7044 137.948 68.6379 138.414 68.5711Z" fill="currentColor"/>`,
    width: 556,
    height: 556,
  },
  'file-csv': {
    body: `<g clip-path="url(#clip0_2931_148091)">
    <path fill-rule="evenodd" clip-rule="evenodd" d="M12.6562 19.7578H2.94141C0.972656 19.7578 0 18.7734 0 16.7695V2.98828C0 0.996094 0.972656 0 2.94141 0H7.07812V6.80859C7.07812 8.05078 7.66406 8.63672 8.90625 8.63672H15.6094V16.7695C15.6094 18.7617 14.625 19.7578 12.6562 19.7578ZM3.46583 15.9741C3.66935 16.0317 3.86903 16.0605 4.06487 16.0605C4.23383 16.0605 4.39607 16.0374 4.55159 15.9914C4.70903 15.9434 4.84919 15.8771 4.97206 15.7926C5.09495 15.7082 5.19191 15.6102 5.26295 15.4989C5.33591 15.3875 5.37239 15.2675 5.37239 15.1389C5.37239 15.0198 5.33207 14.9238 5.25143 14.8509C5.17271 14.776 5.07863 14.7386 4.96919 14.7386C4.83671 14.7386 4.74359 14.7683 4.68983 14.8278C4.63799 14.8854 4.59575 14.9507 4.56311 15.0237C4.54007 15.0813 4.50935 15.1389 4.47095 15.1965C4.43447 15.2522 4.38359 15.2982 4.31831 15.3347C4.25495 15.3712 4.17046 15.3894 4.06487 15.3894C3.93431 15.3894 3.80951 15.352 3.69047 15.2771C3.57335 15.2022 3.47831 15.0803 3.40535 14.9114C3.33239 14.7405 3.29591 14.5139 3.29591 14.2317C3.29591 13.9475 3.33239 13.721 3.40535 13.552C3.47831 13.383 3.57335 13.2611 3.69047 13.1862C3.80951 13.1094 3.93431 13.071 4.06487 13.071C4.17046 13.071 4.25495 13.0893 4.31831 13.1258C4.38359 13.1622 4.43447 13.2093 4.47095 13.2669C4.50935 13.3226 4.54007 13.3792 4.56311 13.4368C4.59575 13.5098 4.63799 13.576 4.68983 13.6355C4.74359 13.6931 4.83671 13.7219 4.96919 13.7219C5.07863 13.7219 5.17271 13.6854 5.25143 13.6125C5.33207 13.5395 5.37239 13.4426 5.37239 13.3216C5.37239 13.193 5.33687 13.073 5.26583 12.9616C5.19479 12.8502 5.09687 12.7523 4.97206 12.6678C4.84919 12.5834 4.70999 12.5181 4.55447 12.472C4.39895 12.424 4.23575 12.4 4.06487 12.4C3.86903 12.4 3.66935 12.4288 3.46583 12.4864C3.26423 12.544 3.07799 12.6419 2.9071 12.7802C2.73623 12.9165 2.59895 13.1037 2.49527 13.3418C2.39159 13.5779 2.33975 13.8746 2.33975 14.2317C2.33975 14.5869 2.39159 14.8826 2.49527 15.1187C2.59895 15.3549 2.73623 15.5421 2.9071 15.6803C3.07799 15.8186 3.26423 15.9165 3.46583 15.9741ZM6.76232 15.9626C6.97736 16.0278 7.22408 16.0605 7.50248 16.0605C7.80584 16.0605 8.07368 16.0154 8.30599 15.9251C8.53832 15.8349 8.71976 15.7072 8.85032 15.5421C8.98088 15.375 9.04615 15.1792 9.04615 14.9546C9.04615 14.7376 8.99048 14.5581 8.87912 14.416C8.76776 14.2739 8.61416 14.1578 8.41831 14.0675C8.22439 13.9773 8.00072 13.9014 7.74728 13.84C7.49384 13.7766 7.30568 13.7094 7.1828 13.6384C7.06184 13.5674 7.00136 13.4829 7.00136 13.385C7.00136 13.3216 7.02248 13.2669 7.06472 13.2208C7.10696 13.1728 7.16648 13.1363 7.24328 13.1114C7.32008 13.0845 7.4084 13.071 7.50824 13.071C7.64456 13.071 7.75976 13.0922 7.85384 13.1344C7.94792 13.1766 8.02184 13.2294 8.07559 13.2928C8.12551 13.3408 8.18984 13.3926 8.26856 13.4483C8.34728 13.504 8.44136 13.5318 8.5508 13.5318C8.66216 13.5318 8.75144 13.5002 8.81863 13.4368C8.88776 13.3715 8.92232 13.2832 8.92232 13.1718C8.92232 13.0778 8.89832 12.9933 8.85032 12.9184C8.80232 12.8435 8.73896 12.7782 8.66024 12.7226C8.52776 12.617 8.36264 12.5373 8.16488 12.4835C7.96712 12.4278 7.76264 12.4 7.55143 12.4C7.26536 12.4 7.00808 12.4403 6.7796 12.521C6.55304 12.6016 6.37352 12.7168 6.24103 12.8666C6.10856 13.0163 6.04232 13.1939 6.04232 13.3994C6.04232 13.6778 6.14407 13.9168 6.3476 14.1165C6.55304 14.3162 6.88423 14.4678 7.3412 14.5715C7.59272 14.6291 7.77992 14.6877 7.9028 14.7472C8.02567 14.8048 8.08712 14.895 8.08712 15.0179C8.08712 15.0947 8.06215 15.161 8.01224 15.2166C7.96232 15.2723 7.89416 15.3155 7.80775 15.3462C7.72328 15.375 7.62728 15.3894 7.51976 15.3894C7.3604 15.3894 7.22888 15.3587 7.1252 15.2973C7.02152 15.2358 6.93032 15.1648 6.8516 15.0842C6.7844 15.0208 6.7124 14.9651 6.6356 14.9171C6.55879 14.8691 6.46472 14.8451 6.35335 14.8451C6.2228 14.8451 6.1268 14.8854 6.06536 14.9661C6.00392 15.0467 5.9732 15.1293 5.9732 15.2138C5.9732 15.4019 6.06344 15.5632 6.24392 15.6976C6.3764 15.807 6.5492 15.8954 6.76232 15.9626ZM10.6578 15.8877C10.7673 15.9664 10.9122 16.0058 11.0927 16.0058H11.2252C11.4076 16.0058 11.5535 15.9664 11.663 15.8877C11.7724 15.809 11.8511 15.6928 11.8991 15.5392L12.7055 12.9904C12.7132 12.9616 12.718 12.9338 12.7199 12.9069C12.7238 12.88 12.7257 12.8541 12.7257 12.8291C12.7257 12.6966 12.6825 12.593 12.5961 12.5181C12.5116 12.4432 12.4031 12.4058 12.2706 12.4058C12.1132 12.4058 12.0009 12.4461 11.9337 12.5267C11.8684 12.6054 11.8166 12.7245 11.7782 12.8838L11.159 15.2051L10.5455 12.8838C10.5071 12.7245 10.4543 12.6054 10.3871 12.5267C10.3218 12.4461 10.2095 12.4058 10.0502 12.4058C9.92153 12.4058 9.81305 12.4422 9.72472 12.5152C9.6364 12.5882 9.59225 12.6918 9.59225 12.8262C9.59225 12.8493 9.59416 12.8752 9.59801 12.904C9.60184 12.9328 9.6076 12.9616 9.61528 12.9904L10.4188 15.5392C10.4687 15.6928 10.5484 15.809 10.6578 15.8877Z" fill="#FFC53D"/>
    <path d="M15.5039 7.26562H8.90625C8.61328 7.26562 8.44922 7.11328 8.44922 6.80859V0.0820312C8.88281 0.152344 9.31641 0.457031 9.79688 0.949219L14.6602 5.91797C15.1523 6.42188 15.4453 6.83203 15.5039 7.26562Z" fill="#FFC53D"/>
    </g>
    <defs>
    <clipPath id="clip0_2931_148091">
    <rect width="15.9609" height="19.7695" fill="white"/>
    </clipPath>
    </defs>`,
    width: 16,
    height: 20,
  },
  'file-doc': {
    body: `<g clip-path="url(#clip0_2931_148089)">
    <path d="M4.2927 15.238C4.1847 15.28 4.06369 15.301 3.92969 15.301H3.63569V12.985H3.92969C4.06369 12.985 4.1847 13.007 4.2927 13.051C4.4007 13.093 4.4927 13.16 4.5687 13.252C4.6467 13.344 4.7067 13.464 4.7487 13.612C4.7907 13.758 4.8117 13.935 4.8117 14.143C4.8117 14.349 4.7907 14.526 4.7487 14.674C4.7067 14.822 4.6467 14.942 4.5687 15.034C4.4927 15.126 4.4007 15.194 4.2927 15.238Z" fill="#3E63DD"/>
    <path d="M8.36858 15.229C8.26458 15.319 8.14258 15.364 8.00258 15.364C7.86058 15.364 7.73658 15.319 7.63058 15.229C7.52457 15.137 7.44257 15.001 7.38457 14.821C7.32857 14.641 7.30057 14.419 7.30057 14.155C7.30057 13.893 7.32857 13.673 7.38457 13.495C7.44257 13.315 7.52457 13.179 7.63058 13.087C7.73658 12.995 7.86058 12.949 8.00258 12.949C8.14258 12.949 8.26458 12.995 8.36858 13.087C8.47258 13.179 8.55358 13.315 8.61158 13.495C8.66959 13.673 8.69859 13.893 8.69859 14.155C8.69859 14.419 8.66959 14.641 8.61158 14.821C8.55358 15.001 8.47258 15.137 8.36858 15.229Z" fill="#3E63DD"/>
    <path fill-rule="evenodd" clip-rule="evenodd" d="M12.6562 19.7578H2.94141C0.972656 19.7578 0 18.7734 0 16.7695V2.98828C0 0.996094 0.972656 0 2.94141 0H7.07812V6.80859C7.07812 8.05078 7.66406 8.63672 8.90625 8.63672H15.6094V16.7695C15.6094 18.7617 14.625 19.7578 12.6562 19.7578ZM4.3557 12.319C4.1937 12.297 4.0527 12.286 3.93269 12.286H3.20669C2.83868 12.286 2.65468 12.47 2.65468 12.838V15.448C2.65468 15.816 2.83868 16 3.20669 16H3.93269C4.0527 16 4.1937 15.989 4.3557 15.967C4.5177 15.945 4.6827 15.901 4.8507 15.835C5.0187 15.767 5.17471 15.666 5.31871 15.532C5.46471 15.396 5.58171 15.216 5.66971 14.992C5.75971 14.766 5.80471 14.483 5.80471 14.143C5.80471 13.803 5.75971 13.521 5.66971 13.297C5.58171 13.071 5.46471 12.891 5.31871 12.757C5.17471 12.621 5.0187 12.52 4.8507 12.454C4.6827 12.386 4.5177 12.341 4.3557 12.319ZM7.39057 15.964C7.59057 16.03 7.79458 16.063 8.00258 16.063C8.20658 16.063 8.40858 16.03 8.60858 15.964C8.81059 15.896 8.99259 15.788 9.15459 15.64C9.31859 15.492 9.44959 15.296 9.54759 15.052C9.6456 14.808 9.6946 14.509 9.6946 14.155C9.6946 13.801 9.6456 13.503 9.54759 13.261C9.44959 13.017 9.31859 12.821 9.15459 12.673C8.99259 12.525 8.81059 12.418 8.60858 12.352C8.40858 12.284 8.20658 12.25 8.00258 12.25C7.79458 12.25 7.59057 12.284 7.39057 12.352C7.19057 12.418 7.00757 12.525 6.84157 12.673C6.67757 12.821 6.54656 13.017 6.44856 13.261C6.35056 13.503 6.30156 13.801 6.30156 14.155C6.30156 14.509 6.35056 14.808 6.44856 15.052C6.54656 15.296 6.67757 15.492 6.84157 15.64C7.00757 15.788 7.19057 15.896 7.39057 15.964ZM11.4275 15.973C11.6395 16.033 11.8475 16.063 12.0515 16.063C12.2275 16.063 12.3965 16.039 12.5585 15.991C12.7225 15.941 12.8685 15.872 12.9965 15.784C13.1245 15.696 13.2255 15.594 13.2995 15.478C13.3755 15.362 13.4135 15.237 13.4135 15.103C13.4135 14.979 13.3715 14.879 13.2875 14.803C13.2055 14.725 13.1075 14.686 12.9935 14.686C12.8555 14.686 12.7585 14.717 12.7025 14.779C12.6485 14.839 12.6045 14.907 12.5705 14.983C12.5465 15.043 12.5145 15.103 12.4745 15.163C12.4365 15.221 12.3835 15.269 12.3155 15.307C12.2495 15.345 12.1615 15.364 12.0515 15.364C11.9155 15.364 11.7855 15.325 11.6615 15.247C11.5395 15.169 11.4405 15.042 11.3645 14.866C11.2885 14.688 11.2505 14.452 11.2505 14.158C11.2505 13.862 11.2885 13.626 11.3645 13.45C11.4405 13.274 11.5395 13.147 11.6615 13.069C11.7855 12.989 11.9155 12.949 12.0515 12.949C12.1615 12.949 12.2495 12.968 12.3155 13.006C12.3835 13.044 12.4365 13.093 12.4745 13.153C12.5145 13.211 12.5465 13.27 12.5705 13.33C12.6045 13.406 12.6485 13.475 12.7025 13.537C12.7585 13.597 12.8555 13.627 12.9935 13.627C13.1075 13.627 13.2055 13.589 13.2875 13.513C13.3715 13.437 13.4135 13.336 13.4135 13.21C13.4135 13.076 13.3765 12.951 13.3025 12.835C13.2285 12.719 13.1265 12.617 12.9965 12.529C12.8685 12.441 12.7235 12.373 12.5615 12.325C12.3995 12.275 12.2295 12.25 12.0515 12.25C11.8475 12.25 11.6395 12.28 11.4275 12.34C11.2175 12.4 11.0235 12.502 10.8455 12.646C10.6674 12.788 10.5244 12.983 10.4164 13.231C10.3084 13.477 10.2544 13.786 10.2544 14.158C10.2544 14.528 10.3084 14.836 10.4164 15.082C10.5244 15.328 10.6674 15.523 10.8455 15.667C11.0235 15.811 11.2175 15.913 11.4275 15.973Z" fill="#3E63DD"/>
    <path d="M15.5039 7.26562H8.90625C8.61328 7.26562 8.44922 7.11328 8.44922 6.80859V0.0820312C8.88281 0.152344 9.31641 0.457031 9.79688 0.949219L14.6602 5.91797C15.1523 6.42188 15.4453 6.83203 15.5039 7.26562Z" fill="#3E63DD"/>
    </g>
    <defs>
    <clipPath id="clip0_2931_148089">
    <rect width="15.9609" height="19.7695" fill="white"/>
    </clipPath>
    </defs>`,
    width: 16,
    height: 20,
  },
  'file-pdf': {
    body: `<g clip-path="url(#clip0_2931_148088)">
    <path d="M3.83969 14.011V12.985H4.1847C4.2547 12.985 4.3227 12.99 4.3887 13C4.4547 13.01 4.5137 13.032 4.5657 13.066C4.6197 13.1 4.6617 13.151 4.6917 13.219C4.7237 13.285 4.7397 13.374 4.7397 13.486C4.7397 13.6 4.7237 13.692 4.6917 13.762C4.6617 13.832 4.6197 13.885 4.5657 13.921C4.5137 13.955 4.4547 13.979 4.3887 13.993C4.3227 14.005 4.2547 14.011 4.1847 14.011H3.83969Z" fill="#E54666"/>
    <path d="M8.10758 15.238C7.99958 15.28 7.87858 15.301 7.74458 15.301H7.45057V12.985H7.74458C7.87858 12.985 7.99958 13.007 8.10758 13.051C8.21558 13.093 8.30758 13.16 8.38358 13.252C8.46158 13.344 8.52158 13.464 8.56358 13.612C8.60559 13.758 8.62659 13.935 8.62659 14.143C8.62659 14.349 8.60559 14.526 8.56358 14.674C8.52158 14.822 8.46158 14.942 8.38358 15.034C8.30758 15.126 8.21558 15.194 8.10758 15.238Z" fill="#E54666"/>
    <path fill-rule="evenodd" clip-rule="evenodd" d="M12.6562 19.7578H2.94141C0.972656 19.7578 0 18.7734 0 16.7695V2.98828C0 0.996094 0.972656 0 2.94141 0H7.07812V6.80859C7.07812 8.05078 7.66406 8.63672 8.90625 8.63672H15.6094V16.7695C15.6094 18.7617 14.625 19.7578 12.6562 19.7578ZM3.83969 14.71H4.3377C4.4297 14.71 4.5347 14.703 4.6527 14.689C4.7727 14.673 4.8947 14.642 5.0187 14.596C5.14471 14.55 5.26071 14.483 5.36671 14.395C5.47471 14.305 5.56071 14.186 5.62471 14.038C5.69071 13.89 5.72371 13.706 5.72371 13.486C5.72371 13.266 5.69071 13.084 5.62471 12.94C5.56071 12.794 5.47471 12.677 5.36671 12.589C5.26071 12.501 5.14471 12.436 5.0187 12.394C4.8947 12.35 4.7727 12.321 4.6527 12.307C4.5347 12.293 4.4297 12.286 4.3377 12.286H3.41069C3.04269 12.286 2.85868 12.47 2.85868 12.838V15.448C2.85868 15.816 3.02268 16 3.35069 16C3.67669 16 3.83969 15.816 3.83969 15.448V14.71ZM8.17058 12.319C8.00858 12.297 7.86758 12.286 7.74758 12.286H7.02157C6.65356 12.286 6.46956 12.47 6.46956 12.838V15.448C6.46956 15.816 6.65356 16 7.02157 16H7.74758C7.86758 16 8.00858 15.989 8.17058 15.967C8.33258 15.945 8.49758 15.901 8.66559 15.835C8.83359 15.767 8.98959 15.666 9.13359 15.532C9.27959 15.396 9.39659 15.216 9.48459 14.992C9.57459 14.766 9.61959 14.483 9.61959 14.143C9.61959 13.803 9.57459 13.521 9.48459 13.297C9.39659 13.071 9.27959 12.891 9.13359 12.757C8.98959 12.621 8.83359 12.52 8.66559 12.454C8.49758 12.386 8.33258 12.341 8.17058 12.319ZM13.1405 12.895C13.2345 12.833 13.2815 12.746 13.2815 12.634C13.2815 12.52 13.2345 12.434 13.1405 12.376C13.0465 12.316 12.9085 12.286 12.7265 12.286H11.1755C10.8095 12.286 10.6264 12.47 10.6264 12.838V15.502C10.6264 15.868 10.7894 16.051 11.1155 16.051C11.4455 16.051 11.6105 15.868 11.6105 15.502V14.788H12.5915C12.8955 14.788 13.0475 14.672 13.0475 14.44C13.0475 14.206 12.8955 14.089 12.5915 14.089H11.6105V12.985H12.7265C12.9085 12.985 13.0465 12.955 13.1405 12.895Z" fill="#E54666"/>
    <path d="M15.5039 7.26562H8.90625C8.61328 7.26562 8.44922 7.11328 8.44922 6.80859V0.0820312C8.88281 0.152344 9.31641 0.457031 9.79688 0.949219L14.6602 5.91797C15.1523 6.42188 15.4453 6.83203 15.5039 7.26562Z" fill="#E54666"/>
    </g>
    <defs>
    <clipPath id="clip0_2931_148088">
    <rect width="15.9609" height="19.7695" fill="white"/>
    </clipPath>
    </defs>`,
    width: 16,
    height: 20,
  },
  'file-ppt': {
    body: `<g clip-path="url(#clip0_2931_148086)">
    <path d="M3.83969 14.011V12.985H4.1847C4.2547 12.985 4.3227 12.99 4.3887 13C4.4547 13.01 4.5137 13.032 4.5657 13.066C4.6197 13.1 4.6617 13.151 4.6917 13.219C4.7237 13.285 4.7397 13.374 4.7397 13.486C4.7397 13.6 4.7237 13.692 4.6917 13.762C4.6617 13.832 4.6197 13.885 4.5657 13.921C4.5137 13.955 4.4547 13.979 4.3887 13.993C4.3227 14.005 4.2547 14.011 4.1847 14.011H3.83969Z" fill="#F76808"/>
    <path d="M7.65458 14.011V12.985H7.99958C8.06958 12.985 8.13758 12.99 8.20358 13C8.26958 13.01 8.32858 13.032 8.38058 13.066C8.43458 13.1 8.47658 13.151 8.50658 13.219C8.53858 13.285 8.55458 13.374 8.55458 13.486C8.55458 13.6 8.53858 13.692 8.50658 13.762C8.47658 13.832 8.43458 13.885 8.38058 13.921C8.32858 13.955 8.26958 13.979 8.20358 13.993C8.13758 14.005 8.06958 14.011 7.99958 14.011H7.65458Z" fill="#F76808"/>
    <path fill-rule="evenodd" clip-rule="evenodd" d="M12.6562 19.7578H2.94141C0.972656 19.7578 0 18.7734 0 16.7695V2.98828C0 0.996094 0.972656 0 2.94141 0H7.07812V6.80859C7.07812 8.05078 7.66406 8.63672 8.90625 8.63672H15.6094V16.7695C15.6094 18.7617 14.625 19.7578 12.6562 19.7578ZM3.83969 14.71H4.3377C4.4297 14.71 4.5347 14.703 4.6527 14.689C4.7727 14.673 4.8947 14.642 5.0187 14.596C5.14471 14.55 5.26071 14.483 5.36671 14.395C5.47471 14.305 5.56071 14.186 5.62471 14.038C5.69071 13.89 5.72371 13.706 5.72371 13.486C5.72371 13.266 5.69071 13.084 5.62471 12.94C5.56071 12.794 5.47471 12.677 5.36671 12.589C5.26071 12.501 5.14471 12.436 5.0187 12.394C4.8947 12.35 4.7727 12.321 4.6527 12.307C4.5347 12.293 4.4297 12.286 4.3377 12.286H3.41069C3.04269 12.286 2.85868 12.47 2.85868 12.838V15.448C2.85868 15.816 3.02268 16 3.35069 16C3.67669 16 3.83969 15.816 3.83969 15.448V14.71ZM7.65458 14.71H8.15258C8.24458 14.71 8.34958 14.703 8.46758 14.689C8.58758 14.673 8.70959 14.642 8.83359 14.596C8.95959 14.55 9.07559 14.483 9.18159 14.395C9.28959 14.305 9.37559 14.186 9.43959 14.038C9.50559 13.89 9.53859 13.706 9.53859 13.486C9.53859 13.266 9.50559 13.084 9.43959 12.94C9.37559 12.794 9.28959 12.677 9.18159 12.589C9.07559 12.501 8.95959 12.436 8.83359 12.394C8.70959 12.35 8.58758 12.321 8.46758 12.307C8.34958 12.293 8.24458 12.286 8.15258 12.286H7.22557C6.85757 12.286 6.67357 12.47 6.67357 12.838V15.448C6.67357 15.816 6.83757 16 7.16557 16C7.49157 16 7.65458 15.816 7.65458 15.448V14.71ZM13.4105 12.634C13.4105 12.402 13.2265 12.286 12.8585 12.286H10.7704C10.5924 12.286 10.4554 12.316 10.3594 12.376C10.2654 12.434 10.2184 12.52 10.2184 12.634C10.2184 12.746 10.2654 12.833 10.3594 12.895C10.4554 12.955 10.5924 12.985 10.7704 12.985H11.3225V15.502C11.3225 15.868 11.4855 16.051 11.8115 16.051C12.1395 16.051 12.3035 15.868 12.3035 15.502V12.985H12.9605C13.1085 12.985 13.2205 12.955 13.2965 12.895C13.3725 12.833 13.4105 12.746 13.4105 12.634Z" fill="#F76808"/>
    <path d="M15.5039 7.26562H8.90625C8.61328 7.26562 8.44922 7.11328 8.44922 6.80859V0.0820312C8.88281 0.152344 9.31641 0.457031 9.79688 0.949219L14.6602 5.91797C15.1523 6.42188 15.4453 6.83203 15.5039 7.26562Z" fill="#F76808"/>
    </g>
    <defs>
    <clipPath id="clip0_2931_148086">
    <rect width="15.9609" height="19.7695" fill="white"/>
    </clipPath>
    </defs>`,
    width: 16,
    height: 20,
  },
  'file-txt': {
    body: `<g clip-path="url(#clip0_2931_148087)">
    <path fill-rule="evenodd" clip-rule="evenodd" d="M12.6562 19.7578H2.94141C0.972656 19.7578 0 18.7734 0 16.7695V2.98828C0 0.996094 0.972656 0 2.94141 0H7.07812V6.80859C7.07812 8.05078 7.66406 8.63672 8.90625 8.63672H15.6094V16.7695C15.6094 18.7617 14.625 19.7578 12.6562 19.7578ZM5.78071 12.634C5.78071 12.402 5.59671 12.286 5.22871 12.286H3.14069C2.96268 12.286 2.82568 12.316 2.72968 12.376C2.63568 12.434 2.58868 12.52 2.58868 12.634C2.58868 12.746 2.63568 12.833 2.72968 12.895C2.82568 12.955 2.96268 12.985 3.14069 12.985H3.69269V15.502C3.69269 15.868 3.85569 16.051 4.1817 16.051C4.5097 16.051 4.6737 15.868 4.6737 15.502V12.985H5.33071C5.47871 12.985 5.59071 12.955 5.66671 12.895C5.74271 12.833 5.78071 12.746 5.78071 12.634ZM6.63757 15.952C6.71557 16.022 6.82457 16.057 6.96457 16.057C7.09657 16.057 7.19657 16.022 7.26457 15.952C7.33457 15.882 7.41057 15.782 7.49257 15.652L7.99358 14.869L8.48858 15.652C8.57058 15.78 8.64559 15.88 8.71359 15.952C8.78359 16.022 8.88659 16.057 9.02259 16.057C9.15859 16.057 9.26759 16.024 9.34959 15.958C9.43359 15.892 9.47559 15.808 9.47559 15.706C9.47559 15.656 9.46659 15.605 9.44859 15.553C9.43259 15.501 9.40159 15.44 9.35559 15.37L8.54858 14.158L9.41559 12.907C9.47959 12.817 9.51159 12.723 9.51159 12.625C9.51159 12.497 9.46959 12.403 9.38559 12.343C9.30359 12.281 9.20159 12.25 9.07959 12.25C8.95159 12.25 8.84859 12.282 8.77059 12.346C8.69259 12.408 8.60458 12.52 8.50658 12.682L8.00558 13.501L7.49257 12.682C7.42657 12.576 7.36957 12.492 7.32157 12.43C7.27357 12.366 7.22057 12.32 7.16257 12.292C7.10657 12.264 7.03357 12.25 6.94357 12.25C6.82357 12.25 6.71857 12.282 6.62856 12.346C6.53856 12.41 6.49356 12.503 6.49356 12.625C6.49356 12.669 6.49856 12.714 6.50856 12.76C6.52056 12.804 6.54456 12.852 6.58056 12.904L7.45657 14.194L6.64056 15.37C6.59656 15.436 6.56556 15.493 6.54756 15.541C6.53156 15.589 6.52356 15.638 6.52356 15.688C6.52356 15.792 6.56156 15.88 6.63757 15.952ZM13.4105 12.634C13.4105 12.402 13.2265 12.286 12.8585 12.286H10.7704C10.5924 12.286 10.4554 12.316 10.3594 12.376C10.2654 12.434 10.2184 12.52 10.2184 12.634C10.2184 12.746 10.2654 12.833 10.3594 12.895C10.4554 12.955 10.5924 12.985 10.7704 12.985H11.3225V15.502C11.3225 15.868 11.4855 16.051 11.8115 16.051C12.1395 16.051 12.3035 15.868 12.3035 15.502V12.985H12.9605C13.1085 12.985 13.2205 12.955 13.2965 12.895C13.3725 12.833 13.4105 12.746 13.4105 12.634Z" fill="#696E77"/>
    <path d="M15.5039 7.26562H8.90625C8.61328 7.26562 8.44922 7.11328 8.44922 6.80859V0.0820312C8.88281 0.152344 9.31641 0.457031 9.79688 0.949219L14.6602 5.91797C15.1523 6.42188 15.4453 6.83203 15.5039 7.26562Z" fill="#696E77"/>
    </g>
    <defs>
    <clipPath id="clip0_2931_148087">
    <rect width="15.9609" height="19.7695" fill="white"/>
    </clipPath>
    </defs>`,
    width: 16,
    height: 20,
  },
  'file-xls': {
    body: `<g clip-path="url(#clip0_2931_148085)">
    <path fill-rule="evenodd" clip-rule="evenodd" d="M12.6562 19.7578H2.94141C0.972656 19.7578 0 18.7734 0 16.7695V2.98828C0 0.996094 0.972656 0 2.94141 0H7.07812V6.80859C7.07812 8.05078 7.66406 8.63672 8.90625 8.63672H15.6094V16.7695C15.6094 18.7617 14.625 19.7578 12.6562 19.7578ZM2.82268 15.952C2.90068 16.022 3.00968 16.057 3.14969 16.057C3.28169 16.057 3.38169 16.022 3.44969 15.952C3.51969 15.882 3.59569 15.782 3.67769 15.652L4.1787 14.869L4.6737 15.652C4.7557 15.78 4.8307 15.88 4.8987 15.952C4.9687 16.022 5.07171 16.057 5.20771 16.057C5.34371 16.057 5.45271 16.024 5.53471 15.958C5.61871 15.892 5.66071 15.808 5.66071 15.706C5.66071 15.656 5.65171 15.605 5.63371 15.553C5.61771 15.501 5.58671 15.44 5.54071 15.37L4.7337 14.158L5.60071 12.907C5.66471 12.817 5.69671 12.723 5.69671 12.625C5.69671 12.497 5.65471 12.403 5.57071 12.343C5.48871 12.281 5.38671 12.25 5.26471 12.25C5.13671 12.25 5.0337 12.282 4.9557 12.346C4.8777 12.408 4.7897 12.52 4.6917 12.682L4.1907 13.501L3.67769 12.682C3.61169 12.576 3.55469 12.492 3.50669 12.43C3.45869 12.366 3.40569 12.32 3.34769 12.292C3.29169 12.264 3.21869 12.25 3.12869 12.25C3.00868 12.25 2.90368 12.282 2.81368 12.346C2.72368 12.41 2.67868 12.503 2.67868 12.625C2.67868 12.669 2.68368 12.714 2.69368 12.76C2.70568 12.804 2.72968 12.852 2.76568 12.904L3.64169 14.194L2.82568 15.37C2.78168 15.436 2.75068 15.493 2.73268 15.541C2.71668 15.589 2.70868 15.638 2.70868 15.688C2.70868 15.792 2.74668 15.88 2.82268 15.952ZM8.89059 15.301H7.90358V12.811C7.90358 12.441 7.73958 12.256 7.41157 12.256C7.08357 12.256 6.91957 12.441 6.91957 12.811V15.448C6.91957 15.816 7.10357 16 7.47157 16H8.89059C9.06859 16 9.20459 15.97 9.29859 15.91C9.39459 15.85 9.44259 15.764 9.44259 15.652C9.44259 15.54 9.39459 15.454 9.29859 15.394C9.20459 15.332 9.06859 15.301 8.89059 15.301ZM11.0465 15.961C11.2705 16.029 11.5275 16.063 11.8175 16.063C12.1335 16.063 12.4125 16.016 12.6545 15.922C12.8965 15.828 13.0855 15.695 13.2215 15.523C13.3575 15.349 13.4255 15.145 13.4255 14.911C13.4255 14.685 13.3675 14.498 13.2515 14.35C13.1355 14.202 12.9755 14.081 12.7715 13.987C12.5695 13.893 12.3365 13.814 12.0725 13.75C11.8085 13.684 11.6125 13.614 11.4845 13.54C11.3585 13.466 11.2955 13.378 11.2955 13.276C11.2955 13.21 11.3175 13.153 11.3615 13.105C11.4055 13.055 11.4675 13.017 11.5475 12.991C11.6275 12.963 11.7195 12.949 11.8235 12.949C11.9655 12.949 12.0855 12.971 12.1835 13.015C12.2815 13.059 12.3585 13.114 12.4145 13.18C12.4665 13.23 12.5335 13.284 12.6155 13.342C12.6975 13.4 12.7955 13.429 12.9095 13.429C13.0255 13.429 13.1185 13.396 13.1885 13.33C13.2605 13.262 13.2965 13.17 13.2965 13.054C13.2965 12.956 13.2715 12.868 13.2215 12.79C13.1715 12.712 13.1055 12.644 13.0235 12.586C12.8855 12.476 12.7135 12.393 12.5075 12.337C12.3015 12.279 12.0885 12.25 11.8685 12.25C11.5705 12.25 11.3025 12.292 11.0645 12.376C10.8285 12.46 10.6414 12.58 10.5034 12.736C10.3654 12.892 10.2964 13.077 10.2964 13.291C10.2964 13.581 10.4024 13.83 10.6144 14.038C10.8285 14.246 11.1735 14.404 11.6495 14.512C11.9115 14.572 12.1065 14.633 12.2345 14.695C12.3625 14.755 12.4265 14.849 12.4265 14.977C12.4265 15.057 12.4005 15.126 12.3485 15.184C12.2965 15.242 12.2255 15.287 12.1355 15.319C12.0475 15.349 11.9475 15.364 11.8355 15.364C11.6695 15.364 11.5325 15.332 11.4245 15.268C11.3165 15.204 11.2215 15.13 11.1395 15.046C11.0695 14.98 10.9945 14.922 10.9145 14.872C10.8345 14.822 10.7364 14.797 10.6204 14.797C10.4844 14.797 10.3844 14.839 10.3204 14.923C10.2564 15.007 10.2244 15.093 10.2244 15.181C10.2244 15.377 10.3184 15.545 10.5064 15.685C10.6444 15.799 10.8245 15.891 11.0465 15.961Z" fill="#29A383"/>
    <path d="M15.5039 7.26562H8.90625C8.61328 7.26562 8.44922 7.11328 8.44922 6.80859V0.0820312C8.88281 0.152344 9.31641 0.457031 9.79688 0.949219L14.6602 5.91797C15.1523 6.42188 15.4453 6.83203 15.5039 7.26562Z" fill="#29A383"/>
    </g>
    <defs>
    <clipPath id="clip0_2931_148085">
    <rect width="15.9609" height="19.7695" fill="white"/>
    </clipPath>
    </defs>`,
    width: 16,
    height: 20,
  },
  'file-zip': {
    body: `<g clip-path="url(#clip0_2931_148090)">
    <path d="M12.6562 19.7578H2.94141C0.972656 19.7578 0 18.7734 0 16.7695V2.98828C0 0.996094 0.972656 0 2.94141 0H3V2H2.5C2.22386 2 2 2.22386 2 2.5C2 2.77614 2.22386 3 2.5 3H3V4H2.5C2.22386 4 2 4.22386 2 4.5C2 4.77614 2.22386 5 2.5 5H3V6H2.5C2.22386 6 2 6.22386 2 6.5C2 6.77614 2.22386 7 2.5 7H3V8H2.5C2.22386 8 2 8.22386 2 8.5C2 8.77614 2.22386 9 2.5 9H3V10H2.5C2.22386 10 2 10.2239 2 10.5C2 10.7761 2.22386 11 2.5 11H3V12H2.5C2.22386 12 2 12.2239 2 12.5C2 12.7761 2.22386 13 2.5 13H3V14H2.75C2.33579 14 2 14.3358 2 14.75V15.5C2 16.3284 2.67157 17 3.5 17C4.32843 17 5 16.3284 5 15.5V14.75C5 14.3358 4.66421 14 4.25 14H4V13H4.5C4.77614 13 5 12.7761 5 12.5C5 12.2239 4.77614 12 4.5 12H4V11H4.5C4.77614 11 5 10.7761 5 10.5C5 10.2239 4.77614 10 4.5 10H4V9H4.5C4.77614 9 5 8.77614 5 8.5C5 8.22386 4.77614 8 4.5 8H4V7H4.5C4.77614 7 5 6.77614 5 6.5C5 6.22386 4.77614 6 4.5 6H4V5H4.5C4.77614 5 5 4.77614 5 4.5C5 4.22386 4.77614 4 4.5 4H4V3H4.5C4.77614 3 5 2.77614 5 2.5C5 2.22386 4.77614 2 4.5 2H4V0H7.07812V6.80859C7.07812 8.05078 7.66406 8.63672 8.90625 8.63672H15.6094V16.7695C15.6094 18.7617 14.625 19.7578 12.6562 19.7578Z" fill="#6958AD"/>
    <path d="M15.5039 7.26562H8.90625C8.61328 7.26562 8.44922 7.11328 8.44922 6.80859V0.0820312C8.88281 0.152344 9.31641 0.457031 9.79688 0.949219L14.6602 5.91797C15.1523 6.42188 15.4453 6.83203 15.5039 7.26562Z" fill="#6958AD"/>
    </g>
    <defs>
    <clipPath id="clip0_2931_148090">
    <rect width="15.9609" height="19.7695" fill="white"/>
    </clipPath>
    </defs>`,
    width: 16,
    height: 20,
  },
};
