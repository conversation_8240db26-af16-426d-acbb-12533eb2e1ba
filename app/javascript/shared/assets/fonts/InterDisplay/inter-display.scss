@font-face {
  font-family: 'InterDisplay';
  font-style: normal;
  font-weight: 100;
  font-display: swap;
  src: url('shared/assets/fonts/InterDisplay/InterDisplay-Thin.woff2') format('woff2');
}

@font-face {
  font-family: 'InterDisplay';
  font-style: normal;
  font-weight: 200;
  font-display: swap;
  src: url('shared/assets/fonts/InterDisplay/InterDisplay-ExtraLight.woff2') format('woff2');
}

@font-face {
  font-family: 'InterDisplay';
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url('shared/assets/fonts/InterDisplay/InterDisplay-Light.woff2') format('woff2');
}

@font-face {
  font-family: 'InterDisplay';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url('shared/assets/fonts/InterDisplay/InterDisplay-Regular.woff2') format('woff2');
}

@font-face {
  font-family: 'InterDisplay';
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url('shared/assets/fonts/InterDisplay/InterDisplay-Medium.woff2') format('woff2');
}

@font-face {
  font-family: 'InterDisplay';
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url('shared/assets/fonts/InterDisplay/InterDisplay-SemiBold.woff2') format('woff2');
}

@font-face {
  font-family: 'InterDisplay';
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url('shared/assets/fonts/InterDisplay/InterDisplay-Bold.woff2') format('woff2');
}

@font-face {
  font-family: 'InterDisplay';
  font-style: normal;
  font-weight: 800;
  font-display: swap;
  src: url('shared/assets/fonts/InterDisplay/InterDisplay-ExtraBold.woff2') format('woff2');
}

@font-face {
  font-family: 'InterDisplay';
  font-style: normal;
  font-weight: 900;
  font-display: swap;
  src: url('shared/assets/fonts/InterDisplay/InterDisplay-Black.woff2') format('woff2');
}
