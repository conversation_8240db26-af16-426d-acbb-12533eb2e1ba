[{"name": "Smileys & Emotion", "slug": "smileys_emotion", "emojis": [{"emoji": "😀", "name": "grinning face", "slug": "grinning_face"}, {"emoji": "😃", "name": "grinning face with big eyes", "slug": "grinning_face_with_big_eyes_mouth_open_smile"}, {"emoji": "😄", "name": "grinning face with smiling eyes", "slug": "grinning_face_with_smiling_eyes_mouth_open_smile"}, {"emoji": "😁", "name": "beaming face with smiling eyes", "slug": "beaming_face_with_smiling_eyes_grin_smile"}, {"emoji": "😆", "name": "grinning squinting face", "slug": "grinning_squinting_face_laugh_mouth_satisfied_smile"}, {"emoji": "😅", "name": "grinning face with sweat", "slug": "grinning_face_with_sweat_open_smile_sweating"}, {"emoji": "🤣", "name": "rolling on the floor laughing", "slug": "rolling_on_the_floor_laughing_laugh_floor_rofl"}, {"emoji": "😂", "name": "face with tears of joy", "slug": "face_with_tears_of_joy_laugh"}, {"emoji": "🙂", "name": "slightly smiling face", "slug": "slightly_smiling_face_smile"}, {"emoji": "🙃", "name": "upside-down face", "slug": "upside_down_face"}, {"emoji": "🫠", "name": "melting face", "slug": "melting_face_disappear_dissolve_melt_liquid"}, {"emoji": "😉", "name": "winking face", "slug": "winking_face"}, {"emoji": "😊", "name": "smiling face with smiling eyes", "slug": "smiling_face_with_smiling_eyes_blush"}, {"emoji": "😇", "name": "smiling face with halo", "slug": "smiling_face_with_halo_angel_fantasy_innocent"}, {"emoji": "🥰", "name": "smiling face with hearts", "slug": "smiling_face_with_hearts_adore_crush_in_love"}, {"emoji": "😍", "name": "smiling face with heart-eyes", "slug": "smiling_face_with_heart_eyes_love"}, {"emoji": "🤩", "name": "star-struck", "slug": "star_struck_starry_eyed_grinning_face"}, {"emoji": "😘", "name": "face blowing a kiss", "slug": "face_blowing_a_kiss"}, {"emoji": "😗", "name": "kissing face", "slug": "kissing_face"}, {"emoji": "😚", "name": "kissing face with closed eyes", "slug": "kissing_face_with_closed_eyes"}, {"emoji": "😙", "name": "kissing face with smiling eyes", "slug": "kissing_face_with_smiling_eyes"}, {"emoji": "🥲", "name": "smiling face with tear", "slug": "smiling_face_with_tear_grateful_proud_relieved_touched"}, {"emoji": "😋", "name": "face savoring food", "slug": "face_savoring_food_delicious_yum_tongue_savouring"}, {"emoji": "😛", "name": "face with tongue", "slug": "face_with_tongue"}, {"emoji": "😜", "name": "winking face with tongue", "slug": "winking_face_with_tongue_eye_joke_wink "}, {"emoji": "🤪", "name": "zany face", "slug": "zany_face_eye_goofy_large_small "}, {"emoji": "😝", "name": "squinting face with tongue", "slug": "squinting_face_with_tongue_eye_face_horrible_tongue"}, {"emoji": "🤑", "name": "money-mouth face", "slug": "money_mouth_face"}, {"emoji": "🤗", "name": "smiling face with open hands", "slug": "smiling_face_with_open_hands_hug_hugging_open_hands"}, {"emoji": "🤭", "name": "face with hand over mouth", "slug": "face_with_hand_over_mouth_whoops_shock_sudden_realization_surprise"}, {"emoji": "🫢", "name": "face with open eyes and hand over mouth", "slug": "face_with_open_eyes_and_hand_over_mouth_amazement_awe_disbelief_embarrass_scared_surprise"}, {"emoji": "🫣", "name": "face with peeking eye", "slug": "face_with_peeking_eye_captivated_peep_stare"}, {"emoji": "🤫", "name": "shushing face", "slug": "shushing_face_quiet_silence"}, {"emoji": "🤔", "name": "thinking face", "slug": "thinking_face"}, {"emoji": "🫡", "name": "saluting face", "slug": "saluting_face_ok_salute_sunny_troops_yes"}, {"emoji": "🤐", "name": "zipper-mouth face", "slug": "zipper_mouth_face"}, {"emoji": "🤨", "name": "face with raised eyebrow", "slug": "face_with_raised_eyebrow_distrust_skeptic_disapproval_disbelief_mild_surprise_skepticism"}, {"emoji": "😐", "name": "neutral face", "slug": "neutral_face_deadpan_meh"}, {"emoji": "😑", "name": "expressionless face", "slug": "expressionless_face_inexpressive_meh_unexpressive"}, {"emoji": "😶", "name": "face without mouth", "slug": "face_without_mouth_quiet_silent"}, {"emoji": "🫥", "name": "dotted line face", "slug": "dotted_line_face_depressed_disappear_introvert_invisible"}, {"emoji": "😶‍🌫️", "name": "face in clouds", "slug": "face_in_clouds_absentminded_fog"}, {"emoji": "😏", "name": "smirking face", "slug": "smirking_face"}, {"emoji": "😒", "name": "unamused face", "slug": "unamused_face_unhappy"}, {"emoji": "🙄", "name": "face with rolling eyes", "slug": "face_with_rolling_eyes_eyeroll"}, {"emoji": "😬", "name": "grimacing face", "slug": "grimacing_face"}, {"emoji": "😮‍💨", "name": "face exhaling", "slug": "face_exhaling_exhale_relief_whisper_whistle_groan_gasp"}, {"emoji": "🤥", "name": "lying face", "slug": "lying_face_lie_pinocchio"}, {"emoji": "😌", "name": "relieved face", "slug": "relieved_face"}, {"emoji": "😔", "name": "pensive face", "slug": "pensive_face_dejected"}, {"emoji": "😪", "name": "sleepy face", "slug": "sleepy_face_good_night"}, {"emoji": "🤤", "name": "drooling face", "slug": "drooling_face"}, {"emoji": "😴", "name": "sleeping face", "slug": "sleeping_face_zzz_good_night"}, {"emoji": "😷", "name": "face with medical mask", "slug": "face_with_medical_mask_cold_doctor_sick"}, {"emoji": "🤒", "name": "face with thermometer", "slug": "face_with_thermometer_sick"}, {"emoji": "🤕", "name": "face with head-bandage", "slug": "face_with_head_bandage_hurt_injury"}, {"emoji": "🤢", "name": "nauseated face", "slug": "nauseated_face_vomit"}, {"emoji": "🤮", "name": "face vomiting", "slug": "face_vomiting_puke"}, {"emoji": "🤧", "name": "sneezing face", "slug": "sneezing_face_gesundheit"}, {"emoji": "🥵", "name": "hot face", "slug": "hot_face_feverish_heat_stroke_red_faced_sweating"}, {"emoji": "🥶", "name": "cold face", "slug": "cold_face_blue-faced_frostbite_icicles"}, {"emoji": "🥴", "name": "woozy face", "slug": "woozy_face_dizzy_ intoxicated_tipsy_uneven_eyes_wavy_mouth"}, {"emoji": "😵", "name": "face with crossed-out eyes", "slug": "face_with_crossed_out_eyes_knocked_out"}, {"emoji": "😵‍💫", "name": "face with spiral eyes", "slug": "face_with_spiral_eyes_dizzy_hypnotized_spiral_trouble_whoa"}, {"emoji": "🤯", "name": "exploding head", "slug": "exploding_head_mind_blown_shocked"}, {"emoji": "🤠", "name": "cowboy hat face", "slug": "cowboy_hat_face_cowgirl"}, {"emoji": "🥳", "name": "partying face", "slug": "partying_face_celebration_hat_horn_party"}, {"emoji": "🥸", "name": "disguised face", "slug": "disguised_face_glasses_incognito_nose"}, {"emoji": "😎", "name": "smiling face with sunglasses", "slug": "smiling_face_with_sunglasses_bright_cool_shades"}, {"emoji": "🤓", "name": "nerd face", "slug": "nerd_face_geek"}, {"emoji": "🧐", "name": "face with monocle", "slug": "face_with_monocle_stuffy_wealthy"}, {"emoji": "😕", "name": "confused face", "slug": "confused_face_meh"}, {"emoji": "🫤", "name": "face with diagonal mouth", "slug": "face_with_diagonal_mouth_disappointed_meh_skeptical_unsure"}, {"emoji": "😟", "name": "worried face", "slug": "worried_face"}, {"emoji": "🙁", "name": "slightly frowning face", "slug": "slightly_frowning_face"}, {"emoji": "😮", "name": "face with open mouth", "slug": "face_with_open_mouth_sympathy_surprise"}, {"emoji": "😯", "name": "hushed face", "slug": "hushed_face_stunned_hushed"}, {"emoji": "😲", "name": "astonished face", "slug": "astonished_face_shocked_totally"}, {"emoji": "😳", "name": "flushed face", "slug": "flushed_face_dazed"}, {"emoji": "🥺", "name": "pleading face", "slug": "pleading_face_angry_cry_proud_resist_sad"}, {"emoji": "🥹", "name": "face holding back tears", "slug": "face_holding_back_tears_frown_open_mouth"}, {"emoji": "😦", "name": "frowning face with open mouth", "slug": "frowning_face_with_open_mouth"}, {"emoji": "😧", "name": "anguished face", "slug": "anguished_face"}, {"emoji": "😨", "name": "fearful face", "slug": "fearful_face_scared"}, {"emoji": "😰", "name": "anxious face with sweat", "slug": "anxious_face_with_sweat_cold_rushed"}, {"emoji": "😥", "name": "sad but relieved face", "slug": "sad_but_relieved_face_disappointed_whew"}, {"emoji": "😢", "name": "crying face", "slug": "crying_face_cry_sad_tear"}, {"emoji": "😭", "name": "loudly crying face", "slug": "loudly_crying_face_sad_sob_tear"}, {"emoji": "😱", "name": "face screaming in fear", "slug": "face_screaming_in_fear_munch_scared_scream"}, {"emoji": "😖", "name": "confounded face", "slug": "confounded_face"}, {"emoji": "😣", "name": "persevering face", "slug": "persevering_face"}, {"emoji": "😞", "name": "disappointed face", "slug": "disappointed_face"}, {"emoji": "😓", "name": "downcast face with sweat", "slug": "downcast_face_with_sweat_cold"}, {"emoji": "😩", "name": "weary face", "slug": "weary_face_tired"}, {"emoji": "😫", "name": "tired face", "slug": "tired_face"}, {"emoji": "🥱", "name": "yawning face", "slug": "yawning_face_bored_yawn"}, {"emoji": "😤", "name": "face with steam from nose", "slug": "face_with_steam_from_nose_triumph_won"}, {"emoji": "😡", "name": "enraged face", "slug": "enraged_face_angry_enraged_mad_red_face"}, {"emoji": "😠", "name": "angry face", "slug": "angry_face_mad"}, {"emoji": "🤬", "name": "face with symbols on mouth", "slug": "face_with_symbols_on_mouth_swearing_cursing"}, {"emoji": "😈", "name": "smiling face with horns", "slug": "smiling_face_with_horns_fantasy_fairy_tail"}, {"emoji": "👿", "name": "angry face with horns", "slug": "angry_face_with_horns_demo_devil_fantasy"}, {"emoji": "💀", "name": "skull", "slug": "skull_fairy_tail_skeleton_monster_death_face"}, {"emoji": "☠️", "name": "skull and crossbones", "slug": "skull_and_crossbones_death_pirate_monster_skeleton"}, {"emoji": "💩", "name": "pile of poo", "slug": "pile_of_poo_poop_dung_monster"}, {"emoji": "🤡", "name": "clown face", "slug": "clown_face"}, {"emoji": "👹", "name": "ogre", "slug": "ogre_fantasy_ogre_troll_creature_face"}, {"emoji": "👺", "name": "goblin", "slug": "goblin_creature_fantasy_goblin_monster_face"}, {"emoji": "👻", "name": "ghost", "slug": "ghost_face_monster_fantasy"}, {"emoji": "👽", "name": "alien", "slug": "alien_creature_extraterrestrial_ufo_fantasy_face"}, {"emoji": "👾", "name": "alien monster", "slug": "alien_monster_face_monster_creature_ufo"}, {"emoji": "🤖", "name": "robot", "slug": "robot_face_monster_robot"}, {"emoji": "😺", "name": "grinning cat", "slug": "grinning_cat_face_open_mouth_smile"}, {"emoji": "😸", "name": "grinning cat with smiling eyes", "slug": "grinning_cat_with_smiling_eyes_face"}, {"emoji": "😹", "name": "cat with tears of joy", "slug": "cat_with_tears_of_joy_face"}, {"emoji": "😻", "name": "smiling cat with heart-eyes", "slug": "smiling_cat_with_heart_eyes_love_smile"}, {"emoji": "😼", "name": "cat with wry smile", "slug": "cat_with_wry_smile_wry_ironic"}, {"emoji": "😽", "name": "kissing cat", "slug": "kissing_cat_kiss_face_eye"}, {"emoji": "🙀", "name": "weary cat", "slug": "weary_cat_surprised_face_oh"}, {"emoji": "😿", "name": "crying cat", "slug": "crying_cat_cry_sad_tear"}, {"emoji": "😾", "name": "pouting cat", "slug": "pouting_cat_face"}, {"emoji": "🙈", "name": "see-no-evil monkey", "slug": "see_no_evil_monkey_forbidden_face"}, {"emoji": "🙉", "name": "hear-no-evil monkey", "slug": "hear_no_evil_monkey_forbidden_face"}, {"emoji": "🙊", "name": "speak-no-evil monkey", "slug": "speak_no_evil_monkey_face_forbidden"}, {"emoji": "💌", "name": "love letter", "slug": "love_letter_mail"}, {"emoji": "💘", "name": "heart with arrow", "slug": "heart_with_arrow_cupid"}, {"emoji": "💝", "name": "heart with ribbon", "slug": "heart_with_ribbon_valentine"}, {"emoji": "💖", "name": "sparkling heart", "slug": "sparkling_heart_excited_love"}, {"emoji": "💗", "name": "growing heart", "slug": "growing_heart_beating_heart_heartbeat_nervous_pulse"}, {"emoji": "💓", "name": "beating heart", "slug": "beating_heart_heartbeat_pulsating"}, {"emoji": "💞", "name": "revolving hearts", "slug": "revolving_hearts"}, {"emoji": "💕", "name": "two hearts", "slug": "two_hearts_love"}, {"emoji": "💟", "name": "heart decoration", "slug": "heart_decoration"}, {"emoji": "❣️", "name": "heart exclamation", "slug": "heart_exclamation_mark_punctuation"}, {"emoji": "💔", "name": "broken heart", "slug": "broken_heart_break_broken"}, {"emoji": "❤️‍🔥", "name": "heart on fire", "slug": "heart_on_fire_burn_heart_lust_scared_heart"}, {"emoji": "❤️‍🩹", "name": "mending heart", "slug": "mending_heart_healthier_improving_recovery_recuperating_well"}, {"emoji": "❤️", "name": "red heart", "slug": "red_heart"}, {"emoji": "🧡", "name": "orange heart", "slug": "orange_heart"}, {"emoji": "💛", "name": "yellow heart", "slug": "yellow_heart"}, {"emoji": "💚", "name": "green heart", "slug": "green_heart"}, {"emoji": "💙", "name": "blue heart", "slug": "blue_heart"}, {"emoji": "💜", "name": "purple heart", "slug": "purple_heart"}, {"emoji": "🤎", "name": "brown heart", "slug": "brown_heart"}, {"emoji": "🖤", "name": "black heart", "slug": "black_heart"}, {"emoji": "🤍", "name": "white heart", "slug": "white_heart"}, {"emoji": "💋", "name": "kiss mark", "slug": "kiss_mark_lips"}, {"emoji": "💯", "name": "hundred points", "slug": "hundred_points_100_score"}, {"emoji": "💢", "name": "anger symbol", "slug": "anger_symbol_comic_mad"}, {"emoji": "💥", "name": "collision", "slug": "collision_boom_comic_explode"}, {"emoji": "💫", "name": "dizzy", "slug": "dizzy_star_comic"}, {"emoji": "💦", "name": "sweat droplets", "slug": "sweat_droplets_splashing_comic_water"}, {"emoji": "💨", "name": "dashing away", "slug": "dashing_away_running_comic"}, {"emoji": "🕳️", "name": "hole", "slug": "hole"}, {"emoji": "💬", "name": "speech balloon", "slug": "speech_balloon_dialogue_speech_comic_bubble"}, {"emoji": "👁️‍🗨️", "name": "eye in speech bubble", "slug": "eye_in_speech_bubble_witness"}, {"emoji": "🗨️", "name": "left speech bubble", "slug": "left_speech_bubble_ballon_bubble"}, {"emoji": "🗯️", "name": "right anger bubble", "slug": "right_anger_bubble_mad"}, {"emoji": "💭", "name": "thought balloon", "slug": "thought_balloon_comic_thought"}, {"emoji": "💤", "name": "ZZZ", "slug": "zzz_good_night_sleep_comic_sleeping"}]}, {"name": "People & Body", "slug": "people_body", "emojis": [{"emoji": "👋", "name": "waving hand", "slug": "waving_hand_wave_hi_hello"}, {"emoji": "🤚", "name": "raised back of hand", "slug": "raised_back_of_hand_backhand"}, {"emoji": "🖐️", "name": "hand with fingers splayed", "slug": "hand_with_fingers_splayed"}, {"emoji": "✋", "name": "raised hand", "slug": "raised_hand_high_5_high_five"}, {"emoji": "🖖", "name": "vulcan salute", "slug": "vulcan_salute_finger_spock_hand"}, {"emoji": "🫱", "name": "rightwards hand", "slug": "rightwards_hand_right"}, {"emoji": "🫲", "name": "leftwards hand", "slug": "leftwards_hand_left"}, {"emoji": "🫳", "name": "palm down hand", "slug": "palm_down_hand_dismiss_drop_shoo"}, {"emoji": "🫴", "name": "palm up hand", "slug": "palm_up_hand_catch_beckon_come_offer"}, {"emoji": "👌", "name": "OK hand", "slug": "ok_hand"}, {"emoji": "🤌", "name": "pinched fingers", "slug": "pinched_fingers_hand_gesture_interrogation_sarcastic"}, {"emoji": "🤏", "name": "pinching hand", "slug": "pinching_hand_small_amount"}, {"emoji": "✌️", "name": "victory hand", "slug": "victory_hand_v"}, {"emoji": "🤞", "name": "crossed fingers", "slug": "crossed_fingers_hand_cross_luck"}, {"emoji": "🫰", "name": "hand with index finger and thumb crossed", "slug": "hand_with_index_finger_and_thumb_crossed_expensive_heart_love_money_snap"}, {"emoji": "🤟", "name": "love-you gesture", "slug": "love_you_gesture_ILY_hand"}, {"emoji": "🤘", "name": "sign of the horns", "slug": "sign_of_the_horns_finger_hand_horns_rock-on"}, {"emoji": "🤙", "name": "call me hand", "slug": "call_me_hand_Shaka_hand_loose"}, {"emoji": "👈", "name": "backhand index pointing left", "slug": "backhand_index_pointing_left_index_point_finger_hand"}, {"emoji": "👉", "name": "backhand index pointing right", "slug": "backhand_index_pointing_right_index_point_finger_hand"}, {"emoji": "👆", "name": "backhand index pointing up", "slug": "backhand_index_pointing_up_hand_point_up_finger"}, {"emoji": "🖕", "name": "middle finger", "slug": "middle_finger_hand"}, {"emoji": "👇", "name": "backhand index pointing down", "slug": "backhand_index_pointing_down_hand_point_down_finger"}, {"emoji": "☝️", "name": "index pointing up", "slug": "index_pointing_up_hand_finger_point_up"}, {"emoji": "🫵", "name": "index pointing at the viewer", "slug": "index_pointing_at_the_viewer_you_point"}, {"emoji": "👍", "name": "thumbs up", "slug": "thumbs_up_+1_hand"}, {"emoji": "👎", "name": "thumbs down", "slug": "thumbs_down_-1_hand"}, {"emoji": "✊", "name": "raised fist", "slug": "raised_fist_hand_punch_clenched_fist"}, {"emoji": "👊", "name": "oncoming fist", "slug": "oncoming_fist_clenched_fist_hand_punch"}, {"emoji": "🤛", "name": "left-facing fist", "slug": "left_facing_fist_leftwards"}, {"emoji": "🤜", "name": "right-facing fist", "slug": "right_facing_fist_rightwards"}, {"emoji": "👏", "name": "clapping hands", "slug": "clapping_hands_clap"}, {"emoji": "🙌", "name": "raising hands", "slug": "raising_hands_celebration_gesture_hooray_raised_hands"}, {"emoji": "🫶", "name": "heart hands", "slug": "heart_hands_love"}, {"emoji": "👐", "name": "open hands", "slug": "open_hands"}, {"emoji": "🤲", "name": "palms up together", "slug": "palms_up_together_prayer_cupped_hands"}, {"emoji": "🤝", "name": "handshake", "slug": "handshake_agreement_hand_deal_handshake"}, {"emoji": "🙏", "name": "folded hands", "slug": "folded_hands_ask_hand_high_5_high_five_please_pray_thanks"}, {"emoji": "✍️", "name": "writing hand", "slug": "writing_hand_write"}, {"emoji": "💅", "name": "nail polish", "slug": "nail_polish_care_manicure_cosmetics"}, {"emoji": "🤳", "name": "selfie", "slug": "selfie_camera_phone_selfie"}, {"emoji": "💪", "name": "flexed biceps", "slug": "flexed_biceps_comic_flex_muscle_strength"}, {"emoji": "🦾", "name": "mechanical arm", "slug": "mechanical_arm"}, {"emoji": "🦿", "name": "mechanical leg", "slug": "mechanical_leg_kick"}, {"emoji": "🦵", "name": "leg", "slug": "leg_kick_limb"}, {"emoji": "🦶", "name": "foot", "slug": "foot_kick_stomp"}, {"emoji": "👂", "name": "ear", "slug": "ear_body_hearing_listen_listen_to"}, {"emoji": "🦻", "name": "ear with hearing aid", "slug": "ear_with_hearing_aid_accessibility"}, {"emoji": "👃", "name": "nose", "slug": "nose_body"}, {"emoji": "🧠", "name": "brain", "slug": "brain_intelligent"}, {"emoji": "🫀", "name": "anatomical heart", "slug": "anatomical_heart_cardiology_organs_pulse"}, {"emoji": "🫁", "name": "lungs", "slug": "lungs_organ_exhalation_inhalation_respiration"}, {"emoji": "🦷", "name": "tooth", "slug": "tooth_dentist"}, {"emoji": "🦴", "name": "bone", "slug": "bone_skeleton"}, {"emoji": "👀", "name": "eyes", "slug": "eyes_face_eye"}, {"emoji": "👁️", "name": "eye", "slug": "eye_body"}, {"emoji": "👅", "name": "tongue", "slug": "tongue_body"}, {"emoji": "👄", "name": "mouth", "slug": "mouth_lips"}, {"emoji": "🫦", "name": "biting lip", "slug": "biting_lip_anxious_fear_flirting_nervous_uncomfortable_worried"}, {"emoji": "👶", "name": "baby", "slug": "baby_young"}, {"emoji": "🧒", "name": "child", "slug": "child_gender_neutral_young_unspecified_gender"}, {"emoji": "👦", "name": "boy", "slug": "boy_young"}, {"emoji": "👧", "name": "girl", "slug": "girl_Virgo_young_zodiac"}, {"emoji": "🧑", "name": "person", "slug": "person_adult_unspecified_gender"}, {"emoji": "👱", "name": "person blond hair", "slug": "person_blond_hair"}, {"emoji": "👨", "name": "man", "slug": "man_adult_man"}, {"emoji": "🧔", "name": "person beard", "slug": "person_beard_bewhiskered"}, {"emoji": "🧔‍♂️", "name": "man beard", "slug": "man_beard"}, {"emoji": "🧔‍♀️", "name": "woman beard", "slug": "woman_beard"}, {"emoji": "👨‍🦰", "name": "man red hair", "slug": "man_red_hair"}, {"emoji": "👨‍🦱", "name": "man curly hair", "slug": "man_curly_hair"}, {"emoji": "👨‍🦳", "name": "man white hair", "slug": "man_white_hair"}, {"emoji": "👨‍🦲", "name": "man bald", "slug": "man_bald"}, {"emoji": "👩", "name": "woman", "slug": "woman_adult"}, {"emoji": "👩‍🦰", "name": "woman red hair", "slug": "woman_red_hair"}, {"emoji": "🧑‍🦰", "name": "person red hair", "slug": "person_red_hair_unspecified_gender"}, {"emoji": "👩‍🦱", "name": "woman curly hair", "slug": "woman_curly_hair"}, {"emoji": "🧑‍🦱", "name": "person curly hair", "slug": "person_curly_hair_unspecified_gender"}, {"emoji": "👩‍🦳", "name": "woman white hair", "slug": "woman_white_hair_adult"}, {"emoji": "🧑‍🦳", "name": "person white hair", "slug": "person_white_hair"}, {"emoji": "👩‍🦲", "name": "woman bald", "slug": "woman_bald"}, {"emoji": "🧑‍🦲", "name": "person bald", "slug": "person_bald"}, {"emoji": "👱‍♀️", "name": "woman blond hair", "slug": "woman_blond_hair"}, {"emoji": "👱‍♂️", "name": "man blond hair", "slug": "man_blond_hair"}, {"emoji": "🧓", "name": "older person", "slug": "older_person"}, {"emoji": "👴", "name": "old man", "slug": "old_man"}, {"emoji": "👵", "name": "old woman", "slug": "old_woman"}, {"emoji": "🙍", "name": "person frowning", "slug": "person_frowning_gesture"}, {"emoji": "🙍‍♂️", "name": "man frowning", "slug": "man_frowning_gesture"}, {"emoji": "🙍‍♀️", "name": "woman frowning", "slug": "woman_frowning_gesture"}, {"emoji": "🙎", "name": "person pouting", "slug": "person_pouting_gesture"}, {"emoji": "🙎‍♂️", "name": "man pouting", "slug": "man_pouting_gesture"}, {"emoji": "🙎‍♀️", "name": "woman pouting", "slug": "woman_pouting_gesture"}, {"emoji": "🙅", "name": "person gesturing NO", "slug": "person_gesturing_no_gesture_hand_prohibited"}, {"emoji": "🙅‍♂️", "name": "man gesturing NO", "slug": "man_gesturing_no_gesture"}, {"emoji": "🙅‍♀️", "name": "woman gesturing NO", "slug": "woman_gesturing_no_gesture_hand"}, {"emoji": "🙆", "name": "person gesturing OK", "slug": "person_gesturing_ok_gesture_hand"}, {"emoji": "🙆‍♂️", "name": "man gesturing OK", "slug": "man_gesturing_ok_gesture_hand"}, {"emoji": "🙆‍♀️", "name": "woman gesturing OK", "slug": "woman_gesturing_ok_gesture_hand"}, {"emoji": "💁", "name": "person tipping hand", "slug": "person_tipping_hand_help_information_sassy_tipping"}, {"emoji": "💁‍♂️", "name": "man tipping hand", "slug": "man_tipping_hand_sassy_tipping"}, {"emoji": "💁‍♀️", "name": "woman tipping hand", "slug": "woman_tipping_hand_sassy"}, {"emoji": "🙋", "name": "person raising hand", "slug": "person_raising_hand_gesture_hand"}, {"emoji": "🙋‍♂️", "name": "man raising hand", "slug": "man_raising_hand_gesture_hand"}, {"emoji": "🙋‍♀️", "name": "woman raising hand", "slug": "woman_raising_hand_gesture_hand"}, {"emoji": "🧏", "name": "deaf person", "slug": "deaf_person_ear_deafness_accessibility"}, {"emoji": "🧏‍♂️", "name": "deaf man", "slug": "deaf_man"}, {"emoji": "🧏‍♀️", "name": "deaf woman", "slug": "deaf_woman"}, {"emoji": "🙇", "name": "person bowing", "slug": "person_bowing_apology_gesture_blow_sorry"}, {"emoji": "🙇‍♂️", "name": "man bowing", "slug": "man_bowing_apology_gesture_favor_sorry"}, {"emoji": "🙇‍♀️", "name": "woman bowing", "slug": "woman_bowing_apology_gesture_favor_sorry"}, {"emoji": "🤦", "name": "person facepalming", "slug": "person_facepalming_disbelief_facepalm_exasperation_palm"}, {"emoji": "🤦‍♂️", "name": "man facepalming", "slug": "man_facepalming_disbelief_facepalm_exasperation_palm"}, {"emoji": "🤦‍♀️", "name": "woman facepalming", "slug": "woman_facepalming_disbelief_facepalm_exasperation_palm"}, {"emoji": "🤷", "name": "person shrugging", "slug": "person_shrugging_ignorance_indifference_shrug_doubt"}, {"emoji": "🤷‍♂️", "name": "man shrugging", "slug": "man_shrugging_ignorance_indifference_shrug_doubt"}, {"emoji": "🤷‍♀️", "name": "woman shrugging", "slug": "woman_shrugging_ignorance_indifference_shrug_doubt"}, {"emoji": "🧑‍⚕️", "name": "health worker", "slug": "health_worker_doctor_nurse_therapist"}, {"emoji": "👨‍⚕️", "name": "man health worker", "slug": "man_health_worker_doctor_nurse_therapist"}, {"emoji": "👩‍⚕️", "name": "woman health worker", "slug": "woman_health_worker_doctor_nurse_therapist"}, {"emoji": "🧑‍🎓", "name": "student", "slug": "student_graduate"}, {"emoji": "👨‍🎓", "name": "man student", "slug": "man_student_graduate"}, {"emoji": "👩‍🎓", "name": "woman student", "slug": "woman_student_graduate"}, {"emoji": "🧑‍🏫", "name": "teacher", "slug": "teacher_instructor_professor"}, {"emoji": "👨‍🏫", "name": "man teacher", "slug": "man_teacher_instructor_professor"}, {"emoji": "👩‍🏫", "name": "woman teacher", "slug": "woman_teacher_instructor_professor"}, {"emoji": "🧑‍⚖️", "name": "judge", "slug": "judge_scales_justice"}, {"emoji": "👨‍⚖️", "name": "man judge", "slug": "man_judge_scales_justice"}, {"emoji": "👩‍⚖️", "name": "woman judge", "slug": "woman_judge_scales_justice"}, {"emoji": "🧑‍🌾", "name": "farmer", "slug": "farmer_gardener_rancher"}, {"emoji": "👨‍🌾", "name": "man farmer", "slug": "man_farmer_gardener_rancher"}, {"emoji": "👩‍🌾", "name": "woman farmer", "slug": "woman_farmer_gardener_rancher"}, {"emoji": "🧑‍🍳", "name": "cook", "slug": "cook_chef"}, {"emoji": "👨‍🍳", "name": "man cook", "slug": "man_cook"}, {"emoji": "👩‍🍳", "name": "woman cook", "slug": "woman_cook"}, {"emoji": "🧑‍🔧", "name": "mechanic", "slug": "mechanic_electrician_plumber_tradesperson"}, {"emoji": "👨‍🔧", "name": "man mechanic", "slug": "man_mechanic_electrician_plumber_tradesperson"}, {"emoji": "👩‍🔧", "name": "woman mechanic", "slug": "woman_mechanic_electrician_plumber_tradesperson"}, {"emoji": "🧑‍🏭", "name": "factory worker", "slug": "factory_worker_assembly_industry"}, {"emoji": "👨‍🏭", "name": "man factory worker", "slug": "man_factory_worker_assembly_industry"}, {"emoji": "👩‍🏭", "name": "woman factory worker", "slug": "woman_factory_worker_assembly_industry"}, {"emoji": "🧑‍💼", "name": "office worker", "slug": "office_worker_business_manager_architect_white_collar"}, {"emoji": "👨‍💼", "name": "man office worker", "slug": "man_office_worker_business_manager_architect"}, {"emoji": "👩‍💼", "name": "woman office worker", "slug": "woman_office_worker_business_manager_architect"}, {"emoji": "🧑‍🔬", "name": "scientist", "slug": "scientist_biologist_researcher_engineer_chemist"}, {"emoji": "👨‍🔬", "name": "man scientist", "slug": "man_scientist_biologist_researcher_engineer_chemist"}, {"emoji": "👩‍🔬", "name": "woman scientist", "slug": "woman_scientist_biologist_researcher_engineer_chemist"}, {"emoji": "🧑‍💻", "name": "technologist", "slug": "technologist_coder_developer_programmer_engineer_software_inventor"}, {"emoji": "👨‍💻", "name": "man technologist", "slug": "man_technologist_coder_developer_programmer_engineer_software_inventor"}, {"emoji": "👩‍💻", "name": "woman technologist", "slug": "woman_technologist_coder_developer_programmer_engineer_software_inventor"}, {"emoji": "🧑‍🎤", "name": "singer", "slug": "singer_actor_entertainer_rock_star"}, {"emoji": "👨‍🎤", "name": "man singer", "slug": "man_singer_actor_entertainer_rock_star"}, {"emoji": "👩‍🎤", "name": "woman singer", "slug": "woman_singer_actress_entertainer_rock_star"}, {"emoji": "🧑‍🎨", "name": "artist", "slug": "artist"}, {"emoji": "👨‍🎨", "name": "man artist", "slug": "man_artist"}, {"emoji": "👩‍🎨", "name": "woman artist", "slug": "woman_artist"}, {"emoji": "🧑‍✈️", "name": "pilot", "slug": "pilot_plane"}, {"emoji": "👨‍✈️", "name": "man pilot", "slug": "man_pilot"}, {"emoji": "👩‍✈️", "name": "woman pilot", "slug": "woman_pilot"}, {"emoji": "🧑‍🚀", "name": "astronaut", "slug": "astronaut"}, {"emoji": "👨‍🚀", "name": "man astronaut", "slug": "man_astronaut"}, {"emoji": "👩‍🚀", "name": "woman astronaut", "slug": "woman_astronaut"}, {"emoji": "🧑‍🚒", "name": "firefighter", "slug": "firefighter"}, {"emoji": "👨‍🚒", "name": "man firefighter", "slug": "man_firefighter"}, {"emoji": "👩‍🚒", "name": "woman firefighter", "slug": "woman_firefighter"}, {"emoji": "👮", "name": "police officer", "slug": "police_officer_cop"}, {"emoji": "👮‍♂️", "name": "man police officer", "slug": "man_police_officer_cop"}, {"emoji": "👮‍♀️", "name": "woman police officer", "slug": "woman_police_officer_cop"}, {"emoji": "🕵️", "name": "detective", "slug": "detective_spy_sleuth"}, {"emoji": "🕵️‍♂️", "name": "man detective", "slug": "man_detective_spy_sleuth"}, {"emoji": "🕵️‍♀️", "name": "woman detective", "slug": "woman_detective_spy_sleuth"}, {"emoji": "💂", "name": "guard", "slug": "guard"}, {"emoji": "💂‍♂️", "name": "man guard", "slug": "man_guard"}, {"emoji": "💂‍♀️", "name": "woman guard", "slug": "woman_guard"}, {"emoji": "🥷", "name": "ninja_fighter_stealth", "slug": "ninja"}, {"emoji": "👷", "name": "construction worker", "slug": "construction_worker"}, {"emoji": "👷‍♂️", "name": "man construction worker", "slug": "man_construction_worker"}, {"emoji": "👷‍♀️", "name": "woman construction worker", "slug": "woman_construction_worker"}, {"emoji": "🫅", "name": "person with crown", "slug": "person_with_crown"}, {"emoji": "🤴", "name": "prince", "slug": "prince"}, {"emoji": "👸", "name": "princess", "slug": "princess"}, {"emoji": "👳", "name": "person wearing turban", "slug": "person_wearing_turban"}, {"emoji": "👳‍♂️", "name": "man wearing turban", "slug": "man_wearing_turban"}, {"emoji": "👳‍♀️", "name": "woman wearing turban", "slug": "woman_wearing_turban"}, {"emoji": "👲", "name": "person with skullcap", "slug": "person_with_skullcap"}, {"emoji": "🧕", "name": "woman with headscarf", "slug": "woman_with_headscarf_hijab"}, {"emoji": "🤵", "name": "person in tuxedo", "slug": "person_in_tuxedo"}, {"emoji": "🤵‍♂️", "name": "man in tuxedo", "slug": "man_in_tuxedo"}, {"emoji": "🤵‍♀️", "name": "woman in tuxedo", "slug": "woman_in_tuxedo"}, {"emoji": "👰", "name": "person with veil", "slug": "person_with_veil"}, {"emoji": "👰‍♂️", "name": "man with veil", "slug": "man_with_veil"}, {"emoji": "👰‍♀️", "name": "woman with veil", "slug": "woman_with_veil"}, {"emoji": "🤰", "name": "pregnant woman", "slug": "pregnant_woman"}, {"emoji": "🫃", "name": "pregnant man", "slug": "pregnant_man"}, {"emoji": "🫄", "name": "pregnant person", "slug": "pregnant_person"}, {"emoji": "🤱", "name": "breast-feeding", "slug": "breast_feeding"}, {"emoji": "👩‍🍼", "name": "woman feeding baby", "slug": "woman_feeding_baby"}, {"emoji": "👨‍🍼", "name": "man feeding baby", "slug": "man_feeding_baby"}, {"emoji": "🧑‍🍼", "name": "person feeding baby", "slug": "person_feeding_baby"}, {"emoji": "👼", "name": "baby angel", "slug": "baby_angel"}, {"emoji": "🎅", "name": "Santa Claus", "slug": "santa_claus_christmas_father_santa_claus"}, {"emoji": "🤶", "name": "Mrs. <PERSON>", "slug": "mrs_claus_christmas_mother_santa_claus"}, {"emoji": "🧑‍🎄", "name": "mx claus", "slug": "mx_claus"}, {"emoji": "🦸", "name": "superhero", "slug": "superhero"}, {"emoji": "🦸‍♂️", "name": "man superhero", "slug": "man_superhero"}, {"emoji": "🦸‍♀️", "name": "woman superhero", "slug": "woman_superhero"}, {"emoji": "🦹", "name": "supervillain", "slug": "supervillain_criminal_evil"}, {"emoji": "🦹‍♂️", "name": "man supervillain", "slug": "man_supervillain_criminal_evil"}, {"emoji": "🦹‍♀️", "name": "woman supervillain", "slug": "woman_supervillain_criminal_evil"}, {"emoji": "🧙", "name": "mage", "slug": "mage_sorceress_wizard"}, {"emoji": "🧙‍♂️", "name": "man mage", "slug": "man_mage_sorceress_wizard"}, {"emoji": "🧙‍♀️", "name": "woman mage", "slug": "witch"}, {"emoji": "🧚", "name": "fairy", "slug": "fairy"}, {"emoji": "🧚‍♂️", "name": "man fairy", "slug": "man_fairy"}, {"emoji": "🧚‍♀️", "name": "woman fairy", "slug": "woman_fairy"}, {"emoji": "🧛", "name": "vampire", "slug": "vampire"}, {"emoji": "🧛‍♂️", "name": "man vampire", "slug": "man_vampire"}, {"emoji": "🧛‍♀️", "name": "woman vampire", "slug": "woman_vampire"}, {"emoji": "🧜", "name": "me<PERSON><PERSON>", "slug": "me<PERSON><PERSON>"}, {"emoji": "🧜‍♂️", "name": "merman", "slug": "merman"}, {"emoji": "🧜‍♀️", "name": "mermaid", "slug": "mermaid"}, {"emoji": "🧝", "name": "elf", "slug": "elf"}, {"emoji": "🧝‍♂️", "name": "man elf", "slug": "man_elf"}, {"emoji": "🧝‍♀️", "name": "woman elf", "slug": "woman_elf"}, {"emoji": "🧞", "name": "genie", "slug": "genie"}, {"emoji": "🧞‍♂️", "name": "man genie", "slug": "man_genie"}, {"emoji": "🧞‍♀️", "name": "woman genie", "slug": "woman_genie"}, {"emoji": "🧟", "name": "zombie", "slug": "zombie"}, {"emoji": "🧟‍♂️", "name": "man zombie", "slug": "man_zombie"}, {"emoji": "🧟‍♀️", "name": "woman zombie", "slug": "woman_zombie"}, {"emoji": "🧌", "name": "troll", "slug": "troll"}, {"emoji": "💆", "name": "person getting massage", "slug": "person_getting_massage_face_massage"}, {"emoji": "💆‍♂️", "name": "man getting massage", "slug": "man_getting_massage_face_massage"}, {"emoji": "💆‍♀️", "name": "woman getting massage", "slug": "woman_getting_massage_face_massage"}, {"emoji": "💇", "name": "person getting haircut", "slug": "person_getting_haircut_hair"}, {"emoji": "💇‍♂️", "name": "man getting haircut", "slug": "man_getting_haircut_hair"}, {"emoji": "💇‍♀️", "name": "woman getting haircut", "slug": "woman_getting_haircut_hair"}, {"emoji": "🚶", "name": "person walking", "slug": "person_walking_hike"}, {"emoji": "🚶‍♂️", "name": "man walking", "slug": "man_walking_hike"}, {"emoji": "🚶‍♀️", "name": "woman walking", "slug": "woman_walking_hike"}, {"emoji": "🧍", "name": "person standing", "slug": "person_standing"}, {"emoji": "🧍‍♂️", "name": "man standing", "slug": "man_standing"}, {"emoji": "🧍‍♀️", "name": "woman standing", "slug": "woman_standing"}, {"emoji": "🧎", "name": "person kneeling", "slug": "person_kneeling"}, {"emoji": "🧎‍♂️", "name": "man kneeling", "slug": "man_kneeling"}, {"emoji": "🧎‍♀️", "name": "woman kneeling", "slug": "woman_kneeling"}, {"emoji": "🧑‍🦯", "name": "person with white cane", "slug": "person_with_white_cane_accessibility"}, {"emoji": "👨‍🦯", "name": "man with white cane", "slug": "man_with_white_cane_accessibility"}, {"emoji": "👩‍🦯", "name": "woman with white cane", "slug": "woman_with_white_cane_accessibility"}, {"emoji": "🧑‍🦼", "name": "person in motorized wheelchair", "slug": "person_in_motorized_wheelchair_accessibility"}, {"emoji": "👨‍🦼", "name": "man in motorized wheelchair", "slug": "man_in_motorized_wheelchair_accessibility"}, {"emoji": "👩‍🦼", "name": "woman in motorized wheelchair", "slug": "woman_in_motorized_wheelchair_accessibility"}, {"emoji": "🧑‍🦽", "name": "person in manual wheelchair", "slug": "person_in_manual_wheelchair_accessibility"}, {"emoji": "👨‍🦽", "name": "man in manual wheelchair", "slug": "man_in_manual_wheelchair_accessibility"}, {"emoji": "👩‍🦽", "name": "woman in manual wheelchair", "slug": "woman_in_manual_wheelchair_accessibility"}, {"emoji": "🏃", "name": "person running", "slug": "person_running"}, {"emoji": "🏃‍♂️", "name": "man running", "slug": "man_running"}, {"emoji": "🏃‍♀️", "name": "woman running", "slug": "woman_running"}, {"emoji": "💃", "name": "woman dancing", "slug": "woman_dancing"}, {"emoji": "🕺", "name": "man dancing", "slug": "man_dancing"}, {"emoji": "🕴️", "name": "person in suit levitating", "slug": "person_in_suit_levitating"}, {"emoji": "👯", "name": "people with bunny ears", "slug": "people_with_bunny_ears_dancer"}, {"emoji": "👯‍♂️", "name": "men with bunny ears", "slug": "men_with_bunny_ears_dancer"}, {"emoji": "👯‍♀️", "name": "women with bunny ears", "slug": "women_with_bunny_ears_dancer"}, {"emoji": "🧖", "name": "person in steamy room", "slug": "person_in_steamy_room"}, {"emoji": "🧖‍♂️", "name": "man in steamy room", "slug": "man_in_steamy_room"}, {"emoji": "🧖‍♀️", "name": "woman in steamy room", "slug": "woman_in_steamy_room"}, {"emoji": "🧗", "name": "person climbing", "slug": "person_climbing"}, {"emoji": "🧗‍♂️", "name": "man climbing", "slug": "man_climbing"}, {"emoji": "🧗‍♀️", "name": "woman climbing", "slug": "woman_climbing"}, {"emoji": "🤺", "name": "person fencing", "slug": "person_fencing"}, {"emoji": "🏇", "name": "horse racing", "slug": "horse_racing"}, {"emoji": "⛷️", "name": "skier", "slug": "skier"}, {"emoji": "🏂", "name": "snowboarder", "slug": "snowboarder"}, {"emoji": "🏌️", "name": "person golfing", "slug": "person_golfing"}, {"emoji": "🏌️‍♂️", "name": "man golfing", "slug": "man_golfing"}, {"emoji": "🏌️‍♀️", "name": "woman golfing", "slug": "woman_golfing"}, {"emoji": "🏄", "name": "person surfing", "slug": "person_surfing"}, {"emoji": "🏄‍♂️", "name": "man surfing", "slug": "man_surfing"}, {"emoji": "🏄‍♀️", "name": "woman surfing", "slug": "woman_surfing"}, {"emoji": "🚣", "name": "person rowing boat", "slug": "person_rowing_boat"}, {"emoji": "🚣‍♂️", "name": "man rowing boat", "slug": "man_rowing_boat"}, {"emoji": "🚣‍♀️", "name": "woman rowing boat", "slug": "woman_rowing_boat"}, {"emoji": "🏊", "name": "person swimming", "slug": "person_swimming"}, {"emoji": "🏊‍♂️", "name": "man swimming", "slug": "man_swimming"}, {"emoji": "🏊‍♀️", "name": "woman swimming", "slug": "woman_swimming"}, {"emoji": "⛹️", "name": "person bouncing ball", "slug": "person_bouncing_ball"}, {"emoji": "⛹️‍♂️", "name": "man bouncing ball", "slug": "man_bouncing_ball"}, {"emoji": "⛹️‍♀️", "name": "woman bouncing ball", "slug": "woman_bouncing_ball"}, {"emoji": "🏋️", "name": "person lifting weights", "slug": "person_lifting_weights"}, {"emoji": "🏋️‍♂️", "name": "man lifting weights", "slug": "man_lifting_weights"}, {"emoji": "🏋️‍♀️", "name": "woman lifting weights", "slug": "woman_lifting_weights"}, {"emoji": "🚴", "name": "person biking", "slug": "person_biking"}, {"emoji": "🚴‍♂️", "name": "man biking", "slug": "man_biking"}, {"emoji": "🚴‍♀️", "name": "woman biking", "slug": "woman_biking"}, {"emoji": "🚵", "name": "person mountain biking", "slug": "person_mountain_biking"}, {"emoji": "🚵‍♂️", "name": "man mountain biking", "slug": "man_mountain_biking"}, {"emoji": "🚵‍♀️", "name": "woman mountain biking", "slug": "woman_mountain_biking"}, {"emoji": "🤸", "name": "person cartwheeling", "slug": "person_cartwheeling"}, {"emoji": "🤸‍♂️", "name": "man cartwheeling", "slug": "man_cartwheeling"}, {"emoji": "🤸‍♀️", "name": "woman cartwheeling", "slug": "woman_cartwheeling"}, {"emoji": "🤼", "name": "people wrestling", "slug": "people_wrestling"}, {"emoji": "🤼‍♂️", "name": "men wrestling", "slug": "men_wrestling"}, {"emoji": "🤼‍♀️", "name": "women wrestling", "slug": "women_wrestling"}, {"emoji": "🤽", "name": "person playing water polo", "slug": "person_playing_water_polo"}, {"emoji": "🤽‍♂️", "name": "man playing water polo", "slug": "man_playing_water_polo"}, {"emoji": "🤽‍♀️", "name": "woman playing water polo", "slug": "woman_playing_water_polo"}, {"emoji": "🤾", "name": "person playing handball", "slug": "person_playing_handball"}, {"emoji": "🤾‍♂️", "name": "man playing handball", "slug": "man_playing_handball"}, {"emoji": "🤾‍♀️", "name": "woman playing handball", "slug": "woman_playing_handball"}, {"emoji": "🤹", "name": "person juggling", "slug": "person_juggling"}, {"emoji": "🤹‍♂️", "name": "man juggling", "slug": "man_juggling"}, {"emoji": "🤹‍♀️", "name": "woman juggling", "slug": "woman_juggling"}, {"emoji": "🧘", "name": "person in lotus position", "slug": "person_in_lotus_position"}, {"emoji": "🧘‍♂️", "name": "man in lotus position", "slug": "man_in_lotus_position"}, {"emoji": "🧘‍♀️", "name": "woman in lotus position", "slug": "woman_in_lotus_position"}, {"emoji": "🛀", "name": "person taking bath", "slug": "person_taking_bath"}, {"emoji": "🛌", "name": "person in bed", "slug": "person_in_bed"}, {"emoji": "🧑‍🤝‍🧑", "name": "people holding hands", "slug": "people_holding_hands"}, {"emoji": "👭", "name": "women holding hands", "slug": "women_holding_hands"}, {"emoji": "👫", "name": "woman and man holding hands", "slug": "woman_and_man_holding_hands"}, {"emoji": "👬", "name": "men holding hands", "slug": "men_holding_hands"}, {"emoji": "💏", "name": "kiss", "slug": "kiss"}, {"emoji": "👩‍❤️‍💋‍👨", "name": "kiss woman, man", "slug": "kiss_woman_man"}, {"emoji": "👨‍❤️‍💋‍👨", "name": "kiss man, man", "slug": "kiss_man_man"}, {"emoji": "👩‍❤️‍💋‍👩", "name": "kiss woman, woman", "slug": "kiss_woman_woman"}, {"emoji": "💑", "name": "couple with heart", "slug": "couple_with_heart"}, {"emoji": "👩‍❤️‍👨", "name": "couple with heart woman, man", "slug": "couple_with_heart_woman_man"}, {"emoji": "👨‍❤️‍👨", "name": "couple with heart man, man", "slug": "couple_with_heart_man_man"}, {"emoji": "👩‍❤️‍👩", "name": "couple with heart woman, woman", "slug": "couple_with_heart_woman_woman"}, {"emoji": "👪", "name": "family", "slug": "family"}, {"emoji": "👨‍👩‍👦", "name": "family man, woman, boy", "slug": "family_man_woman_boy"}, {"emoji": "👨‍👩‍👧", "name": "family man, woman, girl", "slug": "family_man_woman_girl"}, {"emoji": "👨‍👩‍👧‍👦", "name": "family man, woman, girl, boy", "slug": "family_man_woman_girl_boy"}, {"emoji": "👨‍👩‍👦‍👦", "name": "family man, woman, boy, boy", "slug": "family_man_woman_boy_boy"}, {"emoji": "👨‍👩‍👧‍👧", "name": "family man, woman, girl, girl", "slug": "family_man_woman_girl_girl"}, {"emoji": "👨‍👨‍👦", "name": "family man, man, boy", "slug": "family_man_man_boy"}, {"emoji": "👨‍👨‍👧", "name": "family man, man, girl", "slug": "family_man_man_girl"}, {"emoji": "👨‍👨‍👧‍👦", "name": "family man, man, girl, boy", "slug": "family_man_man_girl_boy"}, {"emoji": "👨‍👨‍👦‍👦", "name": "family man, man, boy, boy", "slug": "family_man_man_boy_boy"}, {"emoji": "👨‍👨‍👧‍👧", "name": "family man, man, girl, girl", "slug": "family_man_man_girl_girl"}, {"emoji": "👩‍👩‍👦", "name": "family woman, woman, boy", "slug": "family_woman_woman_boy"}, {"emoji": "👩‍👩‍👧", "name": "family woman, woman, girl", "slug": "family_woman_woman_girl"}, {"emoji": "👩‍👩‍👧‍👦", "name": "family woman, woman, girl, boy", "slug": "family_woman_woman_girl_boy"}, {"emoji": "👩‍👩‍👦‍👦", "name": "family woman, woman, boy, boy", "slug": "family_woman_woman_boy_boy"}, {"emoji": "👩‍👩‍👧‍👧", "name": "family woman, woman, girl, girl", "slug": "family_woman_woman_girl_girl"}, {"emoji": "👨‍👦", "name": "family man, boy", "slug": "family_man_boy"}, {"emoji": "👨‍👦‍👦", "name": "family man, boy, boy", "slug": "family_man_boy_boy"}, {"emoji": "👨‍👧", "name": "family man, girl", "slug": "family_man_girl"}, {"emoji": "👨‍👧‍👦", "name": "family man, girl, boy", "slug": "family_man_girl_boy"}, {"emoji": "👨‍👧‍👧", "name": "family man, girl, girl", "slug": "family_man_girl_girl"}, {"emoji": "👩‍👦", "name": "family woman, boy", "slug": "family_woman_boy"}, {"emoji": "👩‍👦‍👦", "name": "family woman, boy, boy", "slug": "family_woman_boy_boy"}, {"emoji": "👩‍👧", "name": "family woman, girl", "slug": "family_woman_girl"}, {"emoji": "👩‍👧‍👦", "name": "family woman, girl, boy", "slug": "family_woman_girl_boy"}, {"emoji": "👩‍👧‍👧", "name": "family woman, girl, girl", "slug": "family_woman_girl_girl"}, {"emoji": "🗣️", "name": "speaking head", "slug": "speaking_head"}, {"emoji": "👤", "name": "bust in silhouette", "slug": "bust_in_silhouette"}, {"emoji": "👥", "name": "busts in silhouette", "slug": "busts_in_silhouette"}, {"emoji": "🫂", "name": "people hugging", "slug": "people_hugging"}, {"emoji": "👣", "name": "footprints", "slug": "footprints"}]}, {"name": "Animals & Nature", "slug": "animals_nature", "emojis": [{"emoji": "🐵", "name": "monkey face", "slug": "monkey_face"}, {"emoji": "🐒", "name": "monkey", "slug": "monkey"}, {"emoji": "🦍", "name": "gorilla", "slug": "gorilla"}, {"emoji": "🦧", "name": "orangutan", "slug": "orangutan"}, {"emoji": "🐶", "name": "dog face", "slug": "dog_face"}, {"emoji": "🐕", "name": "dog", "slug": "dog"}, {"emoji": "🦮", "name": "guide dog", "slug": "guide_dog"}, {"emoji": "🐕‍🦺", "name": "service dog", "slug": "service_dog"}, {"emoji": "🐩", "name": "poodle", "slug": "poodle"}, {"emoji": "🐺", "name": "wolf", "slug": "wolf"}, {"emoji": "🦊", "name": "fox", "slug": "fox"}, {"emoji": "🦝", "name": "raccoon", "slug": "raccoon"}, {"emoji": "🐱", "name": "cat face", "slug": "cat_face"}, {"emoji": "🐈", "name": "cat", "slug": "cat"}, {"emoji": "🐈‍⬛", "name": "black cat", "slug": "black_cat"}, {"emoji": "🦁", "name": "lion", "slug": "lion"}, {"emoji": "🐯", "name": "tiger face", "slug": "tiger_face"}, {"emoji": "🐅", "name": "tiger", "slug": "tiger"}, {"emoji": "🐆", "name": "leopard", "slug": "leopard"}, {"emoji": "🐴", "name": "horse face", "slug": "horse_face"}, {"emoji": "🐎", "name": "horse", "slug": "horse"}, {"emoji": "🦄", "name": "unicorn", "slug": "unicorn"}, {"emoji": "🦓", "name": "zebra", "slug": "zebra"}, {"emoji": "🦌", "name": "deer", "slug": "deer"}, {"emoji": "🦬", "name": "bison", "slug": "bison"}, {"emoji": "🐮", "name": "cow face", "slug": "cow_face"}, {"emoji": "🐂", "name": "ox", "slug": "ox"}, {"emoji": "🐃", "name": "water buffalo", "slug": "water_buffalo"}, {"emoji": "🐄", "name": "cow", "slug": "cow"}, {"emoji": "🐷", "name": "pig face", "slug": "pig_face"}, {"emoji": "🐖", "name": "pig", "slug": "pig"}, {"emoji": "🐗", "name": "boar", "slug": "boar"}, {"emoji": "🐽", "name": "pig nose", "slug": "pig_nose"}, {"emoji": "🐏", "name": "ram", "slug": "ram"}, {"emoji": "🐑", "name": "ewe", "slug": "ewe"}, {"emoji": "🐐", "name": "goat", "slug": "goat"}, {"emoji": "🐪", "name": "camel", "slug": "camel"}, {"emoji": "🐫", "name": "two-hump camel", "slug": "two_hump_camel"}, {"emoji": "🦙", "name": "llama", "slug": "llama"}, {"emoji": "🦒", "name": "giraffe", "slug": "giraffe"}, {"emoji": "🐘", "name": "elephant", "slug": "elephant"}, {"emoji": "🦣", "name": "mammoth", "slug": "mammoth"}, {"emoji": "🦏", "name": "rhinoceros", "slug": "rhinoceros"}, {"emoji": "🦛", "name": "hippopotamus", "slug": "hippopotamus"}, {"emoji": "🐭", "name": "mouse face", "slug": "mouse_face"}, {"emoji": "🐁", "name": "mouse", "slug": "mouse"}, {"emoji": "🐀", "name": "rat", "slug": "rat"}, {"emoji": "🐹", "name": "hamster", "slug": "hamster"}, {"emoji": "🐰", "name": "rabbit face", "slug": "rabbit_face"}, {"emoji": "🐇", "name": "rabbit", "slug": "rabbit"}, {"emoji": "🐿️", "name": "chipmunk", "slug": "chipmunk"}, {"emoji": "🦫", "name": "beaver", "slug": "beaver"}, {"emoji": "🦔", "name": "hedgehog", "slug": "hedgehog"}, {"emoji": "🦇", "name": "bat", "slug": "bat"}, {"emoji": "🐻", "name": "bear", "slug": "bear"}, {"emoji": "🐻‍❄️", "name": "polar bear", "slug": "polar_bear"}, {"emoji": "🐨", "name": "koala", "slug": "koala"}, {"emoji": "🐼", "name": "panda", "slug": "panda"}, {"emoji": "🦥", "name": "sloth", "slug": "sloth"}, {"emoji": "🦦", "name": "otter", "slug": "otter"}, {"emoji": "🦨", "name": "skunk", "slug": "skunk"}, {"emoji": "🦘", "name": "kangaroo", "slug": "kangaroo"}, {"emoji": "🦡", "name": "badger", "slug": "badger"}, {"emoji": "🐾", "name": "paw prints", "slug": "paw_prints"}, {"emoji": "🦃", "name": "turkey", "slug": "turkey"}, {"emoji": "🐔", "name": "chicken", "slug": "chicken"}, {"emoji": "🐓", "name": "rooster", "slug": "rooster"}, {"emoji": "🐣", "name": "hatching chick", "slug": "hatching_chick"}, {"emoji": "🐤", "name": "baby chick", "slug": "baby_chick"}, {"emoji": "🐥", "name": "front-facing baby chick", "slug": "front_facing_baby_chick"}, {"emoji": "🐦", "name": "bird", "slug": "bird"}, {"emoji": "🐧", "name": "penguin", "slug": "penguin"}, {"emoji": "🕊️", "name": "dove", "slug": "dove"}, {"emoji": "🦅", "name": "eagle", "slug": "eagle"}, {"emoji": "🦆", "name": "duck", "slug": "duck"}, {"emoji": "🦢", "name": "swan", "slug": "swan"}, {"emoji": "🦉", "name": "owl", "slug": "owl"}, {"emoji": "🦤", "name": "dodo", "slug": "dodo"}, {"emoji": "🪶", "name": "feather", "slug": "feather"}, {"emoji": "🦩", "name": "flamingo", "slug": "flamingo"}, {"emoji": "🦚", "name": "peacock", "slug": "peacock"}, {"emoji": "🦜", "name": "parrot", "slug": "parrot"}, {"emoji": "🐸", "name": "frog", "slug": "frog"}, {"emoji": "🐊", "name": "crocodile", "slug": "crocodile"}, {"emoji": "🐢", "name": "turtle", "slug": "turtle"}, {"emoji": "🦎", "name": "lizard", "slug": "lizard"}, {"emoji": "🐍", "name": "snake", "slug": "snake"}, {"emoji": "🐲", "name": "dragon face", "slug": "dragon_face"}, {"emoji": "🐉", "name": "dragon", "slug": "dragon"}, {"emoji": "🦕", "name": "sauropod", "slug": "sauropod"}, {"emoji": "🦖", "name": "T-Rex", "slug": "t_rex"}, {"emoji": "🐳", "name": "spouting whale", "slug": "spouting_whale"}, {"emoji": "🐋", "name": "whale", "slug": "whale"}, {"emoji": "🐬", "name": "dolphin", "slug": "dolphin"}, {"emoji": "🦭", "name": "seal", "slug": "seal"}, {"emoji": "🐟", "name": "fish", "slug": "fish"}, {"emoji": "🐠", "name": "tropical fish", "slug": "tropical_fish"}, {"emoji": "🐡", "name": "blowfish", "slug": "blowfish"}, {"emoji": "🦈", "name": "shark", "slug": "shark"}, {"emoji": "🐙", "name": "octopus", "slug": "octopus"}, {"emoji": "🐚", "name": "spiral shell", "slug": "spiral_shell"}, {"emoji": "🪸", "name": "coral", "slug": "coral"}, {"emoji": "🐌", "name": "snail", "slug": "snail"}, {"emoji": "🦋", "name": "butterfly", "slug": "butterfly"}, {"emoji": "🐛", "name": "bug", "slug": "bug"}, {"emoji": "🐜", "name": "ant", "slug": "ant"}, {"emoji": "🐝", "name": "honeybee", "slug": "honeybee"}, {"emoji": "🪲", "name": "beetle", "slug": "beetle"}, {"emoji": "🐞", "name": "lady beetle", "slug": "lady_beetle"}, {"emoji": "🦗", "name": "cricket", "slug": "cricket"}, {"emoji": "🪳", "name": "cockroach", "slug": "cockroach"}, {"emoji": "🕷️", "name": "spider", "slug": "spider"}, {"emoji": "🕸️", "name": "spider web", "slug": "spider_web"}, {"emoji": "🦂", "name": "scorpion", "slug": "scorpion"}, {"emoji": "🦟", "name": "mosquito", "slug": "mosquito"}, {"emoji": "🪰", "name": "fly", "slug": "fly"}, {"emoji": "🪱", "name": "worm", "slug": "worm"}, {"emoji": "🦠", "name": "microbe", "slug": "microbe"}, {"emoji": "💐", "name": "bouquet", "slug": "bouquet"}, {"emoji": "🌸", "name": "cherry blossom", "slug": "cherry_blossom"}, {"emoji": "💮", "name": "white flower", "slug": "white_flower"}, {"emoji": "🪷", "name": "lotus", "slug": "lotus"}, {"emoji": "🏵️", "name": "rosette", "slug": "rosette"}, {"emoji": "🌹", "name": "rose", "slug": "rose"}, {"emoji": "🥀", "name": "wilted flower", "slug": "wilted_flower"}, {"emoji": "🌺", "name": "hibiscus", "slug": "hibiscus"}, {"emoji": "🌻", "name": "sunflower", "slug": "sunflower"}, {"emoji": "🌼", "name": "blossom", "slug": "blossom"}, {"emoji": "🌷", "name": "tulip", "slug": "tulip"}, {"emoji": "🌱", "name": "seedling", "slug": "seedling"}, {"emoji": "🪴", "name": "potted plant", "slug": "potted_plant"}, {"emoji": "🌲", "name": "evergreen tree", "slug": "evergreen_tree"}, {"emoji": "🌳", "name": "deciduous tree", "slug": "deciduous_tree"}, {"emoji": "🌴", "name": "palm tree", "slug": "palm_tree"}, {"emoji": "🌵", "name": "cactus", "slug": "cactus"}, {"emoji": "🌾", "name": "sheaf of rice", "slug": "sheaf_of_rice"}, {"emoji": "🌿", "name": "herb", "slug": "herb"}, {"emoji": "☘️", "name": "shamrock", "slug": "shamrock"}, {"emoji": "🍀", "name": "four leaf clover", "slug": "four_leaf_clover"}, {"emoji": "🍁", "name": "maple leaf", "slug": "maple_leaf"}, {"emoji": "🍂", "name": "fallen leaf", "slug": "fallen_leaf"}, {"emoji": "🍃", "name": "leaf fluttering in wind", "slug": "leaf_fluttering_in_wind"}, {"emoji": "🪹", "name": "empty nest", "slug": "empty_nest"}, {"emoji": "🪺", "name": "nest with eggs", "slug": "nest_with_eggs"}, {"emoji": "🍄", "name": "mushroom", "slug": "mushroom"}]}, {"name": "Food & Drink", "slug": "food_drink", "emojis": [{"emoji": "🍇", "name": "grapes", "slug": "grapes_fruit"}, {"emoji": "🍈", "name": "melon", "slug": "melon_fruit"}, {"emoji": "🍉", "name": "watermelon", "slug": "watermelon_fruit"}, {"emoji": "🍊", "name": "tangerine", "slug": "tangerine_fruit"}, {"emoji": "🍋", "name": "lemon", "slug": "lemon_fruit"}, {"emoji": "🍌", "name": "banana", "slug": "banana_fruit"}, {"emoji": "🍍", "name": "pineapple", "slug": "pineapple_fruit"}, {"emoji": "🥭", "name": "mango", "slug": "mango_fruit"}, {"emoji": "🍎", "name": "red apple", "slug": "red_apple"}, {"emoji": "🍏", "name": "green apple", "slug": "green_apple"}, {"emoji": "🍐", "name": "pear", "slug": "pear_fruit"}, {"emoji": "🍑", "name": "peach", "slug": "peach_fruit"}, {"emoji": "🍒", "name": "cherries", "slug": "cherries_fruit"}, {"emoji": "🍓", "name": "strawberry", "slug": "strawberry_fruit"}, {"emoji": "🫐", "name": "blueberries", "slug": "blueberries_blue"}, {"emoji": "🥝", "name": "kiwi fruit", "slug": "kiwi_fruit_fruit"}, {"emoji": "🍅", "name": "tomato", "slug": "tomato_fruit"}, {"emoji": "🫒", "name": "olive", "slug": "olive"}, {"emoji": "🥥", "name": "coconut", "slug": "coconut_palm"}, {"emoji": "🥑", "name": "avocado", "slug": "avocado_fruit"}, {"emoji": "🍆", "name": "eggplant", "slug": "eggplant"}, {"emoji": "🥔", "name": "potato", "slug": "potato"}, {"emoji": "🥕", "name": "carrot", "slug": "carrot"}, {"emoji": "🌽", "name": "ear of corn", "slug": "ear_of_corn"}, {"emoji": "🌶️", "name": "hot pepper", "slug": "hot_pepper"}, {"emoji": "🫑", "name": "bell pepper", "slug": "bell_pepper"}, {"emoji": "🥒", "name": "cucumber", "slug": "cucumber"}, {"emoji": "🥬", "name": "leafy green", "slug": "leafy_green"}, {"emoji": "🥦", "name": "broccoli", "slug": "broccoli"}, {"emoji": "🧄", "name": "garlic", "slug": "garlic"}, {"emoji": "🧅", "name": "onion", "slug": "onion"}, {"emoji": "🥜", "name": "peanuts", "slug": "peanuts"}, {"emoji": "🫘", "name": "beans", "slug": "beans"}, {"emoji": "🌰", "name": "chestnut", "slug": "chestnut"}, {"emoji": "🍞", "name": "bread", "slug": "bread_loaf"}, {"emoji": "🥐", "name": "croissant", "slug": "croissant_breakfast"}, {"emoji": "🥖", "name": "baguette bread", "slug": "baguette_bread"}, {"emoji": "🫓", "name": "flatbread", "slug": "flatbread"}, {"emoji": "🥨", "name": "pretzel", "slug": "pretzel"}, {"emoji": "🥯", "name": "bagel", "slug": "bagel"}, {"emoji": "🥞", "name": "pancakes", "slug": "pancakes"}, {"emoji": "🧇", "name": "waffle", "slug": "waffle"}, {"emoji": "🧀", "name": "cheese wedge", "slug": "cheese_wedge"}, {"emoji": "🍖", "name": "meat on bone", "slug": "meat_on_bone"}, {"emoji": "🍗", "name": "poultry leg", "slug": "poultry_leg_chicken"}, {"emoji": "🥩", "name": "cut of meat", "slug": "cut_of_meat"}, {"emoji": "🥓", "name": "bacon", "slug": "bacon"}, {"emoji": "🍔", "name": "hamburger", "slug": "hamburger"}, {"emoji": "🍟", "name": "french fries", "slug": "french_fries"}, {"emoji": "🍕", "name": "pizza", "slug": "pizza"}, {"emoji": "🌭", "name": "hot dog", "slug": "hot_dog"}, {"emoji": "🥪", "name": "sandwich", "slug": "sandwich"}, {"emoji": "🌮", "name": "taco", "slug": "taco"}, {"emoji": "🌯", "name": "burrito", "slug": "burrito"}, {"emoji": "🫔", "name": "tamale", "slug": "tamale"}, {"emoji": "🥙", "name": "stuffed flatbread", "slug": "stuffed_flatbread"}, {"emoji": "🧆", "name": "falafel", "slug": "falafel"}, {"emoji": "🥚", "name": "egg", "slug": "egg"}, {"emoji": "🍳", "name": "cooking", "slug": "cooking"}, {"emoji": "🥘", "name": "shallow pan of food", "slug": "shallow_pan_of_food"}, {"emoji": "🍲", "name": "pot of food", "slug": "pot_of_food"}, {"emoji": "🫕", "name": "fondue", "slug": "fondue"}, {"emoji": "🥣", "name": "bowl with spoon", "slug": "bowl_with_spoon"}, {"emoji": "🥗", "name": "green salad", "slug": "green_salad"}, {"emoji": "🍿", "name": "popcorn", "slug": "popcorn"}, {"emoji": "🧈", "name": "butter", "slug": "butter"}, {"emoji": "🧂", "name": "salt", "slug": "salt"}, {"emoji": "🥫", "name": "canned food", "slug": "canned_food"}, {"emoji": "🍱", "name": "bento box", "slug": "bento_box"}, {"emoji": "🍘", "name": "rice cracker", "slug": "rice_cracker"}, {"emoji": "🍙", "name": "rice ball", "slug": "rice_ball"}, {"emoji": "🍚", "name": "cooked rice", "slug": "cooked_rice"}, {"emoji": "🍛", "name": "curry rice", "slug": "curry_rice"}, {"emoji": "🍜", "name": "steaming bowl", "slug": "steaming_bowl"}, {"emoji": "🍝", "name": "spaghetti", "slug": "spaghetti"}, {"emoji": "🍠", "name": "roasted sweet potato", "slug": "roasted_sweet_potato"}, {"emoji": "🍢", "name": "oden", "slug": "oden"}, {"emoji": "🍣", "name": "sushi", "slug": "sushi"}, {"emoji": "🍤", "name": "fried shrimp", "slug": "fried_shrimp"}, {"emoji": "🍥", "name": "fish cake with swirl", "slug": "fish_cake_with_swirl"}, {"emoji": "🥮", "name": "moon cake", "slug": "moon_cake"}, {"emoji": "🍡", "name": "dango", "slug": "dango"}, {"emoji": "🥟", "name": "dumpling", "slug": "dumpling"}, {"emoji": "🥠", "name": "fortune cookie", "slug": "fortune_cookie"}, {"emoji": "🥡", "name": "takeout box", "slug": "takeout_box"}, {"emoji": "🦀", "name": "crab", "slug": "crab"}, {"emoji": "🦞", "name": "lobster", "slug": "lobster"}, {"emoji": "🦐", "name": "shrimp", "slug": "shrimp"}, {"emoji": "🦑", "name": "squid", "slug": "squid"}, {"emoji": "🦪", "name": "oyster", "slug": "oyster"}, {"emoji": "🍦", "name": "soft ice cream", "slug": "soft_ice_cream"}, {"emoji": "🍧", "name": "shaved ice", "slug": "shaved_ice"}, {"emoji": "🍨", "name": "ice cream", "slug": "ice_cream"}, {"emoji": "🍩", "name": "doughnut", "slug": "doughnut"}, {"emoji": "🍪", "name": "cookie", "slug": "cookie"}, {"emoji": "🎂", "name": "birthday cake", "slug": "birthday_cake"}, {"emoji": "🍰", "name": "shortcake", "slug": "shortcake"}, {"emoji": "🧁", "name": "cupcake", "slug": "cupcake"}, {"emoji": "🥧", "name": "pie", "slug": "pie"}, {"emoji": "🍫", "name": "chocolate bar", "slug": "chocolate_bar"}, {"emoji": "🍬", "name": "candy", "slug": "candy"}, {"emoji": "🍭", "name": "lollipop", "slug": "lollipop"}, {"emoji": "🍮", "name": "custard", "slug": "custard"}, {"emoji": "🍯", "name": "honey pot", "slug": "honey_pot"}, {"emoji": "🍼", "name": "baby bottle", "slug": "baby_bottle"}, {"emoji": "🥛", "name": "glass of milk", "slug": "glass_of_milk"}, {"emoji": "☕", "name": "hot beverage", "slug": "hot_beverage"}, {"emoji": "🫖", "name": "teapot", "slug": "teapot"}, {"emoji": "🍵", "name": "teacup without handle", "slug": "teacup_without_handle"}, {"emoji": "🍶", "name": "sake", "slug": "sake"}, {"emoji": "🍾", "name": "bottle with popping cork", "slug": "bottle_with_popping_cork"}, {"emoji": "🍷", "name": "wine glass", "slug": "wine_glass"}, {"emoji": "🍸", "name": "cocktail glass", "slug": "cocktail_glass"}, {"emoji": "🍹", "name": "tropical drink", "slug": "tropical_drink"}, {"emoji": "🍺", "name": "beer mug", "slug": "beer_mug"}, {"emoji": "🍻", "name": "clinking beer mugs", "slug": "clinking_beer_mugs"}, {"emoji": "🥂", "name": "clinking glasses", "slug": "clinking_glasses"}, {"emoji": "🥃", "name": "tumbler glass", "slug": "tumbler_glass"}, {"emoji": "🫗", "name": "pouring liquid", "slug": "pouring_liquid"}, {"emoji": "🥤", "name": "cup with straw", "slug": "cup_with_straw"}, {"emoji": "🧋", "name": "bubble tea", "slug": "bubble_tea"}, {"emoji": "🧃", "name": "beverage box", "slug": "beverage_box"}, {"emoji": "🧉", "name": "mate", "slug": "mate"}, {"emoji": "🧊", "name": "ice", "slug": "ice"}, {"emoji": "🥢", "name": "chopsticks", "slug": "chopsticks"}, {"emoji": "🍽️", "name": "fork and knife with plate", "slug": "fork_and_knife_with_plate"}, {"emoji": "🍴", "name": "fork and knife", "slug": "fork_and_knife"}, {"emoji": "🥄", "name": "spoon", "slug": "spoon"}, {"emoji": "🔪", "name": "kitchen knife", "slug": "kitchen_knife"}, {"emoji": "🫙", "name": "jar", "slug": "jar"}, {"emoji": "🏺", "name": "amphora", "slug": "amphora"}]}, {"name": "Travel & Places", "slug": "travel_places", "emojis": [{"emoji": "🌍", "name": "globe showing Europe-Africa", "slug": "globe_showing_europe_africa_Africa_earth_Europe_world"}, {"emoji": "🌎", "name": "globe showing Americas", "slug": "globe_showing_americas_Americas_earth_globe_world "}, {"emoji": "🌏", "name": "globe showing Asia-Australia", "slug": "globe_showing_asia_australia_Asia_Australia_earth_world"}, {"emoji": "🌐", "name": "globe with meridians", "slug": "globe_with_meridians_world_earth"}, {"emoji": "🗺️", "name": "world map", "slug": "world_map"}, {"emoji": "🗾", "name": "map of Japan", "slug": "map_of_japan"}, {"emoji": "🧭", "name": "compass", "slug": "compass_navigation_orientation_magnetic"}, {"emoji": "🏔️", "name": "snow-capped mountain", "slug": "snow_capped_mountain"}, {"emoji": "⛰️", "name": "mountain", "slug": "mountain"}, {"emoji": "🌋", "name": "volcano", "slug": "volcano"}, {"emoji": "🗻", "name": "mount fuji", "slug": "mount_fuji"}, {"emoji": "🏕️", "name": "camping", "slug": "camping"}, {"emoji": "🏖️", "name": "beach with umbrella", "slug": "beach_with_umbrella"}, {"emoji": "🏜️", "name": "desert", "slug": "desert"}, {"emoji": "🏝️", "name": "desert island", "slug": "desert_island"}, {"emoji": "🏞️", "name": "national park", "slug": "national_park"}, {"emoji": "🏟️", "name": "stadium", "slug": "stadium"}, {"emoji": "🏛️", "name": "classical building", "slug": "classical_building"}, {"emoji": "🏗️", "name": "building construction", "slug": "building_construction"}, {"emoji": "🧱", "name": "brick", "slug": "brick"}, {"emoji": "🪨", "name": "rock", "slug": "rock"}, {"emoji": "🪵", "name": "wood", "slug": "wood"}, {"emoji": "🛖", "name": "hut", "slug": "hut"}, {"emoji": "🏘️", "name": "houses", "slug": "houses"}, {"emoji": "🏚️", "name": "derelict house", "slug": "derelict_house"}, {"emoji": "🏠", "name": "house", "slug": "house"}, {"emoji": "🏡", "name": "house with garden", "slug": "house_with_garden"}, {"emoji": "🏢", "name": "office building", "slug": "office_building"}, {"emoji": "🏣", "name": "Japanese post office", "slug": "japanese_post_office"}, {"emoji": "🏤", "name": "post office", "slug": "post_office"}, {"emoji": "🏥", "name": "hospital", "slug": "hospital"}, {"emoji": "🏦", "name": "bank", "slug": "bank"}, {"emoji": "🏨", "name": "hotel", "slug": "hotel"}, {"emoji": "🏩", "name": "love hotel", "slug": "love_hotel"}, {"emoji": "🏪", "name": "convenience store", "slug": "convenience_store"}, {"emoji": "🏫", "name": "school", "slug": "school"}, {"emoji": "🏬", "name": "department store", "slug": "department_store"}, {"emoji": "🏭", "name": "factory", "slug": "factory"}, {"emoji": "🏯", "name": "Japanese castle", "slug": "japanese_castle"}, {"emoji": "🏰", "name": "castle", "slug": "castle"}, {"emoji": "💒", "name": "wedding", "slug": "wedding"}, {"emoji": "🗼", "name": "Tokyo tower", "slug": "tokyo_tower"}, {"emoji": "🗽", "name": "Statue of Liberty", "slug": "statue_of_liberty"}, {"emoji": "⛪", "name": "church", "slug": "church"}, {"emoji": "🕌", "name": "mosque", "slug": "mosque"}, {"emoji": "🛕", "name": "hindu temple", "slug": "hindu_temple"}, {"emoji": "🕍", "name": "synagogue", "slug": "synagogue"}, {"emoji": "⛩️", "name": "shinto shrine", "slug": "shinto_shrine"}, {"emoji": "🕋", "name": "kaaba", "slug": "kaaba"}, {"emoji": "⛲", "name": "fountain", "slug": "fountain"}, {"emoji": "⛺", "name": "tent", "slug": "tent"}, {"emoji": "🌁", "name": "foggy", "slug": "foggy"}, {"emoji": "🌃", "name": "night with stars", "slug": "night_with_stars"}, {"emoji": "🏙️", "name": "cityscape", "slug": "cityscape"}, {"emoji": "🌄", "name": "sunrise over mountains", "slug": "sunrise_over_mountains"}, {"emoji": "🌅", "name": "sunrise", "slug": "sunrise"}, {"emoji": "🌆", "name": "cityscape at dusk", "slug": "cityscape_at_dusk"}, {"emoji": "🌇", "name": "sunset", "slug": "sunset"}, {"emoji": "🌉", "name": "bridge at night", "slug": "bridge_at_night"}, {"emoji": "♨️", "name": "hot springs", "slug": "hot_springs"}, {"emoji": "🎠", "name": "carousel horse", "slug": "carousel_horse"}, {"emoji": "🛝", "name": "playground slide", "slug": "playground_slide"}, {"emoji": "🎡", "name": "ferris wheel", "slug": "ferris_wheel"}, {"emoji": "🎢", "name": "roller coaster", "slug": "roller_coaster"}, {"emoji": "💈", "name": "barber pole", "slug": "barber_pole"}, {"emoji": "🎪", "name": "circus tent", "slug": "circus_tent"}, {"emoji": "🚂", "name": "locomotive", "slug": "locomotive"}, {"emoji": "🚃", "name": "railway car", "slug": "railway_car"}, {"emoji": "🚄", "name": "high-speed train", "slug": "high_speed_train"}, {"emoji": "🚅", "name": "bullet train", "slug": "bullet_train"}, {"emoji": "🚆", "name": "train", "slug": "train"}, {"emoji": "🚇", "name": "metro", "slug": "metro"}, {"emoji": "🚈", "name": "light rail", "slug": "light_rail"}, {"emoji": "🚉", "name": "station", "slug": "station"}, {"emoji": "🚊", "name": "tram", "slug": "tram"}, {"emoji": "🚝", "name": "monorail", "slug": "monorail"}, {"emoji": "🚞", "name": "mountain railway", "slug": "mountain_railway"}, {"emoji": "🚋", "name": "tram car", "slug": "tram_car"}, {"emoji": "🚌", "name": "bus", "slug": "bus"}, {"emoji": "🚍", "name": "oncoming bus", "slug": "oncoming_bus"}, {"emoji": "🚎", "name": "trolleybus", "slug": "trolleybus"}, {"emoji": "🚐", "name": "minibus", "slug": "minibus"}, {"emoji": "🚑", "name": "ambulance", "slug": "ambulance"}, {"emoji": "🚒", "name": "fire engine", "slug": "fire_engine"}, {"emoji": "🚓", "name": "police car", "slug": "police_car"}, {"emoji": "🚔", "name": "oncoming police car", "slug": "oncoming_police_car"}, {"emoji": "🚕", "name": "taxi", "slug": "taxi"}, {"emoji": "🚖", "name": "oncoming taxi", "slug": "oncoming_taxi"}, {"emoji": "🚗", "name": "automobile", "slug": "automobile"}, {"emoji": "🚘", "name": "oncoming automobile", "slug": "oncoming_automobile"}, {"emoji": "🚙", "name": "sport utility vehicle", "slug": "sport_utility_vehicle"}, {"emoji": "🛻", "name": "pickup truck", "slug": "pickup_truck"}, {"emoji": "🚚", "name": "delivery truck", "slug": "delivery_truck"}, {"emoji": "🚛", "name": "articulated lorry", "slug": "articulated_lorry"}, {"emoji": "🚜", "name": "tractor", "slug": "tractor"}, {"emoji": "🏎️", "name": "racing car", "slug": "racing_car"}, {"emoji": "🏍️", "name": "motorcycle", "slug": "motorcycle"}, {"emoji": "🛵", "name": "motor scooter", "slug": "motor_scooter"}, {"emoji": "🦽", "name": "manual wheelchair", "slug": "manual_wheelchair"}, {"emoji": "🦼", "name": "motorized wheelchair", "slug": "motorized_wheelchair"}, {"emoji": "🛺", "name": "auto rickshaw", "slug": "auto_rickshaw"}, {"emoji": "🚲", "name": "bicycle", "slug": "bicycle"}, {"emoji": "🛴", "name": "kick scooter", "slug": "kick_scooter"}, {"emoji": "🛹", "name": "skateboard", "slug": "skateboard"}, {"emoji": "🛼", "name": "roller skate", "slug": "roller_skate"}, {"emoji": "🚏", "name": "bus stop", "slug": "bus_stop"}, {"emoji": "🛣️", "name": "motorway", "slug": "motorway"}, {"emoji": "🛤️", "name": "railway track", "slug": "railway_track"}, {"emoji": "🛢️", "name": "oil drum", "slug": "oil_drum"}, {"emoji": "⛽", "name": "fuel pump", "slug": "fuel_pump"}, {"emoji": "🛞", "name": "wheel", "slug": "wheel"}, {"emoji": "🚨", "name": "police car light", "slug": "police_car_light"}, {"emoji": "🚥", "name": "horizontal traffic light", "slug": "horizontal_traffic_light"}, {"emoji": "🚦", "name": "vertical traffic light", "slug": "vertical_traffic_light"}, {"emoji": "🛑", "name": "stop sign", "slug": "stop_sign"}, {"emoji": "🚧", "name": "construction", "slug": "construction"}, {"emoji": "⚓", "name": "anchor", "slug": "anchor"}, {"emoji": "🛟", "name": "ring buoy", "slug": "ring_buoy"}, {"emoji": "⛵", "name": "sailboat", "slug": "sailboat"}, {"emoji": "🛶", "name": "canoe", "slug": "canoe"}, {"emoji": "🚤", "name": "speedboat", "slug": "speedboat"}, {"emoji": "🛳️", "name": "passenger ship", "slug": "passenger_ship"}, {"emoji": "⛴️", "name": "ferry", "slug": "ferry"}, {"emoji": "🛥️", "name": "motor boat", "slug": "motor_boat"}, {"emoji": "🚢", "name": "ship", "slug": "ship"}, {"emoji": "✈️", "name": "airplane", "slug": "airplane"}, {"emoji": "🛩️", "name": "small airplane", "slug": "small_airplane"}, {"emoji": "🛫", "name": "airplane departure", "slug": "airplane_departure"}, {"emoji": "🛬", "name": "airplane arrival", "slug": "airplane_arrival"}, {"emoji": "🪂", "name": "parachute", "slug": "parachute"}, {"emoji": "💺", "name": "seat", "slug": "seat"}, {"emoji": "🚁", "name": "helicopter", "slug": "helicopter"}, {"emoji": "🚟", "name": "suspension railway", "slug": "suspension_railway"}, {"emoji": "🚠", "name": "mountain cableway", "slug": "mountain_cableway"}, {"emoji": "🚡", "name": "aerial tramway", "slug": "aerial_tramway"}, {"emoji": "🛰️", "name": "satellite", "slug": "satellite"}, {"emoji": "🚀", "name": "rocket", "slug": "rocket"}, {"emoji": "🛸", "name": "flying saucer", "slug": "flying_saucer"}, {"emoji": "🛎️", "name": "bellhop bell", "slug": "bellhop_bell"}, {"emoji": "🧳", "name": "luggage", "slug": "luggage"}, {"emoji": "⌛", "name": "hourglass done", "slug": "hourglass_done"}, {"emoji": "⏳", "name": "hourglass not done", "slug": "hourglass_not_done"}, {"emoji": "⌚", "name": "watch", "slug": "watch_clock"}, {"emoji": "⏰", "name": "alarm clock", "slug": "alarm_clock"}, {"emoji": "⏱️", "name": "stopwatch", "slug": "stopwatch"}, {"emoji": "⏲️", "name": "timer clock", "slug": "timer_clock"}, {"emoji": "🕰️", "name": "mantelpiece clock", "slug": "mantelpiece_clock"}, {"emoji": "🕛", "name": "twelve o’clock", "slug": "twelve_o_clock_00_12_12:00"}, {"emoji": "🕧", "name": "twelve-thirty", "slug": "twelve_thirty_12_12:30"}, {"emoji": "🕐", "name": "one o’clock", "slug": "one_o_clock_00_1_1:00"}, {"emoji": "🕜", "name": "one-thirty", "slug": "one_thirty_1_1:30"}, {"emoji": "🕑", "name": "two o’clock", "slug": "two_o_clock_00_2_2:00"}, {"emoji": "🕝", "name": "two-thirty", "slug": "two_thirty_2_2:30"}, {"emoji": "🕒", "name": "three o’clock", "slug": "three_o_clock_00_3_3:00"}, {"emoji": "🕞", "name": "three-thirty", "slug": "three_thirty_3_3:30"}, {"emoji": "🕓", "name": "four o’clock", "slug": "four_o_clock_00_4_4:00"}, {"emoji": "🕟", "name": "four-thirty", "slug": "four_thirty_4_4:30"}, {"emoji": "🕔", "name": "five o’clock", "slug": "five_o_clock_00_5_5:00"}, {"emoji": "🕠", "name": "five-thirty", "slug": "five_thirty_5_5:30"}, {"emoji": "🕕", "name": "six o’clock", "slug": "six_o_clock_00_6_6:00"}, {"emoji": "🕡", "name": "six-thirty", "slug": "six_thirty_6_6:30"}, {"emoji": "🕖", "name": "seven o’clock", "slug": "seven_o_clock_00_7_7:00"}, {"emoji": "🕢", "name": "seven-thirty", "slug": "seven_thirty_7_7:30"}, {"emoji": "🕗", "name": "eight o’clock", "slug": "eight_o_clock_00_8_8:00"}, {"emoji": "🕣", "name": "eight-thirty", "slug": "eight_thirty_8_8:30"}, {"emoji": "🕘", "name": "nine o’clock", "slug": "nine_o_clock_00_9_9:00"}, {"emoji": "🕤", "name": "nine-thirty", "slug": "nine_thirty_9_9:30"}, {"emoji": "🕙", "name": "ten o’clock", "slug": "ten_o_clock_00_10_10:00"}, {"emoji": "🕥", "name": "ten-thirty", "slug": "ten_thirty_10_10:30"}, {"emoji": "🕚", "name": "eleven o’clock", "slug": "eleven_o_clock_00_11_11:00"}, {"emoji": "🕦", "name": "eleven-thirty", "slug": "eleven_thirty_11_11:30"}, {"emoji": "🌑", "name": "new moon", "slug": "new_moon_dark"}, {"emoji": "🌒", "name": "waxing crescent moon", "slug": "waxing_crescent_moon"}, {"emoji": "🌓", "name": "first quarter moon", "slug": "first_quarter_moon"}, {"emoji": "🌔", "name": "waxing gibbous moon", "slug": "waxing_gibbous_moon"}, {"emoji": "🌕", "name": "full moon", "slug": "full_moon"}, {"emoji": "🌖", "name": "waning gibbous moon", "slug": "waning_gibbous_moon"}, {"emoji": "🌗", "name": "last quarter moon", "slug": "last_quarter_moon"}, {"emoji": "🌘", "name": "waning crescent moon", "slug": "waning_crescent_moon"}, {"emoji": "🌙", "name": "crescent moon", "slug": "crescent_moon"}, {"emoji": "🌚", "name": "new moon face", "slug": "new_moon_face"}, {"emoji": "🌛", "name": "first quarter moon face", "slug": "first_quarter_moon_face"}, {"emoji": "🌜", "name": "last quarter moon face", "slug": "last_quarter_moon_face"}, {"emoji": "🌡️", "name": "thermometer", "slug": "thermometer"}, {"emoji": "☀️", "name": "sun", "slug": "sun"}, {"emoji": "🌝", "name": "full moon face", "slug": "full_moon_face"}, {"emoji": "🌞", "name": "sun with face", "slug": "sun_with_face"}, {"emoji": "🪐", "name": "ringed planet", "slug": "ringed_planet"}, {"emoji": "⭐", "name": "star", "slug": "star"}, {"emoji": "🌟", "name": "glowing star", "slug": "glowing_star"}, {"emoji": "🌠", "name": "shooting star", "slug": "shooting_star"}, {"emoji": "🌌", "name": "milky way", "slug": "milky_way"}, {"emoji": "☁️", "name": "cloud", "slug": "cloud"}, {"emoji": "⛅", "name": "sun behind cloud", "slug": "sun_behind_cloud"}, {"emoji": "⛈️", "name": "cloud with lightning and rain", "slug": "cloud_with_lightning_and_rain"}, {"emoji": "🌤️", "name": "sun behind small cloud", "slug": "sun_behind_small_cloud"}, {"emoji": "🌥️", "name": "sun behind large cloud", "slug": "sun_behind_large_cloud"}, {"emoji": "🌦️", "name": "sun behind rain cloud", "slug": "sun_behind_rain_cloud"}, {"emoji": "🌧️", "name": "cloud with rain", "slug": "cloud_with_rain"}, {"emoji": "🌨️", "name": "cloud with snow", "slug": "cloud_with_snow"}, {"emoji": "🌩️", "name": "cloud with lightning", "slug": "cloud_with_lightning"}, {"emoji": "🌪️", "name": "tornado", "slug": "tornado"}, {"emoji": "🌫️", "name": "fog", "slug": "fog"}, {"emoji": "🌬️", "name": "wind face", "slug": "wind_face"}, {"emoji": "🌀", "name": "cyclone", "slug": "cyclone"}, {"emoji": "🌈", "name": "rainbow", "slug": "rainbow"}, {"emoji": "🌂", "name": "closed umbrella", "slug": "closed_umbrella"}, {"emoji": "☂️", "name": "umbrella", "slug": "umbrella"}, {"emoji": "☔", "name": "umbrella with rain drops", "slug": "umbrella_with_rain_drops"}, {"emoji": "⛱️", "name": "umbrella on ground", "slug": "umbrella_on_ground"}, {"emoji": "⚡", "name": "high voltage", "slug": "high_voltage"}, {"emoji": "❄️", "name": "snowflake", "slug": "snowflake"}, {"emoji": "☃️", "name": "snowman", "slug": "snowman"}, {"emoji": "⛄", "name": "snowman without snow", "slug": "snowman_without_snow"}, {"emoji": "☄️", "name": "comet", "slug": "comet"}, {"emoji": "🔥", "name": "fire", "slug": "fire"}, {"emoji": "💧", "name": "droplet", "slug": "droplet"}, {"emoji": "🌊", "name": "water wave", "slug": "water_wave"}]}, {"name": "Activities", "slug": "activities", "emojis": [{"emoji": "🎃", "name": "jack-o-lantern", "slug": "jack_o_lantern_halloween_jack"}, {"emoji": "🎄", "name": "Christmas tree", "slug": "christmas_tree"}, {"emoji": "🎆", "name": "fireworks", "slug": "fireworks_celebration"}, {"emoji": "🎇", "name": "sparkler", "slug": "sparkler_celebration"}, {"emoji": "🧨", "name": "firecracker", "slug": "firecracker_explosive"}, {"emoji": "✨", "name": "sparkles", "slug": "sparkles_star"}, {"emoji": "🎈", "name": "balloon", "slug": "balloon"}, {"emoji": "🎉", "name": "party popper", "slug": "party_popper_celebration"}, {"emoji": "🎊", "name": "confetti ball", "slug": "confetti_ball_celebration"}, {"emoji": "🎋", "name": "tanabata tree", "slug": "tanabata_tree"}, {"emoji": "🎍", "name": "pine decoration", "slug": "pine_decoration"}, {"emoji": "🎎", "name": "Japanese dolls", "slug": "japanese_dolls"}, {"emoji": "🎏", "name": "carp streamer", "slug": "carp_streamer"}, {"emoji": "🎐", "name": "wind chime", "slug": "wind_chime"}, {"emoji": "🎑", "name": "moon viewing ceremony", "slug": "moon_viewing_ceremony"}, {"emoji": "🧧", "name": "red envelope", "slug": "red_envelope"}, {"emoji": "🎀", "name": "ribbon", "slug": "ribbon_celebration"}, {"emoji": "🎁", "name": "wrapped gift", "slug": "wrapped_gift"}, {"emoji": "🎗️", "name": "reminder ribbon", "slug": "reminder_ribbon"}, {"emoji": "🎟️", "name": "admission tickets", "slug": "admission_tickets"}, {"emoji": "🎫", "name": "ticket", "slug": "ticket"}, {"emoji": "🎖️", "name": "military medal", "slug": "military_medal"}, {"emoji": "🏆", "name": "trophy", "slug": "trophy"}, {"emoji": "🏅", "name": "sports medal", "slug": "sports_medal"}, {"emoji": "🥇", "name": "1st place medal", "slug": "1st_place_medal"}, {"emoji": "🥈", "name": "2nd place medal", "slug": "2nd_place_medal"}, {"emoji": "🥉", "name": "3rd place medal", "slug": "3rd_place_medal"}, {"emoji": "⚽", "name": "soccer ball", "slug": "soccer_ball"}, {"emoji": "⚾", "name": "baseball", "slug": "baseball"}, {"emoji": "🥎", "name": "softball", "slug": "softball"}, {"emoji": "🏀", "name": "basketball", "slug": "basketball"}, {"emoji": "🏐", "name": "volleyball", "slug": "volleyball"}, {"emoji": "🏈", "name": "american football", "slug": "american_football"}, {"emoji": "🏉", "name": "rugby football", "slug": "rugby_football"}, {"emoji": "🎾", "name": "tennis", "slug": "tennis"}, {"emoji": "🥏", "name": "flying disc", "slug": "flying_disc"}, {"emoji": "🎳", "name": "bowling", "slug": "bowling"}, {"emoji": "🏏", "name": "cricket game", "slug": "cricket_game"}, {"emoji": "🏑", "name": "field hockey", "slug": "field_hockey"}, {"emoji": "🏒", "name": "ice hockey", "slug": "ice_hockey"}, {"emoji": "🥍", "name": "lacrosse", "slug": "lacrosse"}, {"emoji": "🏓", "name": "ping pong", "slug": "ping_pong"}, {"emoji": "🏸", "name": "badminton", "slug": "badminton"}, {"emoji": "🥊", "name": "boxing glove", "slug": "boxing_glove"}, {"emoji": "🥋", "name": "martial arts uniform", "slug": "martial_arts_uniform"}, {"emoji": "🥅", "name": "goal net", "slug": "goal_net"}, {"emoji": "⛳", "name": "flag in hole", "slug": "flag_in_hole"}, {"emoji": "⛸️", "name": "ice skate", "slug": "ice_skate"}, {"emoji": "🎣", "name": "fishing pole", "slug": "fishing_pole"}, {"emoji": "🤿", "name": "diving mask", "slug": "diving_mask"}, {"emoji": "🎽", "name": "running shirt", "slug": "running_shirt"}, {"emoji": "🎿", "name": "skis", "slug": "skis"}, {"emoji": "🛷", "name": "sled", "slug": "sled"}, {"emoji": "🥌", "name": "curling stone", "slug": "curling_stone"}, {"emoji": "🎯", "name": "bullseye", "slug": "bullseye"}, {"emoji": "🪀", "name": "yo-yo", "slug": "yo_yo"}, {"emoji": "🪁", "name": "kite", "slug": "kite"}, {"emoji": "🔫", "name": "water pistol", "slug": "water_pistol"}, {"emoji": "🎱", "name": "pool 8 ball", "slug": "pool_8_ball"}, {"emoji": "🔮", "name": "crystal ball", "slug": "crystal_ball"}, {"emoji": "🪄", "name": "magic wand", "slug": "magic_wand"}, {"emoji": "🎮", "name": "video game", "slug": "video_game"}, {"emoji": "🕹️", "name": "joystick", "slug": "joystick"}, {"emoji": "🎰", "name": "slot machine", "slug": "slot_machine"}, {"emoji": "🎲", "name": "game die", "slug": "game_die"}, {"emoji": "🧩", "name": "puzzle piece", "slug": "puzzle_piece"}, {"emoji": "🧸", "name": "teddy bear", "slug": "teddy_bear"}, {"emoji": "🪅", "name": "pi<PERSON><PERSON>", "slug": "pinata"}, {"emoji": "🪩", "name": "mirror ball", "slug": "mirror_ball"}, {"emoji": "🪆", "name": "nesting dolls", "slug": "nesting_dolls"}, {"emoji": "♠️", "name": "spade suit", "slug": "spade_suit"}, {"emoji": "♥️", "name": "heart suit", "slug": "heart_suit"}, {"emoji": "♦️", "name": "diamond suit", "slug": "diamond_suit"}, {"emoji": "♣️", "name": "club suit", "slug": "club_suit"}, {"emoji": "♟️", "name": "chess pawn", "slug": "chess_pawn"}, {"emoji": "🃏", "name": "joker", "slug": "joker"}, {"emoji": "🀄", "name": "mahjong red dragon", "slug": "mahjong_red_dragon"}, {"emoji": "🎴", "name": "flower playing cards", "slug": "flower_playing_cards"}, {"emoji": "🎭", "name": "performing arts", "slug": "performing_arts"}, {"emoji": "🖼️", "name": "framed picture", "slug": "framed_picture"}, {"emoji": "🎨", "name": "artist palette", "slug": "artist_palette"}, {"emoji": "🧵", "name": "thread", "slug": "thread"}, {"emoji": "🪡", "name": "sewing needle", "slug": "sewing_needle"}, {"emoji": "🧶", "name": "yarn", "slug": "yarn"}, {"emoji": "🪢", "name": "knot", "slug": "knot"}]}, {"name": "Objects", "slug": "objects", "emojis": [{"emoji": "👓", "name": "glasses", "slug": "glasses"}, {"emoji": "🕶️", "name": "sunglasses", "slug": "sunglasses"}, {"emoji": "🥽", "name": "goggles", "slug": "goggles"}, {"emoji": "🥼", "name": "lab coat", "slug": "lab_coat"}, {"emoji": "🦺", "name": "safety vest", "slug": "safety_vest"}, {"emoji": "👔", "name": "necktie", "slug": "necktie"}, {"emoji": "👕", "name": "t-shirt", "slug": "t_shirt"}, {"emoji": "👖", "name": "jeans", "slug": "jeans"}, {"emoji": "🧣", "name": "scarf", "slug": "scarf"}, {"emoji": "🧤", "name": "gloves", "slug": "gloves"}, {"emoji": "🧥", "name": "coat", "slug": "coat"}, {"emoji": "🧦", "name": "socks", "slug": "socks"}, {"emoji": "👗", "name": "dress", "slug": "dress"}, {"emoji": "👘", "name": "kimono", "slug": "kimono"}, {"emoji": "🥻", "name": "sari", "slug": "sari"}, {"emoji": "🩱", "name": "one-piece swimsuit", "slug": "one_piece_swimsuit"}, {"emoji": "🩲", "name": "briefs", "slug": "briefs"}, {"emoji": "🩳", "name": "shorts", "slug": "shorts"}, {"emoji": "👙", "name": "bikini", "slug": "bikini"}, {"emoji": "👚", "name": "woman’s clothes", "slug": "woman_s_clothes"}, {"emoji": "👛", "name": "purse", "slug": "purse"}, {"emoji": "👜", "name": "handbag", "slug": "handbag"}, {"emoji": "👝", "name": "clutch bag", "slug": "clutch_bag"}, {"emoji": "🛍️", "name": "shopping bags", "slug": "shopping_bags"}, {"emoji": "🎒", "name": "backpack", "slug": "backpack"}, {"emoji": "🩴", "name": "thong sandal", "slug": "thong_sandal"}, {"emoji": "👞", "name": "man’s shoe", "slug": "man_s_shoe"}, {"emoji": "👟", "name": "running shoe", "slug": "running_shoe"}, {"emoji": "🥾", "name": "hiking boot", "slug": "hiking_boot"}, {"emoji": "🥿", "name": "flat shoe", "slug": "flat_shoe"}, {"emoji": "👠", "name": "high-heeled shoe", "slug": "high_heeled_shoe"}, {"emoji": "👡", "name": "woman’s sandal", "slug": "woman_s_sandal"}, {"emoji": "🩰", "name": "ballet shoes", "slug": "ballet_shoes"}, {"emoji": "👢", "name": "woman’s boot", "slug": "woman_s_boot"}, {"emoji": "👑", "name": "crown", "slug": "crown"}, {"emoji": "👒", "name": "woman’s hat", "slug": "woman_s_hat"}, {"emoji": "🎩", "name": "top hat", "slug": "top_hat"}, {"emoji": "🎓", "name": "graduation cap", "slug": "graduation_cap"}, {"emoji": "🧢", "name": "billed cap", "slug": "billed_cap"}, {"emoji": "🪖", "name": "military helmet", "slug": "military_helmet"}, {"emoji": "⛑️", "name": "rescue worker’s helmet", "slug": "rescue_worker_s_helmet"}, {"emoji": "📿", "name": "prayer beads", "slug": "prayer_beads"}, {"emoji": "💄", "name": "lipstick", "slug": "lipstick"}, {"emoji": "💍", "name": "ring", "slug": "ring"}, {"emoji": "💎", "name": "gem stone", "slug": "gem_stone"}, {"emoji": "🔇", "name": "muted speaker", "slug": "muted_speaker"}, {"emoji": "🔈", "name": "speaker low volume", "slug": "speaker_low_volume"}, {"emoji": "🔉", "name": "speaker medium volume", "slug": "speaker_medium_volume"}, {"emoji": "🔊", "name": "speaker high volume", "slug": "speaker_high_volume"}, {"emoji": "📢", "name": "loudspeaker", "slug": "loudspeaker"}, {"emoji": "📣", "name": "megaphone", "slug": "megaphone"}, {"emoji": "📯", "name": "postal horn", "slug": "postal_horn"}, {"emoji": "🔔", "name": "bell", "slug": "bell"}, {"emoji": "🔕", "name": "bell with slash", "slug": "bell_with_slash"}, {"emoji": "🎼", "name": "musical score", "slug": "musical_score"}, {"emoji": "🎵", "name": "musical note", "slug": "musical_note"}, {"emoji": "🎶", "name": "musical notes", "slug": "musical_notes"}, {"emoji": "🎙️", "name": "studio microphone", "slug": "studio_microphone"}, {"emoji": "🎚️", "name": "level slider", "slug": "level_slider"}, {"emoji": "🎛️", "name": "control knobs", "slug": "control_knobs"}, {"emoji": "🎤", "name": "microphone", "slug": "microphone"}, {"emoji": "🎧", "name": "headphone", "slug": "headphone"}, {"emoji": "📻", "name": "radio", "slug": "radio"}, {"emoji": "🎷", "name": "saxophone", "slug": "saxophone"}, {"emoji": "🪗", "name": "accordion", "slug": "accordion"}, {"emoji": "🎸", "name": "guitar", "slug": "guitar"}, {"emoji": "🎹", "name": "musical keyboard", "slug": "musical_keyboard"}, {"emoji": "🎺", "name": "trumpet", "slug": "trumpet"}, {"emoji": "🎻", "name": "violin", "slug": "violin"}, {"emoji": "🪕", "name": "banjo", "slug": "banjo"}, {"emoji": "🥁", "name": "drum", "slug": "drum"}, {"emoji": "🪘", "name": "long drum", "slug": "long_drum"}, {"emoji": "📱", "name": "mobile phone", "slug": "mobile_phone"}, {"emoji": "📲", "name": "mobile phone with arrow", "slug": "mobile_phone_with_arrow"}, {"emoji": "☎️", "name": "telephone", "slug": "telephone"}, {"emoji": "📞", "name": "telephone receiver", "slug": "telephone_receiver"}, {"emoji": "📟", "name": "pager", "slug": "pager"}, {"emoji": "📠", "name": "fax machine", "slug": "fax_machine"}, {"emoji": "🔋", "name": "battery", "slug": "battery"}, {"emoji": "🪫", "name": "low battery", "slug": "low_battery"}, {"emoji": "🔌", "name": "electric plug", "slug": "electric_plug"}, {"emoji": "💻", "name": "laptop", "slug": "laptop"}, {"emoji": "🖥️", "name": "desktop computer", "slug": "desktop_computer"}, {"emoji": "🖨️", "name": "printer", "slug": "printer"}, {"emoji": "⌨️", "name": "keyboard", "slug": "keyboard"}, {"emoji": "🖱️", "name": "computer mouse", "slug": "computer_mouse"}, {"emoji": "🖲️", "name": "trackball", "slug": "trackball"}, {"emoji": "💽", "name": "computer disk", "slug": "computer_disk"}, {"emoji": "💾", "name": "floppy disk", "slug": "floppy_disk"}, {"emoji": "💿", "name": "optical disk", "slug": "optical_disk"}, {"emoji": "📀", "name": "dvd", "slug": "dvd"}, {"emoji": "🧮", "name": "abacus", "slug": "abacus"}, {"emoji": "🎥", "name": "movie camera", "slug": "movie_camera"}, {"emoji": "🎞️", "name": "film frames", "slug": "film_frames"}, {"emoji": "📽️", "name": "film projector", "slug": "film_projector"}, {"emoji": "🎬", "name": "clapper board", "slug": "clapper_board"}, {"emoji": "📺", "name": "television", "slug": "television"}, {"emoji": "📷", "name": "camera", "slug": "camera"}, {"emoji": "📸", "name": "camera with flash", "slug": "camera_with_flash"}, {"emoji": "📹", "name": "video camera", "slug": "video_camera"}, {"emoji": "📼", "name": "videocassette", "slug": "videocassette"}, {"emoji": "🔍", "name": "magnifying glass tilted left", "slug": "magnifying_glass_tilted_left"}, {"emoji": "🔎", "name": "magnifying glass tilted right", "slug": "magnifying_glass_tilted_right"}, {"emoji": "🕯️", "name": "candle", "slug": "candle"}, {"emoji": "💡", "name": "light bulb", "slug": "light_bulb"}, {"emoji": "🔦", "name": "flashlight", "slug": "flashlight"}, {"emoji": "🏮", "name": "red paper lantern", "slug": "red_paper_lantern"}, {"emoji": "🪔", "name": "diya lamp", "slug": "diya_lamp"}, {"emoji": "📔", "name": "notebook with decorative cover", "slug": "notebook_with_decorative_cover"}, {"emoji": "📕", "name": "closed book", "slug": "closed_book"}, {"emoji": "📖", "name": "open book", "slug": "open_book"}, {"emoji": "📗", "name": "green book", "slug": "green_book"}, {"emoji": "📘", "name": "blue book", "slug": "blue_book"}, {"emoji": "📙", "name": "orange book", "slug": "orange_book"}, {"emoji": "📚", "name": "books", "slug": "books"}, {"emoji": "📓", "name": "notebook", "slug": "notebook"}, {"emoji": "📒", "name": "ledger", "slug": "ledger"}, {"emoji": "📃", "name": "page with curl", "slug": "page_with_curl"}, {"emoji": "📜", "name": "scroll", "slug": "scroll"}, {"emoji": "📄", "name": "page facing up", "slug": "page_facing_up"}, {"emoji": "📰", "name": "newspaper", "slug": "newspaper"}, {"emoji": "🗞️", "name": "rolled-up newspaper", "slug": "rolled_up_newspaper"}, {"emoji": "📑", "name": "bookmark tabs", "slug": "bookmark_tabs"}, {"emoji": "🔖", "name": "bookmark", "slug": "bookmark"}, {"emoji": "🏷️", "name": "label", "slug": "label"}, {"emoji": "💰", "name": "money bag", "slug": "money_bag"}, {"emoji": "🪙", "name": "coin", "slug": "coin"}, {"emoji": "💴", "name": "yen banknote", "slug": "yen_banknote"}, {"emoji": "💵", "name": "dollar banknote", "slug": "dollar_banknote"}, {"emoji": "💶", "name": "euro banknote", "slug": "euro_banknote"}, {"emoji": "💷", "name": "pound banknote", "slug": "pound_banknote"}, {"emoji": "💸", "name": "money with wings", "slug": "money_with_wings"}, {"emoji": "💳", "name": "credit card", "slug": "credit_card"}, {"emoji": "🧾", "name": "receipt", "slug": "receipt"}, {"emoji": "💹", "name": "chart increasing with yen", "slug": "chart_increasing_with_yen"}, {"emoji": "✉️", "name": "envelope", "slug": "envelope_email_letter"}, {"emoji": "📧", "name": "e-mail", "slug": "e_mail_email"}, {"emoji": "📨", "name": "incoming envelope", "slug": "incoming_envelope"}, {"emoji": "📩", "name": "envelope with arrow", "slug": "envelope_with_arrow_email_letter"}, {"emoji": "📤", "name": "outbox tray", "slug": "outbox_tray_sent"}, {"emoji": "📥", "name": "inbox tray", "slug": "inbox_tray_receive"}, {"emoji": "📦", "name": "package", "slug": "package_parcel_box"}, {"emoji": "📫", "name": "closed mailbox with raised flag", "slug": "closed_mailbox_with_raised_flag"}, {"emoji": "📪", "name": "closed mailbox with lowered flag", "slug": "closed_mailbox_with_lowered_flag"}, {"emoji": "📬", "name": "open mailbox with raised flag", "slug": "open_mailbox_with_raised_flag"}, {"emoji": "📭", "name": "open mailbox with lowered flag", "slug": "open_mailbox_with_lowered_flag"}, {"emoji": "📮", "name": "postbox", "slug": "postbox"}, {"emoji": "🗳️", "name": "ballot box with ballot", "slug": "ballot_box_with_ballot"}, {"emoji": "✏️", "name": "pencil", "slug": "pencil"}, {"emoji": "✒️", "name": "black nib", "slug": "black_nib"}, {"emoji": "🖋️", "name": "fountain pen", "slug": "fountain_pen"}, {"emoji": "🖊️", "name": "pen", "slug": "pen"}, {"emoji": "🖌️", "name": "paintbrush", "slug": "paintbrush"}, {"emoji": "🖍️", "name": "crayon", "slug": "crayon"}, {"emoji": "📝", "name": "memo", "slug": "memo"}, {"emoji": "💼", "name": "briefcase", "slug": "briefcase"}, {"emoji": "📁", "name": "file folder", "slug": "file_folder"}, {"emoji": "📂", "name": "open file folder", "slug": "open_file_folder"}, {"emoji": "🗂️", "name": "card index dividers", "slug": "card_index_dividers"}, {"emoji": "📅", "name": "calendar", "slug": "calendar"}, {"emoji": "📆", "name": "tear-off calendar", "slug": "tear_off_calendar"}, {"emoji": "🗒️", "name": "spiral notepad", "slug": "spiral_notepad"}, {"emoji": "🗓️", "name": "spiral calendar", "slug": "spiral_calendar"}, {"emoji": "📇", "name": "card index", "slug": "card_index"}, {"emoji": "📈", "name": "chart increasing", "slug": "chart_increasing"}, {"emoji": "📉", "name": "chart decreasing", "slug": "chart_decreasing"}, {"emoji": "📊", "name": "bar chart", "slug": "bar_chart"}, {"emoji": "📋", "name": "clipboard", "slug": "clipboard"}, {"emoji": "📌", "name": "pushpin", "slug": "pushpin"}, {"emoji": "📍", "name": "round pushpin", "slug": "round_pushpin"}, {"emoji": "📎", "name": "paperclip", "slug": "paperclip"}, {"emoji": "🖇️", "name": "linked paperclips", "slug": "linked_paperclips"}, {"emoji": "📏", "name": "straight ruler", "slug": "straight_ruler"}, {"emoji": "📐", "name": "triangular ruler", "slug": "triangular_ruler"}, {"emoji": "✂️", "name": "scissors", "slug": "scissors"}, {"emoji": "🗃️", "name": "card file box", "slug": "card_file_box"}, {"emoji": "🗄️", "name": "file cabinet", "slug": "file_cabinet"}, {"emoji": "🗑️", "name": "wastebasket", "slug": "wastebasket"}, {"emoji": "🔒", "name": "locked", "slug": "locked"}, {"emoji": "🔓", "name": "unlocked", "slug": "unlocked"}, {"emoji": "🔏", "name": "locked with pen", "slug": "locked_with_pen"}, {"emoji": "🔐", "name": "locked with key", "slug": "locked_with_key"}, {"emoji": "🔑", "name": "key", "slug": "key"}, {"emoji": "🗝️", "name": "old key", "slug": "old_key"}, {"emoji": "🔨", "name": "hammer", "slug": "hammer_tool"}, {"emoji": "🪓", "name": "axe", "slug": "axe"}, {"emoji": "⛏️", "name": "pick", "slug": "pick_tool"}, {"emoji": "⚒️", "name": "hammer and pick", "slug": "hammer_and_pick_tool"}, {"emoji": "🛠️", "name": "hammer and wrench", "slug": "hammer_and_wrench_tool"}, {"emoji": "🗡️", "name": "dagger", "slug": "dagger_knife_weapon"}, {"emoji": "⚔️", "name": "crossed swords", "slug": "crossed_swords_weapon"}, {"emoji": "💣", "name": "bomb", "slug": "bomb_comic"}, {"emoji": "🪃", "name": "boomerang", "slug": "boomerang"}, {"emoji": "🏹", "name": "bow and arrow", "slug": "bow_and_arrow"}, {"emoji": "🛡️", "name": "shield", "slug": "shield"}, {"emoji": "🪚", "name": "carpentry saw", "slug": "carpentry_saw"}, {"emoji": "🔧", "name": "wrench", "slug": "wrench_tool"}, {"emoji": "🪛", "name": "screwdriver", "slug": "screwdriver_tool"}, {"emoji": "🔩", "name": "nut and bolt", "slug": "nut_and_bolt_tool"}, {"emoji": "⚙️", "name": "gear", "slug": "gear_tool"}, {"emoji": "🗜️", "name": "clamp", "slug": "clamp_tool"}, {"emoji": "⚖️", "name": "balance scale", "slug": "balance_scale_justice"}, {"emoji": "🦯", "name": "white cane", "slug": "white_cane"}, {"emoji": "🔗", "name": "link", "slug": "link"}, {"emoji": "⛓️", "name": "chains", "slug": "chains"}, {"emoji": "🪝", "name": "hook", "slug": "hook"}, {"emoji": "🧰", "name": "toolbox", "slug": "toolbox"}, {"emoji": "🧲", "name": "magnet", "slug": "magnet"}, {"emoji": "🪜", "name": "ladder", "slug": "ladder"}, {"emoji": "⚗️", "name": "alembic", "slug": "alembic"}, {"emoji": "🧪", "name": "test tube", "slug": "test_tube"}, {"emoji": "🧫", "name": "petri dish", "slug": "petri_dish"}, {"emoji": "🧬", "name": "dna", "slug": "dna"}, {"emoji": "🔬", "name": "microscope", "slug": "microscope"}, {"emoji": "🔭", "name": "telescope", "slug": "telescope"}, {"emoji": "📡", "name": "satellite antenna", "slug": "satellite_antenna"}, {"emoji": "💉", "name": "syringe", "slug": "syringe"}, {"emoji": "🩸", "name": "drop of blood", "slug": "drop_of_blood"}, {"emoji": "💊", "name": "pill", "slug": "pill_medical_medicine_sick"}, {"emoji": "🩹", "name": "adhesive bandage", "slug": "adhesive_bandage"}, {"emoji": "🩼", "name": "crutch", "slug": "crutch"}, {"emoji": "🩺", "name": "stethoscope", "slug": "stethoscope"}, {"emoji": "🩻", "name": "x-ray", "slug": "x_ray"}, {"emoji": "🚪", "name": "door", "slug": "door"}, {"emoji": "🛗", "name": "elevator", "slug": "elevator"}, {"emoji": "🪞", "name": "mirror", "slug": "mirror"}, {"emoji": "🪟", "name": "window", "slug": "window"}, {"emoji": "🛏️", "name": "bed", "slug": "bed"}, {"emoji": "🛋️", "name": "couch and lamp", "slug": "couch_and_lamp"}, {"emoji": "🪑", "name": "chair", "slug": "chair"}, {"emoji": "🚽", "name": "toilet", "slug": "toilet"}, {"emoji": "🪠", "name": "plunger", "slug": "plunger"}, {"emoji": "🚿", "name": "shower", "slug": "shower"}, {"emoji": "🛁", "name": "bathtub", "slug": "bathtub"}, {"emoji": "🪤", "name": "mouse trap", "slug": "mouse_trap"}, {"emoji": "🪒", "name": "razor", "slug": "razor"}, {"emoji": "🧴", "name": "lotion bottle", "slug": "lotion_bottle"}, {"emoji": "🧷", "name": "safety pin", "slug": "safety_pin"}, {"emoji": "🧹", "name": "broom", "slug": "broom"}, {"emoji": "🧺", "name": "basket", "slug": "basket"}, {"emoji": "🧻", "name": "roll of paper", "slug": "roll_of_paper"}, {"emoji": "🪣", "name": "bucket", "slug": "bucket"}, {"emoji": "🧼", "name": "soap", "slug": "soap"}, {"emoji": "🫧", "name": "bubbles", "slug": "bubbles"}, {"emoji": "🪥", "name": "toothbrush", "slug": "toothbrush"}, {"emoji": "🧽", "name": "sponge", "slug": "sponge"}, {"emoji": "🧯", "name": "fire extinguisher", "slug": "fire_extinguisher"}, {"emoji": "🛒", "name": "shopping cart", "slug": "shopping_cart"}, {"emoji": "🚬", "name": "cigarette", "slug": "cigarette"}, {"emoji": "⚰️", "name": "coffin", "slug": "coffin"}, {"emoji": "🪦", "name": "headstone", "slug": "headstone"}, {"emoji": "⚱️", "name": "funeral urn", "slug": "funeral_urn"}, {"emoji": "🧿", "name": "nazar amulet", "slug": "nazar_amulet"}, {"emoji": "🪬", "name": "hamsa", "slug": "hamsa"}, {"emoji": "🗿", "name": "moai", "slug": "moai"}, {"emoji": "🪧", "name": "placard", "slug": "placard"}, {"emoji": "🪪", "name": "identification card", "slug": "identification_card"}]}, {"name": "Symbols", "slug": "symbols", "emojis": [{"emoji": "🏧", "name": "ATM sign", "slug": "atm_sign_bank_automated_teller_machine"}, {"emoji": "🚮", "name": "litter in bin sign", "slug": "litter_in_bin_sign"}, {"emoji": "🚰", "name": "potable water", "slug": "potable_water"}, {"emoji": "♿", "name": "wheelchair symbol", "slug": "wheelchair_symbol"}, {"emoji": "🚹", "name": "men’s room", "slug": "men_s_room_bathroom"}, {"emoji": "🚺", "name": "women’s room", "slug": "women_s_room_bathroom"}, {"emoji": "🚻", "name": "restroom", "slug": "restroom"}, {"emoji": "🚼", "name": "baby symbol", "slug": "baby_symbol"}, {"emoji": "🚾", "name": "water closet", "slug": "water_closet"}, {"emoji": "🛂", "name": "passport control", "slug": "passport_control"}, {"emoji": "🛃", "name": "customs", "slug": "customs"}, {"emoji": "🛄", "name": "baggage claim", "slug": "baggage_claim"}, {"emoji": "🛅", "name": "left luggage", "slug": "left_luggage"}, {"emoji": "⚠️", "name": "warning", "slug": "warning"}, {"emoji": "🚸", "name": "children crossing", "slug": "children_crossing"}, {"emoji": "⛔", "name": "no entry", "slug": "no_entry"}, {"emoji": "🚫", "name": "prohibited", "slug": "prohibited"}, {"emoji": "🚳", "name": "no bicycles", "slug": "no_bicycles"}, {"emoji": "🚭", "name": "no smoking", "slug": "no_smoking"}, {"emoji": "🚯", "name": "no littering", "slug": "no_littering"}, {"emoji": "🚱", "name": "non-potable water", "slug": "non_potable_water"}, {"emoji": "🚷", "name": "no pedestrians", "slug": "no_pedestrians"}, {"emoji": "📵", "name": "no mobile phones", "slug": "no_mobile_phones"}, {"emoji": "🔞", "name": "no one under eighteen", "slug": "no_one_under_eighteen_18_age_limit_prohibited"}, {"emoji": "☢️", "name": "radioactive", "slug": "radioactive"}, {"emoji": "☣️", "name": "biohazard", "slug": "biohazard"}, {"emoji": "⬆️", "name": "up arrow", "slug": "up_arrow"}, {"emoji": "↗️", "name": "up-right arrow", "slug": "up_right_arrow"}, {"emoji": "➡️", "name": "right arrow", "slug": "right_arrow"}, {"emoji": "↘️", "name": "down-right arrow", "slug": "down_right_arrow"}, {"emoji": "⬇️", "name": "down arrow", "slug": "down_arrow"}, {"emoji": "↙️", "name": "down-left arrow", "slug": "down_left_arrow"}, {"emoji": "⬅️", "name": "left arrow", "slug": "left_arrow"}, {"emoji": "↖️", "name": "up-left arrow", "slug": "up_left_arrow"}, {"emoji": "↕️", "name": "up-down arrow", "slug": "up_down_arrow"}, {"emoji": "↔️", "name": "left-right arrow", "slug": "left_right_arrow"}, {"emoji": "↩️", "name": "right arrow curving left", "slug": "right_arrow_curving_left"}, {"emoji": "↪️", "name": "left arrow curving right", "slug": "left_arrow_curving_right"}, {"emoji": "⤴️", "name": "right arrow curving up", "slug": "right_arrow_curving_up"}, {"emoji": "⤵️", "name": "right arrow curving down", "slug": "right_arrow_curving_down"}, {"emoji": "🔃", "name": "clockwise vertical arrows", "slug": "clockwise_vertical_arrows"}, {"emoji": "🔄", "name": "counterclockwise arrows button", "slug": "counterclockwise_arrows_button"}, {"emoji": "🔙", "name": "BACK arrow", "slug": "back_arrow"}, {"emoji": "🔚", "name": "END arrow", "slug": "end_arrow"}, {"emoji": "🔛", "name": "ON! arrow", "slug": "on_arrow"}, {"emoji": "🔜", "name": "SOON arrow", "slug": "soon_arrow"}, {"emoji": "🔝", "name": "TOP arrow", "slug": "top_arrow"}, {"emoji": "🛐", "name": "place of worship", "slug": "place_of_worship"}, {"emoji": "⚛️", "name": "atom symbol", "slug": "atom_symbol"}, {"emoji": "🕉️", "name": "om", "slug": "om"}, {"emoji": "✡️", "name": "star of David", "slug": "star_of_david"}, {"emoji": "☸️", "name": "wheel of dharma", "slug": "wheel_of_dharma"}, {"emoji": "☯️", "name": "yin yang", "slug": "yin_yang"}, {"emoji": "✝️", "name": "latin cross", "slug": "latin_cross"}, {"emoji": "☦️", "name": "orthodox cross", "slug": "orthodox_cross"}, {"emoji": "☪️", "name": "star and crescent", "slug": "star_and_crescent"}, {"emoji": "☮️", "name": "peace symbol", "slug": "peace_symbol"}, {"emoji": "🕎", "name": "menorah", "slug": "menorah"}, {"emoji": "🔯", "name": "dotted six-pointed star", "slug": "dotted_six_pointed_star"}, {"emoji": "♈", "name": "<PERSON><PERSON>", "slug": "aries"}, {"emoji": "♉", "name": "<PERSON><PERSON>", "slug": "taurus"}, {"emoji": "♊", "name": "Gemini", "slug": "gemini"}, {"emoji": "♋", "name": "Cancer", "slug": "cancer"}, {"emoji": "♌", "name": "<PERSON>", "slug": "leo"}, {"emoji": "♍", "name": "Virgo", "slug": "virgo"}, {"emoji": "♎", "name": "Libra", "slug": "libra"}, {"emoji": "♏", "name": "<PERSON><PERSON><PERSON>", "slug": "scorpio"}, {"emoji": "♐", "name": "<PERSON><PERSON><PERSON><PERSON>", "slug": "sagittarius"}, {"emoji": "♑", "name": "Capricorn", "slug": "capricorn"}, {"emoji": "♒", "name": "A<PERSON><PERSON>", "slug": "aquarius"}, {"emoji": "♓", "name": "Pisces", "slug": "pisces"}, {"emoji": "⛎", "name": "<PERSON>hi<PERSON><PERSON>", "slug": "ophi<PERSON>us"}, {"emoji": "🔀", "name": "shuffle tracks button", "slug": "shuffle_tracks_button"}, {"emoji": "🔁", "name": "repeat button", "slug": "repeat_button"}, {"emoji": "🔂", "name": "repeat single button", "slug": "repeat_single_button"}, {"emoji": "▶️", "name": "play button", "slug": "play_button"}, {"emoji": "⏩", "name": "fast-forward button", "slug": "fast_forward_button"}, {"emoji": "⏭️", "name": "next track button", "slug": "next_track_button"}, {"emoji": "⏯️", "name": "play or pause button", "slug": "play_or_pause_button"}, {"emoji": "◀️", "name": "reverse button", "slug": "reverse_button"}, {"emoji": "⏪", "name": "fast reverse button", "slug": "fast_reverse_button"}, {"emoji": "⏮️", "name": "last track button", "slug": "last_track_button"}, {"emoji": "🔼", "name": "upwards button", "slug": "upwards_button"}, {"emoji": "⏫", "name": "fast up button", "slug": "fast_up_button"}, {"emoji": "🔽", "name": "downwards button", "slug": "downwards_button"}, {"emoji": "⏬", "name": "fast down button", "slug": "fast_down_button"}, {"emoji": "⏸️", "name": "pause button", "slug": "pause_button"}, {"emoji": "⏹️", "name": "stop button", "slug": "stop_button"}, {"emoji": "⏺️", "name": "record button", "slug": "record_button"}, {"emoji": "⏏️", "name": "eject button", "slug": "eject_button"}, {"emoji": "🎦", "name": "cinema", "slug": "cinema"}, {"emoji": "🔅", "name": "dim button", "slug": "dim_button"}, {"emoji": "🔆", "name": "bright button", "slug": "bright_button"}, {"emoji": "📶", "name": "antenna bars", "slug": "antenna_bars"}, {"emoji": "📳", "name": "vibration mode", "slug": "vibration_mode"}, {"emoji": "📴", "name": "mobile phone off", "slug": "mobile_phone_off"}, {"emoji": "♀️", "name": "female sign", "slug": "female_sign"}, {"emoji": "♂️", "name": "male sign", "slug": "male_sign"}, {"emoji": "⚧️", "name": "transgender symbol", "slug": "transgender_symbol"}, {"emoji": "✖️", "name": "multiply", "slug": "multiply"}, {"emoji": "➕", "name": "plus", "slug": "plus"}, {"emoji": "➖", "name": "minus", "slug": "minus"}, {"emoji": "➗", "name": "divide", "slug": "divide"}, {"emoji": "🟰", "name": "heavy equals sign", "slug": "heavy_equals_sign"}, {"emoji": "♾️", "name": "infinity", "slug": "infinity"}, {"emoji": "‼️", "name": "double exclamation mark", "slug": "double_exclamation_mark"}, {"emoji": "⁉️", "name": "exclamation question mark", "slug": "exclamation_question_mark"}, {"emoji": "❓", "name": "red question mark", "slug": "red_question_mark"}, {"emoji": "❔", "name": "white question mark", "slug": "white_question_mark"}, {"emoji": "❕", "name": "white exclamation mark", "slug": "white_exclamation_mark"}, {"emoji": "❗", "name": "red exclamation mark", "slug": "red_exclamation_mark"}, {"emoji": "〰️", "name": "wavy dash", "slug": "wavy_dash"}, {"emoji": "💱", "name": "currency exchange", "slug": "currency_exchange"}, {"emoji": "💲", "name": "heavy dollar sign", "slug": "heavy_dollar_sign"}, {"emoji": "⚕️", "name": "medical symbol", "slug": "medical_symbol"}, {"emoji": "♻️", "name": "recycling symbol", "slug": "recycling_symbol"}, {"emoji": "⚜️", "name": "fleur-de-lis", "slug": "fleur_de_lis"}, {"emoji": "🔱", "name": "trident emblem", "slug": "trident_emblem"}, {"emoji": "📛", "name": "name badge", "slug": "name_badge"}, {"emoji": "🔰", "name": "Japanese symbol for beginner", "slug": "japanese_symbol_for_beginner"}, {"emoji": "⭕", "name": "hollow red circle", "slug": "hollow_red_circle"}, {"emoji": "✅", "name": "check mark button", "slug": "check_mark_button"}, {"emoji": "☑️", "name": "check box with check", "slug": "check_box_with_check"}, {"emoji": "✔️", "name": "check mark", "slug": "check_mark"}, {"emoji": "❌", "name": "cross mark", "slug": "cross_mark"}, {"emoji": "❎", "name": "cross mark button", "slug": "cross_mark_button"}, {"emoji": "➰", "name": "curly loop", "slug": "curly_loop"}, {"emoji": "➿", "name": "double curly loop", "slug": "double_curly_loop"}, {"emoji": "〽️", "name": "part alternation mark", "slug": "part_alternation_mark"}, {"emoji": "✳️", "name": "eight-spoked asterisk", "slug": "eight_spoked_asterisk"}, {"emoji": "✴️", "name": "eight-pointed star", "slug": "eight_pointed_star"}, {"emoji": "❇️", "name": "sparkle", "slug": "sparkle"}, {"emoji": "©️", "name": "copyright", "slug": "copyright"}, {"emoji": "®️", "name": "registered", "slug": "registered"}, {"emoji": "™️", "name": "trade mark", "slug": "trade_mark"}, {"emoji": "#️⃣", "name": "keycap #", "slug": "keycap_number_sign"}, {"emoji": "*️⃣", "name": "keycap *", "slug": "keycap_asterisk"}, {"emoji": "0️⃣", "name": "keycap 0", "slug": "keycap_0"}, {"emoji": "1️⃣", "name": "keycap 1", "slug": "keycap_1"}, {"emoji": "2️⃣", "name": "keycap 2", "slug": "keycap_2"}, {"emoji": "3️⃣", "name": "keycap 3", "slug": "keycap_3"}, {"emoji": "4️⃣", "name": "keycap 4", "slug": "keycap_4"}, {"emoji": "5️⃣", "name": "keycap 5", "slug": "keycap_5"}, {"emoji": "6️⃣", "name": "keycap 6", "slug": "keycap_6"}, {"emoji": "7️⃣", "name": "keycap 7", "slug": "keycap_7"}, {"emoji": "8️⃣", "name": "keycap 8", "slug": "keycap_8"}, {"emoji": "9️⃣", "name": "keycap 9", "slug": "keycap_9"}, {"emoji": "🔟", "name": "keycap 10", "slug": "keycap_10"}, {"emoji": "🔠", "name": "input latin uppercase", "slug": "input_latin_uppercase"}, {"emoji": "🔡", "name": "input latin lowercase", "slug": "input_latin_lowercase"}, {"emoji": "🔢", "name": "input numbers", "slug": "input_numbers"}, {"emoji": "🔣", "name": "input symbols", "slug": "input_symbols"}, {"emoji": "🔤", "name": "input latin letters", "slug": "input_latin_letters"}, {"emoji": "🅰️", "name": "A button (blood type)", "slug": "a_button"}, {"emoji": "🆎", "name": "AB button (blood type)", "slug": "ab_button"}, {"emoji": "🅱️", "name": "B button (blood type)", "slug": "b_button"}, {"emoji": "🆑", "name": "CL button", "slug": "cl_button"}, {"emoji": "🆒", "name": "COOL button", "slug": "cool_button"}, {"emoji": "🆓", "name": "FREE button", "slug": "free_button"}, {"emoji": "ℹ️", "name": "information", "slug": "information"}, {"emoji": "🆔", "name": "ID button", "slug": "id_button"}, {"emoji": "Ⓜ️", "name": "circled M", "slug": "circled_m"}, {"emoji": "🆕", "name": "NEW button", "slug": "new_button"}, {"emoji": "🆖", "name": "NG button", "slug": "ng_button"}, {"emoji": "🅾️", "name": "O button (blood type)", "slug": "o_button"}, {"emoji": "🆗", "name": "OK button", "slug": "ok_button"}, {"emoji": "🅿️", "name": "P button", "slug": "p_button"}, {"emoji": "🆘", "name": "SOS button", "slug": "sos_button"}, {"emoji": "🆙", "name": "UP! button", "slug": "up_button"}, {"emoji": "🆚", "name": "VS button", "slug": "vs_button"}, {"emoji": "🈁", "name": "Japanese “here” button", "slug": "japanese_here_button"}, {"emoji": "🈂️", "name": "Japanese “service charge” button", "slug": "japanese_service_charge_button"}, {"emoji": "🈷️", "name": "Japanese “monthly amount” button", "slug": "japanese_monthly_amount_button"}, {"emoji": "🈶", "name": "Japanese “not free of charge” button", "slug": "japanese_not_free_of_charge_button"}, {"emoji": "🈯", "name": "Japanese “reserved” button", "slug": "japanese_reserved_button"}, {"emoji": "🉐", "name": "Japanese “bargain” button", "slug": "japanese_bargain_button"}, {"emoji": "🈹", "name": "Japanese “discount” button", "slug": "japanese_discount_button"}, {"emoji": "🈚", "name": "Japanese “free of charge” button", "slug": "japanese_free_of_charge_button"}, {"emoji": "🈲", "name": "Japanese “prohibited” button", "slug": "japanese_prohibited_button"}, {"emoji": "🉑", "name": "Japanese “acceptable” button", "slug": "japanese_acceptable_button"}, {"emoji": "🈸", "name": "Japanese “application” button", "slug": "japanese_application_button"}, {"emoji": "🈴", "name": "Japanese “passing grade” button", "slug": "japanese_passing_grade_button"}, {"emoji": "🈳", "name": "Japanese “vacancy” button", "slug": "japanese_vacancy_button"}, {"emoji": "㊗️", "name": "Japanese “congratulations” button", "slug": "japanese_congratulations_button"}, {"emoji": "㊙️", "name": "Japanese “secret” button", "slug": "japanese_secret_button"}, {"emoji": "🈺", "name": "Japanese “open for business” button", "slug": "japanese_open_for_business_button"}, {"emoji": "🈵", "name": "Japanese “no vacancy” button", "slug": "japanese_no_vacancy_button"}, {"emoji": "🔴", "name": "red circle", "slug": "red_circle"}, {"emoji": "🟠", "name": "orange circle", "slug": "orange_circle"}, {"emoji": "🟡", "name": "yellow circle", "slug": "yellow_circle"}, {"emoji": "🟢", "name": "green circle", "slug": "green_circle"}, {"emoji": "🔵", "name": "blue circle", "slug": "blue_circle"}, {"emoji": "🟣", "name": "purple circle", "slug": "purple_circle"}, {"emoji": "🟤", "name": "brown circle", "slug": "brown_circle"}, {"emoji": "⚫", "name": "black circle", "slug": "black_circle"}, {"emoji": "⚪", "name": "white circle", "slug": "white_circle"}, {"emoji": "🟥", "name": "red square", "slug": "red_square"}, {"emoji": "🟧", "name": "orange square", "slug": "orange_square"}, {"emoji": "🟨", "name": "yellow square", "slug": "yellow_square"}, {"emoji": "🟩", "name": "green square", "slug": "green_square"}, {"emoji": "🟦", "name": "blue square", "slug": "blue_square"}, {"emoji": "🟪", "name": "purple square", "slug": "purple_square"}, {"emoji": "🟫", "name": "brown square", "slug": "brown_square"}, {"emoji": "⬛", "name": "black large square", "slug": "black_large_square"}, {"emoji": "⬜", "name": "white large square", "slug": "white_large_square"}, {"emoji": "◼️", "name": "black medium square", "slug": "black_medium_square"}, {"emoji": "◻️", "name": "white medium square", "slug": "white_medium_square"}, {"emoji": "◾", "name": "black medium-small square", "slug": "black_medium_small_square"}, {"emoji": "◽", "name": "white medium-small square", "slug": "white_medium_small_square"}, {"emoji": "▪️", "name": "black small square", "slug": "black_small_square"}, {"emoji": "▫️", "name": "white small square", "slug": "white_small_square"}, {"emoji": "🔶", "name": "large orange diamond", "slug": "large_orange_diamond"}, {"emoji": "🔷", "name": "large blue diamond", "slug": "large_blue_diamond"}, {"emoji": "🔸", "name": "small orange diamond", "slug": "small_orange_diamond"}, {"emoji": "🔹", "name": "small blue diamond", "slug": "small_blue_diamond"}, {"emoji": "🔺", "name": "red triangle pointed up", "slug": "red_triangle_pointed_up"}, {"emoji": "🔻", "name": "red triangle pointed down", "slug": "red_triangle_pointed_down"}, {"emoji": "💠", "name": "diamond with a dot", "slug": "diamond_with_a_dot"}, {"emoji": "🔘", "name": "radio button", "slug": "radio_button"}, {"emoji": "🔳", "name": "white square button", "slug": "white_square_button"}, {"emoji": "🔲", "name": "black square button", "slug": "black_square_button"}]}, {"name": "Flags", "slug": "flags", "emojis": [{"emoji": "🏁", "name": "chequered flag", "slug": "chequered_flag_racing_race"}, {"emoji": "🚩", "name": "triangular flag", "slug": "triangular_flag_post"}, {"emoji": "🎌", "name": "crossed flags", "slug": "crossed_flags_celebration"}, {"emoji": "🏴", "name": "black flag", "slug": "black_flag_waving"}, {"emoji": "🏳️", "name": "white flag", "slug": "white_flag_waving"}, {"emoji": "🏳️‍🌈", "name": "rainbow flag", "slug": "rainbow_flag_pride"}, {"emoji": "🏳️‍⚧️", "name": "transgender flag", "slug": "transgender_flag_pink_white"}, {"emoji": "🏴‍☠️", "name": "pirate flag", "slug": "pirate_flag_treasure"}, {"emoji": "🇦🇨", "name": "flag Ascension Island", "slug": "flag_ascension_island"}, {"emoji": "🇦🇩", "name": "flag Andorra", "slug": "flag_andorra"}, {"emoji": "🇦🇪", "name": "flag United Arab Emirates", "slug": "flag_united_arab_emirates"}, {"emoji": "🇦🇫", "name": "flag Afghanistan", "slug": "flag_afghanistan"}, {"emoji": "🇦🇬", "name": "flag Antigua & Barbuda", "slug": "flag_antigua_barbuda"}, {"emoji": "🇦🇮", "name": "flag <PERSON><PERSON><PERSON>", "slug": "flag_anguilla"}, {"emoji": "🇦🇱", "name": "flag Albania", "slug": "flag_albania"}, {"emoji": "🇦🇲", "name": "flag Armenia", "slug": "flag_armenia"}, {"emoji": "🇦🇴", "name": "flag Angola", "slug": "flag_angola"}, {"emoji": "🇦🇶", "name": "flag Antarctica", "slug": "flag_antarctica"}, {"emoji": "🇦🇷", "name": "flag Argentina", "slug": "flag_argentina"}, {"emoji": "🇦🇸", "name": "flag American Samoa", "slug": "flag_american_samoa"}, {"emoji": "🇦🇹", "name": "flag Austria", "slug": "flag_austria"}, {"emoji": "🇦🇺", "name": "flag Australia", "slug": "flag_australia"}, {"emoji": "🇦🇼", "name": "flag Aruba", "slug": "flag_aruba"}, {"emoji": "🇦🇽", "name": "flag Åland Islands", "slug": "flag_aland_islands"}, {"emoji": "🇦🇿", "name": "flag Azerbaijan", "slug": "flag_azerbaijan"}, {"emoji": "🇧🇦", "name": "flag Bosnia & Herzegovina", "slug": "flag_bosnia_herzegovina"}, {"emoji": "🇧🇧", "name": "flag Barbados", "slug": "flag_barbados"}, {"emoji": "🇧🇩", "name": "flag Bangladesh", "slug": "flag_bangladesh"}, {"emoji": "🇧🇪", "name": "flag Belgium", "slug": "flag_belgium"}, {"emoji": "🇧🇫", "name": "flag Burkina Faso", "slug": "flag_burkina_faso"}, {"emoji": "🇧🇬", "name": "flag Bulgaria", "slug": "flag_bulgaria"}, {"emoji": "🇧🇭", "name": "flag Bahrain", "slug": "flag_bahrain"}, {"emoji": "🇧🇮", "name": "flag Burundi", "slug": "flag_burundi"}, {"emoji": "🇧🇯", "name": "flag Benin", "slug": "flag_benin"}, {"emoji": "🇧🇱", "name": "flag <PERSON><PERSON>", "slug": "flag_st_bar<PERSON><PERSON>y"}, {"emoji": "🇧🇲", "name": "flag Bermuda", "slug": "flag_bermuda"}, {"emoji": "🇧🇳", "name": "flag Brunei", "slug": "flag_brunei"}, {"emoji": "🇧🇴", "name": "flag Bolivia", "slug": "flag_bolivia"}, {"emoji": "🇧🇶", "name": "flag Caribbean Netherlands", "slug": "flag_caribbean_netherlands"}, {"emoji": "🇧🇷", "name": "flag Brazil", "slug": "flag_brazil"}, {"emoji": "🇧🇸", "name": "flag Bahamas", "slug": "flag_bahamas"}, {"emoji": "🇧🇹", "name": "flag Bhutan", "slug": "flag_bhutan"}, {"emoji": "🇧🇻", "name": "flag Bouvet Island", "slug": "flag_bouvet_island"}, {"emoji": "🇧🇼", "name": "flag Botswana", "slug": "flag_botswana"}, {"emoji": "🇧🇾", "name": "flag Belarus", "slug": "flag_belarus"}, {"emoji": "🇧🇿", "name": "flag Belize", "slug": "flag_belize"}, {"emoji": "🇨🇦", "name": "flag Canada", "slug": "flag_canada"}, {"emoji": "🇨🇨", "name": "flag Cocos (Keeling) Islands", "slug": "flag_cocos_islands"}, {"emoji": "🇨🇩", "name": "flag Congo - Kinshasa", "slug": "flag_congo_kinshasa"}, {"emoji": "🇨🇫", "name": "flag Central African Republic", "slug": "flag_central_african_republic"}, {"emoji": "🇨🇬", "name": "flag Congo - Brazzaville", "slug": "flag_congo_brazzaville"}, {"emoji": "🇨🇭", "name": "flag Switzerland", "slug": "flag_switzerland"}, {"emoji": "🇨🇮", "name": "flag Côte d’Ivoire", "slug": "flag_cote_d_ivoire"}, {"emoji": "🇨🇰", "name": "flag Cook Islands", "slug": "flag_cook_islands"}, {"emoji": "🇨🇱", "name": "flag Chile", "slug": "flag_chile"}, {"emoji": "🇨🇲", "name": "flag Cameroon", "slug": "flag_cameroon"}, {"emoji": "🇨🇳", "name": "flag China", "slug": "flag_china"}, {"emoji": "🇨🇴", "name": "flag Colombia", "slug": "flag_colombia"}, {"emoji": "🇨🇵", "name": "flag C<PERSON>perton Island", "slug": "flag_clipperton_island"}, {"emoji": "🇨🇷", "name": "flag Costa Rica", "slug": "flag_costa_rica"}, {"emoji": "🇨🇺", "name": "flag Cuba", "slug": "flag_cuba"}, {"emoji": "🇨🇻", "name": "flag Cape Verde", "slug": "flag_cape_verde"}, {"emoji": "🇨🇼", "name": "flag C<PERSON><PERSON><PERSON>", "slug": "flag_curacao"}, {"emoji": "🇨🇽", "name": "flag Christmas Island", "slug": "flag_christmas_island"}, {"emoji": "🇨🇾", "name": "flag Cyprus", "slug": "flag_cyprus"}, {"emoji": "🇨🇿", "name": "flag Czechia", "slug": "flag_czechia"}, {"emoji": "🇩🇪", "name": "flag Germany", "slug": "flag_germany"}, {"emoji": "🇩🇬", "name": "flag <PERSON>", "slug": "flag_diego_garcia"}, {"emoji": "🇩🇯", "name": "flag Djibouti", "slug": "flag_djibouti"}, {"emoji": "🇩🇰", "name": "flag Denmark", "slug": "flag_denmark"}, {"emoji": "🇩🇲", "name": "flag <PERSON><PERSON>", "slug": "flag_dominica"}, {"emoji": "🇩🇴", "name": "flag Dominican Republic", "slug": "flag_dominican_republic"}, {"emoji": "🇩🇿", "name": "flag Algeria", "slug": "flag_algeria"}, {"emoji": "🇪🇦", "name": "flag Ceuta & Melilla", "slug": "flag_ceuta_melilla"}, {"emoji": "🇪🇨", "name": "flag Ecuador", "slug": "flag_ecuador"}, {"emoji": "🇪🇪", "name": "flag Estonia", "slug": "flag_estonia"}, {"emoji": "🇪🇬", "name": "flag Egypt", "slug": "flag_egypt"}, {"emoji": "🇪🇭", "name": "flag Western Sahara", "slug": "flag_western_sahara"}, {"emoji": "🇪🇷", "name": "flag <PERSON><PERSON><PERSON>", "slug": "flag_eritrea"}, {"emoji": "🇪🇸", "name": "flag Spain", "slug": "flag_spain"}, {"emoji": "🇪🇹", "name": "flag Ethiopia", "slug": "flag_ethiopia"}, {"emoji": "🇪🇺", "name": "flag European Union", "slug": "flag_european_union"}, {"emoji": "🇫🇮", "name": "flag Finland", "slug": "flag_finland"}, {"emoji": "🇫🇯", "name": "flag Fiji", "slug": "flag_fiji"}, {"emoji": "🇫🇰", "name": "flag Falkland Islands", "slug": "flag_falkland_islands"}, {"emoji": "🇫🇲", "name": "flag Micronesia", "slug": "flag_micronesia"}, {"emoji": "🇫🇴", "name": "flag Faroe Islands", "slug": "flag_faroe_islands"}, {"emoji": "🇫🇷", "name": "flag France", "slug": "flag_france"}, {"emoji": "🇬🇦", "name": "flag Gabon", "slug": "flag_gabon"}, {"emoji": "🇬🇧", "name": "flag United Kingdom", "slug": "flag_united_kingdom"}, {"emoji": "🇬🇩", "name": "flag Grenada", "slug": "flag_grenada"}, {"emoji": "🇬🇪", "name": "flag Georgia", "slug": "flag_georgia"}, {"emoji": "🇬🇫", "name": "flag French Guiana", "slug": "flag_french_guiana"}, {"emoji": "🇬🇬", "name": "flag Guernsey", "slug": "flag_guernsey"}, {"emoji": "🇬🇭", "name": "flag Ghana", "slug": "flag_ghana"}, {"emoji": "🇬🇮", "name": "flag Gibraltar", "slug": "flag_gibraltar"}, {"emoji": "🇬🇱", "name": "flag Greenland", "slug": "flag_greenland"}, {"emoji": "🇬🇲", "name": "flag Gambia", "slug": "flag_gambia"}, {"emoji": "🇬🇳", "name": "flag Guinea", "slug": "flag_guinea"}, {"emoji": "🇬🇵", "name": "flag Guadeloupe", "slug": "flag_guadeloupe"}, {"emoji": "🇬🇶", "name": "flag Equatorial Guinea", "slug": "flag_equatorial_guinea"}, {"emoji": "🇬🇷", "name": "flag Greece", "slug": "flag_greece"}, {"emoji": "🇬🇸", "name": "flag South Georgia & South Sandwich Islands", "slug": "flag_south_georgia_south_sandwich_islands"}, {"emoji": "🇬🇹", "name": "flag Guatemala", "slug": "flag_guatemala"}, {"emoji": "🇬🇺", "name": "flag Guam", "slug": "flag_guam"}, {"emoji": "🇬🇼", "name": "flag Guinea-Bissau", "slug": "flag_guinea_bissau"}, {"emoji": "🇬🇾", "name": "flag Guyana", "slug": "flag_guyana"}, {"emoji": "🇭🇰", "name": "flag Hong Kong SAR China", "slug": "flag_hong_kong_sar_china"}, {"emoji": "🇭🇲", "name": "flag Heard & McDonald Islands", "slug": "flag_heard_mcdonald_islands"}, {"emoji": "🇭🇳", "name": "flag Honduras", "slug": "flag_honduras"}, {"emoji": "🇭🇷", "name": "flag Croatia", "slug": "flag_croatia"}, {"emoji": "🇭🇹", "name": "flag Haiti", "slug": "flag_haiti"}, {"emoji": "🇭🇺", "name": "flag Hungary", "slug": "flag_hungary"}, {"emoji": "🇮🇨", "name": "flag Canary Islands", "slug": "flag_canary_islands"}, {"emoji": "🇮🇩", "name": "flag Indonesia", "slug": "flag_indonesia"}, {"emoji": "🇮🇪", "name": "flag Ireland", "slug": "flag_ireland"}, {"emoji": "🇮🇱", "name": "flag Israel", "slug": "flag_israel"}, {"emoji": "🇮🇲", "name": "flag Isle of Man", "slug": "flag_isle_of_man"}, {"emoji": "🇮🇳", "name": "flag India", "slug": "flag_india"}, {"emoji": "🇮🇴", "name": "flag British Indian Ocean Territory", "slug": "flag_british_indian_ocean_territory"}, {"emoji": "🇮🇶", "name": "flag Iraq", "slug": "flag_iraq"}, {"emoji": "🇮🇷", "name": "flag Iran", "slug": "flag_iran"}, {"emoji": "🇮🇸", "name": "flag Iceland", "slug": "flag_iceland"}, {"emoji": "🇮🇹", "name": "flag Italy", "slug": "flag_italy"}, {"emoji": "🇯🇪", "name": "flag Jersey", "slug": "flag_jersey"}, {"emoji": "🇯🇲", "name": "flag Jamaica", "slug": "flag_jamaica"}, {"emoji": "🇯🇴", "name": "flag Jordan", "slug": "flag_jordan"}, {"emoji": "🇯🇵", "name": "flag Japan", "slug": "flag_japan"}, {"emoji": "🇰🇪", "name": "flag Kenya", "slug": "flag_kenya"}, {"emoji": "🇰🇬", "name": "flag Kyrgyzstan", "slug": "flag_kyrgyzstan"}, {"emoji": "🇰🇭", "name": "flag Cambodia", "slug": "flag_cambodia"}, {"emoji": "🇰🇮", "name": "flag <PERSON><PERSON><PERSON><PERSON>", "slug": "flag_kiribati"}, {"emoji": "🇰🇲", "name": "flag <PERSON><PERSON>", "slug": "flag_comoros"}, {"emoji": "🇰🇳", "name": "flag St. Kitts & Nevis", "slug": "flag_st_kitts_nevis"}, {"emoji": "🇰🇵", "name": "flag North Korea", "slug": "flag_north_korea"}, {"emoji": "🇰🇷", "name": "flag South Korea", "slug": "flag_south_korea"}, {"emoji": "🇰🇼", "name": "flag Kuwait", "slug": "flag_kuwait"}, {"emoji": "🇰🇾", "name": "flag Cayman Islands", "slug": "flag_cayman_islands"}, {"emoji": "🇰🇿", "name": "flag Kazakhstan", "slug": "flag_kazakhstan"}, {"emoji": "🇱🇦", "name": "flag Laos", "slug": "flag_laos"}, {"emoji": "🇱🇧", "name": "flag Lebanon", "slug": "flag_lebanon"}, {"emoji": "🇱🇨", "name": "flag St. Lucia", "slug": "flag_st_lucia"}, {"emoji": "🇱🇮", "name": "flag Liechtenstein", "slug": "flag_liechtenstein"}, {"emoji": "🇱🇰", "name": "flag Sri Lanka", "slug": "flag_sri_lanka"}, {"emoji": "🇱🇷", "name": "flag Liberia", "slug": "flag_liberia"}, {"emoji": "🇱🇸", "name": "flag Lesotho", "slug": "flag_lesotho"}, {"emoji": "🇱🇹", "name": "flag Lithuania", "slug": "flag_lithuania"}, {"emoji": "🇱🇺", "name": "flag Luxembourg", "slug": "flag_luxembourg"}, {"emoji": "🇱🇻", "name": "flag Latvia", "slug": "flag_latvia"}, {"emoji": "🇱🇾", "name": "flag Libya", "slug": "flag_libya"}, {"emoji": "🇲🇦", "name": "flag Morocco", "slug": "flag_morocco"}, {"emoji": "🇲🇨", "name": "flag Monaco", "slug": "flag_monaco"}, {"emoji": "🇲🇩", "name": "flag Moldova", "slug": "flag_moldova"}, {"emoji": "🇲🇪", "name": "flag Montenegro", "slug": "flag_montenegro"}, {"emoji": "🇲🇫", "name": "flag <PERSON><PERSON>", "slug": "flag_st_martin"}, {"emoji": "🇲🇬", "name": "flag Madagascar", "slug": "flag_madagascar"}, {"emoji": "🇲🇭", "name": "flag Marshall Islands", "slug": "flag_marshall_islands"}, {"emoji": "🇲🇰", "name": "flag North Macedonia", "slug": "flag_north_macedonia"}, {"emoji": "🇲🇱", "name": "flag Mali", "slug": "flag_mali"}, {"emoji": "🇲🇲", "name": "flag Myanmar (Burma)", "slug": "flag_myanmar"}, {"emoji": "🇲🇳", "name": "flag Mongolia", "slug": "flag_mongolia"}, {"emoji": "🇲🇴", "name": "flag Macao SAR China", "slug": "flag_macao_sar_china"}, {"emoji": "🇲🇵", "name": "flag Northern Mariana Islands", "slug": "flag_northern_mariana_islands"}, {"emoji": "🇲🇶", "name": "flag <PERSON><PERSON>", "slug": "flag_martinique"}, {"emoji": "🇲🇷", "name": "flag Mauritania", "slug": "flag_mauritania"}, {"emoji": "🇲🇸", "name": "flag <PERSON><PERSON><PERSON>", "slug": "flag_montserrat"}, {"emoji": "🇲🇹", "name": "flag Malta", "slug": "flag_malta"}, {"emoji": "🇲🇺", "name": "flag Mauritius", "slug": "flag_mauritius"}, {"emoji": "🇲🇻", "name": "flag Maldives", "slug": "flag_maldives"}, {"emoji": "🇲🇼", "name": "flag Malawi", "slug": "flag_malawi"}, {"emoji": "🇲🇽", "name": "flag Mexico", "slug": "flag_mexico"}, {"emoji": "🇲🇾", "name": "flag Malaysia", "slug": "flag_malaysia"}, {"emoji": "🇲🇿", "name": "flag Mozambique", "slug": "flag_mozambique"}, {"emoji": "🇳🇦", "name": "flag Namibia", "slug": "flag_namibia"}, {"emoji": "🇳🇨", "name": "flag New Caledonia", "slug": "flag_new_caledonia"}, {"emoji": "🇳🇪", "name": "flag Niger", "slug": "flag_niger"}, {"emoji": "🇳🇫", "name": "flag Norfolk Island", "slug": "flag_norfolk_island"}, {"emoji": "🇳🇬", "name": "flag Nigeria", "slug": "flag_nigeria"}, {"emoji": "🇳🇮", "name": "flag Nicaragua", "slug": "flag_nicaragua"}, {"emoji": "🇳🇱", "name": "flag Netherlands", "slug": "flag_netherlands"}, {"emoji": "🇳🇴", "name": "flag Norway", "slug": "flag_norway"}, {"emoji": "🇳🇵", "name": "flag Nepal", "slug": "flag_nepal"}, {"emoji": "🇳🇷", "name": "flag Nauru", "slug": "flag_nauru"}, {"emoji": "🇳🇺", "name": "flag <PERSON><PERSON>", "slug": "flag_niue"}, {"emoji": "🇳🇿", "name": "flag New Zealand", "slug": "flag_new_zealand"}, {"emoji": "🇴🇲", "name": "flag Oman", "slug": "flag_oman"}, {"emoji": "🇵🇦", "name": "flag Panama", "slug": "flag_panama"}, {"emoji": "🇵🇪", "name": "flag Peru", "slug": "flag_peru"}, {"emoji": "🇵🇫", "name": "flag French Polynesia", "slug": "flag_french_polynesia"}, {"emoji": "🇵🇬", "name": "flag Papua New Guinea", "slug": "flag_papua_new_guinea"}, {"emoji": "🇵🇭", "name": "flag Philippines", "slug": "flag_philippines"}, {"emoji": "🇵🇰", "name": "flag Pakistan", "slug": "flag_pakistan"}, {"emoji": "🇵🇱", "name": "flag Poland", "slug": "flag_poland"}, {"emoji": "🇵🇲", "name": "flag St. Pierre & Miquelon", "slug": "flag_st_pierre_miquelon"}, {"emoji": "🇵🇳", "name": "flag Pitcairn Islands", "slug": "flag_pitcairn_islands"}, {"emoji": "🇵🇷", "name": "flag Puerto Rico", "slug": "flag_puerto_rico"}, {"emoji": "🇵🇸", "name": "flag Palestinian Territories", "slug": "flag_palestinian_territories"}, {"emoji": "🇵🇹", "name": "flag Portugal", "slug": "flag_portugal"}, {"emoji": "🇵🇼", "name": "flag <PERSON><PERSON>", "slug": "flag_palau"}, {"emoji": "🇵🇾", "name": "flag Paraguay", "slug": "flag_paraguay"}, {"emoji": "🇶🇦", "name": "flag Qatar", "slug": "flag_qatar"}, {"emoji": "🇷🇪", "name": "flag Ré<PERSON>", "slug": "flag_reunion"}, {"emoji": "🇷🇴", "name": "flag Romania", "slug": "flag_romania"}, {"emoji": "🇷🇸", "name": "flag Serbia", "slug": "flag_serbia"}, {"emoji": "🇷🇺", "name": "flag Russia", "slug": "flag_russia"}, {"emoji": "🇷🇼", "name": "flag Rwanda", "slug": "flag_rwanda"}, {"emoji": "🇸🇦", "name": "flag Saudi Arabia", "slug": "flag_saudi_arabia"}, {"emoji": "🇸🇧", "name": "flag Solomon Islands", "slug": "flag_solomon_islands"}, {"emoji": "🇸🇨", "name": "flag Seychelles", "slug": "flag_seychelles"}, {"emoji": "🇸🇩", "name": "flag Sudan", "slug": "flag_sudan"}, {"emoji": "🇸🇪", "name": "flag Sweden", "slug": "flag_sweden"}, {"emoji": "🇸🇬", "name": "flag Singapore", "slug": "flag_singapore"}, {"emoji": "🇸🇭", "name": "flag St. Helena", "slug": "flag_st_helena"}, {"emoji": "🇸🇮", "name": "flag Slovenia", "slug": "flag_slovenia"}, {"emoji": "🇸🇯", "name": "flag Svalbard & Jan <PERSON>", "slug": "flag_svalbard_jan_mayen"}, {"emoji": "🇸🇰", "name": "flag Slovakia", "slug": "flag_slovakia"}, {"emoji": "🇸🇱", "name": "flag Sierra Leone", "slug": "flag_sierra_leone"}, {"emoji": "🇸🇲", "name": "flag San Marino", "slug": "flag_san_marino"}, {"emoji": "🇸🇳", "name": "flag Senegal", "slug": "flag_senegal"}, {"emoji": "🇸🇴", "name": "flag Somalia", "slug": "flag_somalia"}, {"emoji": "🇸🇷", "name": "flag Suriname", "slug": "flag_suriname"}, {"emoji": "🇸🇸", "name": "flag South Sudan", "slug": "flag_south_sudan"}, {"emoji": "🇸🇹", "name": "flag São Tomé & Príncipe", "slug": "flag_sao_tome_principe"}, {"emoji": "🇸🇻", "name": "flag El Salvador", "slug": "flag_el_salvador"}, {"emoji": "🇸🇽", "name": "flag <PERSON><PERSON>", "slug": "flag_sint_maarten"}, {"emoji": "🇸🇾", "name": "flag Syria", "slug": "flag_syria"}, {"emoji": "🇸🇿", "name": "flag <PERSON><PERSON><PERSON><PERSON>", "slug": "flag_eswatini"}, {"emoji": "🇹🇦", "name": "flag <PERSON>", "slug": "flag_tristan_<PERSON>_cunha"}, {"emoji": "🇹🇨", "name": "flag Turks & Caicos Islands", "slug": "flag_turks_caicos_islands"}, {"emoji": "🇹🇩", "name": "flag Chad", "slug": "flag_chad"}, {"emoji": "🇹🇫", "name": "flag French Southern Territories", "slug": "flag_french_southern_territories"}, {"emoji": "🇹🇬", "name": "flag Togo", "slug": "flag_togo"}, {"emoji": "🇹🇭", "name": "flag Thailand", "slug": "flag_thailand"}, {"emoji": "🇹🇯", "name": "flag Tajikistan", "slug": "flag_tajikistan"}, {"emoji": "🇹🇰", "name": "flag <PERSON><PERSON><PERSON>", "slug": "flag_tokelau"}, {"emoji": "🇹🇱", "name": "flag Timor-Leste", "slug": "flag_timor_leste"}, {"emoji": "🇹🇲", "name": "flag Turkmenistan", "slug": "flag_turkmenistan"}, {"emoji": "🇹🇳", "name": "flag Tunisia", "slug": "flag_tunisia"}, {"emoji": "🇹🇴", "name": "flag Tonga", "slug": "flag_tonga"}, {"emoji": "🇹🇷", "name": "flag Turkey", "slug": "flag_turkey"}, {"emoji": "🇹🇹", "name": "flag Trinidad & Tobago", "slug": "flag_trinidad_tobago"}, {"emoji": "🇹🇻", "name": "flag Tu<PERSON><PERSON>", "slug": "flag_tuvalu"}, {"emoji": "🇹🇼", "name": "flag Taiwan", "slug": "flag_taiwan"}, {"emoji": "🇹🇿", "name": "flag Tanzania", "slug": "flag_tanzania"}, {"emoji": "🇺🇦", "name": "flag Ukraine", "slug": "flag_ukraine"}, {"emoji": "🇺🇬", "name": "flag Uganda", "slug": "flag_uganda"}, {"emoji": "🇺🇲", "name": "flag U.S. Outlying Islands", "slug": "flag_u_s_outlying_islands"}, {"emoji": "🇺🇳", "name": "flag United Nations", "slug": "flag_united_nations"}, {"emoji": "🇺🇸", "name": "flag United States", "slug": "flag_united_states"}, {"emoji": "🇺🇾", "name": "flag Uruguay", "slug": "flag_uruguay"}, {"emoji": "🇺🇿", "name": "flag Uzbekistan", "slug": "flag_uzbekistan"}, {"emoji": "🇻🇦", "name": "flag Vatican City", "slug": "flag_vatican_city"}, {"emoji": "🇻🇨", "name": "flag St. Vincent & Grenadines", "slug": "flag_st_vincent_grenadines"}, {"emoji": "🇻🇪", "name": "flag Venezuela", "slug": "flag_venezuela"}, {"emoji": "🇻🇬", "name": "flag British Virgin Islands", "slug": "flag_british_virgin_islands"}, {"emoji": "🇻🇮", "name": "flag U.S. Virgin Islands", "slug": "flag_u_s_virgin_islands"}, {"emoji": "🇻🇳", "name": "flag Vietnam", "slug": "flag_vietnam"}, {"emoji": "🇻🇺", "name": "flag Vanuatu", "slug": "flag_vanuatu"}, {"emoji": "🇼🇫", "name": "flag Wallis & Futuna", "slug": "flag_wallis_futuna"}, {"emoji": "🇼🇸", "name": "flag Samoa", "slug": "flag_samoa"}, {"emoji": "🇽🇰", "name": "flag Kosovo", "slug": "flag_kosovo"}, {"emoji": "🇾🇪", "name": "flag Yemen", "slug": "flag_yemen"}, {"emoji": "🇾🇹", "name": "flag <PERSON><PERSON>", "slug": "flag_mayotte"}, {"emoji": "🇿🇦", "name": "flag South Africa", "slug": "flag_south_africa"}, {"emoji": "🇿🇲", "name": "flag Zambia", "slug": "flag_zambia"}, {"emoji": "🇿🇼", "name": "flag Zimbabwe", "slug": "flag_zimbabwe"}, {"emoji": "🏴󠁧󠁢󠁥󠁮󠁧󠁿", "name": "flag England", "slug": "flag_england"}, {"emoji": "🏴󠁧󠁢󠁳󠁣󠁴󠁿", "name": "flag Scotland", "slug": "flag_scotland"}, {"emoji": "🏴󠁧󠁢󠁷󠁬󠁳󠁿", "name": "flag Wales", "slug": "flag_wales"}]}]