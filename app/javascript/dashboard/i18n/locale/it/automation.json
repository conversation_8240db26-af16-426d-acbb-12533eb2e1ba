{"AUTOMATION": {"HEADER": "Automazione", "DESCRIPTION": "Automation can replace and streamline existing processes that require manual effort, such as adding labels and assigning conversations to the most suitable agent. This allows the team to focus on their strengths while reducing time spent on routine tasks.", "LEARN_MORE": "Learn more about automation", "HEADER_BTN_TXT": "Aggiungi regola di automazione", "LOADING": "Recupero delle regole di automazione", "ADD": {"TITLE": "Aggiungi regola di automazione", "SUBMIT": "<PERSON><PERSON>", "CANCEL_BUTTON_TEXT": "<PERSON><PERSON><PERSON>", "FORM": {"NAME": {"LABEL": "Nome regola", "PLACEHOLDER": "Inserisci il nome della regola", "ERROR": "Il nome è obbligatorio"}, "DESC": {"LABEL": "Descrizione", "PLACEHOLDER": "Inserisci la descrizione della regola", "ERROR": "La descrizione è obbligatoria"}, "EVENT": {"LABEL": "Evento", "PLACEHOLDER": "Si prega di selezionarne uno", "ERROR": "L'evento è obbligatorio"}, "CONDITIONS": {"LABEL": "Condizioni"}, "ACTIONS": {"LABEL": "Azioni"}}, "CONDITION_BUTTON_LABEL": "Aggiungi condizione", "ACTION_BUTTON_LABEL": "Aggiungi azione", "API": {"SUCCESS_MESSAGE": "Regola di automazione aggiunta con successo", "ERROR_MESSAGE": "Impossibile creare una regola di automazione, riprova più tardi"}}, "LIST": {"TABLE_HEADER": {"NAME": "Nome", "DESCRIPTION": "Descrizione", "ACTIVE": "Attivo", "CREATED_ON": "Creato il"}, "404": "Nessuna regola di automazione trovata"}, "DELETE": {"TITLE": "Elimina regola di automazione", "SUBMIT": "Elimina", "CANCEL_BUTTON_TEXT": "<PERSON><PERSON><PERSON>", "CONFIRM": {"TITLE": "Conferma eliminazione", "MESSAGE": "Sei sicuro di voler eliminare ", "YES": "Sì, elimina ", "NO": "No, conserva "}, "API": {"SUCCESS_MESSAGE": "Regola di automazione eliminata con successo", "ERROR_MESSAGE": "Impossibile eliminare una regola di automazione, riprova più tardi"}}, "EDIT": {"TITLE": "Modifica regola di automazione", "SUBMIT": "Aggiorna", "CANCEL_BUTTON_TEXT": "<PERSON><PERSON><PERSON>", "API": {"SUCCESS_MESSAGE": "Regola di automazione aggiornata con successo", "ERROR_MESSAGE": "Impossibile aggiornare la regola di automazione, riprova più tardi"}}, "CLONE": {"TOOLTIP": "Clona", "API": {"SUCCESS_MESSAGE": "Automazione clonata con successo", "ERROR_MESSAGE": "Impossibile clonare la regola di automazione, riprova più tardi"}}, "FORM": {"EDIT": "Modifica", "CREATE": "<PERSON><PERSON>", "DELETE": "Elimina", "CANCEL": "<PERSON><PERSON><PERSON>", "RESET_MESSAGE": "Cambiare il tipo di evento resetterà le condizioni e gli eventi che hai aggiunto di seguito"}, "CONDITION": {"DELETE_MESSAGE": "È necessario avere almeno una condizione per salvare", "CONTACT_CUSTOM_ATTR_LABEL": "Contact Custom Attributes", "CONVERSATION_CUSTOM_ATTR_LABEL": "Conversation Custom Attributes"}, "ACTION": {"DELETE_MESSAGE": "È necessario avere almeno una azione da salvare", "TEAM_MESSAGE_INPUT_PLACEHOLDER": "Inserisci qui il tuo messaggio", "TEAM_DROPDOWN_PLACEHOLDER": "Seleziona i team", "EMAIL_INPUT_PLACEHOLDER": "Enter email", "URL_INPUT_PLACEHOLDER": "Enter URL"}, "TOGGLE": {"ACTIVATION_TITLE": "Attiva regola di automazione", "DEACTIVATION_TITLE": "Disattiva regola di automazione", "ACTIVATION_DESCRIPTION": "Questa azione attiverà la regola di automazione '{automationName}'. Sei sicuro di voler procedere?", "DEACTIVATION_DESCRIPTION": "Questa azione disattiverà la regola di automazione '{automationName}'. Sei sicuro di voler procedere?", "ACTIVATION_SUCCESFUL": "Regola di automazione attivata con successo", "DEACTIVATION_SUCCESFUL": "Regola di automazione disattivata con successo", "ACTIVATION_ERROR": "Impossibile attivare l'automazione, riprova più tardi", "DEACTIVATION_ERROR": "Impossibile disattivare l'automazione, riprova più tardi", "CONFIRMATION_LABEL": "Sì", "CANCEL_LABEL": "No"}, "ATTACHMENT": {"UPLOAD_ERROR": "Impossibile caricare l'allegato, si prega di riprovare", "LABEL_IDLE": "Carica allegato", "LABEL_UPLOADING": "Caricamento...", "LABEL_UPLOADED": "Successfully Uploaded", "LABEL_UPLOAD_FAILED": "Caricamento fallito"}, "ERRORS": {"ATTRIBUTE_KEY_REQUIRED": "Attribute key is required", "FILTER_OPERATOR_REQUIRED": "Filter operator is required", "VALUE_REQUIRED": "Il valore è obbligatorio", "VALUE_MUST_BE_BETWEEN_1_AND_998": "Value must be between 1 and 998", "ACTION_PARAMETERS_REQUIRED": "Action parameters are required", "ATLEAST_ONE_CONDITION_REQUIRED": "At least one condition is required", "ATLEAST_ONE_ACTION_REQUIRED": "At least one action is required"}, "NONE_OPTION": "<PERSON><PERSON><PERSON>", "EVENTS": {"CONVERSATION_CREATED": "Conversazione creata", "CONVERSATION_UPDATED": "Conversazione aggiornata", "MESSAGE_CREATED": "Message Created", "CONVERSATION_OPENED": "Conversation Opened"}, "ACTIONS": {"ASSIGN_AGENT": "Assign to Agent", "ASSIGN_TEAM": "Assign a Team", "ADD_LABEL": "Add a Label", "REMOVE_LABEL": "Remove a Label", "SEND_EMAIL_TO_TEAM": "Send an Email to Team", "SEND_EMAIL_TRANSCRIPT": "Send an Email Transcript", "MUTE_CONVERSATION": "Silenzia conversazione", "SNOOZE_CONVERSATION": "Posticipa conversazione", "RESOLVE_CONVERSATION": "Risolvi la conversazione", "SEND_WEBHOOK_EVENT": "Send Webhook Event", "SEND_ATTACHMENT": "Send Attachment", "SEND_MESSAGE": "Send a Message", "CHANGE_PRIORITY": "Change Priority", "ADD_SLA": "Add SLA"}, "ATTRIBUTES": {"MESSAGE_TYPE": "Message Type", "MESSAGE_CONTAINS": "Message Contains", "EMAIL": "Email", "INBOX": "<PERSON><PERSON>", "CONVERSATION_LANGUAGE": "Conversation Language", "PHONE_NUMBER": "Numero di telefono", "STATUS": "Stato", "BROWSER_LANGUAGE": "Lingua del browser", "MAIL_SUBJECT": "Email Subject", "COUNTRY_NAME": "<PERSON><PERSON>", "REFERER_LINK": "Referrer Link", "ASSIGNEE_NAME": "Assignee", "TEAM_NAME": "Team", "PRIORITY": "Priorità"}}}