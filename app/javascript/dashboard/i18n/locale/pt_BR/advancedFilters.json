{"FILTER": {"TITLE": "Filtrar conversas", "SUBTITLE": "Adicione seus filtros abaixo e clique em 'Aplicar filtros' para reduzir a desordem na conversa.", "EDIT_CUSTOM_FILTER": "<PERSON><PERSON>", "CUSTOM_VIEWS_SUBTITLE": "Adicione ou remova filtros e atualize sua pasta.", "ADD_NEW_FILTER": "<PERSON><PERSON><PERSON><PERSON> filtro", "FILTER_DELETE_ERROR": "Ops! Parece que não podemos salvar nada! Por favor, adicione pelo menos um filtro para salvá-lo.", "SUBMIT_BUTTON_LABEL": "Aplicar filtros", "UPDATE_BUTTON_LABEL": "Atualizar pasta", "CANCEL_BUTTON_LABEL": "<PERSON><PERSON><PERSON>", "CLEAR_BUTTON_LABEL": "Limpar filtros", "FOLDER_LABEL": "Nome da pasta", "FOLDER_QUERY_LABEL": "Consulta de Pasta", "EMPTY_VALUE_ERROR": "Valor obrigatório.", "TOOLTIP_LABEL": "Filtrar conversas", "QUERY_DROPDOWN_LABELS": {"AND": "E", "OR": "OU"}, "INPUT_PLACEHOLDER": "Inserir valor", "OPERATOR_LABELS": {"equal_to": "Igual a", "not_equal_to": "Di<PERSON><PERSON>", "does_not_contain": "Não contém", "is_present": "Está presente", "is_not_present": "Não está presente", "is_greater_than": "<PERSON> maior que", "is_less_than": "É menor que", "days_before": "É <PERSON> dias antes", "starts_with": "Começa com", "equalTo": "Igual a", "notEqualTo": "Di<PERSON><PERSON>", "contains": "<PERSON><PERSON><PERSON>", "doesNotContain": "Não contém", "isPresent": "Está presente", "isNotPresent": "Não está presente", "isGreaterThan": "<PERSON> maior que", "isLessThan": "É menor que", "daysBefore": "É <PERSON> dias antes", "startsWith": "Começa com"}, "ATTRIBUTE_LABELS": {"TRUE": "<PERSON><PERSON><PERSON><PERSON>", "FALSE": "<PERSON><PERSON><PERSON>"}, "ATTRIBUTES": {"STATUS": "Status", "ASSIGNEE_NAME": "Agente at<PERSON>", "INBOX_NAME": "Caixa de Entrada", "TEAM_NAME": "Nome do Time", "CONVERSATION_IDENTIFIER": "Identificador da conversa", "CAMPAIGN_NAME": "<PERSON><PERSON> da campanha", "LABELS": "Etiquetas", "BROWSER_LANGUAGE": "Idioma do navegador", "PRIORITY": "Prioridade", "COUNTRY_NAME": "Nome do País", "REFERER_LINK": "<PERSON> de origem", "CUSTOM_ATTRIBUTE_LIST": "Lista", "CUSTOM_ATTRIBUTE_TEXT": "Texto", "CUSTOM_ATTRIBUTE_NUMBER": "Número", "CUSTOM_ATTRIBUTE_LINK": "Link", "CUSTOM_ATTRIBUTE_CHECKBOX": "Checkbox", "CREATED_AT": "C<PERSON><PERSON> em", "LAST_ACTIVITY": "Última atividade"}, "ERRORS": {"VALUE_REQUIRED": "Valor obrigatório", "ATTRIBUTE_KEY_REQUIRED": "A chave do atributo é necessária", "FILTER_OPERATOR_REQUIRED": "Operador do filtro é necessário", "VALUE_MUST_BE_BETWEEN_1_AND_998": "O valor deve ser entre 1 e 998"}, "GROUPS": {"STANDARD_FILTERS": "<PERSON><PERSON><PERSON>", "ADDITIONAL_FILTERS": "<PERSON><PERSON><PERSON> adicionais", "CUSTOM_ATTRIBUTES": "Atributos personalizados"}, "CUSTOM_VIEWS": {"ADD": {"TITLE": "Você quer salvar este filtro?", "LABEL": "Nomear este filtro", "PLACEHOLDER": "Nomeie seu filtro para referenciá-lo posteriormente.", "ERROR_MESSAGE": "O nome é obrigatório.", "SAVE_BUTTON": "<PERSON><PERSON> filtro", "CANCEL_BUTTON": "<PERSON><PERSON><PERSON>", "API_FOLDERS": {"SUCCESS_MESSAGE": "Pasta criada com sucesso.", "ERROR_MESSAGE": "Erro ao criar pasta."}, "API_SEGMENTS": {"SUCCESS_MESSAGE": "Segmento criado com sucesso.", "ERROR_MESSAGE": "Erro ao criar segmento."}}, "EDIT": {"EDIT_BUTTON": "Alterar <PERSON>"}, "DELETE": {"DELETE_BUTTON": "Excluir filtro", "MODAL": {"CONFIRM": {"TITLE": "Confirmar exclusão", "MESSAGE": "Tem certeza que deseja excluir o filtro?", "YES": "Sim, excluir", "NO": "Não, mantenha-o"}}, "API_FOLDERS": {"SUCCESS_MESSAGE": "Pasta excluída com sucesso.", "ERROR_MESSAGE": "Erro ao excluir pasta."}, "API_SEGMENTS": {"SUCCESS_MESSAGE": "Segmento apagado com sucesso.", "ERROR_MESSAGE": "Erro ao excluir segmento."}}}}}