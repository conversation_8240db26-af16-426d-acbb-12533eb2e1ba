{"INBOX_MGMT": {"HEADER": "Caixas de Entrada", "DESCRIPTION": "Um canal é o modo de comunicação que seu cliente escolhe para interagir com você. Uma caixa de entrada é onde você gerencia interações para um canal específico. Pode incluir comunicações de várias fontes, como e-mail, chat ao vivo e mídia social.", "LEARN_MORE": "<PERSON><PERSON> mais sobre as caixas de entrada", "RECONNECTION_REQUIRED": "Sua caixa de entrada está desconectada. Você não receberá novas mensagens até reautorizar.", "CLICK_TO_RECONNECT": "Clique aqui para reconectar.", "LIST": {"404": "Não há caixas de entrada anexadas a esta conta."}, "CREATE_FLOW": {"CHANNEL": {"TITLE": "Escolha o Canal", "BODY": "Escolha o provedor que você deseja integrar com o Chatwoot."}, "INBOX": {"TITLE": "Criar Caixa de Entrada", "BODY": "Autenticar sua conta e criar uma caixa de entrada."}, "AGENT": {"TITLE": "<PERSON><PERSON><PERSON><PERSON>", "BODY": "Adicionar agentes à caixa de entrada criada."}, "FINISH": {"TITLE": "Então!", "BODY": "Está tudo pronto para começar!"}}, "ADD": {"CHANNEL_NAME": {"LABEL": "Nome da Caixa de Entrada", "PLACEHOLDER": "Digite o nome da caixa de entrada (ex: Acme Inc)", "ERROR": "Por favor, insira um nome completo válido"}, "WEBSITE_NAME": {"LABEL": "Nome do site", "PLACEHOLDER": "Informe o nome do seu site (por exemplo: Acme Inc)"}, "FB": {"HELP": "Obs: ao fazer login, apenas temos acesso às mensagens da sua página. Suas mensagens privadas nunca podem ser acessadas pelo Chatwoot.", "CHOOSE_PAGE": "<PERSON><PERSON><PERSON><PERSON>", "CHOOSE_PLACEHOLDER": "Selecione uma página da lista", "INBOX_NAME": "Nome da Caixa de Entrada", "ADD_NAME": "Adicione um nome para sua caixa de entrada", "PICK_NAME": "Escolha um nome para sua caixa de entrada", "PICK_A_VALUE": "Escolha um valor", "CREATE_INBOX": "Criar Caixa de Entrada"}, "INSTAGRAM": {"CONTINUE_WITH_INSTAGRAM": "Continuar com o Instagram", "CONNECT_YOUR_INSTAGRAM_PROFILE": "Conecte seu perfil do Instagram", "HELP": "Para adicionar seu perfil do Instagram como um canal, você precisa autenticar seu perfil do Instagram clicando em 'Continuar com o Instagram' ", "ERROR_MESSAGE": "Houve um erro ao conectar ao Instagram, por favor, tente novamente", "ERROR_AUTH": "Houve um erro ao conectar ao Instagram, por favor, tente novamente", "NEW_INBOX_SUGGESTION": "Esta conta do Instagram estava conectada a uma caixa de entrada diferente e agora foi migrada para aqui. <PERSON><PERSON> as novas mensagens aparecerão aqui. A caixa de entrada antiga não poderá mais enviar ou receber mensagens para esta conta.", "DUPLICATE_INBOX_BANNER": "Esta conta do Instagram foi migrada para a nova caixa de entrada de canal do Instagram. Você não poderá mais enviar/receber mensagens do Instagram desta caixa de entrada."}, "TWITTER": {"HELP": "Para adicionar seu perfil do Twitter como um canal, você precisa autenticar seu perfil do Twitter clicando em 'Entrar com o Twitter' ", "ERROR_MESSAGE": "Houve um erro ao conectar com o Twitter, por favor, tente novamente", "TWEETS": {"ENABLE": "Criar conversas a partir dos Tweets mencionados"}}, "WEBSITE_CHANNEL": {"TITLE": "Canal do website", "DESC": "Crie um canal para seu site e comece a oferecer suporte a seus clientes através do nosso widget do site.", "LOADING_MESSAGE": "Criando canal de suporte ao site", "CHANNEL_AVATAR": {"LABEL": "Imagem do <PERSON>"}, "CHANNEL_WEBHOOK_URL": {"LABEL": "URL do webhook", "PLACEHOLDER": "Insira o URL do seu webhook", "ERROR": "Por favor, insira uma URL válida"}, "CHANNEL_DOMAIN": {"LABEL": "Domínio do website", "PLACEHOLDER": "Informe o domínio do seu site (por exemplo: acme.com)"}, "CHANNEL_WELCOME_TITLE": {"LABEL": "<PERSON><PERSON> bem-vindo", "PLACEHOLDER": "Ol<PERSON> !"}, "CHANNEL_WELCOME_TAGLINE": {"LABEL": "Bem-vindo, saudação", "PLACEHOLDER": "Nós tornamos simples a conexão conosco. Pergunte qualquer assunto ou compartilhe seus comentários."}, "CHANNEL_GREETING_MESSAGE": {"LABEL": "Mensagem de saudação do canal", "PLACEHOLDER": "Acme Inc normalmente responde em algumas horas."}, "CHANNEL_GREETING_TOGGLE": {"LABEL": "Ativar saudação do canal", "HELP_TEXT": "Enviar automaticamente uma mensagem de saudação quando uma nova conversa é criada.", "ENABLED": "<PERSON><PERSON>do", "DISABLED": "Desativado"}, "REPLY_TIME": {"TITLE": "Definir o Tempo de Resposta", "IN_A_FEW_MINUTES": "Em alguns minutos", "IN_A_FEW_HOURS": "Em algumas horas", "IN_A_DAY": "Em um dia", "HELP_TEXT": "Este tempo de resposta será exibido no widget do chat"}, "WIDGET_COLOR": {"LABEL": "<PERSON><PERSON> do <PERSON>idget", "PLACEHOLDER": "Atualize a cor do widget"}, "SUBMIT_BUTTON": "Criar caixa de entrada", "API": {"ERROR_MESSAGE": "Não conseguimos criar um canal de site, por favor, tente novamente"}}, "TWILIO": {"TITLE": "Canal Twilio SMS/WhatsApp", "DESC": "Integre o Twilio e comece a oferecer suporte a seus clientes por SMS ou WhatsApp.", "ACCOUNT_SID": {"LABEL": "SID da Conta", "PLACEHOLDER": "Por favor, insira o SID sua conta no Twilio", "ERROR": "Este campo é obrigatório"}, "API_KEY": {"USE_API_KEY": "Usar autenticação de chave API", "LABEL": "Chave da <PERSON> SID", "PLACEHOLDER": "Por favor, insira sua chave de API SID", "ERROR": "Este campo é obrigatório"}, "API_KEY_SECRET": {"LABEL": "<PERSON><PERSON><PERSON> da <PERSON>ve <PERSON>", "PLACEHOLDER": "Por favor, use sua API Key Secret", "ERROR": "Este campo é obrigatório"}, "MESSAGING_SERVICE_SID": {"LABEL": "SID do Serviço de Mensagens", "PLACEHOLDER": "Por favor, informe seu SID do Serviço de Mensagens do Twilio", "ERROR": "Este campo é obrigatório", "USE_MESSAGING_SERVICE": "Usar um Serviço de Mensagens do Twilio"}, "CHANNEL_TYPE": {"LABEL": "Tipo de canal", "ERROR": "Por favor, selecione seu tipo de canal"}, "AUTH_TOKEN": {"LABEL": "Token de autenticação", "PLACEHOLDER": "Por favor, digite seu Token de Autenticação do Twilio", "ERROR": "Este campo é obrigatório"}, "CHANNEL_NAME": {"LABEL": "Nome da Caixa de Entrada", "PLACEHOLDER": "Por favor, digite um nome para caixa de entrada", "ERROR": "Este campo é obrigatório"}, "PHONE_NUMBER": {"LABEL": "Número de Telefone", "PLACEHOLDER": "Por favor, insira o número de telefone do qual a mensagem será enviada.", "ERROR": "Por favor, forneça um número de telefone válido que comece com o símbolo \"+\" e não contenha espaços."}, "API_CALLBACK": {"TITLE": "URL de Callback", "SUBTITLE": "Você precisa configurar a URL de Callback de mensagem no Twilio com a URL mencionada aqui."}, "SUBMIT_BUTTON": "Criar canal Twilio", "API": {"ERROR_MESSAGE": "Não fomos capazes de autenticar as creden<PERSON><PERSON>, por favor, tente novamente"}}, "SMS": {"TITLE": "Canal SMS", "DESC": "Comece a oferecer suporte a seus clientes por SMS.", "PROVIDERS": {"LABEL": "Provedor de API", "TWILIO": "<PERSON><PERSON><PERSON>", "BANDWIDTH": "Bandwidth"}, "API": {"ERROR_MESSAGE": "Não foi possível salvar o canal SMS"}, "BANDWIDTH": {"ACCOUNT_ID": {"LABEL": "ID da Conta", "PLACEHOLDER": "Por favor, insira o ID de sua conta no Bandwidth", "ERROR": "Este campo é obrigatório"}, "API_KEY": {"LABEL": "Chave API", "PLACEHOLDER": "Insira sua chave API Bandwidth", "ERROR": "Este campo é obrigatório"}, "API_SECRET": {"LABEL": "Chave secreta API", "PLACEHOLDER": "Insira sua API Secret do Bandwidth", "ERROR": "Este campo é obrigatório"}, "APPLICATION_ID": {"LABEL": "ID da aplicação", "PLACEHOLDER": "Por favor, insira o ID de sua conta no Bandwidth", "ERROR": "Este campo é obrigatório"}, "INBOX_NAME": {"LABEL": "Nome da Caixa de Entrada", "PLACEHOLDER": "Por favor, digite um nome para caixa de entrada", "ERROR": "Este campo é obrigatório"}, "PHONE_NUMBER": {"LABEL": "Número de telefone", "PLACEHOLDER": "Por favor, insira o número de telefone do qual a mensagem será enviada.", "ERROR": "Por favor, forneça um número de telefone válido que comece com o símbolo \"+\" e não contenha espaços."}, "SUBMIT_BUTTON": "Criar canal Bandwidth", "API": {"ERROR_MESSAGE": "Não pudemos autenticar as creden<PERSON><PERSON> de Bandwidth, por favor, tente novamente"}, "API_CALLBACK": {"TITLE": "URL de Callback", "SUBTITLE": "Você precisa configurar a URL de callback de mensagem no Bandwidth com a URL mencionada aqui."}}}, "WHATSAPP": {"TITLE": "Canal do WhatsApp", "DESC": "Comece a oferecer suporte a seus clientes pelo WhatsApp.", "PROVIDERS": {"LABEL": "Provedor de API", "TWILIO": "<PERSON><PERSON><PERSON>", "WHATSAPP_CLOUD": "Cloud do WhatsApp", "360_DIALOG": "360Dialog"}, "INBOX_NAME": {"LABEL": "Nome da Caixa de Entrada", "PLACEHOLDER": "Por favor, digite um nome para caixa de entrada", "ERROR": "Este campo é obrigatório"}, "PHONE_NUMBER": {"LABEL": "Número de telefone", "PLACEHOLDER": "Por favor, insira o número de telefone do qual a mensagem será enviada.", "ERROR": "Por favor, forneça um número de telefone válido que comece com o símbolo \"+\" e não contém quaisquer espaços."}, "PHONE_NUMBER_ID": {"LABEL": "ID do número de telefone", "PLACEHOLDER": "Por favor, insira o ID do número de telefone obtido do painel do desenvolvedor do Facebook.", "ERROR": "Por favor, insira um valor válido."}, "BUSINESS_ACCOUNT_ID": {"LABEL": "ID da conta do WhatsApp Business", "PLACEHOLDER": "Por favor, insira o ID da conta do WhatsApp Business obtido do painel do desenvolvedor do Facebook.", "ERROR": "Por favor, insira um valor válido."}, "WEBHOOK_VERIFY_TOKEN": {"LABEL": "Token de verificação do Webhook", "PLACEHOLDER": "Insira um token de verificação que você deseja configurar para webhooks do Facebook.", "ERROR": "Por favor, insira um valor válido."}, "API_KEY": {"LABEL": "<PERSON><PERSON> da <PERSON>", "SUBTITLE": "Configure a chave API do WhatsApp.", "PLACEHOLDER": "<PERSON><PERSON> da <PERSON>", "ERROR": "Por favor, insira um valor válido."}, "API_CALLBACK": {"TITLE": "URL de callback", "SUBTITLE": "Você deve configurar a URL do webhook e o token de verificação no portal do desenvolvedor do Facebook com os valores mostrados abaixo.", "WEBHOOK_URL": "URL do Webhook", "WEBHOOK_VERIFICATION_TOKEN": "Token de verificação Webhook"}, "SUBMIT_BUTTON": "Criar canal do WhatsApp", "API": {"ERROR_MESSAGE": "Não foi possível salvar o canal do WhatsApp"}}, "API_CHANNEL": {"TITLE": "Canal da API", "DESC": "Integre com canal API e comece a ajudar seus clientes.", "CHANNEL_NAME": {"LABEL": "Nome do Canal", "PLACEHOLDER": "Por favor, insira um nome de canal", "ERROR": "Este campo é obrigatório"}, "WEBHOOK_URL": {"LABEL": "URL do Webhook", "SUBTITLE": "Configure a URL onde você deseja receber callbacks em eventos.", "PLACEHOLDER": "URL do Webhook"}, "SUBMIT_BUTTON": "Criar canal de API", "API": {"ERROR_MESSAGE": "Não foi possível salvar o canal de API"}}, "EMAIL_CHANNEL": {"TITLE": "Canal de e-mail", "DESC": "Integre sua caixa de entrada de e-mail.", "CHANNEL_NAME": {"LABEL": "Nome do Canal", "PLACEHOLDER": "Por favor, insira um nome de canal", "ERROR": "Este campo é obrigatório"}, "EMAIL": {"LABEL": "e-mail", "SUBTITLE": "E-mail para onde os seus clientes lhe enviam tickets de suporte", "PLACEHOLDER": "e-mail"}, "SUBMIT_BUTTON": "Criar canal de e-mail", "API": {"ERROR_MESSAGE": "Não foi possível salvar o canal de e-mail"}, "FINISH_MESSAGE": "Comece a encaminhar seus e-mails para o seguinte endereço de e-mail."}, "LINE_CHANNEL": {"TITLE": "Canal LINE", "DESC": "Integre com o canal LINE e comece a apoiar seus clientes.", "CHANNEL_NAME": {"LABEL": "Nome do Canal", "PLACEHOLDER": "Por favor, insira um nome de canal", "ERROR": "Este campo é obrigatório"}, "LINE_CHANNEL_ID": {"LABEL": "ID do canal LINE", "PLACEHOLDER": "ID do canal LINE"}, "LINE_CHANNEL_SECRET": {"LABEL": "Canal de LINHA secreto", "PLACEHOLDER": "Canal de LINHA secreto"}, "LINE_CHANNEL_TOKEN": {"LABEL": "ID do canal Token", "PLACEHOLDER": "ID do canal Token"}, "SUBMIT_BUTTON": "Criar Canal", "API": {"ERROR_MESSAGE": "Não foi possível salvar o canal da LINHA"}, "API_CALLBACK": {"TITLE": "URL de retorno", "SUBTITLE": "Você precisa configurar a URL do webhook no aplicativo LINE com a URL mencionada aqui."}}, "TELEGRAM_CHANNEL": {"TITLE": "Canal do Telegram", "DESC": "Integre com o canal do Telegram e comece a apoiar seus clientes.", "BOT_TOKEN": {"LABEL": "Bot <PERSON>", "SUBTITLE": "Configure o token do bot que obteve do Telegram BotFather.", "PLACEHOLDER": "Bot <PERSON>"}, "SUBMIT_BUTTON": "Criar canal do Telegram", "API": {"ERROR_MESSAGE": "Não foi possível salvar o canal de e-mail"}}, "AUTH": {"TITLE": "Escolha um canal", "DESC": "O Chatwoot suporta widgets de chats ao vivo, Facebook Messenger, perfis do Twitter, WhatsApp, E-mails, etc., como canais. Se você quiser criar um canal personalizado, você pode criá-lo usando o canal API. Para começar, escolha um dos canais abaixo."}, "AGENTS": {"TITLE": "<PERSON><PERSON>", "DESC": "Aqui você pode adicionar agentes para gerenciar sua caixa de entrada recém-criada. Somente esses agentes selecionados terão acesso à sua caixa de entrada. Os agentes que não fazem parte desta caixa de entrada não poderão ver ou responder a mensagens nessa caixa de entrada quando fizerem login. <br> <b> PS: </b> Como administrador, se você precisar acessar todas as caixas de entrada, adicione-se como agente a todas as caixas de entrada criadas.", "VALIDATION_ERROR": "Adicione pelo menos um agente à sua nova caixa de entrada", "PICK_AGENTS": "Escolha agentes para a caixa de entrada"}, "DETAILS": {"TITLE": "Detalhes da Caixa de Entrada", "DESC": "No menu abaixo, selecione a Página do Facebook que você deseja se conectar ao Chatwoot. Você também pode dar um nome personalizado para sua caixa de entrada para uma melhor identificação."}, "FINISH": {"TITLE": "Tudo funcionando. Deu certo!", "DESC": "Você concluiu a integração da sua página do Facebook com o Chatwoot. Na próxima vez que um cliente enviar uma mensagem para sua página, a conversa aparecerá automaticamente na sua caixa de entrada. <br> Também estamos fornecendo um script de widget que você pode adicionar facilmente ao seu site. Assim que estiver disponível no seu site, os clientes poderão enviar mensagens diretamente do seu site, sem a ajuda de nenhuma ferramenta externa, e a conversa aparecerá aqui, no Chatwoot. Legal, hein? Com certeza estamos tentamos ser :)"}, "EMAIL_PROVIDER": {"TITLE": "Selecione seu provedor de e-mail", "DESCRIPTION": "Selecione um provedor de e-mail da lista abaixo. Se você não ver seu provedor de e-mail na lista, você pode selecionar a outra opção de provedor e fornecer as credenciais IMAP e SMTP."}, "MICROSOFT": {"TITLE": "Microsoft Email", "DESCRIPTION": "Clique no botão Entrar com a Microsoft para começar. Você será redirecionado para o login do e-mail. Após aceitar as permissões solicitadas, você será redirecionado de volta para a etapa de criação da caixa de entrada.", "EMAIL_PLACEHOLDER": "Digite o endereço de e-mail", "SIGN_IN": "Entre com uma conta Microsoft", "ERROR_MESSAGE": "Ocorreu um erro ao conectar com a Microsoft, por favor, tente novamente"}, "GOOGLE": {"TITLE": "E-mail do Google", "DESCRIPTION": "Clique no botão Entrar com o Google para começar. Você será redirecionado para o login do e-mail. Depois que você aceitar as permissões solicitadas, você será redirecionado de volta para a etapa de criação da caixa de entrada.", "SIGN_IN": "Entrar com o Google", "EMAIL_PLACEHOLDER": "Digite o endereço de e-mail", "ERROR_MESSAGE": "Houve um erro ao conectar com o Google, por favor, tente novamente"}}, "DETAILS": {"LOADING_FB": "Autenticando você com o Facebook...", "ERROR_FB_LOADING": "Erro ao carregar o SDK do Facebook. Por favor, desative qualquer bloqueador de anúncios e tente novamente de um navegador diferente.", "ERROR_FB_AUTH": "<PERSON>go deu errado, por favor, atualize a página...", "ERROR_FB_UNAUTHORIZED": "Você não está autorizado a realizar esta ação. ", "ERROR_FB_UNAUTHORIZED_HELP": "A tradução é:\n\nPor favor, certifique-se de que você tem acesso à página do Facebook com controle total. Você pode ler mais sobre as funções do Facebook <a href=\" https://www.facebook.com/help/187316341316631\">aqui</a>.", "CREATING_CHANNEL": "Criando sua caixa de entrada...", "TITLE": "Configurar <PERSON><PERSON><PERSON> da Caixa de Entrada", "DESC": ""}, "AGENTS": {"BUTTON_TEXT": "Adicionar agentes", "ADD_AGENTS": "Adicionando agentes à sua caixa de entrada..."}, "FINISH": {"TITLE": "Sua caixa de entrada está pronta!", "MESSAGE": "Agora você ja pode oferecer uma excelente experiência no atendimento de seus clientes através do seu novo Canal", "BUTTON_TEXT": "Leva-me lá", "MORE_SETTINGS": "<PERSON><PERSON> configu<PERSON>", "WEBSITE_SUCCESS": "Você concluiu a criação de um canal de site. Copie o código mostrado abaixo e cole-o no seu site. Na próxima vez que um cliente usar o bate-papo ao vivo, a conversa aparecerá automaticamente na sua caixa de entrada."}, "REAUTH": "<PERSON><PERSON><PERSON><PERSON>", "VIEW": "Visualizar", "EDIT": {"API": {"SUCCESS_MESSAGE": "Configurações de caixa de entrada atualizadas com sucesso", "AUTO_ASSIGNMENT_SUCCESS_MESSAGE": "Agente atualizado com sucesso", "ERROR_MESSAGE": "Não foi possível atualizar as configurações da caixa de entrada. Por favor, tente novamente mais tarde."}, "EMAIL_COLLECT_BOX": {"ENABLED": "<PERSON><PERSON>do", "DISABLED": "Desativado"}, "ENABLE_CSAT": {"ENABLED": "<PERSON><PERSON>do", "DISABLED": "Desativado"}, "SENDER_NAME_SECTION": {"TITLE": "Nome do remetente", "SUB_TEXT": "Selecione o nome mostrado ao seu cliente quando ele recebe e-mails dos seus agentes.", "FOR_EG": "Por ex:", "FRIENDLY": {"TITLE": "Amigável", "FROM": "de", "SUBTITLE": "Adicione o nome do agente que enviou a resposta ao nome do remetente para torná-la amigável."}, "PROFESSIONAL": {"TITLE": "Profissional", "SUBTITLE": "Utilize apenas o nome da empresa configurada como nome do remetente no cabeçalho do e-mail."}, "BUSINESS_NAME": {"BUTTON_TEXT": "Configure o nome da sua empresa", "PLACEHOLDER": "Insira o nome de sua empresa", "SAVE_BUTTON_TEXT": "<PERSON><PERSON>"}}, "ALLOW_MESSAGES_AFTER_RESOLVED": {"ENABLED": "<PERSON><PERSON>do", "DISABLED": "Desativado"}, "ENABLE_CONTINUITY_VIA_EMAIL": {"ENABLED": "<PERSON><PERSON>do", "DISABLED": "Desativado"}, "LOCK_TO_SINGLE_CONVERSATION": {"ENABLED": "<PERSON><PERSON>do", "DISABLED": "Desativado"}, "ENABLE_HMAC": {"LABEL": "Ativar"}}, "DELETE": {"BUTTON_TEXT": "Excluir", "AVATAR_DELETE_BUTTON_TEXT": "<PERSON>pagar Avatar", "CONFIRM": {"TITLE": "Confirmar exclusão", "MESSAGE": "Você tem certeza que deseja excluir ", "PLACE_HOLDER": "Digite {inboxName} para confirmar", "YES": "Sim, excluir ", "NO": "Não, Mantenha "}, "API": {"SUCCESS_MESSAGE": "Agente excluído com sucesso", "ERROR_MESSAGE": "Não foi possível excluir a caixa de entrada. Tente novamente mais tarde.", "AVATAR_SUCCESS_MESSAGE": "Perfil da caixa de entrada excluído com sucesso", "AVATAR_ERROR_MESSAGE": "Não foi possível excluir o perfil da caixa de entrada. Por favor, tente novamente mais tarde."}}, "TABS": {"SETTINGS": "Configurações", "COLLABORATORS": "Colaboradores", "CONFIGURATION": "Configuração", "CAMPAIGN": "<PERSON><PERSON><PERSON>", "PRE_CHAT_FORM": "<PERSON><PERSON><PERSON><PERSON>", "BUSINESS_HOURS": "Horário de funcionamento", "WIDGET_BUILDER": "<PERSON><PERSON><PERSON><PERSON>", "BOT_CONFIGURATION": "Configuração do Bot", "CSAT": "CSAT"}, "SETTINGS": "Configurações", "FEATURES": {"LABEL": "Funcionalidades", "DISPLAY_FILE_PICKER": "<PERSON><PERSON><PERSON> se<PERSON>or de arquivos no widget", "DISPLAY_EMOJI_PICKER": "<PERSON><PERSON><PERSON> se<PERSON>or de emoji no widget", "ALLOW_END_CONVERSATION": "Permitir que usuários terminem a conversa a partir do widget", "USE_INBOX_AVATAR_FOR_BOT": "Use o nome da caixa de entrada e avatar do bot"}, "SETTINGS_POPUP": {"MESSENGER_HEADING": "<PERSON><PERSON><PERSON> <scripit>", "MESSENGER_SUB_HEAD": "<PERSON>avor, insira essse código <script> dentro da tag Body de sua página html", "INBOX_AGENTS": "<PERSON><PERSON>", "INBOX_AGENTS_SUB_TEXT": "Adicionar ou remover agentes dessa caixa de entrada", "AGENT_ASSIGNMENT": "Atribuição de conversa", "AGENT_ASSIGNMENT_SUB_TEXT": "Atualizar configurações de atribuição de conversa", "UPDATE": "<PERSON><PERSON><PERSON><PERSON>", "ENABLE_EMAIL_COLLECT_BOX": "Ativar caixa de coleta de e-mail", "ENABLE_EMAIL_COLLECT_BOX_SUB_TEXT": "Ativar ou desativar caixa de coleta de e-mails em novas conversas", "AUTO_ASSIGNMENT": "Habilitar atribuição automática", "SENDER_NAME_SECTION": "Habilitar o Nome do Agente no E-mail", "SENDER_NAME_SECTION_TEXT": "Ativar/Desativar exibição do nome do agente no e-mail, se estiver desativado, exibirá o nome da empresa", "ENABLE_CONTINUITY_VIA_EMAIL": "Habilitar continuidade das conversas por e-mail", "ENABLE_CONTINUITY_VIA_EMAIL_SUB_TEXT": "As conversas continuarão sobre o e-mail se o endereço de e-mail de contato estiver disponível.", "LOCK_TO_SINGLE_CONVERSATION": "Bloquear para conversa única", "LOCK_TO_SINGLE_CONVERSATION_SUB_TEXT": "Ativar ou desativar várias conversas para o mesmo contato nesta caixa de entrada", "INBOX_UPDATE_TITLE": "Configurações da Caixa de Entrada", "INBOX_UPDATE_SUB_TEXT": "Atualize suas configurações de caixa de entrada", "AUTO_ASSIGNMENT_SUB_TEXT": "Ativar ou desativar a atribuição automática de novas conversas aos agentes adicionados a essa caixa de entrada.", "HMAC_VERIFICATION": "Validação de Identidade do Usuário", "HMAC_DESCRIPTION": "Para validar a identidade do usuário, você pode passar um `identifier_hash` para cada usuário. Você pode gerar um hash HMAC sha256 usando o `identifier` com a chave mostrada aqui.", "HMAC_LINK_TO_DOCS": "Você pode ler mais aqui.", "HMAC_MANDATORY_VERIFICATION": "Forçar validação de identidade do usuário", "HMAC_MANDATORY_DESCRIPTION": "Se ativado, as solicitações sem o 'identifier_hash' serão rejeitadas.", "INBOX_IDENTIFIER": "Identificador da caixa de entrada", "INBOX_IDENTIFIER_SUB_TEXT": "Use o token 'inbox_identifier' mostrado aqui para autenticar os seus clientes API.", "FORWARD_EMAIL_TITLE": "Encaminhar para o E-mail", "FORWARD_EMAIL_SUB_TEXT": "Comece a encaminhar seus e-mails para o seguinte endereço de e-mail.", "ALLOW_MESSAGES_AFTER_RESOLVED": "<PERSON><PERSON><PERSON> men<PERSON> após a resolução da conversa", "ALLOW_MESSAGES_AFTER_RESOLVED_SUB_TEXT": "Permite que os usuários finais enviem mensagens mesmo depois que a conversa for resolvida.", "WHATSAPP_SECTION_SUBHEADER": "Esta chave de API é usada para a integração com as APIs do WhatsApp.", "WHATSAPP_SECTION_UPDATE_SUBHEADER": "Insira a nova chave API a ser utilizada para integração com as APIs do WhatsApp.", "WHATSAPP_SECTION_TITLE": "Chave API", "WHATSAPP_SECTION_UPDATE_TITLE": "Atualizar Chave de <PERSON>", "WHATSAPP_SECTION_UPDATE_PLACEHOLDER": "Digite a nova chave de API aqui", "WHATSAPP_SECTION_UPDATE_BUTTON": "<PERSON><PERSON><PERSON><PERSON>", "WHATSAPP_WEBHOOK_TITLE": "Token de verificação Webhook", "WHATSAPP_WEBHOOK_SUBHEADER": "Este token é usado para verificar a autenticidade do webhook endpoint.", "UPDATE_PRE_CHAT_FORM_SETTINGS": "Atualizar configurações do Formulário Pre Chat"}, "HELP_CENTER": {"LABEL": "Centro de Ajuda", "PLACEHOLDER": "Selecionar Centro de Ajuda", "SELECT_PLACEHOLDER": "Selecionar Centro de Ajuda", "REMOVE": "Remover Centro de Ajuda", "SUB_TEXT": "Anexe um Centro de Ajuda com a caixa de entrada"}, "AUTO_ASSIGNMENT": {"MAX_ASSIGNMENT_LIMIT": "Limite de atribuição automática", "MAX_ASSIGNMENT_LIMIT_RANGE_ERROR": "Por favor, insira um valor maior que 0", "MAX_ASSIGNMENT_LIMIT_SUB_TEXT": "Limitar o número máximo de conversas desta caixa de entrada que pode ser atribuído automaticamente a um agente"}, "FACEBOOK_REAUTHORIZE": {"TITLE": "<PERSON><PERSON><PERSON><PERSON>", "SUBTITLE": "Sua conexão com o Facebook expirou, reconecte sua página do Facebook para continuar", "MESSAGE_SUCCESS": "Reconexão bem sucedida", "MESSAGE_ERROR": "Ocorreu um erro, por favor tente novamente"}, "PRE_CHAT_FORM": {"DESCRIPTION": "Formulários de bate-papo permitem que você capture informações de usuário antes de iniciar uma conversa com você.", "SET_FIELDS": "Campos do formulário Pré Chat", "SET_FIELDS_HEADER": {"FIELDS": "Campos", "LABEL": "Nome do campo", "PLACE_HOLDER": "Valor de exemplo", "KEY": "Chave", "TYPE": "Tipo", "REQUIRED": "Obrigatório"}, "ENABLE": {"LABEL": "Ativar formul<PERSON>rio de bate-papo antes", "OPTIONS": {"ENABLED": "<PERSON>m", "DISABLED": "Não"}}, "PRE_CHAT_MESSAGE": {"LABEL": "Mensagem pré chat", "PLACEHOLDER": "Esta mensagem será visível para os usuários junto com o formulário"}, "REQUIRE_EMAIL": {"LABEL": "Os visitantes devem fornecer seu nome e endereço de e-mail antes de iniciar o bate-papo"}}, "CSAT": {"TITLE": "Habilitar CSAT", "SUBTITLE": "Iniciar automaticamente pesquisas de CSAT no final das conversas para entender como os clientes se sentem em relação à sua experiência de suporte. Acompanhe as tendências de satisfação e identifique áreas para melhoria ao longo do tempo.", "DISPLAY_TYPE": {"LABEL": "Tipo de exibição"}, "MESSAGE": {"LABEL": "Mensagem", "PLACEHOLDER": "Digite uma mensagem para mostrar aos usuários com o formulário"}, "SURVEY_RULE": {"LABEL": "Regra de pesquisa", "DESCRIPTION_PREFIX": "Enviar a pesquisa se a conversa", "DESCRIPTION_SUFFIX": "qualquer uma das etiquetas", "OPERATOR": {"CONTAINS": "contém", "DOES_NOT_CONTAINS": "não contém"}, "SELECT_PLACEHOLDER": "selecionar etiquetas"}, "NOTE": "Nota: pesquisas de CSAT são enviadas apenas uma vez por conversa", "API": {"SUCCESS_MESSAGE": "Configurações de CSAT atualizadas com sucesso", "ERROR_MESSAGE": "Não foi possível atualizar as configurações do CSAT. Por favor, tente novamente mais tarde."}}, "BUSINESS_HOURS": {"TITLE": "Definir a sua disponibilidade", "SUBTITLE": "Defina a sua disponibilidade no widget livechat", "WEEKLY_TITLE": "Definir horas semanais", "TIMEZONE_LABEL": "Selecionar fuso horário", "UPDATE": "Atualizar configurações do horário comercial", "TOGGLE_AVAILABILITY": "Permitir a disponibilidade de negócios para essa caixa de entrada", "UNAVAILABLE_MESSAGE_LABEL": "Mensagem indisponível para visitantes", "TOGGLE_HELP": "Permitir a disponibilidade de negócios mostrará as horas disponíveis no widget de bate-papo ao vivo, mesmo que todos os agentes estejam offline. Os vistores disponíveis horários externos podem ser avisados com uma mensagem e um formulário de pré-bate-papo.", "DAY": {"ENABLE": "Permitir a disponibilidade para este dia", "UNAVAILABLE": "Indisponível", "HOURS": "horas", "VALIDATION_ERROR": "Hora inicial deve ser antes de hora de fechamento.", "CHOOSE": "Selecione"}, "ALL_DAY": "O dia todo"}, "IMAP": {"TITLE": "IMAP", "SUBTITLE": "Defina seus dados IMAP", "NOTE_TEXT": "Para habilitar o SMTP, por favor configure o IMAP.", "UPDATE": "Atualizar configurações do IMAP", "TOGGLE_AVAILABILITY": "Habilitar a configuração IMAP para esta caixa de entrada", "TOGGLE_HELP": "Ativar o IMAP ajudará o usuário a receber e-mails", "EDIT": {"SUCCESS_MESSAGE": "Configurações IMAP atualizadas com sucesso", "ERROR_MESSAGE": "Não é possível atualizar as configurações IMAP"}, "ADDRESS": {"LABEL": "Endereço", "PLACE_HOLDER": "Endereço (Eg: imap.gmail.com)"}, "PORT": {"LABEL": "Porta", "PLACE_HOLDER": "Porta"}, "LOGIN": {"LABEL": "Entrar", "PLACE_HOLDER": "Entrar"}, "PASSWORD": {"LABEL": "<PERSON><PERSON>", "PLACE_HOLDER": "<PERSON><PERSON>"}, "ENABLE_SSL": "Habilitar o SSL"}, "MICROSOFT": {"TITLE": "Microsoft", "SUBTITLE": "Autorize novamente sua conta MICROSOFT"}, "SMTP": {"TITLE": "SMTP", "SUBTITLE": "Defina seus detalhes do SMTP", "UPDATE": "Atualizar configurações de SMTP", "TOGGLE_AVAILABILITY": "Ativar a configuração SMTP para esta caixa de entrada", "TOGGLE_HELP": "Habilitar o SMTP ajudará o usuário a enviar e-mail", "EDIT": {"SUCCESS_MESSAGE": "Configurações de SMTP atualizadas com sucesso", "ERROR_MESSAGE": "Não é possível atualizar as configurações de SMTP"}, "ADDRESS": {"LABEL": "Endereço", "PLACE_HOLDER": "Endereço (Eg: smtp.gmail.com)"}, "PORT": {"LABEL": "Porta", "PLACE_HOLDER": "Porta"}, "LOGIN": {"LABEL": "Entrar", "PLACE_HOLDER": "Entrar"}, "PASSWORD": {"LABEL": "<PERSON><PERSON>", "PLACE_HOLDER": "<PERSON><PERSON>"}, "DOMAIN": {"LABEL": "<PERSON><PERSON><PERSON>", "PLACE_HOLDER": "<PERSON><PERSON><PERSON>"}, "ENCRYPTION": "Criptografia", "SSL_TLS": "SSL/TLS", "START_TLS": "STARTTLS", "OPEN_SSL_VERIFY_MODE": "Abrir modo de verificação SSL", "AUTH_MECHANISM": "Autenticação"}, "NOTE": "Nota: ", "WIDGET_BUILDER": {"WIDGET_OPTIONS": {"AVATAR": {"LABEL": "Avatar do site", "DELETE": {"API": {"SUCCESS_MESSAGE": "Avatar excluído com sucesso", "ERROR_MESSAGE": "Ocorreu um erro, por favor tente novamente"}}}, "WEBSITE_NAME": {"LABEL": "Nome do site", "PLACE_HOLDER": "Informe o nome do seu site (por exemplo: Acme Inc)", "ERROR": "Por favor, insira um nome válido para o website"}, "WELCOME_HEADING": {"LABEL": "<PERSON><PERSON> bem-vindo", "PLACE_HOLDER": "Ol<PERSON>!"}, "WELCOME_TAGLINE": {"LABEL": "Bem-vindo, saudação", "PLACE_HOLDER": "Nós tornamos simples a conexão conosco. Pergunte qualquer assunto ou compartilhe seus comentários."}, "REPLY_TIME": {"LABEL": "Tempo de Resposta", "IN_A_FEW_MINUTES": "Em alguns minutos", "IN_A_FEW_HOURS": "Em algumas horas", "IN_A_DAY": "Em um dia"}, "WIDGET_COLOR_LABEL": "<PERSON><PERSON> do <PERSON>idget", "WIDGET_BUBBLE_POSITION_LABEL": "Posição do Balão do Widget", "WIDGET_BUBBLE_TYPE_LABEL": "Tipo de Balão do Widget", "WIDGET_BUBBLE_LAUNCHER_TITLE": {"DEFAULT": "Fale conosco no chat", "LABEL": "Título do disparador da Bolha do Widget", "PLACE_HOLDER": "Fale conosco no chat"}, "UPDATE": {"BUTTON_TEXT": "Atualizar Configurações do Widget", "API": {"SUCCESS_MESSAGE": "Configurações do widget atualizadas com sucesso", "ERROR_MESSAGE": "Não é possível atualizar as configurações do widget"}}, "WIDGET_VIEW_OPTION": {"PREVIEW": "Pré-visualizar", "SCRIPT": "<PERSON><PERSON><PERSON>"}, "WIDGET_BUBBLE_POSITION": {"LEFT": "E<PERSON>rda", "RIGHT": "<PERSON><PERSON><PERSON>"}, "WIDGET_BUBBLE_TYPE": {"STANDARD": "Padrão", "EXPANDED_BUBBLE": "Balão Expandido"}}, "WIDGET_SCREEN": {"DEFAULT": "Padrão", "CHAT": "Cha<PERSON>"}, "REPLY_TIME": {"IN_A_FEW_MINUTES": "Normalmente responde em alguns minutos", "IN_A_FEW_HOURS": "Normalmente responde em algumas horas", "IN_A_DAY": "Normalmente responde em um dia"}, "FOOTER": {"START_CONVERSATION_BUTTON_TEXT": "Iniciar <PERSON>", "CHAT_INPUT_PLACEHOLDER": "Digite sua mensagem"}, "BODY": {"TEAM_AVAILABILITY": {"ONLINE": "Estamos On-line", "OFFLINE": "Estamos ausentes no momento"}, "USER_MESSAGE": "Oi", "AGENT_MESSAGE": "O<PERSON><PERSON>"}, "BRANDING_TEXT": "Desenvolvido por Chatwoot", "SCRIPT_SETTINGS": "\n      window.chatwootSettings = {options};"}, "EMAIL_PROVIDERS": {"MICROSOFT": "Microsoft", "GOOGLE": "Google", "OTHER_PROVIDERS": "Outros Provedores"}, "CHANNELS": {"MESSENGER": "<PERSON>", "WEB_WIDGET": "Site", "TWITTER_PROFILE": "Twitter", "TWILIO_SMS": "SMS Twilio", "WHATSAPP": "WhatsApp", "SMS": "SMS", "EMAIL": "e-mail", "TELEGRAM": "Telegram", "LINE": "Line", "API": "Canal da API", "INSTAGRAM": "Instagram"}}}