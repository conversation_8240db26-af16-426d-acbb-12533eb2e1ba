{"AUDIT_LOGS": {"HEADER": "Auditoria", "HEADER_BTN_TXT": "Adicionar Logs de Auditoria", "LOADING": "Buscando Logs de Auditoria", "DESCRIPTION": "Logs de Auditoria mantêm um registro de atividades em sua conta, permitindo que você acompanhe e auditore sua conta, time ou serviços.", "LEARN_MORE": "Saiba mais sobre os logs de auditoria", "SEARCH_404": "Não existem itens correspondentes a esta consulta", "SIDEBAR_TXT": "<p><b>Logs de Auditoria</b> </p><p> Os Logs de Auditoria são rastros para eventos e ações em um Sistema Chatwoot. </p>", "LIST": {"404": "Não há Logs de Auditoria disponíveis nesta conta.", "TITLE": "Gerenciar Logs de Auditoria", "DESC": "Logs de auditoria são rastros para eventos e ações em um Sistema de Chatwoot.", "TABLE_HEADER": {"ACTIVITY": "<PERSON><PERSON><PERSON><PERSON>", "TIME": "Ação", "IP_ADDRESS": "Endereço IP"}}, "API": {"SUCCESS_MESSAGE": "AuditLogs recuperados com sucesso", "ERROR_MESSAGE": "Não foi possível conectar ao servidor Woot, por favor tente novamente mais tarde"}, "DEFAULT_USER": "Sistema", "AUTOMATION_RULE": {"ADD": "{agentName} nova regra de automação criada (#{id})", "EDIT": "{agentName} atualizou regra de automação (#{id})", "DELETE": "{agentName} excluiu uma regra de automação (#{id})"}, "ACCOUNT_USER": {"ADD": "{agent<PERSON><PERSON>} Con<PERSON><PERSON> {invitee} para sua conta como {role}", "EDIT": {"SELF": "{agentName} Alt<PERSON>u seu {attributes} para {values}", "OTHER": "{agentName} Alterou {attributes} de {user} para {values}", "DELETED": "{agentName} alterou {attributes} de um usuário excluído para {values}"}}, "INBOX": {"ADD": "{agentName} criou uma caixa de entrada (#{id})", "EDIT": "{agentName} atualizou uma caixa de entrada (#{id})", "DELETE": "{agentName} excluiu uma caixa de entrada (#{id})"}, "WEBHOOK": {"ADD": "{agentName} criou um webhook (##{id})", "EDIT": "{agentName} atualizou um webhook (#{id})", "DELETE": "{agentName} excluiu um webhook (#{id})"}, "USER_ACTION": {"SIGN_IN": "{<PERSON><PERSON><PERSON>} Se conectou", "SIGN_OUT": "{agent<PERSON>ame} Se desconectou"}, "TEAM": {"ADD": "{agentName} criou um time (#{id})", "EDIT": "{agentName} at<PERSON><PERSON><PERSON> um time (#{id})", "DELETE": "{agentName} excluiu um time (#{id})"}, "MACRO": {"ADD": "{agentName} criou uma nova macro (#{id})", "EDIT": "{agentName} atualizou uma macro (#{id})", "DELETE": "{agentName} excluiu uma macro (#{id})"}, "INBOX_MEMBER": {"ADD": "{agentName} adicionou {user} à caixa de entrada (#{inbox_id})", "REMOVE": "{agentName} removeu {user} da caixa de entrada (#{inbox_id})"}, "TEAM_MEMBER": {"ADD": "{agent<PERSON>ame} adicionou {user} ao time (#{team_id})", "REMOVE": "{agent<PERSON><PERSON>} removeu {user} do time (#{team_id})"}, "ACCOUNT": {"EDIT": "O {agentName} atualizou a configuração da conta (#{id})"}}}