{"AUDIT_LOGS": {"HEADER": "Audita <PERSON>", "HEADER_BTN_TXT": "<PERSON>vienot Audi<PERSON>", "LOADING": "Audita Žurnālu Iegūšana", "DESCRIPTION": "Audita žurnāli saglabā ierakstus par jūsu konta darbību aktivitātēm, <PERSON><PERSON><PERSON><PERSON> jums izsekot un pārbaudīt savu kontu, komandu vai pakalpoju<PERSON>.", "LEARN_MORE": "<PERSON><PERSON><PERSON><PERSON><PERSON> vairāk par audita žurnāliem", "SEARCH_404": "<PERSON><PERSON> v<PERSON>m nav at<PERSON>š<PERSON> vienumu", "SIDEBAR_TXT": "<p><b><PERSON><PERSON></b> </p><p> Audita Žurnāli ir notikumu un darbību pēdas Chatwoot sistēmā. </p>", "LIST": {"404": "<PERSON><PERSON><PERSON> k<PERSON> nav pieejami Audita Žurnāli.", "TITLE": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Audi<PERSON> Žurnā<PERSON>", "DESC": "Audita Žurnāli ir notikumu un darbību pēdas Chatwoot sistēmā.", "TABLE_HEADER": {"ACTIVITY": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "TIME": "Darbība", "IP_ADDRESS": "IP adrese"}}, "API": {"SUCCESS_MESSAGE": "AuditaŽurnāli ir ve<PERSON> i<PERSON>", "ERROR_MESSAGE": "Nevar izveidot savienojumu ar Woot serveri. <PERSON><PERSON><PERSON><PERSON>, vēlāk pamēģiniet vēlreiz"}, "DEFAULT_USER": "<PERSON><PERSON><PERSON><PERSON>", "AUTOMATION_RULE": {"ADD": "{agentName} izveidoja jaunu automatizācijas noteikumu (#{id})", "EDIT": "{agentName} atjaunināja automatizācijas noteikumu (#{id})", "DELETE": "{agentName} izdzēsa automatizācijas noteikumu (#{id})"}, "ACCOUNT_USER": {"ADD": "{agent<PERSON>ame} u<PERSON><PERSON><PERSON><PERSON> {invitee} k<PERSON><PERSON> kā {role}", "EDIT": {"SELF": "{agentName} main<PERSON><PERSON> savu {attributes} uz {values}", "OTHER": "{agentName} mainīja {user} {attributes} uz {values}", "DELETED": "{agentName} main<PERSON>ja dzēstā lietotāja {attributes} uz {values}"}}, "INBOX": {"ADD": "{agentName} izve<PERSON>ja jaunu i<PERSON> (#{id})", "EDIT": "{agentName} at<PERSON><PERSON><PERSON><PERSON><PERSON> i<PERSON> (#{id})", "DELETE": "{agentName} izd<PERSON><PERSON><PERSON> i<PERSON> (#{id})"}, "WEBHOOK": {"ADD": "{agentName} izveidoja jaunu webhook (#{id})", "EDIT": "{agentName} atjaunin<PERSON>ja webhook (#{id})", "DELETE": "{agentName} izd<PERSON><PERSON><PERSON> webhook (#{id})"}, "USER_ACTION": {"SIGN_IN": "{agent<PERSON><PERSON>} pier<PERSON><PERSON><PERSON><PERSON><PERSON>", "SIGN_OUT": "{agent<PERSON>ame} i<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "TEAM": {"ADD": "{agentName} iz<PERSON><PERSON><PERSON> jaunu komandu (#{id})", "EDIT": "{agentName} at<PERSON><PERSON><PERSON><PERSON><PERSON> komandu (#{id})", "DELETE": "{agentName} izd<PERSON><PERSON><PERSON> komandu (#{id})"}, "MACRO": {"ADD": "{agentName} izve<PERSON><PERSON> jaunu mak<PERSON> (#{id})", "EDIT": "{agentName} at<PERSON><PERSON><PERSON><PERSON><PERSON> makro (#{id})", "DELETE": "{agentName} izd<PERSON><PERSON><PERSON> makro (#{id})"}, "INBOX_MEMBER": {"ADD": "{agentName} pievienoja {user} iesūtnei(#{inbox_id})", "REMOVE": "{agentName} noņ<PERSON>ma {user} no iesūtnes(#{inbox_id})"}, "TEAM_MEMBER": {"ADD": "{agentName} pievienoja {user} komandai(#{team_id})", "REMOVE": "{agentName} noņ<PERSON>ma {user} no komandas(#{team_id})"}, "ACCOUNT": {"EDIT": "{agentName} at<PERSON><PERSON><PERSON><PERSON><PERSON> konta kon<PERSON> (#{id})"}}}