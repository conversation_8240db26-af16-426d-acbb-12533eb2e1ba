{"CANNED_MGMT": {"HEADER": "Sagatavotā<PERSON>", "LEARN_MORE": "<PERSON><PERSON><PERSON><PERSON><PERSON> vairāk par sagatavotajām atbildēm", "DESCRIPTION": "Sagatavotās atbildes ir iep<PERSON><PERSON>š uzrakstī<PERSON> atbilžu veid<PERSON>, kas palīdz <PERSON>tri atbildēt uz sarunu. <PERSON> sarunas laikā ievietotu gatavu atbildi, aģenti var ierakstīt r<PERSON> “/”, kam seko <PERSON>. ", "HEADER_BTN_TXT": "<PERSON><PERSON>not sagatavoto atbildi", "LOADING": "Notiek sagatavoto atbilžu iegūšana...", "SEARCH_404": "<PERSON><PERSON> v<PERSON>m nav at<PERSON>š<PERSON> vienumu.", "LIST": {"404": "<PERSON><PERSON><PERSON> k<PERSON> nav pieejama neviena sagatavota atbilde.", "TITLE": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> sagatavotās atbildes", "DESC": "Sagatavotās atbildes ir iep<PERSON>k<PERSON> definētas atbilžu veid<PERSON>, kuras var izmantot lai ātri nosūtītu atbildes uz sarunām.", "TABLE_HEADER": {"SHORT_CODE": "Īsais kods", "CONTENT": "<PERSON><PERSON><PERSON>", "ACTIONS": "<PERSON><PERSON><PERSON><PERSON>"}}, "ADD": {"TITLE": "<PERSON><PERSON>not sagatavoto atbildi", "DESC": "Sagatavotās atbildes ir iep<PERSON>k<PERSON> definētas atbilžu veid<PERSON>, kuras var izmantot lai ātri nosūtītu atbildes uz sarunām.", "CANCEL_BUTTON_TEXT": "Atcelt", "FORM": {"SHORT_CODE": {"LABEL": "Īsais kods", "PLACEHOLDER": "<PERSON><PERSON><PERSON><PERSON>, ievadiet <PERSON><PERSON> kodu.", "ERROR": "Nepieciešams īsais kods."}, "CONTENT": {"LABEL": "<PERSON><PERSON>ņ<PERSON><PERSON><PERSON>", "PLACEHOLDER": "<PERSON><PERSON><PERSON><PERSON>, u<PERSON><PERSON><PERSON><PERSON>, kuru vēlaties saglabāt kā veidni, ko i<PERSON><PERSON>t vēl<PERSON>k.", "ERROR": "Nepieciešams ziņojums."}, "SUBMIT": "<PERSON><PERSON><PERSON><PERSON>"}, "API": {"SUCCESS_MESSAGE": "Sagatavotā atbilde ir veiksm<PERSON>.", "ERROR_MESSAGE": "Nevarēja izveidot savienojumu ar Woot serveri. Lūdzu mēģiniet vēlreiz."}}, "EDIT": {"TITLE": "Rediģēt sagatavoto atbildi", "CANCEL_BUTTON_TEXT": "Atcelt", "FORM": {"SHORT_CODE": {"LABEL": "Īsais kods", "PLACEHOLDER": "<PERSON><PERSON><PERSON><PERSON>, ievadiet <PERSON><PERSON> kodu.", "ERROR": "Nepieciešams īsais kods."}, "CONTENT": {"LABEL": "<PERSON><PERSON>ņ<PERSON><PERSON><PERSON>", "PLACEHOLDER": "<PERSON><PERSON><PERSON><PERSON>, u<PERSON><PERSON><PERSON><PERSON>, kuru vēlaties saglabāt kā veidni, ko i<PERSON><PERSON>t vēl<PERSON>k.", "ERROR": "Nepieciešams ziņojums."}, "SUBMIT": "<PERSON><PERSON><PERSON><PERSON>"}, "BUTTON_TEXT": "Rediģēt", "API": {"SUCCESS_MESSAGE": "Sagatavotā atbilde ir ve<PERSON>gi at<PERSON>.", "ERROR_MESSAGE": "Nevarēja izveidot savienojumu ar Woot serveri. Lūdzu mēģiniet vēlreiz."}}, "DELETE": {"BUTTON_TEXT": "<PERSON><PERSON><PERSON><PERSON>", "API": {"SUCCESS_MESSAGE": "Sagatavotā atbilde ir veiksmīgi izdzēsta.", "ERROR_MESSAGE": "Nevarēja izveidot savienojumu ar Woot serveri. Lūdzu mēģiniet vēlreiz."}, "CONFIRM": {"TITLE": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "MESSAGE": "Vai vēlaties izdzēst ", "YES": "Jā, dzēst ", "NO": "<PERSON><PERSON>, patur<PERSON>t "}}}}