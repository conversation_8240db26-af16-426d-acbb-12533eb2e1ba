{"INTEGRATION_SETTINGS": {"SHOPIFY": {"DELETE": {"TITLE": "Delete Shopify Integration", "MESSAGE": "Are you sure you want to delete the Shopify integration?"}, "STORE_URL": {"TITLE": "Connect Shopify Store", "LABEL": "Store URL", "PLACEHOLDER": "your-store.myshopify.com", "HELP": "Enter your Shopify store's myshopify.com URL", "CANCEL": "Atcelt", "SUBMIT": "Connect Store"}, "ERROR": "There was an error connecting to Shopify. Please try again or contact support if the issue persists."}, "HEADER": "Integr<PERSON><PERSON><PERSON>", "DESCRIPTION": "Chatwoot integrējas ar vairākiem rīkiem un pakalpojumiem, lai uzlabotu jūsu komandas efektivitāti. Izpētiet tālāk eso<PERSON> sa<PERSON>, lai konfigur<PERSON>tu savas iecienītāk<PERSON> lieto<PERSON>.", "LEARN_MORE": "<PERSON><PERSON><PERSON><PERSON><PERSON> vairāk par integrācijām", "LOADING": "Integrā<PERSON><PERSON>", "CAPTAIN": {"DISABLED": "Captain <PERSON><PERSON><PERSON> nav iespējo<PERSON>.", "CLICK_HERE_TO_CONFIGURE": "Noklikšķiniet šeit, lai konfigurētu", "LOADING_CONSOLE": "Notiek Captain konsoles i<PERSON>...", "FAILED_TO_LOAD_CONSOLE": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>t Captain kons<PERSON>. <PERSON><PERSON><PERSON><PERSON>, mēģiniet vēl<PERSON><PERSON>."}, "WEBHOOK": {"SUBSCRIBED_EVENTS": "<PERSON><PERSON><PERSON><PERSON>", "LEARN_MORE": "<PERSON><PERSON><PERSON><PERSON><PERSON> vair<PERSON>k par webhooks", "FORM": {"CANCEL": "Atcelt", "DESC": "Webhook notikumi sniedz Jums reāllaika informāciju par to, kas notiek jūsu Chatwoot kontā. <PERSON><PERSON><PERSON><PERSON>, ievadiet der<PERSON><PERSON>, lai nokon<PERSON>gu<PERSON><PERSON><PERSON>.", "SUBSCRIPTIONS": {"LABEL": "<PERSON><PERSON><PERSON>", "EVENTS": {"CONVERSATION_CREATED": "<PERSON><PERSON><PERSON>", "CONVERSATION_STATUS_CHANGED": "<PERSON><PERSON><PERSON>", "CONVERSATION_UPDATED": "<PERSON><PERSON><PERSON>", "MESSAGE_CREATED": "Ziņojums izveidots", "MESSAGE_UPDATED": "Ziņ<PERSON><PERSON><PERSON> atjaunin<PERSON>", "WEBWIDGET_TRIGGERED": "Lietotājs atvēra tieš<PERSON>stes tērzēšanas <PERSON>r<PERSON>ku", "CONTACT_CREATED": "Kontaktpersona izveidota", "CONTACT_UPDATED": "Kontakt<PERSON><PERSON>", "CONVERSATION_TYPING_ON": "Conversation Typing On", "CONVERSATION_TYPING_OFF": "Conversation Typing Off"}}, "END_POINT": {"LABEL": "Webhook URL", "PLACEHOLDER": "Piemēram: {webhookExampleURL}", "ERROR": "<PERSON><PERSON><PERSON><PERSON>, ievadiet derīgu URL"}, "EDIT_SUBMIT": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> webhook", "ADD_SUBMIT": "Izveidot webhook"}, "TITLE": "Webhook", "CONFIGURE": "Kon<PERSON><PERSON><PERSON><PERSON><PERSON>", "HEADER": "Webhook iestatījumi", "HEADER_BTN_TXT": "<PERSON><PERSON><PERSON> jau<PERSON> webhook", "LOADING": "Notiek pievienoto webhook iegūšana", "SEARCH_404": "<PERSON><PERSON> v<PERSON>m nav at<PERSON>š<PERSON> vienumu", "SIDEBAR_TXT": "<p><b>Webhook</b> </p> <p>Webhook ir HTTP atzvani, kurus var definēt katram kontam. Tos aktivizē tādi notikumi kā ziņojumu izveide pakalpojumā Chatwoot. <PERSON>im kontam varat izveidot vairāk nekā vienu webhook. <br /><br /> <PERSON> <b>webhook</b>, noklikšķiniet uz <b>Pievienot jaunu webhook</b> pogas. Jūs varat arī noņemt jebkuru esošo webhook, noklikšķinot uz pogas Dzēst.</p>", "LIST": {"404": "<PERSON><PERSON> kontam nav nokonfigurēts neviens webhook.", "TITLE": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> webhook", "TABLE_HEADER": {"WEBHOOK_ENDPOINT": "Webhook galapunkts", "ACTIONS": "<PERSON><PERSON><PERSON><PERSON>"}}, "EDIT": {"BUTTON_TEXT": "Rediģēt", "TITLE": "Rediģēt webhook", "API": {"SUCCESS_MESSAGE": "Webhook konfigurācija ir veiksmīgi at<PERSON>", "ERROR_MESSAGE": "Nevar izveidot savienojumu ar Woot serveri. <PERSON><PERSON><PERSON><PERSON>, vēlāk pamēģiniet vēlreiz"}}, "ADD": {"CANCEL": "Atcelt", "TITLE": "<PERSON><PERSON><PERSON> jau<PERSON> webhook", "API": {"SUCCESS_MESSAGE": "Webhook konfigurācija ir veiksmīgi pievienota", "ERROR_MESSAGE": "Nevar izveidot savienojumu ar Woot serveri. <PERSON><PERSON><PERSON><PERSON>, vēlāk pamēģiniet vēlreiz"}}, "DELETE": {"BUTTON_TEXT": "<PERSON><PERSON><PERSON><PERSON>", "API": {"SUCCESS_MESSAGE": "Webhook ir veiksmīgi izdzē<PERSON>", "ERROR_MESSAGE": "Nevar izveidot savienojumu ar Woot serveri. <PERSON><PERSON><PERSON><PERSON>, vēlāk pamēģiniet vēlreiz"}, "CONFIRM": {"TITLE": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "MESSAGE": "Vai tiešām vēlaties izdzēst webhook? ({webhookURL})", "YES": "Jā, Dzēst ", "NO": "Nē, Paturēt"}}}, "SLACK": {"DELETE": "<PERSON><PERSON><PERSON><PERSON>", "DELETE_CONFIRMATION": {"TITLE": "Dzēst integrāciju", "MESSAGE": "Vai tiešām vēlaties dzēst integrāciju? To darot, tiks zaudēta piekļuve sarunām Jūsu Slack workspace."}, "HELP_TEXT": {"TITLE": "<PERSON><PERSON> i<PERSON>t Slack integrāciju?", "BODY": "Izmantojot šo integrāciju, visas jūsu ienākošās sarunas tiks sinhronizētas ar kanālu ***{selectedChannelName}*** jūsu Slack darbvietā. Jūs varat pārvaldīt visas savas klientu sarunas tieši kanālā un nekad nepalaist garām nevienu ziņojumu.\n\nŠ<PERSON><PERSON> ir galvenās integrācijas īpašības:\n\n**Atbldēt uz sarunām no Slack:** Lai atbildētu uz sarunu ***{selectedChannelName}*** Slack kanālā, uzrakstiet savu ziņojumu un nosūtiet to kā pavedienu. Šādi tiks izveidota atbilde klientam, izmantojot Chatwoot.\n\n**Izveidot privātas piezīmes:** ja vēlaties izveidot privātas piezīmes, nevis atbildes, sāciet ziņojumu ar ***`note:`***. <PERSON><PERSON>, ka jūsu ziņojums ir privāts un nebūs redzams klientam.\n\n**Saistīt aģenta profilu:** ja personai, kas atbildēja Slack, ir Chatwoot aģenta profils ar to pašu e-pasta adresi, atbildes tiks automātiski saistītas ar šo aģenta profilu. Tas nozīmē, ka varat viegli izsekot kurš ko teica un kad. No otras puses, ja atbildētājam nav saistīta aģenta profila, atbildes klientam tiks rādītas no robotprogrammatūras profila.", "SELECTED": "izvēlēts"}, "SELECT_CHANNEL": {"OPTION_LABEL": "Izvēlēties kanālu", "UPDATE": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "BUTTON_TEXT": "<PERSON><PERSON><PERSON>", "DESCRIPTION": "Jūsu Slack workspace tagad ir saist<PERSON>ta ar <PERSON>, ta<PERSON>u integrācija pašlaik ir neaktīva. Lai aktivizētu integrāciju un savienotu kanālu ar <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, noklikšķiniet uz tālāk esošās pogas.\n\n**Piezīme:** Ja mēģinat izveidot savienojumu ar privātu kanālu, pirms turpināt šo darbību, pievienojiet lietotni Chatwoot Slack kanālam.", "ATTENTION_REQUIRED": "Nepiecie<PERSON><PERSON>", "EXPIRED": "<PERSON><PERSON><PERSON> S<PERSON>ck integrācijas derīguma termiņ<PERSON> ir be<PERSON>. <PERSON> turpin<PERSON>tu saņ<PERSON>t ziņoju<PERSON> paka<PERSON>, <PERSON><PERSON><PERSON><PERSON>, izd<PERSON><PERSON><PERSON>t integrāciju un vēlreiz pievienojiet savu workspace."}, "UPDATE_ERROR": "<PERSON><PERSON><PERSON><PERSON> integrā<PERSON><PERSON> rad<PERSON> k<PERSON>. <PERSON><PERSON><PERSON><PERSON>, mēģiniet vēlre<PERSON>", "UPDATE_SUCCESS": "<PERSON><PERSON><PERSON><PERSON> ir ve<PERSON> pie<PERSON>", "FAILED_TO_FETCH_CHANNELS": "Ienesot kanālus no Slack radās k<PERSON>ūda. <PERSON><PERSON><PERSON><PERSON>, mēģiniet vē<PERSON><PERSON><PERSON>"}, "DYTE": {"CLICK_HERE_TO_JOIN": "Noklikšķiniet šeit, lai pievienotos", "LEAVE_THE_ROOM": "Iziet no istabas", "START_VIDEO_CALL_HELP_TEXT": "<PERSON><PERSON><PERSON> jaunu <PERSON> ar k<PERSON>u", "JOIN_ERROR": "Pievie<PERSON><PERSON><PERSON>, r<PERSON><PERSON><PERSON>. <PERSON><PERSON><PERSON><PERSON>, mēģiniet vē<PERSON><PERSON><PERSON>", "CREATE_ERROR": "Veidojot sapu<PERSON>ces saiti, r<PERSON><PERSON><PERSON> k<PERSON>. <PERSON><PERSON><PERSON><PERSON>, mēģiniet vē<PERSON><PERSON><PERSON>"}, "OPEN_AI": {"AI_ASSIST": "AI Palīgs", "WITH_AI": " {option} ar AI ", "OPTIONS": {"REPLY_SUGGESTION": "Atbildes Ieteikums", "SUMMARIZE": "Apkopot", "REPHRASE": "<PERSON><PERSON><PERSON><PERSON>", "FIX_SPELLING_GRAMMAR": "Izlabot Pareizrakstību un Gramatiku", "SHORTEN": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "EXPAND": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "MAKE_FRIENDLY": "<PERSON><PERSON><PERSON> ziņoju<PERSON> toni uz draudz<PERSON>gu", "MAKE_FORMAL": "<PERSON>zmantot form<PERSON> toni", "SIMPLIFY": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "ASSISTANCE_MODAL": {"DRAFT_TITLE": "Melnraksta saturs", "GENERATED_TITLE": "Ģenerētais saturs", "AI_WRITING": "AI raksta", "BUTTONS": {"APPLY": "Izmantot š<PERSON> ieteikumu", "CANCEL": "Atcelt"}}, "CTA_MODAL": {"TITLE": "Integrēt ar OpenAI", "DESC": "Ievietot uzlabotās AI funkcijas savā informācijas panelī, izmantojot OpenAI GPT modeļus. <PERSON> s<PERSON>, ievadiet sava OpenAI konta API atslēgu.", "KEY_PLACEHOLDER": "Ievadiet savu OpenAI API atslēgu", "BUTTONS": {"NEED_HELP": "Nepieciešama palīdzība?", "DISMISS": "Atcelt", "FINISH": "<PERSON><PERSON><PERSON>"}, "DISMISS_MESSAGE": "OpenAI integrāciju varat iestatīt vēl<PERSON>k, kad vien vēlaties.", "SUCCESS_MESSAGE": "OpenAI integrācija iestatīta veiksmīgi"}, "TITLE": "Uzlabot ar AI", "SUMMARY_TITLE": "Kopsavilkums ar AI", "REPLY_TITLE": "Atbildes rekomendācija ar AI", "SUBTITLE": "<PERSON>zmantojot AI, tiks ģenerēta uzlabota atbilde, pamatojoties uz jūsu pašreizējo melnrakstu.", "TONE": {"TITLE": "<PERSON><PERSON>", "OPTIONS": {"PROFESSIONAL": "Profesion<PERSON><PERSON>", "FRIENDLY": "Draudzīgs"}}, "BUTTONS": {"GENERATE": "Ģenerēt", "GENERATING": "Notiek ģenerēšana...", "CANCEL": "Atcelt"}, "GENERATE_ERROR": "Apstrād<PERSON><PERSON>t saturu radās k<PERSON>. <PERSON><PERSON><PERSON><PERSON>, pārbaudiet savu OpenAI API atslēgu un mēģiniet vēlreiz"}, "DELETE": {"BUTTON_TEXT": "<PERSON><PERSON><PERSON><PERSON>", "API": {"SUCCESS_MESSAGE": "Integrācija ir veiksmīgi izdzēsta"}}, "CONNECT": {"BUTTON_TEXT": "<PERSON><PERSON><PERSON>"}, "DASHBOARD_APPS": {"TITLE": "Informācijas paneļa lietotnes", "HEADER_BTN_TXT": "<PERSON><PERSON><PERSON> j<PERSON><PERSON> pan<PERSON>ļ<PERSON> lieto<PERSON>ni", "SIDEBAR_TXT": "<p><b>Informācijas Paneļa Lietotnes</b></p><p>Informācijas paneļa lietotnes ļauj organizācijām iegult lietojumprogrammu Chatwoot informācijas panelī, lai nodrošinātu kontekstu klientu atbalsta aģentiem. Šī funkcija ļauj Jums izveidot lietojumprogrammu neatkarīgi un iegult to informācijas panelī, lai sniegtu informāciju par lietotāju, viņu pasūtījumiem vai viņu iepriekšējo maksājumu vēsturi.</p><p>Kad iegulsiet lietojumprogrammu, izmantojot Chatwoot informācijas paneli, <PERSON><PERSON><PERSON> lietojumprogramma iegūs sarunas un kontaktpersonas kontekstu kā loga notikumu. Ieviesiet savā lapā ziņojuma notikuma uztvērēju, lai saņemtu kontekstu.</p><p>Lai pievienotu jaunu informācijas paneļa lietotni, noklikšķiniet uz pogas 'Pievienot jaunu informācijas paneļa lietotni'.</p>", "DESCRIPTION": "Informācijas paneļa lietotnes ļauj organizācijām iegult lietojumprogrammu informācijas panelī, lai nodrošin<PERSON>tu kontekstu klientu atbalsta aģentiem. Šī funkcija ļauj Jums neatkarīgi izveidot lietojumprogrammu un iegult to, lai sniegtu informāciju par lietotāju, viņu pasūtījumiem vai iepriekšējo maksājumu vēsturi.", "LEARN_MORE": "<PERSON><PERSON><PERSON><PERSON><PERSON> vair<PERSON>k par Informācijas paneļa Lieto<PERSON>nēm", "LIST": {"404": "<PERSON><PERSON><PERSON> k<PERSON> vēl nav nokonfigurēta neviena informācijas paneļa lietotne", "LOADING": "Notiek informācijas paneļa lietotņu iegūšana...", "TABLE_HEADER": {"NAME": "Nosa<PERSON>ms", "ENDPOINT": "Endpoint"}, "EDIT_TOOLTIP": "Rediģēt lietotni", "DELETE_TOOLTIP": "Dzēst lietotni"}, "FORM": {"TITLE_LABEL": "Nosa<PERSON>ms", "TITLE_PLACEHOLDER": "Ievadiet informācijas paneļa lietotnes nosaukumu", "TITLE_ERROR": "Informācijas paneļa lietotnei ir j<PERSON><PERSON><PERSON><PERSON> no<PERSON>", "URL_LABEL": "Endpoint", "URL_PLACEHOLDER": "Ievadiet endpoint <PERSON><PERSON>, k<PERSON><PERSON> tiek mitin<PERSON>ta j<PERSON><PERSON> lieto<PERSON>ne", "URL_ERROR": "Nepieciešams derīgs URL"}, "CREATE": {"HEADER": "<PERSON><PERSON><PERSON> j<PERSON><PERSON> pan<PERSON>ļ<PERSON> lieto<PERSON>ni", "FORM_SUBMIT": "<PERSON><PERSON><PERSON><PERSON>", "FORM_CANCEL": "Atcelt", "API_SUCCESS": "Informācijas paneļa lietotne ir veiksmīgi nokonfigurēta", "API_ERROR": "<PERSON><PERSON><PERSON>m izve<PERSON>t lietotni. Lū<PERSON><PERSON>, vēlāk pamēģiniet vēlreiz"}, "UPDATE": {"HEADER": "Rediģēt informācijas paneļa lieto<PERSON>ni", "FORM_SUBMIT": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FORM_CANCEL": "Atcelt", "API_SUCCESS": "Informācijas paneļa lietotne ir veiksmīgi atjaunināta", "API_ERROR": "<PERSON><PERSON><PERSON> ne<PERSON>m at<PERSON>unin<PERSON>t lietotni. <PERSON><PERSON><PERSON><PERSON>, vēlāk pamēģiniet vēlreiz"}, "DELETE": {"CONFIRM_YES": "Jā, dzēst", "CONFIRM_NO": "<PERSON><PERSON>, patur<PERSON>t", "TITLE": "Apstipriniet d<PERSON>ēšanu", "MESSAGE": "Vai tiešām vēlaties izdzēst lietotni - {appName}?", "API_SUCCESS": "Informācijas paneļa lietotne ir veiksmīgi izdzēsta", "API_ERROR": "<PERSON><PERSON><PERSON> ne<PERSON>m izdzēst lietotni. Lū<PERSON><PERSON>, vēlāk pamēģiniet vēlreiz"}}, "LINEAR": {"ADD_OR_LINK_BUTTON": "Izveidot/Saistīt lineāru problēmu", "LOADING": "Notiek lineāru problēmu i<PERSON>...", "LOADING_ERROR": "<PERSON><PERSON><PERSON> problēmu i<PERSON> laikā radās k<PERSON>. <PERSON><PERSON><PERSON><PERSON>, mēģiniet vēlreiz", "CREATE": "Izveidot", "LINK": {"SEARCH": "<PERSON><PERSON><PERSON><PERSON><PERSON> pro<PERSON>l<PERSON>", "SELECT": "Izvēlieties problēmu", "TITLE": "<PERSON><PERSON>", "EMPTY_LIST": "<PERSON>ika atrasta neviena line<PERSON>ra probl<PERSON>ma", "LOADING": "<PERSON><PERSON>", "ERROR": "<PERSON><PERSON><PERSON> problēmu i<PERSON> laikā radās k<PERSON>. <PERSON><PERSON><PERSON><PERSON>, mēģiniet vēlreiz", "LINK_SUCCESS": "Problē<PERSON> ir ve<PERSON>gi sasai<PERSON>ta", "LINK_ERROR": "Sasaistot problēmu radā<PERSON> k<PERSON>. <PERSON><PERSON><PERSON><PERSON>, mēģiniet vēlre<PERSON>", "LINK_TITLE": "<PERSON><PERSON><PERSON> (#{conversationId}) ar {name}"}, "ADD_OR_LINK": {"TITLE": "Izveidot/saistīt lineāru problēmu", "DESCRIPTION": "<PERSON>zveidot lineārus jautājumus no sarunām, vai saistīt esošos netraucētai izsekošanai.", "FORM": {"TITLE": {"LABEL": "Nosa<PERSON>ms", "PLACEHOLDER": "Ievadiet virsrakstu", "REQUIRED_ERROR": "Nepieciešams nosaukums"}, "DESCRIPTION": {"LABEL": "<PERSON><PERSON><PERSON>", "PLACEHOLDER": "Ievadiet aprakstu"}, "TEAM": {"LABEL": "<PERSON><PERSON><PERSON>", "PLACEHOLDER": "Izvēlieties komandu", "SEARCH": "Meklēt komandu", "REQUIRED_ERROR": "Nepieciešama koman<PERSON>"}, "ASSIGNEE": {"LABEL": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PLACEHOLDER": "Izvēlēties pilnvaroto", "SEARCH": "Meklēt pilnvaroto"}, "PRIORITY": {"LABEL": "Prioritāte", "PLACEHOLDER": "Izvēlieties prioritāti", "SEARCH": "<PERSON><PERSON><PERSON><PERSON><PERSON> priorit<PERSON>ti"}, "LABEL": {"LABEL": "Etiķete", "PLACEHOLDER": "Izvēlēties etiķeti", "SEARCH": "Meklēt etiķeti"}, "STATUS": {"LABEL": "Statuss", "PLACEHOLDER": "Izvēlēties statusu", "SEARCH": "Meklēt statusu"}, "PROJECT": {"LABEL": "Projekts", "PLACEHOLDER": "Izvēlēties projektu", "SEARCH": "Meklēt projektu"}}, "CREATE": "Izveidot", "CANCEL": "Atcelt", "CREATE_SUCCESS": "Problēma ir veiksmīgi izveidota", "CREATE_ERROR": "Veidojo<PERSON> problēmu radā<PERSON> k<PERSON>. <PERSON><PERSON><PERSON><PERSON>, mēģiniet vēlre<PERSON>", "LOADING_TEAM_ERROR": "Ienesot komandas radās k<PERSON>. <PERSON><PERSON><PERSON><PERSON>, mēģiniet vēl<PERSON><PERSON>", "LOADING_TEAM_ENTITIES_ERROR": "Ienesot komandas entītijas radās kļūda. <PERSON><PERSON><PERSON><PERSON>, mēģiniet vēlre<PERSON>"}, "ISSUE": {"STATUS": "Statuss", "PRIORITY": "Prioritāte", "ASSIGNEE": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "LABELS": "Etiķetes", "CREATED_AT": "Izveidots {createdAt}"}, "UNLINK": {"TITLE": "Atsaistīt", "SUCCESS": "Problē<PERSON> ir ve<PERSON>gi at<PERSON>ta", "ERROR": "Atsaistot jautāju<PERSON> radā<PERSON> k<PERSON>. <PERSON><PERSON><PERSON><PERSON>, mēģiniet vēl<PERSON><PERSON>"}, "DELETE": {"TITLE": "Vai tiešām vēlaties dzēst integrāciju?", "MESSAGE": "Vai tiešām vēlaties dzēst integrāciju?", "CONFIRM": "Jā, dzēst", "CANCEL": "Atcelt"}}}, "CAPTAIN": {"NAME": "<PERSON><PERSON><PERSON><PERSON>", "HEADER_KNOW_MORE": "Know more", "COPILOT": {"SEND_MESSAGE": "<PERSON><PERSON><PERSON><PERSON><PERSON>...", "EMPTY_MESSAGE": "There was an error generating the response. Please try again.", "LOADER": "<PERSON><PERSON><PERSON><PERSON>", "YOU": "<PERSON><PERSON><PERSON>", "USE": "Izmantot šo", "RESET": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SELECT_ASSISTANT": "Izvēlēties Asistentu", "PROMPTS": {"SUMMARIZE": {"LABEL": "Summarize this conversation", "CONTENT": "Summarize the key points discussed between the customer and the support agent, including the customer's concerns, questions, and the solutions or responses provided by the support agent"}, "SUGGEST": {"LABEL": "Suggest an answer", "CONTENT": "Analyze the customer's inquiry, and draft a response that effectively addresses their concerns or questions. Ensure the reply is clear, concise, and provides helpful information."}, "RATE": {"LABEL": "Rate this conversation", "CONTENT": "Review the conversation to see how well it meets the customer's needs. Share a rating out of 5 based on tone, clarity, and effectiveness."}}}, "PLAYGROUND": {"USER": "<PERSON><PERSON><PERSON>", "ASSISTANT": "Asistents", "MESSAGE_PLACEHOLDER": "Rakstiet savu ziņojumu...", "HEADER": "Playground", "DESCRIPTION": "Use this playground to send messages to your assistant and check if it responds accurately, quickly, and in the tone you expect.", "CREDIT_NOTE": "Messages sent here will count toward your Captain credits."}, "PAYWALL": {"TITLE": "<PERSON><PERSON><PERSON><PERSON><PERSON>, la<PERSON> <PERSON><PERSON><PERSON> Captain AI", "AVAILABLE_ON": "Captain nav <PERSON><PERSON><PERSON> be<PERSON> a<PERSON>.", "UPGRADE_PROMPT": "Modernizējiet savu a<PERSON>u, lai iegūtu piekļuvi viruālajiem asistentiem un copilot.", "UPGRADE_NOW": "Pāriet uz maksas versiju tagad", "CANCEL_ANYTIME": "<PERSON><PERSON><PERSON> varat jebkurā laikā mainīt vai atcelt savu versiju"}, "ENTERPRISE_PAYWALL": {"AVAILABLE_ON": "Captain <PERSON>ja ir pieejama tikai maksas a<PERSON>.", "UPGRADE_PROMPT": "Modernizējiet savu a<PERSON>u, lai iegūtu piekļuvi viruālajiem asistentiem un copilot.", "ASK_ADMIN": "<PERSON> p<PERSON>tu uz maksas vers<PERSON>, l<PERSON><PERSON><PERSON> sazin<PERSON>ies ar savu <PERSON>u."}, "BANNER": {"RESPONSES": "<PERSON><PERSON><PERSON> esat izmantojis vair<PERSON>k kā 80% no sava atbilžu ierobežojuma. <PERSON> tur<PERSON> i<PERSON>t Captain <PERSON>, <PERSON><PERSON><PERSON><PERSON>, at<PERSON><PERSON><PERSON><PERSON> abonementu.", "DOCUMENTS": "Sasniegts dokumentu limits. <PERSON><PERSON><PERSON><PERSON><PERSON>, lai t<PERSON><PERSON><PERSON><PERSON> Captain AI."}, "FORM": {"CANCEL": "Atcelt", "CREATE": "Izveidot", "EDIT": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "ASSISTANTS": {"HEADER": "<PERSON><PERSON><PERSON><PERSON>", "ADD_NEW": "Izveidot jaunu asistentu", "DELETE": {"TITLE": "Vai tiešām vēlaties izdzēst asistentu?", "DESCRIPTION": "<PERSON><PERSON> darbība ir pastāvīga. <PERSON><PERSON><PERSON><PERSON><PERSON> asistent<PERSON>, tas tiks noņemts no visām pievienotajām iesūtnēm un neatgriezeniski dzēstas visas ģenerētās zināšanas.", "CONFIRM": "Jā, dzēst", "SUCCESS_MESSAGE": "Asistents ir veiksmīgi izdzē<PERSON>", "ERROR_MESSAGE": "<PERSON><PERSON><PERSON><PERSON><PERSON> asistentu radā<PERSON> k<PERSON>. <PERSON><PERSON><PERSON><PERSON>, mēģiniet vēlre<PERSON>."}, "FORM_DESCRIPTION": "<PERSON><PERSON><PERSON><PERSON><PERSON>, lai nosa<PERSON>tu savu asistentu, nor<PERSON><PERSON><PERSON>tu tā mērķi un produktu, ko tas atbalstīs.", "CREATE": {"TITLE": "Izveidot asistentu", "SUCCESS_MESSAGE": "Asistents ir veiksmīgi izveidots", "ERROR_MESSAGE": "<PERSON>eidojot asistentu radā<PERSON> k<PERSON>. <PERSON><PERSON><PERSON><PERSON>, mēģiniet vēlre<PERSON>."}, "FORM": {"UPDATE": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SECTIONS": {"BASIC_INFO": "Basic Information", "SYSTEM_MESSAGES": "System Messages", "INSTRUCTIONS": "Instructions", "FEATURES": "Īpašī<PERSON>", "TOOLS": "Tools "}, "NAME": {"LABEL": "Nosa<PERSON>ms", "PLACEHOLDER": "Enter assistant name", "ERROR": "The name is required"}, "DESCRIPTION": {"LABEL": "<PERSON><PERSON><PERSON>", "PLACEHOLDER": "Enter assistant description", "ERROR": "The description is required"}, "PRODUCT_NAME": {"LABEL": "Produkt<PERSON>", "PLACEHOLDER": "Enter product name", "ERROR": "Nepieciešams produkta nosaukums"}, "WELCOME_MESSAGE": {"LABEL": "Welcome Message", "PLACEHOLDER": "Enter welcome message"}, "HANDOFF_MESSAGE": {"LABEL": "Handoff Message", "PLACEHOLDER": "Enter handoff message"}, "RESOLUTION_MESSAGE": {"LABEL": "Resolution Message", "PLACEHOLDER": "Enter resolution message"}, "INSTRUCTIONS": {"LABEL": "Instructions", "PLACEHOLDER": "Enter instructions for the assistant"}, "FEATURES": {"TITLE": "Īpašī<PERSON>", "ALLOW_CONVERSATION_FAQS": "Ģenerēt bieži uzdotos jautājumus no atrisinātajām sarunām", "ALLOW_MEMORIES": "<PERSON><PERSON><PERSON><PERSON> galvenās nianses kā atmiņas no klientu miji<PERSON>b<PERSON>m."}}, "EDIT": {"TITLE": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> as<PERSON>", "SUCCESS_MESSAGE": "Asistents ir veik<PERSON><PERSON><PERSON>", "ERROR_MESSAGE": "<PERSON><PERSON><PERSON><PERSON> asistentu radā<PERSON> k<PERSON>. <PERSON><PERSON><PERSON><PERSON>, mēģiniet vēlre<PERSON>.", "NOT_FOUND": "Could not find the assistant. Please try again."}, "OPTIONS": {"EDIT_ASSISTANT": "Rediģēt As<PERSON>entu", "DELETE_ASSISTANT": "<PERSON><PERSON><PERSON><PERSON>", "VIEW_CONNECTED_INBOXES": "<PERSON><PERSON><PERSON><PERSON>"}, "EMPTY_STATE": {"TITLE": "Asistenti nav pieejami", "SUBTITLE": "<PERSON><PERSON><PERSON><PERSON><PERSON>alī<PERSON>, lai sniegtu lietotājiem ātras un precīzas atbildes. Tas var mācīties no Jūsu palīdzības rakstiem un iepriekšējām sarunām.", "FEATURE_SPOTLIGHT": {"TITLE": "Captain Assistant", "NOTE": "Captain Assistant engages directly with customers, learns from your help docs and past conversations, and delivers instant, accurate responses. It handles the initial queries, providing quick resolutions before  transferring to an agent when needed."}}}, "DOCUMENTS": {"HEADER": "Dokumenti", "ADD_NEW": "Izveidot jaunu dokumentu", "RELATED_RESPONSES": {"TITLE": "Saistītie bi<PERSON>ži uzdotie j<PERSON>āju<PERSON>", "DESCRIPTION": "<PERSON>ie bieži uzdotie jautājumi tiek ģenerēti tieši no dokumenta."}, "FORM_DESCRIPTION": "Ievadiet dokumenta URL, lai to pievienotu kā zinā<PERSON> avo<PERSON>, un izvēlieties asistentu, ar kuru to saist<PERSON>t.", "CREATE": {"TITLE": "Pievienot dokumentu", "SUCCESS_MESSAGE": "Dokuments ir veiksmīgi izveido<PERSON>", "ERROR_MESSAGE": "<PERSON><PERSON><PERSON><PERSON><PERSON>, r<PERSON><PERSON><PERSON>. <PERSON><PERSON><PERSON><PERSON>, mēģiniet vēlre<PERSON>."}, "FORM": {"URL": {"LABEL": "URL", "PLACEHOLDER": "Ievadiet dokumenta URL", "ERROR": "<PERSON><PERSON><PERSON><PERSON>, norādiet pareizu dokumenta URL"}, "ASSISTANT": {"LABEL": "Asistents", "PLACEHOLDER": "Izvēlēties asistentu", "ERROR": "<PERSON><PERSON><PERSON><PERSON> la<PERSON> ir oblig<PERSON><PERSON> j<PERSON>"}}, "DELETE": {"TITLE": "Vai tiešām vēlaties izdzēst šo dokumentu?", "DESCRIPTION": "<PERSON><PERSON> darbība ir pastāvīga. <PERSON><PERSON><PERSON>šot šo dokumentu visas ģenerētās zināšanas tiks neatgriezeniski izdzēstas.", "CONFIRM": "Jā, dzēst", "SUCCESS_MESSAGE": "Dokuments ir veiksmīgi izdz<PERSON>", "ERROR_MESSAGE": "<PERSON><PERSON><PERSON>š<PERSON> dokumentu radās k<PERSON>ūda. L<PERSON><PERSON><PERSON>, mēģiniet vēlreiz."}, "OPTIONS": {"VIEW_RELATED_RESPONSES": "<PERSON><PERSON><PERSON><PERSON>", "DELETE_DOCUMENT": "Dzēst Dokumentu"}, "EMPTY_STATE": {"TITLE": "Dokumenti nav pieejami", "SUBTITLE": "<PERSON><PERSON><PERSON> palīgs i<PERSON>to dokumentus, lai izveidotu bieži uzdotos jautājumus. <PERSON><PERSON><PERSON> varat importēt dokumentus, lai nodro<PERSON><PERSON><PERSON>tu kontekstu savam palīgam.", "FEATURE_SPOTLIGHT": {"TITLE": "Captain <PERSON>ument", "NOTE": "A document in Captain serves as a knowledge resource for the assistant. By connecting your help center or guides, Captain can analyze the content and provide accurate responses for customer inquiries."}}}, "RESPONSES": {"HEADER": "Bieži uzdotie j<PERSON>", "ADD_NEW": "Izveidot jaunu sarakstu ar bieži uzdotiem jautājumiem", "DOCUMENTABLE": {"CONVERSATION": "<PERSON><PERSON><PERSON> #{id}"}, "SELECTED": "<PERSON><PERSON><PERSON> {count}", "BULK_APPROVE_BUTTON": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "BULK_DELETE_BUTTON": "<PERSON><PERSON><PERSON><PERSON>", "BULK_APPROVE": {"SUCCESS_MESSAGE": "Bieži uzdotie jautājumi ir veiksmīgi a<PERSON>tiprināti", "ERROR_MESSAGE": "Aps<PERSON>rinot bieži uzdotos jautājumus radā<PERSON>. L<PERSON>d<PERSON>, mēģiniet vēlreiz."}, "BULK_DELETE": {"TITLE": "Vai d<PERSON>st bieži uz<PERSON> j<PERSON>?", "DESCRIPTION": "Vai tiešām vēlaties dzēst atlasītos bieži uzdotos j<PERSON>ā<PERSON>? Šo darbību nevar atsaukt.", "CONFIRM": "<PERSON><PERSON>, dzēst visu", "SUCCESS_MESSAGE": "Bieži uzdotie jautājumi ir veiksmīgi izdzēsti", "ERROR_MESSAGE": "Dz<PERSON>š<PERSON> bieži uzdo<PERSON> j<PERSON>ājumus radā<PERSON>. Lūdzu, mēģiniet vēlreiz."}, "DELETE": {"TITLE": "Vai tiešām vēlaties izdzēst šos bieži uzdo<PERSON> j<PERSON>?", "DESCRIPTION": "", "CONFIRM": "Jā, dzēst", "SUCCESS_MESSAGE": "Bieži uzdotie jautājumi ir veiksmīgi izdzēsti", "ERROR_MESSAGE": "Dz<PERSON>š<PERSON> bieži uzdo<PERSON> j<PERSON>ājumus radā<PERSON>. Lūdzu, mēģiniet vēlreiz."}, "FILTER": {"ASSISTANT": "Asistents: {selected}", "STATUS": "Statuss: {selected}", "ALL_ASSISTANTS": "Visi"}, "STATUS": {"TITLE": "Statuss", "PENDING": "<PERSON><PERSON><PERSON>", "APPROVED": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ALL": "Visi"}, "FORM_DESCRIPTION": "<PERSON><PERSON><PERSON> j<PERSON><PERSON> un tā atbildi z<PERSON> bā<PERSON>, kā ar<PERSON> izvēl<PERSON> asistentu, ar kuru tas būs saist<PERSON>ts.", "CREATE": {"TITLE": "Pievienot bi<PERSON>ži uzdotos j<PERSON>", "SUCCESS_MESSAGE": "Atbilde ir veik<PERSON>.", "ERROR_MESSAGE": "Pievienojot atbildi radās k<PERSON>. L<PERSON><PERSON><PERSON>, mēģiniet vēlreiz."}, "FORM": {"QUESTION": {"LABEL": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PLACEHOLDER": "Ievadiet j<PERSON><PERSON><PERSON>", "ERROR": "<PERSON><PERSON><PERSON><PERSON>, ievadiet derīgu <PERSON>."}, "ANSWER": {"LABEL": "Atbilde", "PLACEHOLDER": "Ievadiet atbildi šeit", "ERROR": "<PERSON><PERSON><PERSON><PERSON>, ievadiet derīgu atbildi."}, "ASSISTANT": {"LABEL": "Asistents", "PLACEHOLDER": "Izvēlieties asistentu", "ERROR": "<PERSON><PERSON><PERSON><PERSON>, izvēlieties asistentu."}}, "EDIT": {"TITLE": "Atjaunināt bi<PERSON>ži uzdo<PERSON> j<PERSON>", "SUCCESS_MESSAGE": "Bieži uzdotie j<PERSON>ājumi ir veiksmīgi at<PERSON>", "ERROR_MESSAGE": "<PERSON><PERSON><PERSON><PERSON> bi<PERSON>, r<PERSON><PERSON><PERSON>. <PERSON><PERSON><PERSON><PERSON>, mēģiniet vēlreiz", "APPROVE_SUCCESS_MESSAGE": "Bieži uzdotie j<PERSON>ājumi tika at<PERSON>īm<PERSON>ti kā apstiprināti"}, "OPTIONS": {"APPROVE": "Atzīmēt kā apstiprinātu", "EDIT_RESPONSE": "Rediģēt bie<PERSON>i u<PERSON> j<PERSON>", "DELETE_RESPONSE": "Dzēst bieži uzdotos j<PERSON>"}, "EMPTY_STATE": {"TITLE": "Bieži uzdoto j<PERSON> saraksti nav atrasti", "SUBTITLE": "Bieži uzdotie jautājumi palīdz Jū<PERSON> asistentam sniegt ātras un precīzas atbildes uz Jūsu klientu jautājumiem. Tos var ģenerēt automātiski no jūsu satura vai pievienot manuāli.", "FEATURE_SPOTLIGHT": {"TITLE": "Captain FAQ", "NOTE": "Captain <PERSON><PERSON><PERSON> detects common customer questions—whether missing from your knowledge base or frequently asked—and generates relevant FAQs to improve support. You can review each suggestion and decide whether to approve or reject it."}}}, "INBOXES": {"HEADER": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ADD_NEW": "<PERSON><PERSON><PERSON> j<PERSON>", "OPTIONS": {"DISCONNECT": "Atvienot"}, "DELETE": {"TITLE": "Vai tiešām vēlaties atvienot iesūtni?", "DESCRIPTION": "", "CONFIRM": "Jā, dzēst", "SUCCESS_MESSAGE": "<PERSON><PERSON><PERSON><PERSON><PERSON> tika ve<PERSON>vie<PERSON>.", "ERROR_MESSAGE": "Atvienojot iesūtni radās k<PERSON>ūda. <PERSON><PERSON><PERSON><PERSON>, mēģiniet vēlreiz."}, "FORM_DESCRIPTION": "Izvēlie<PERSON> i<PERSON>, lai izveidotu savienojumu ar asistentu.", "CREATE": {"TITLE": "<PERSON><PERSON><PERSON>", "SUCCESS_MESSAGE": "<PERSON><PERSON><PERSON><PERSON>ne tika ve<PERSON> pie<PERSON>.", "ERROR_MESSAGE": "Pievienojot i<PERSON>ūtni radās k<PERSON>ūda. <PERSON><PERSON><PERSON><PERSON>, mēģiniet vēlreiz."}, "FORM": {"INBOX": {"LABEL": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PLACEHOLDER": "Izvēlieties iesū<PERSON>ni, lai aktivētu asistentu.", "ERROR": "Nepieciešams izvēlēties iesūtni."}}, "EMPTY_STATE": {"TITLE": "<PERSON>v <PERSON>vie<PERSON>", "SUBTITLE": "Iesūtnes pievie<PERSON> asistentam apstrādāt <PERSON> s<PERSON>, pirms tos pā<PERSON><PERSON><PERSON><PERSON><PERSON>."}}}}