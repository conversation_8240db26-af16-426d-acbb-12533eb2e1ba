{"INTEGRATION_APPS": {"FETCHING": "Notiek Integrāciju <PERSON>", "NO_HOOK_CONFIGURED": "<PERSON><PERSON><PERSON> k<PERSON> nav nokonfigurēta neviena {integrationId} integrācija.", "HEADER": "Lietojumprogrammas", "STATUS": {"ENABLED": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DISABLED": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "CONFIGURE": "Kon<PERSON><PERSON><PERSON><PERSON><PERSON>", "ADD_BUTTON": "<PERSON><PERSON><PERSON><PERSON><PERSON> jaunu <PERSON>", "DELETE": {"TITLE": {"INBOX": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ACCOUNT": "Atvienot"}, "MESSAGE": {"INBOX": "Vai tiešām vēlaties izdzēst?", "ACCOUNT": "Vai tiešām vēlaties atvienot?"}, "CONFIRM_BUTTON_TEXT": {"INBOX": "Jā, Dzēst", "ACCOUNT": "Jā, Atvienot"}, "CANCEL_BUTTON_TEXT": "Atcelt", "API": {"SUCCESS_MESSAGE": "<PERSON> ir veik<PERSON><PERSON><PERSON> i<PERSON>", "ERROR_MESSAGE": "Nevar izveidot savienojumu ar Woot serveri. <PERSON><PERSON><PERSON><PERSON>, vēlāk pamēģiniet vēlreiz"}}, "LIST": {"FETCHING": "Notiek integrāci<PERSON> i<PERSON><PERSON>", "INBOX": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DELETE": {"BUTTON_TEXT": "<PERSON><PERSON><PERSON><PERSON>"}}, "ADD": {"FORM": {"INBOX": {"LABEL": "Izvēlieties Iesūtni", "PLACEHOLDER": "Izvēlieties Iesūtni"}, "SUBMIT": "Izveidot", "CANCEL": "Atcelt"}, "API": {"SUCCESS_MESSAGE": "Integrā<PERSON><PERSON> hook ir veik<PERSON><PERSON><PERSON> pie<PERSON>s", "ERROR_MESSAGE": "Nevar izveidot savienojumu ar Woot serveri. <PERSON><PERSON><PERSON><PERSON>, vēlāk pamēģiniet vēlreiz"}}, "CONNECT": {"BUTTON_TEXT": "Savienoties"}, "DISCONNECT": {"BUTTON_TEXT": "Atvienot"}, "SIDEBAR_DESCRIPTION": {"DIALOGFLOW": "Dialogflow ir dabiskas valodas apstrādes platforma, sarunvalodas interfeisa veidošanai. Integrējot to ar {installationName}, roboti vispirms apstrādās vaicājumus un vajadzības gadījumā pārsūtīs tos aģentiem. Tas palīdz kvalificēt potenciālos klientus un samazināt aģentu darba slodzi, atbildot uz bieži uzdotajiem jautājimiem. Lai pievienotu Dialogflow, pakalpojumā Google Console izveidojiet Service Account un kopīgojiet akreditācijas datus. Sīkāku informāciju skatiet dokumentācijā"}}}