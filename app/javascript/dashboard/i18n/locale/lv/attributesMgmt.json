{"ATTRIBUTES_MGMT": {"HEADER": "<PERSON><PERSON>ā<PERSON><PERSON>", "HEADER_BTN_TXT": "<PERSON><PERSON><PERSON>", "LOADING": "Notiek pielāgotu <PERSON>", "DESCRIPTION": "Pielāgots atribūts ļauj izsekot papildu informāciju par jūsu kontaktpersonām vai sarunām, pie<PERSON><PERSON><PERSON>, a<PERSON><PERSON><PERSON><PERSON> plānu vai viņu pirmā pirkuma datumu. Varat pievienot dažāda veida pielāgotus atribūtus, <PERSON><PERSON><PERSON><PERSON>, te<PERSON><PERSON>, sarak<PERSON><PERSON> vai skait<PERSON>, lai tvertu konkrētu jums nepieciešamo informāciju.", "LEARN_MORE": "<PERSON><PERSON><PERSON><PERSON><PERSON> vairāk par pielāgotajiem atribūtiem", "ADD": {"TITLE": "<PERSON><PERSON><PERSON>", "SUBMIT": "Izveidot", "CANCEL_BUTTON_TEXT": "Atcelt", "FORM": {"NAME": {"LABEL": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PLACEHOLDER": "Ievadiet pielā<PERSON><PERSON><PERSON> parād<PERSON> no<PERSON>", "ERROR": "Nepieciešams nosaukums"}, "DESC": {"LABEL": "<PERSON><PERSON><PERSON>", "PLACEHOLDER": "Ievadiet pielā<PERSON><PERSON><PERSON>", "ERROR": "Nepieciešams apraksts"}, "MODEL": {"LABEL": "Attiecas uz", "PLACEHOLDER": "Lūdzu, izvēlieties vienu", "ERROR": "Nepieciešams modelis"}, "TYPE": {"LABEL": "Tips", "PLACEHOLDER": "Lūdzu, izvēlieties tipu", "ERROR": "Nepieciešams tips", "LIST": {"LABEL": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PLACEHOLDER": "<PERSON><PERSON><PERSON><PERSON>, ievadiet vērtību un nospiediet taustiņu enter", "ERROR": "<PERSON><PERSON><PERSON><PERSON><PERSON> vismaz vienai vērtībai"}}, "KEY": {"LABEL": "Atslēga", "PLACEHOLDER": "Ievadiet pielā<PERSON> atslēgu", "ERROR": "Nepieciešama at<PERSON>lē<PERSON>", "IN_VALID": "Nederīga atslēga"}, "REGEX_PATTERN": {"LABEL": "Regex Šablons", "PLACEHOLDER": "<PERSON><PERSON><PERSON><PERSON>, ievadiet piel<PERSON>gotu atribūtu regex šab<PERSON>u. (Neobligāti)"}, "REGEX_CUE": {"LABEL": "Regex Norādījums", "PLACEHOLDER": "<PERSON><PERSON><PERSON><PERSON>, ievadiet regex šablona nor<PERSON>. (Neobligāti)"}, "ENABLE_REGEX": {"LABEL": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> rege<PERSON>"}}, "API": {"SUCCESS_MESSAGE": "Pie<PERSON>ā<PERSON><PERSON> ir veiksmīgi pievienota!", "ERROR_MESSAGE": "Nevarēja izveidot Pielāgotu Atribūtu. L<PERSON><PERSON><PERSON>, vēlāk pamēģiniet vēlreiz."}}, "DELETE": {"BUTTON_TEXT": "<PERSON><PERSON><PERSON><PERSON>", "API": {"SUCCESS_MESSAGE": "Pie<PERSON>ā<PERSON><PERSON> ir veiksmīgi izdzēsta.", "ERROR_MESSAGE": "Nevarēja izdzēst pielāgoto īpašību. Mēģiniet vēlreiz."}, "CONFIRM": {"TITLE": "Vai esat pā<PERSON>, ka vēlaties izdzēst - {attributeName}", "PLACE_HOLDER": "<PERSON>, l<PERSON><PERSON><PERSON>, uzrakst<PERSON> {attributeName}", "MESSAGE": "<PERSON><PERSON><PERSON><PERSON>na noņems pielāgoto īpašību", "YES": "<PERSON><PERSON><PERSON><PERSON> ", "NO": "Atcelt"}}, "EDIT": {"TITLE": "Rediģēt <PERSON><PERSON><PERSON><PERSON><PERSON>", "UPDATE_BUTTON_TEXT": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "TYPE": {"LIST": {"LABEL": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PLACEHOLDER": "<PERSON><PERSON><PERSON><PERSON>, ieva<PERSON>t vērtības un nospiediet taustiņu enter"}}, "API": {"SUCCESS_MESSAGE": "<PERSON><PERSON><PERSON><PERSON><PERSON> ir ve<PERSON>īgi at<PERSON>", "ERROR_MESSAGE": "<PERSON><PERSON><PERSON><PERSON>, rad<PERSON><PERSON>. <PERSON><PERSON><PERSON><PERSON>, mēģiniet vēlreiz"}}, "TABS": {"HEADER": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "CONVERSATION": "<PERSON><PERSON><PERSON>", "CONTACT": "Kontaktpersona"}, "LIST": {"TABLE_HEADER": {"NAME": "Nosa<PERSON>ms", "DESCRIPTION": "<PERSON><PERSON><PERSON>", "TYPE": "Tips", "KEY": "Atslēga"}, "BUTTONS": {"EDIT": "Rediģēt", "DELETE": "<PERSON><PERSON><PERSON><PERSON>"}, "EMPTY_RESULT": {"404": "Nav izveidotas pielāgotas īpašības", "NOT_FOUND": "Nav nokonfigurētas pielāgotas ī<PERSON>šības"}, "REGEX_PATTERN": {"LABEL": "Regex Šablons", "PLACEHOLDER": "<PERSON><PERSON><PERSON><PERSON>, ievadiet piel<PERSON>gotu atribūtu regex šab<PERSON>u. (Neobligāti)"}, "REGEX_CUE": {"LABEL": "Regex Norādījums", "PLACEHOLDER": "<PERSON><PERSON><PERSON><PERSON>, ievadiet regex šablona nor<PERSON>. (Neobligāti)"}, "ENABLE_REGEX": {"LABEL": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> rege<PERSON>"}}}}