{"CONTACTS_FILTER": {"TITLE": "Filtrēt Kontaktpersonas", "SUBTITLE": "Pievienojiet filtrus un noklikšķiniet uz 'Iesniegt', lai filtr<PERSON>tu kontakt<PERSON>.", "EDIT_CUSTOM_SEGMENT": "Rediģēt Segmentu", "CUSTOM_VIEWS_SUBTITLE": "Pievienot vai noņemt filtrus un atjaunināt segmentu.", "ADD_NEW_FILTER": "<PERSON><PERSON><PERSON>", "CLEAR_ALL_FILTERS": "Atcelt Visus Filtrus", "FILTER_DELETE_ERROR": "<PERSON>, <PERSON><PERSON> <PERSON>r j<PERSON><PERSON><PERSON><PERSON> vismaz vienam filtram", "SUBMIT_BUTTON_LABEL": "<PERSON><PERSON><PERSON><PERSON>", "UPDATE_BUTTON_LABEL": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "CANCEL_BUTTON_LABEL": "Atcelt", "CLEAR_BUTTON_LABEL": "Atcelt Filtrus", "EMPTY_VALUE_ERROR": "Nepieciešama vērtība", "SEGMENT_LABEL": "Segmenta No<PERSON>ukums", "SEGMENT_QUERY_LABEL": "Segmenta Vaic<PERSON>", "TOOLTIP_LABEL": "Filtrēt kontaktpersonas", "QUERY_DROPDOWN_LABELS": {"AND": "UN", "OR": "VAI"}, "OPERATOR_LABELS": {"equal_to": "<PERSON><PERSON><PERSON><PERSON> ar", "not_equal_to": "Nav vien<PERSON>ds ar", "contains": "Satur", "does_not_contain": "Nesatur", "is_present": "<PERSON><PERSON><PERSON><PERSON><PERSON> sevī", "is_not_present": "Neiek<PERSON><PERSON><PERSON> sevī", "is_greater_than": "<PERSON><PERSON> par", "is_lesser_than": "<PERSON><PERSON> <PERSON> par", "days_before": "Ir x dienas pirms"}, "ERRORS": {"VALUE_REQUIRED": "Nepieciešama vērtība"}, "ATTRIBUTES": {"NAME": "Nosa<PERSON>ms", "EMAIL": "E-pasts", "PHONE_NUMBER": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> numurs", "IDENTIFIER": "Identifikators", "CITY": "Pilsē<PERSON>", "COUNTRY": "Valsts", "CUSTOM_ATTRIBUTE_LIST": "Saraksts", "CUSTOM_ATTRIBUTE_TEXT": "Teksts", "CUSTOM_ATTRIBUTE_NUMBER": "<PERSON><PERSON><PERSON>", "CUSTOM_ATTRIBUTE_LINK": "<PERSON><PERSON>", "CUSTOM_ATTRIBUTE_CHECKBOX": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "CREATED_AT": "Izveidots", "LAST_ACTIVITY": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "REFERER_LINK": "Atsauces sniedz<PERSON>ja saite", "BLOCKED": "Bloķēts"}, "GROUPS": {"STANDARD_FILTERS": "<PERSON><PERSON><PERSON>", "ADDITIONAL_FILTERS": "<PERSON><PERSON><PERSON><PERSON>", "CUSTOM_ATTRIBUTES": "<PERSON><PERSON>ā<PERSON><PERSON>"}}}