{"INTEGRATION_SETTINGS": {"SHOPIFY": {"DELETE": {"TITLE": "Delete Shopify Integration", "MESSAGE": "Are you sure you want to delete the Shopify integration?"}, "STORE_URL": {"TITLE": "Connect Shopify Store", "LABEL": "Store URL", "PLACEHOLDER": "your-store.myshopify.com", "HELP": "Enter your Shopify store's myshopify.com URL", "CANCEL": "انصراف", "SUBMIT": "Connect Store"}, "ERROR": "There was an error connecting to Shopify. Please try again or contact support if the issue persists."}, "HEADER": "برنامه‌های تلفیق شده", "DESCRIPTION": "Chatwoot integrates with multiple tools and services to improve your team's efficiency. Explore the list below to configure your favorite apps.", "LEARN_MORE": "Learn more about integrations", "LOADING": "Fetching integrations", "CAPTAIN": {"DISABLED": "Captain is not enabled on your account.", "CLICK_HERE_TO_CONFIGURE": "Click here to configure", "LOADING_CONSOLE": "Loading Captain <PERSON><PERSON><PERSON>...", "FAILED_TO_LOAD_CONSOLE": "Failed to load Captain <PERSON><PERSON>. Please refresh and try again."}, "WEBHOOK": {"SUBSCRIBED_EVENTS": "رویدادهای مشترک شده", "LEARN_MORE": "Learn more about webhooks", "FORM": {"CANCEL": "انصراف", "DESC": "رویدادهای وب هوک اطلاعات لحظه‌ای حساب چت ووت شما را منتقل می‌کنند. لطفا آدرس URL صحیحی وارد کنید.", "SUBSCRIPTIONS": {"LABEL": "رویدادها", "EVENTS": {"CONVERSATION_CREATED": "گفتگو ایجاد شد", "CONVERSATION_STATUS_CHANGED": "وضعیت گفتگو تغییر کرد", "CONVERSATION_UPDATED": "گفتگو به روز شد", "MESSAGE_CREATED": "پیام ایجاد شد", "MESSAGE_UPDATED": "پیام به روز شد", "WEBWIDGET_TRIGGERED": "ابزارک گفتگو زنده توسط کاربر باز شده است", "CONTACT_CREATED": "مخا<PERSON><PERSON> ایجاد شد", "CONTACT_UPDATED": "مخاطب به‌روزرسانی شد", "CONVERSATION_TYPING_ON": "Conversation Typing On", "CONVERSATION_TYPING_OFF": "Conversation Typing Off"}}, "END_POINT": {"LABEL": "آدرس URL وب هوک", "PLACEHOLDER": "Example: {webhookExampleURL}", "ERROR": "لطفا آدرس URL صحیحی وارد کنید"}, "EDIT_SUBMIT": "به‌روزرسانی وب‌هوک", "ADD_SUBMIT": "ساخت وب هوک"}, "TITLE": "وب هوک", "CONFIGURE": "تنظیمات", "HEADER": "تنظیمات وب هوک", "HEADER_BTN_TXT": "اضافه کردن یک وب هوک جدید", "LOADING": "درحال دریافت اطلاعات وب هوک", "SEARCH_404": "هیچ گزینه‌ای با این شرایط پیدا نشد", "SIDEBAR_TXT": "<p><b>وب هوک‌ها</b> </p> \n<p>وب هوک‌ها اجرا کننده‌ی درخواست‌های HTTP هستند که برای هر حسابی قابل تنظیم شدن هستند. به عنوان مثال می‌توان وقتی گفتگوی جدیدی ایجاد شد یک وب سرویس صدا زده شود. برای هر حساب می‌توان چند وب هوک ایجاد کرد. <br /><br /> برای ساختن یک <b>وب هوک</b>, روی دکمه <b>اضافه کردن وب هوک جدید</b> کلیک کنید. همچنین با زدن دکمه «حذف» می‌توانید وب هوک ساخته شده را حذف کنید.</p>", "LIST": {"404": "هیچ وب هوکی برای این حساب ساخته نشده است", "TITLE": "مدیریت وب هوک‌ها", "TABLE_HEADER": {"WEBHOOK_ENDPOINT": "آدرس مقصد وب هوک", "ACTIONS": "عملیات"}}, "EDIT": {"BUTTON_TEXT": "ویرایش", "TITLE": "ویرایش وب هوک", "API": {"SUCCESS_MESSAGE": "پیکربندی وب‌هوک با موفقیت به روز شد", "ERROR_MESSAGE": "متاسفانه ارتباط با سرور برقرار نشد، مجددا امتحان کنید"}}, "ADD": {"CANCEL": "انصراف", "TITLE": "اضافه کردن وب هوک جدید", "API": {"SUCCESS_MESSAGE": "پیکربندی وب‌هوک با موفقیت اضافه شد", "ERROR_MESSAGE": "متاسفانه ارتباط با سرور برقرار نشد، مجددا امتحان کنید"}}, "DELETE": {"BUTTON_TEXT": "<PERSON><PERSON><PERSON>", "API": {"SUCCESS_MESSAGE": "وب هوک حذف شد", "ERROR_MESSAGE": "متاسفانه ارتباط با سرور برقرار نشد، مجددا امتحان کنید"}, "CONFIRM": {"TITLE": "تاییدیه حذف", "MESSAGE": "آیا برای حذف وب‌هوک مطمئن هستید؟ \n({webhookURL})", "YES": "بله، حذف شود", "NO": "<PERSON><PERSON><PERSON>، بماند"}}}, "SLACK": {"DELETE": "<PERSON><PERSON><PERSON>", "DELETE_CONFIRMATION": {"TITLE": "Delete the integration", "MESSAGE": "Are you sure you want to delete the integration? Doing so will result in the loss of access to conversations on your Slack workspace."}, "HELP_TEXT": {"TITLE": "استفاده از اسلک", "BODY": "With this integration, all of your incoming conversations will be synced to the ***{selectedChannelName}*** channel in your Slack workspace. You can manage all your customer conversations right within the channel and never miss a message.\n\nHere are the main features of the integration:\n\n**Respond to conversations from within Slack:** To respond to a conversation in the ***{selectedChannelName}*** Slack channel, simply type out your message and send it as a thread. This will create a response back to the customer through Chatwoot. It's that simple!\n\n **Create private notes:** If you want to create private notes instead of replies, start your message with ***`note:`***. This ensures that your message is kept private and won't be visible to the customer.\n\n**Associate an agent profile:** If the person who replied on Slack has an agent profile in Chatwoot under the same email, the replies will be associated with that agent profile automatically. This means you can easily track who said what and when. On the other hand, when the replier doesn't have an associated agent profile, the replies will appear from the bot profile to the customer.", "SELECTED": "انتخاب شد"}, "SELECT_CHANNEL": {"OPTION_LABEL": "یک کانال انتخاب کنید", "UPDATE": "اعمال شود", "BUTTON_TEXT": "اتصال کانال", "DESCRIPTION": "Your Slack workspace is now linked with Chatwoot. However, the integration is currently inactive. To activate the integration and connect a channel to Chatwoot, please click the button below.\n\n**Note:** If you are attempting to connect a private channel, add the Chatwoot app to the Slack channel before proceeding with this step.", "ATTENTION_REQUIRED": "Attention required", "EXPIRED": "Your Slack integration has expired. To continue receiving messages on Slack, please delete the integration and connect your workspace again."}, "UPDATE_ERROR": "There was an error updating the integration, please try again", "UPDATE_SUCCESS": "The channel is connected successfully", "FAILED_TO_FETCH_CHANNELS": "There was an error fetching the channels from Slack, please try again"}, "DYTE": {"CLICK_HERE_TO_JOIN": "برای عضویت اینجا را کلیک کنید", "LEAVE_THE_ROOM": "اتاق را ترک کن", "START_VIDEO_CALL_HELP_TEXT": "یک تماس ویدیویی جدید با مشتری شروع کنید", "JOIN_ERROR": "هنگام پیوستن به تماس خطایی روی داد، لطفاً دوباره امتحان کنید", "CREATE_ERROR": "در ایجاد پیوند جلسه خطایی روی داد، لطفاً دوباره امتحان کنید"}, "OPEN_AI": {"AI_ASSIST": "دستیار هوش مصنوعی", "WITH_AI": " {option} with AI ", "OPTIONS": {"REPLY_SUGGESTION": "پیشنه<PERSON> پاسخ", "SUMMARIZE": "Summarize", "REPHRASE": "Improve Writing", "FIX_SPELLING_GRAMMAR": "Fix Spelling and Grammar", "SHORTEN": "<PERSON>en", "EXPAND": "Expand", "MAKE_FRIENDLY": "Change message tone to friendly", "MAKE_FORMAL": "Use formal tone", "SIMPLIFY": "Simplify"}, "ASSISTANCE_MODAL": {"DRAFT_TITLE": "Draft content", "GENERATED_TITLE": "Generated content", "AI_WRITING": "AI is writing", "BUTTONS": {"APPLY": "Use this suggestion", "CANCEL": "انصراف"}}, "CTA_MODAL": {"TITLE": "Integrate with OpenAI", "DESC": "Bring advanced AI features to your dashboard with OpenAI's GPT models. To begin, enter the API key from your OpenAI account.", "KEY_PLACEHOLDER": "Enter your OpenAI API key", "BUTTONS": {"NEED_HELP": "به کمک احتياج داری؟", "DISMISS": "نادیده بگیر", "FINISH": "Finish Setup"}, "DISMISS_MESSAGE": "You can setup OpenAI integration later Whenever you want.", "SUCCESS_MESSAGE": "OpenAI integration setup successfully"}, "TITLE": "Improve With AI", "SUMMARY_TITLE": "Summary with AI", "REPLY_TITLE": "Reply suggestion with AI", "SUBTITLE": "An improved reply will be generated using AI, based on your current draft.", "TONE": {"TITLE": "<PERSON><PERSON>", "OPTIONS": {"PROFESSIONAL": "حرفه‌<PERSON>ی", "FRIENDLY": "دوستانه"}}, "BUTTONS": {"GENERATE": "تولید", "GENERATING": "در حال تولید...", "CANCEL": "انصراف"}, "GENERATE_ERROR": "There was an error processing the content, please try again"}, "DELETE": {"BUTTON_TEXT": "<PERSON><PERSON><PERSON>", "API": {"SUCCESS_MESSAGE": "ادغام با موفقیت حذف شد"}}, "CONNECT": {"BUTTON_TEXT": "اتصال"}, "DASHBOARD_APPS": {"TITLE": "برنامه‌های پیشخوان", "HEADER_BTN_TXT": "اضافه کردن یک برنامه پیشخوان جدید", "SIDEBAR_TXT": "<p><b>برنامه‌های داشبورد</b></p><p>برنامه‌های داشبورد به سازمان‌ها اجازه می‌دهند تا برنامه‌ای را در داشبورد Chatwoot جاسازی کنند تا زمینه را برای عوامل پشتیبانی مشتری فراهم کنند. این ویژگی به شما این امکان را می دهد که به طور مستقل یک برنامه ایجاد کنید و آن را در داشبورد جاسازی کنید تا اطلاعات کاربر، سفارشات یا سابقه پرداخت قبلی آنها را ارائه کنید.</p><p>وقتی برنامه خود را با استفاده از داشبورد در Chatwoot جاسازی می کنید، برنامه شما این کار را انجام می دهد. زمینه گفتگو و تماس را به عنوان یک رویداد پنجره دریافت کنید. یک شنونده برای رویداد پیام در صفحه خود اجرا کنید تا زمینه را دریافت کنید.</p><p>برای افزودن یک برنامه داشبورد جدید، روی دکمه \"افزودن یک برنامه داشبورد جدید\" کلیک کنید.</p>", "DESCRIPTION": "برنامه‌های داشبورد به سازمان‌ها اجازه می‌دهند تا برنامه‌ای را در داشبورد جاسازی کنند تا زمینه را برای عوامل پشتیبانی مشتری فراهم کنند. این ویژگی به شما این امکان را می دهد که به طور مستقل یک برنامه کاربردی ایجاد کنید و آن را برای ارائه اطلاعات کاربر، سفارشات یا سابقه پرداخت قبلی آنها درج کنید.", "LEARN_MORE": "Learn more about Dashboard Apps", "LIST": {"404": "هنوز هیچ برنامه داشبوردی روی این حساب پیکربندی نشده است", "LOADING": "در حال واکشی برنامه های داشبورد...", "TABLE_HEADER": {"NAME": "نام", "ENDPOINT": "نقطه پایانی"}, "EDIT_TOOLTIP": "ویرایش برنامه", "DELETE_TOOLTIP": "حذ<PERSON> برنا<PERSON>ه"}, "FORM": {"TITLE_LABEL": "نام", "TITLE_PLACEHOLDER": "یک نام برای برنامه پیشخوان خود وارد کنید", "TITLE_ERROR": "یک نام برای برنامه داشبورد لازم است", "URL_LABEL": "نقطه پایانی", "URL_PLACEHOLDER": "URL نقطه پایانی را که برنامه شما در آن میزبانی شده است وارد کنید", "URL_ERROR": "یک URL معتبر مورد نیاز است"}, "CREATE": {"HEADER": "اضافه کردن یک برنامه پیشخوان جدید", "FORM_SUBMIT": "ثبت", "FORM_CANCEL": "انصراف", "API_SUCCESS": "برنامه داشبورد با موفقیت پیکربندی شد", "API_ERROR": "ما نتوانستیم یک برنامه ایجاد کنیم. لطفاً بعداً دوباره امتحان کنید"}, "UPDATE": {"HEADER": "ویرایش برنامه پیشخوان", "FORM_SUBMIT": "اعمال شود", "FORM_CANCEL": "انصراف", "API_SUCCESS": "برنامه پیشخوان با موفقیت به‌روز شد", "API_ERROR": "ما نمی‌توانستیم برنامه را به‌روز کنیم. لطفا بعدا دوباره امتحان کنید"}, "DELETE": {"CONFIRM_YES": "بله، حذف شود", "CONFIRM_NO": "<PERSON><PERSON><PERSON>، بماند", "TITLE": "تاییدیه حذف", "MESSAGE": "آیا مطمئن هستید که برنامه {appName} حذف شود؟", "API_SUCCESS": "برنامه پیشخوان با موفقیت حذف شد", "API_ERROR": "ما نتوانستیم برنامه را حذف کنیم. لطفا بعدا دوباره امتحان کنید"}}, "LINEAR": {"ADD_OR_LINK_BUTTON": "Create/Link Linear Issue", "LOADING": "Fetching linear issues...", "LOADING_ERROR": "There was an error fetching the linear issues, please try again", "CREATE": "ايج<PERSON> كردن", "LINK": {"SEARCH": "Search issues", "SELECT": "Select issue", "TITLE": "پیوند", "EMPTY_LIST": "No linear issues found", "LOADING": "Loading", "ERROR": "There was an error fetching the linear issues, please try again", "LINK_SUCCESS": "Issue linked successfully", "LINK_ERROR": "There was an error linking the issue, please try again", "LINK_TITLE": "Conversation (#{conversationId}) with {name}"}, "ADD_OR_LINK": {"TITLE": "Create/link linear issue", "DESCRIPTION": "Create Linear issues from conversations, or link existing ones for seamless tracking.", "FORM": {"TITLE": {"LABEL": "عنوان", "PLACEHOLDER": "Enter title", "REQUIRED_ERROR": "عنوان الزامی است"}, "DESCRIPTION": {"LABEL": "توضیحات", "PLACEHOLDER": "Enter description"}, "TEAM": {"LABEL": "تیم‌", "PLACEHOLDER": "انتخاب تیم", "SEARCH": "Search team", "REQUIRED_ERROR": "Team is required"}, "ASSIGNEE": {"LABEL": "مسئول", "PLACEHOLDER": "Select assignee", "SEARCH": "Search assignee"}, "PRIORITY": {"LABEL": "اولویت", "PLACEHOLDER": "انتخاب اولویت", "SEARCH": "Search priority"}, "LABEL": {"LABEL": "برچسب", "PLACEHOLDER": "Select label", "SEARCH": "Search label"}, "STATUS": {"LABEL": "وضعیت", "PLACEHOLDER": "Select status", "SEARCH": "Search status"}, "PROJECT": {"LABEL": "Project", "PLACEHOLDER": "Select project", "SEARCH": "Search project"}}, "CREATE": "ايج<PERSON> كردن", "CANCEL": "انصراف", "CREATE_SUCCESS": "Issue created successfully", "CREATE_ERROR": "There was an error creating the issue, please try again", "LOADING_TEAM_ERROR": "There was an error fetching the teams, please try again", "LOADING_TEAM_ENTITIES_ERROR": "There was an error fetching the team entities, please try again"}, "ISSUE": {"STATUS": "وضعیت", "PRIORITY": "اولویت", "ASSIGNEE": "مسئول", "LABELS": "برچسب‌ها", "CREATED_AT": "Created at {createdAt}"}, "UNLINK": {"TITLE": "Unlink", "SUCCESS": "Issue unlinked successfully", "ERROR": "There was an error unlinking the issue, please try again"}, "DELETE": {"TITLE": "Are you sure you want to delete the integration?", "MESSAGE": "Are you sure you want to delete the integration?", "CONFIRM": "بله، حذف شود", "CANCEL": "انصراف"}}}, "CAPTAIN": {"NAME": "Captain", "HEADER_KNOW_MORE": "Know more", "COPILOT": {"SEND_MESSAGE": "ارسال پیام...", "EMPTY_MESSAGE": "There was an error generating the response. Please try again.", "LOADER": "Captain is thinking", "YOU": "شما", "USE": "Use this", "RESET": "Reset", "SELECT_ASSISTANT": "Select Assistant", "PROMPTS": {"SUMMARIZE": {"LABEL": "Summarize this conversation", "CONTENT": "Summarize the key points discussed between the customer and the support agent, including the customer's concerns, questions, and the solutions or responses provided by the support agent"}, "SUGGEST": {"LABEL": "Suggest an answer", "CONTENT": "Analyze the customer's inquiry, and draft a response that effectively addresses their concerns or questions. Ensure the reply is clear, concise, and provides helpful information."}, "RATE": {"LABEL": "Rate this conversation", "CONTENT": "Review the conversation to see how well it meets the customer's needs. Share a rating out of 5 based on tone, clarity, and effectiveness."}}}, "PLAYGROUND": {"USER": "شما", "ASSISTANT": "Assistant", "MESSAGE_PLACEHOLDER": "پیام خود را وارد کنید...", "HEADER": "Playground", "DESCRIPTION": "Use this playground to send messages to your assistant and check if it responds accurately, quickly, and in the tone you expect.", "CREDIT_NOTE": "Messages sent here will count toward your Captain credits."}, "PAYWALL": {"TITLE": "Upgrade to use Captain AI", "AVAILABLE_ON": "Captain is not available on the free plan.", "UPGRADE_PROMPT": "Upgrade your plan to get access to our assistants, copilot and more.", "UPGRADE_NOW": "حالا ارتقا دهید", "CANCEL_ANYTIME": "You can change or cancel your plan anytime"}, "ENTERPRISE_PAYWALL": {"AVAILABLE_ON": "Captain AI feature is only available in a paid plan.", "UPGRADE_PROMPT": "Upgrade your plan to get access to our assistants, copilot and more.", "ASK_ADMIN": "لطفاً برای ارتقا با ادمین خود تماس بگیرید."}, "BANNER": {"RESPONSES": "You've used over 80% of your response limit. To continue using Captain AI, please upgrade.", "DOCUMENTS": "Document limit reached. Upgrade to continue using Captain AI."}, "FORM": {"CANCEL": "انصراف", "CREATE": "ايج<PERSON> كردن", "EDIT": "اعمال شود"}, "ASSISTANTS": {"HEADER": "Assistants", "ADD_NEW": "Create a new assistant", "DELETE": {"TITLE": "Are you sure to delete the assistant?", "DESCRIPTION": "This action is permanent. Deleting this assistant will remove it from all connected inboxes and permanently erase all generated knowledge.", "CONFIRM": "بله، حذف شود", "SUCCESS_MESSAGE": "The assistant has been successfully deleted", "ERROR_MESSAGE": "There was an error deleting the assistant, please try again."}, "FORM_DESCRIPTION": "Fill out the details below to name your assistant, describe its purpose, and specify the product it will support.", "CREATE": {"TITLE": "Create an assistant", "SUCCESS_MESSAGE": "The assistant has been successfully created", "ERROR_MESSAGE": "There was an error creating the assistant, please try again."}, "FORM": {"UPDATE": "اعمال شود", "SECTIONS": {"BASIC_INFO": "Basic Information", "SYSTEM_MESSAGES": "System Messages", "INSTRUCTIONS": "Instructions", "FEATURES": "امکانات", "TOOLS": "Tools "}, "NAME": {"LABEL": "نام", "PLACEHOLDER": "Enter assistant name", "ERROR": "The name is required"}, "DESCRIPTION": {"LABEL": "توضیحات", "PLACEHOLDER": "Enter assistant description", "ERROR": "The description is required"}, "PRODUCT_NAME": {"LABEL": "Product Name", "PLACEHOLDER": "Enter product name", "ERROR": "The product name is required"}, "WELCOME_MESSAGE": {"LABEL": "Welcome Message", "PLACEHOLDER": "Enter welcome message"}, "HANDOFF_MESSAGE": {"LABEL": "Handoff Message", "PLACEHOLDER": "Enter handoff message"}, "RESOLUTION_MESSAGE": {"LABEL": "Resolution Message", "PLACEHOLDER": "Enter resolution message"}, "INSTRUCTIONS": {"LABEL": "Instructions", "PLACEHOLDER": "Enter instructions for the assistant"}, "FEATURES": {"TITLE": "امکانات", "ALLOW_CONVERSATION_FAQS": "Generate FAQs from resolved conversations", "ALLOW_MEMORIES": "Capture key details as memories from customer interactions."}}, "EDIT": {"TITLE": "Update the assistant", "SUCCESS_MESSAGE": "The assistant has been successfully updated", "ERROR_MESSAGE": "There was an error updating the assistant, please try again.", "NOT_FOUND": "Could not find the assistant. Please try again."}, "OPTIONS": {"EDIT_ASSISTANT": "Edit Assistant", "DELETE_ASSISTANT": "Delete Assistant", "VIEW_CONNECTED_INBOXES": "View connected inboxes"}, "EMPTY_STATE": {"TITLE": "No assistants available", "SUBTITLE": "Create an assistant to provide quick and accurate responses to your users. It can learn from your help articles and past conversations.", "FEATURE_SPOTLIGHT": {"TITLE": "Captain Assistant", "NOTE": "Captain Assistant engages directly with customers, learns from your help docs and past conversations, and delivers instant, accurate responses. It handles the initial queries, providing quick resolutions before  transferring to an agent when needed."}}}, "DOCUMENTS": {"HEADER": "Documents", "ADD_NEW": "Create a new document", "RELATED_RESPONSES": {"TITLE": "Related FAQs", "DESCRIPTION": "These FAQs are generated directly from the document."}, "FORM_DESCRIPTION": "Enter the URL of the document to add it as a knowledge source and choose the assistant to associate it with.", "CREATE": {"TITLE": "Add a document", "SUCCESS_MESSAGE": "The document has been successfully created", "ERROR_MESSAGE": "There was an error creating the document, please try again."}, "FORM": {"URL": {"LABEL": "URL", "PLACEHOLDER": "Enter the URL of the document", "ERROR": "Please provide a valid URL for the document"}, "ASSISTANT": {"LABEL": "Assistant", "PLACEHOLDER": "Select the assistant", "ERROR": "The assistant field is required"}}, "DELETE": {"TITLE": "Are you sure to delete the document?", "DESCRIPTION": "This action is permanent. Deleting this document will permanently erase all generated knowledge.", "CONFIRM": "بله، حذف شود", "SUCCESS_MESSAGE": "The document has been successfully deleted", "ERROR_MESSAGE": "There was an error deleting the document, please try again."}, "OPTIONS": {"VIEW_RELATED_RESPONSES": "View Related Responses", "DELETE_DOCUMENT": "Delete Document"}, "EMPTY_STATE": {"TITLE": "No documents available", "SUBTITLE": "Documents are used by your assistant to generate FAQs. You can import documents to provide context for your assistant.", "FEATURE_SPOTLIGHT": {"TITLE": "Captain <PERSON>ument", "NOTE": "A document in Captain serves as a knowledge resource for the assistant. By connecting your help center or guides, Captain can analyze the content and provide accurate responses for customer inquiries."}}}, "RESPONSES": {"HEADER": "FAQs", "ADD_NEW": "Create new FAQ", "DOCUMENTABLE": {"CONVERSATION": "Conversation #{id}"}, "SELECTED": "{count} selected", "BULK_APPROVE_BUTTON": "Approve", "BULK_DELETE_BUTTON": "<PERSON><PERSON><PERSON>", "BULK_APPROVE": {"SUCCESS_MESSAGE": "FAQs approved successfully", "ERROR_MESSAGE": "There was an error approving the FAQs, please try again."}, "BULK_DELETE": {"TITLE": "Delete FAQs?", "DESCRIPTION": "Are you sure you want to delete the selected FAQs? This action cannot be undone.", "CONFIRM": "Yes, delete all", "SUCCESS_MESSAGE": "FAQs deleted successfully", "ERROR_MESSAGE": "There was an error deleting the FAQs, please try again."}, "DELETE": {"TITLE": "Are you sure to delete the FAQ?", "DESCRIPTION": "", "CONFIRM": "بله، حذف شود", "SUCCESS_MESSAGE": "FAQ deleted successfully", "ERROR_MESSAGE": "There was an error deleting the FAQ, please try again."}, "FILTER": {"ASSISTANT": "Assistant: {selected}", "STATUS": "Status: {selected}", "ALL_ASSISTANTS": "همه"}, "STATUS": {"TITLE": "وضعیت", "PENDING": "در انتظار", "APPROVED": "Approved", "ALL": "همه"}, "FORM_DESCRIPTION": "Add a question and its corresponding answer to the knowledge base and select the assistant it should be associated with.", "CREATE": {"TITLE": "Add an FAQ", "SUCCESS_MESSAGE": "The response has been added successfully.", "ERROR_MESSAGE": "An error occurred while adding the response. Please try again."}, "FORM": {"QUESTION": {"LABEL": "Question", "PLACEHOLDER": "Enter the question here", "ERROR": "Please provide a valid question."}, "ANSWER": {"LABEL": "Answer", "PLACEHOLDER": "Enter the answer here", "ERROR": "Please provide a valid answer."}, "ASSISTANT": {"LABEL": "Assistant", "PLACEHOLDER": "Select an assistant", "ERROR": "Please select an assistant."}}, "EDIT": {"TITLE": "Update the FAQ", "SUCCESS_MESSAGE": "The FAQ has been successfully updated", "ERROR_MESSAGE": "There was an error updating the FAQ, please try again", "APPROVE_SUCCESS_MESSAGE": "The FAQ was marked as approved"}, "OPTIONS": {"APPROVE": "Mark as approved", "EDIT_RESPONSE": "Edit FAQ", "DELETE_RESPONSE": "Delete FAQ"}, "EMPTY_STATE": {"TITLE": "No FAQs Found", "SUBTITLE": "FAQs help your assistant provide quick and accurate answers to questions from your customers. They can be generated automatically from your content or can be added manually.", "FEATURE_SPOTLIGHT": {"TITLE": "Captain FAQ", "NOTE": "Captain <PERSON><PERSON><PERSON> detects common customer questions—whether missing from your knowledge base or frequently asked—and generates relevant FAQs to improve support. You can review each suggestion and decide whether to approve or reject it."}}}, "INBOXES": {"HEADER": "Connected Inboxes", "ADD_NEW": "Connect a new inbox", "OPTIONS": {"DISCONNECT": "قعط کردن"}, "DELETE": {"TITLE": "Are you sure to disconnect the inbox?", "DESCRIPTION": "", "CONFIRM": "بله، حذف شود", "SUCCESS_MESSAGE": "The inbox was successfully disconnected.", "ERROR_MESSAGE": "There was an error disconnecting the inbox, please try again."}, "FORM_DESCRIPTION": "Choose an inbox to connect with the assistant.", "CREATE": {"TITLE": "Connect an Inbox", "SUCCESS_MESSAGE": "The inbox was successfully connected.", "ERROR_MESSAGE": "An error occurred while connecting the inbox. Please try again."}, "FORM": {"INBOX": {"LABEL": "صندوق ورودی", "PLACEHOLDER": "Choose the inbox to deploy the assistant.", "ERROR": "An inbox selection is required."}}, "EMPTY_STATE": {"TITLE": "No Connected Inboxes", "SUBTITLE": "Connecting an inbox allows the assistant to handle initial questions from your customers before transferring them to you."}}}}