{"INBOX_MGMT": {"HEADER": "Innbokser", "DESCRIPTION": "A channel is the mode of communication your customer chooses to interact with you. An inbox is where you manage interactions for a specific channel. It can include communications from various sources such as email, live chat, and social media.", "LEARN_MORE": "Learn more about inboxes", "RECONNECTION_REQUIRED": "Your inbox is disconnected. You won't receive new messages until you reauthorize it.", "CLICK_TO_RECONNECT": "Click here to reconnect.", "LIST": {"404": "Det er ingen innbokser tilknyttet denne kontoen."}, "CREATE_FLOW": {"CHANNEL": {"TITLE": "Velg kanal", "BODY": "Velg tilbyderen du vil integrere med Chatwoot."}, "INBOX": {"TITLE": "Opprett innboks", "BODY": "Autoriser din konto og opprett en innboks."}, "AGENT": {"TITLE": "<PERSON><PERSON> til <PERSON>er", "BODY": "Legg agenter til i den opprettede innboksen."}, "FINISH": {"TITLE": "Voilà!", "BODY": "<PERSON><PERSON> - ferdig - gå!"}}, "ADD": {"CHANNEL_NAME": {"LABEL": "Navn på innboks", "PLACEHOLDER": "Enter your inbox name (eg: Acme Inc)", "ERROR": "Please enter a valid inbox name"}, "WEBSITE_NAME": {"LABEL": "Nettstedsnavn", "PLACEHOLDER": "Angi navnet på nettsiden (f. eks.: Acme Inc)"}, "FB": {"HELP": "PS: Ved å logge inn får vi bare tilgang til meldingene til siden. Dine private meldinger vil aldri bli lest av Chatwoot.", "CHOOSE_PAGE": "Velg side", "CHOOSE_PLACEHOLDER": "Velg en side fra listen", "INBOX_NAME": "Navn på innboks", "ADD_NAME": "Legge til et navn på innboksen din", "PICK_NAME": "Pick a Name for your Inbox", "PICK_A_VALUE": "Velg en verdi", "CREATE_INBOX": "Opprett innboks"}, "INSTAGRAM": {"CONTINUE_WITH_INSTAGRAM": "Continue with Instagram", "CONNECT_YOUR_INSTAGRAM_PROFILE": "Connect your Instagram Profile", "HELP": "To add your Instagram profile as a channel, you need to authenticate your Instagram Profile by clicking on 'Continue with Instagram' ", "ERROR_MESSAGE": "There was an error connecting to Instagram, please try again", "ERROR_AUTH": "There was an error connecting to Instagram, please try again", "NEW_INBOX_SUGGESTION": "This Instagram account was previously linked to a different inbox and has now been migrated here. All new messages will appear here. The old inbox will no longer be able to send or receive messages for this account.", "DUPLICATE_INBOX_BANNER": "This Instagram account was migrated to the new Instagram channel inbox. You won’t be able to send/receive Instagram messages from this inbox anymore."}, "TWITTER": {"HELP": "For å legge til din Twitter-profil som kanal, må du autorisere din Twitter-profil ved å klikke på 'Logg inn med Twitter' ", "ERROR_MESSAGE": "There was an error connecting to Twitter, please try again", "TWEETS": {"ENABLE": "Create conversations from mentioned Tweets"}}, "WEBSITE_CHANNEL": {"TITLE": "Nettsidekanal", "DESC": "Opprett en kanal for nettstedet ditt og begynn å hjelpe kundene dine via nettstedswidgeten.", "LOADING_MESSAGE": "Oppretter nettstedskanal", "CHANNEL_AVATAR": {"LABEL": "Kanal profilbilde"}, "CHANNEL_WEBHOOK_URL": {"LABEL": "Webhook URL", "PLACEHOLDER": "Please enter your Webhook URL", "ERROR": "Vennligst skriv inn en gyldig URL"}, "CHANNEL_DOMAIN": {"LABEL": "Nettstedets domene", "PLACEHOLDER": "Angi URL på nettsiden (f. eks.: acme.no)"}, "CHANNEL_WELCOME_TITLE": {"LABEL": "Velkomsttittel", "PLACEHOLDER": "T<PERSON><PERSON><PERSON>!"}, "CHANNEL_WELCOME_TAGLINE": {"LABEL": "Velkomsthilsen undertittel", "PLACEHOLDER": "Det skal være lett å kontakte oss. Spør oss om hva som helst, eller del din tilbakemelding."}, "CHANNEL_GREETING_MESSAGE": {"LABEL": "<PERSON><PERSON><PERSON><PERSON>", "PLACEHOLDER": "Acme AS pleier vanligvis å svare i løpet av noen timer."}, "CHANNEL_GREETING_TOGGLE": {"LABEL": "Akt<PERSON> kanal<PERSON>", "HELP_TEXT": "Automatically send a greeting message when a new conversation is created.", "ENABLED": "Aktivert", "DISABLED": "Deaktivert"}, "REPLY_TIME": {"TITLE": "<PERSON><PERSON>tid", "IN_A_FEW_MINUTES": "Om noen få minutter", "IN_A_FEW_HOURS": "Om noen få timer", "IN_A_DAY": "Om en dag", "HELP_TEXT": "<PERSON>ne svartiden vil bli vist i live chat widgeten"}, "WIDGET_COLOR": {"LABEL": "Widget farge", "PLACEHOLDER": "Oppdater fargen brukt i widgeten"}, "SUBMIT_BUTTON": "Opprett innboks", "API": {"ERROR_MESSAGE": "We were not able to create a website channel, please try again"}}, "TWILIO": {"TITLE": "Twilio SMS/WhatsApp Channel", "DESC": "Integrate Twilio and start supporting your customers via SMS or WhatsApp.", "ACCOUNT_SID": {"LABEL": "Konto-SID", "PLACEHOLDER": "Skriv inn din Twilio konto-SID", "ERROR": "<PERSON>te feltet er obligatorisk"}, "API_KEY": {"USE_API_KEY": "Use API Key Authentication", "LABEL": "API Key SID", "PLACEHOLDER": "Please enter your API Key SID", "ERROR": "<PERSON>te feltet er obligatorisk"}, "API_KEY_SECRET": {"LABEL": "API Key Secret", "PLACEHOLDER": "Please enter your API Key Secret", "ERROR": "<PERSON>te feltet er obligatorisk"}, "MESSAGING_SERVICE_SID": {"LABEL": "Messaging Service SID", "PLACEHOLDER": "Please enter your Twilio Messaging Service SID", "ERROR": "<PERSON>te feltet er obligatorisk", "USE_MESSAGING_SERVICE": "Use a Twilio Messaging Service"}, "CHANNEL_TYPE": {"LABEL": "Kanaltype", "ERROR": "Velg din kanaltype"}, "AUTH_TOKEN": {"LABEL": "Autoriseringstoken", "PLACEHOLDER": "Vennligst skriv inn din Twilio autoriseringstoken", "ERROR": "<PERSON>te feltet er obligatorisk"}, "CHANNEL_NAME": {"LABEL": "Navn på innboks", "PLACEHOLDER": "Please enter a inbox name", "ERROR": "<PERSON>te feltet er obligatorisk"}, "PHONE_NUMBER": {"LABEL": "Telefonnummer", "PLACEHOLDER": "Vennligst angi telefonnummeret som meldinger skal sendes fra.", "ERROR": "Please provide a valid phone number that starts with a `+` sign and does not contain any spaces."}, "API_CALLBACK": {"TITLE": "Callback URL", "SUBTITLE": "<PERSON> må konfigurere callback URL for meldinger med URLen nevnt her."}, "SUBMIT_BUTTON": "<PERSON><PERSON><PERSON><PERSON>nal", "API": {"ERROR_MESSAGE": "Vi kunne ikke godkjenne Twilio legitimasjonsopplysningene, vennligst prøv igjen"}}, "SMS": {"TITLE": "SMS Channel", "DESC": "Start supporting your customers via SMS.", "PROVIDERS": {"LABEL": "API Provider", "TWILIO": "<PERSON><PERSON><PERSON>", "BANDWIDTH": "Bandwidth"}, "API": {"ERROR_MESSAGE": "We were not able to save the SMS channel"}, "BANDWIDTH": {"ACCOUNT_ID": {"LABEL": "Account ID", "PLACEHOLDER": "Please enter your Bandwidth Account ID", "ERROR": "<PERSON>te feltet er obligatorisk"}, "API_KEY": {"LABEL": "API Key", "PLACEHOLDER": "Please enter your Bandwidth API Key", "ERROR": "<PERSON>te feltet er obligatorisk"}, "API_SECRET": {"LABEL": "API Secret", "PLACEHOLDER": "Please enter your Bandwidth API Secret", "ERROR": "<PERSON>te feltet er obligatorisk"}, "APPLICATION_ID": {"LABEL": "Application ID", "PLACEHOLDER": "Please enter your Bandwidth Application ID", "ERROR": "<PERSON>te feltet er obligatorisk"}, "INBOX_NAME": {"LABEL": "Navn på innboks", "PLACEHOLDER": "Please enter a inbox name", "ERROR": "<PERSON>te feltet er obligatorisk"}, "PHONE_NUMBER": {"LABEL": "Telefonnummer", "PLACEHOLDER": "Vennligst angi telefonnummeret som meldinger skal sendes fra.", "ERROR": "Please provide a valid phone number that starts with a `+` sign and does not contain any spaces."}, "SUBMIT_BUTTON": "Create Bandwidth Channel", "API": {"ERROR_MESSAGE": "We were not able to authenticate Bandwidth credentials, please try again"}, "API_CALLBACK": {"TITLE": "Callback URL", "SUBTITLE": "You have to configure the message callback URL in Bandwidth with the URL mentioned here."}}}, "WHATSAPP": {"TITLE": "WhatsApp Channel", "DESC": "Start supporting your customers via WhatsApp.", "PROVIDERS": {"LABEL": "API Provider", "TWILIO": "<PERSON><PERSON><PERSON>", "WHATSAPP_CLOUD": "WhatsApp Cloud", "360_DIALOG": "360Dialog"}, "INBOX_NAME": {"LABEL": "Navn på innboks", "PLACEHOLDER": "Please enter an inbox name", "ERROR": "<PERSON>te feltet er obligatorisk"}, "PHONE_NUMBER": {"LABEL": "Telefonnummer", "PLACEHOLDER": "Vennligst angi telefonnummeret som meldinger skal sendes fra.", "ERROR": "Please provide a valid phone number that starts with a `+` sign and does not contain any spaces."}, "PHONE_NUMBER_ID": {"LABEL": "Phone number ID", "PLACEHOLDER": "Please enter the Phone number ID obtained from Facebook developer dashboard.", "ERROR": "Please enter a valid value."}, "BUSINESS_ACCOUNT_ID": {"LABEL": "Business Account ID", "PLACEHOLDER": "Please enter the Business Account ID obtained from Facebook developer dashboard.", "ERROR": "Please enter a valid value."}, "WEBHOOK_VERIFY_TOKEN": {"LABEL": "Webhook Verify Token", "PLACEHOLDER": "Enter a verify token which you want to configure for Facebook webhooks.", "ERROR": "Please enter a valid value."}, "API_KEY": {"LABEL": "API key", "SUBTITLE": "Configure the WhatsApp API key.", "PLACEHOLDER": "API key", "ERROR": "Please enter a valid value."}, "API_CALLBACK": {"TITLE": "Callback URL", "SUBTITLE": "You have to configure the webhook URL and the verification token in the Facebook Developer portal with the values shown below.", "WEBHOOK_URL": "Webhook URL", "WEBHOOK_VERIFICATION_TOKEN": "Webhook Verification Token"}, "SUBMIT_BUTTON": "Create WhatsApp Channel", "API": {"ERROR_MESSAGE": "We were not able to save the WhatsApp channel"}}, "API_CHANNEL": {"TITLE": "API Kanal", "DESC": "Integrer med en API-kanal for å støtte dine kunder.", "CHANNEL_NAME": {"LABEL": "Kanalnavn", "PLACEHOLDER": "Vennligst skriv inn et kanalnavn", "ERROR": "<PERSON>te feltet er obligatorisk"}, "WEBHOOK_URL": {"LABEL": "Webhook URL", "SUBTITLE": "Configure the URL where you want to receive callbacks on events.", "PLACEHOLDER": "Webhook URL"}, "SUBMIT_BUTTON": "Opprett API-kanal", "API": {"ERROR_MESSAGE": "Vi klarte ikke å lagre API-kanalen"}}, "EMAIL_CHANNEL": {"TITLE": "E-postkanal", "DESC": "Integrate your email inbox.", "CHANNEL_NAME": {"LABEL": "Kanalnavn", "PLACEHOLDER": "Vennligst skriv inn et kanalnavn", "ERROR": "<PERSON>te feltet er obligatorisk"}, "EMAIL": {"LABEL": "E-post", "SUBTITLE": "E-post der kundene dine sender deg hen<PERSON><PERSON><PERSON>", "PLACEHOLDER": "E-post"}, "SUBMIT_BUTTON": "Opprett e-postkanal", "API": {"ERROR_MESSAGE": "Vi kunne ikke lagre e-postkanalen"}, "FINISH_MESSAGE": "Begynn å videresende e-post til følgende e-postadresse."}, "LINE_CHANNEL": {"TITLE": "LINE Channel", "DESC": "Integrate with LINE channel and start supporting your customers.", "CHANNEL_NAME": {"LABEL": "Kanalnavn", "PLACEHOLDER": "Vennligst skriv inn et kanalnavn", "ERROR": "<PERSON>te feltet er obligatorisk"}, "LINE_CHANNEL_ID": {"LABEL": "LINE Channel ID", "PLACEHOLDER": "LINE Channel ID"}, "LINE_CHANNEL_SECRET": {"LABEL": "LINE Channel Secret", "PLACEHOLDER": "LINE Channel Secret"}, "LINE_CHANNEL_TOKEN": {"LABEL": "LINE Channel Token", "PLACEHOLDER": "LINE Channel Token"}, "SUBMIT_BUTTON": "Create LINE Channel", "API": {"ERROR_MESSAGE": "We were not able to save the LINE channel"}, "API_CALLBACK": {"TITLE": "Callback URL", "SUBTITLE": "You have to configure the webhook URL in LINE application with the URL mentioned here."}}, "TELEGRAM_CHANNEL": {"TITLE": "Telegram Channel", "DESC": "Integrate with Telegram channel and start supporting your customers.", "BOT_TOKEN": {"LABEL": "Bot <PERSON>", "SUBTITLE": "Configure the bot token you have obtained from Telegram BotFather.", "PLACEHOLDER": "Bot <PERSON>"}, "SUBMIT_BUTTON": "Create Telegram Channel", "API": {"ERROR_MESSAGE": "We were not able to save the telegram channel"}}, "AUTH": {"TITLE": "Choose a channel", "DESC": "Chatwoot supports live-chat widgets, Facebook Messenger, Twitter profiles, WhatsApp, Emails, etc., as channels. If you want to build a custom channel, you can create it using the API channel. To get started, choose one of the channels below."}, "AGENTS": {"TITLE": "<PERSON><PERSON>", "DESC": "Her kan du legge til agenter for å administrere innboksen din. Bare de valgte agentene har tilgang til innboksen din. Agenter som ikke er en del av denne innboksen vil ikke kunne se eller svare på meldinger i denne innboksen når de logger inn. <br> <b>PS:</b> Dersom du som administrator trenger tilgang til alle innbokser, må du legge deg selv til som agent i alle innbokser du lager.", "VALIDATION_ERROR": "Add at least one agent to your new Inbox", "PICK_AGENTS": "Velg agenter for innboksen"}, "DETAILS": {"TITLE": "Innboksdetaljer", "DESC": "Fra listen under, velg facebook-siden du vil koble til Chatwoot. Du kan også gi innboksen et egendefinert navn for bedre identifisering."}, "FINISH": {"TITLE": "Den satt!", "DESC": "Du er ferdig med å integrere din facebook-side med Chatwoot. Neste gang en kunde sender en melding fra siden din vil samtalen vises automatisk i innboksen din.<br>Vi gir deg også et widget skript som du enkelt kan legge til på nettstedet ditt. Når dette er direkte på din nettside kan kunder sende deg meldinger fra nettstedet ditt uten hjelp av eksterne verktøy og samtalen vil vises her, i Chatwoot.<br><PERSON><PERSON>, eller hva? Vel, vi prøver :)"}, "EMAIL_PROVIDER": {"TITLE": "Select your email provider", "DESCRIPTION": "Select an email provider from the list below. If you don't see your email provider in the list, you can select the other provider option and provide the IMAP and SMTP Credentials."}, "MICROSOFT": {"TITLE": "Microsoft Email", "DESCRIPTION": "Click on the Sign in with Microsoft button to get started. You will redirected to the email sign in page. Once you accept the requested permissions, you would be redirected back to the inbox creation step.", "EMAIL_PLACEHOLDER": "Enter email address", "SIGN_IN": "Sign in with Microsoft", "ERROR_MESSAGE": "There was an error connecting to Microsoft, please try again"}, "GOOGLE": {"TITLE": "Google Email", "DESCRIPTION": "Click on the Sign in with Google button to get started. You will redirected to the email sign in page. Once you accept the requested permissions, you would be redirected back to the inbox creation step.", "SIGN_IN": "Sign in with Google", "EMAIL_PLACEHOLDER": "Enter email address", "ERROR_MESSAGE": "There was an error connecting to Google, please try again"}}, "DETAILS": {"LOADING_FB": "Autentiserer deg med Facebook...", "ERROR_FB_LOADING": "Error loading Facebook SDK. Please disable any ad-blockers and try again from a different browser.", "ERROR_FB_AUTH": "<PERSON>e gikk galt, oppdater siden...", "ERROR_FB_UNAUTHORIZED": "You're not authorized to perform this action. ", "ERROR_FB_UNAUTHORIZED_HELP": "Please ensure you have access to the Facebook page with full control. You can read more about Facebook roles <a href=\" https://www.facebook.com/help/187316341316631\">here</a>.", "CREATING_CHANNEL": "Oppretter innboksen din...", "TITLE": "Konfigurer innboksdetaljer", "DESC": ""}, "AGENTS": {"BUTTON_TEXT": "<PERSON><PERSON> til <PERSON>er", "ADD_AGENTS": "<PERSON><PERSON> til agenter i innboksen..."}, "FINISH": {"TITLE": "Inn<PERSON>ks<PERSON> din er klar!", "MESSAGE": "You can now engage with your customers through your new Channel. Happy supporting", "BUTTON_TEXT": "Ta meg dit", "MORE_SETTINGS": "More settings", "WEBSITE_SUCCESS": "Du har nå fullført opprettingen av nettstedskanalen. Kopier koden nedenfor og lim den inn på nettstedet. Neste gang en kunde bruker live-chatten vil samtalen vises automatisk i innboksen din."}, "REAUTH": "Reautoriser", "VIEW": "Vis", "EDIT": {"API": {"SUCCESS_MESSAGE": "Innboksinnstillinger ble oppdatert", "AUTO_ASSIGNMENT_SUCCESS_MESSAGE": "Autotildeling ble oppdatert", "ERROR_MESSAGE": "We couldn't update inbox settings. Please try again later."}, "EMAIL_COLLECT_BOX": {"ENABLED": "Aktivert", "DISABLED": "Deaktivert"}, "ENABLE_CSAT": {"ENABLED": "Aktivert", "DISABLED": "Deaktivert"}, "SENDER_NAME_SECTION": {"TITLE": "Sender name", "SUB_TEXT": "Select the name shown to your customer when they receive emails from your agents.", "FOR_EG": "For eg:", "FRIENDLY": {"TITLE": "Friendly", "FROM": "fra", "SUBTITLE": "Add the name of the agent who sent the reply in the sender name to make it friendly."}, "PROFESSIONAL": {"TITLE": "Professional", "SUBTITLE": "Use only the configured business name as the sender name in the email header."}, "BUSINESS_NAME": {"BUTTON_TEXT": "+ Configure your business name", "PLACEHOLDER": "Enter your business name", "SAVE_BUTTON_TEXT": "Save"}}, "ALLOW_MESSAGES_AFTER_RESOLVED": {"ENABLED": "Aktivert", "DISABLED": "Deaktivert"}, "ENABLE_CONTINUITY_VIA_EMAIL": {"ENABLED": "Aktivert", "DISABLED": "Deaktivert"}, "LOCK_TO_SINGLE_CONVERSATION": {"ENABLED": "Aktivert", "DISABLED": "Deaktivert"}, "ENABLE_HMAC": {"LABEL": "Enable"}}, "DELETE": {"BUTTON_TEXT": "<PERSON><PERSON>", "AVATAR_DELETE_BUTTON_TEXT": "Delete Avatar", "CONFIRM": {"TITLE": "Bekreft sletting", "MESSAGE": "<PERSON><PERSON> du sikker på at du vil slette ", "PLACE_HOLDER": "Please type {inboxName} to confirm", "YES": "J<PERSON>, s<PERSON> ", "NO": "<PERSON><PERSON>, behold "}, "API": {"SUCCESS_MESSAGE": "Innboksen ble slettet", "ERROR_MESSAGE": "Kan ikke slette innboks. Prøv igjen senere.", "AVATAR_SUCCESS_MESSAGE": "Inbox avatar deleted successfully", "AVATAR_ERROR_MESSAGE": "Could not delete the inbox avatar. Please try again later."}}, "TABS": {"SETTINGS": "Innstillinger", "COLLABORATORS": "Samarbeidspartnere", "CONFIGURATION": "Konfigurasjon", "CAMPAIGN": "Campaigns", "PRE_CHAT_FORM": "Pre Chat Form", "BUSINESS_HOURS": "Business Hours", "WIDGET_BUILDER": "Widget Builder", "BOT_CONFIGURATION": "Bot Configuration", "CSAT": "CSAT"}, "SETTINGS": "Innstillinger", "FEATURES": {"LABEL": "Funksjoner", "DISPLAY_FILE_PICKER": "Vis filvelger i widget", "DISPLAY_EMOJI_PICKER": "Vis emoji-velger i widget", "ALLOW_END_CONVERSATION": "Allow users to end conversation from the widget", "USE_INBOX_AVATAR_FOR_BOT": "Use inbox name and avatar for the bot"}, "SETTINGS_POPUP": {"MESSENGER_HEADING": "<PERSON><PERSON> av samtale", "MESSENGER_SUB_HEAD": "<PERSON><PERSON>er denne knappen innenfor body-taggen", "INBOX_AGENTS": "<PERSON><PERSON>", "INBOX_AGENTS_SUB_TEXT": "<PERSON><PERSON> til eller fjern agenter fra denne innboksen", "AGENT_ASSIGNMENT": "Conversation Assignment", "AGENT_ASSIGNMENT_SUB_TEXT": "Update conversation assignment settings", "UPDATE": "<PERSON><PERSON><PERSON><PERSON>", "ENABLE_EMAIL_COLLECT_BOX": "Enable email collect box", "ENABLE_EMAIL_COLLECT_BOX_SUB_TEXT": "Enable or disable email collect box on new conversation", "AUTO_ASSIGNMENT": "Aktiver autotilordning", "SENDER_NAME_SECTION": "Enable Agent Name in Email", "SENDER_NAME_SECTION_TEXT": "Enable/Disable showing Agent's name in email, if disabled it will show business name", "ENABLE_CONTINUITY_VIA_EMAIL": "Enable conversation continuity via email", "ENABLE_CONTINUITY_VIA_EMAIL_SUB_TEXT": "Conversations will continue over email if the contact email address is available.", "LOCK_TO_SINGLE_CONVERSATION": "Lock to single conversation", "LOCK_TO_SINGLE_CONVERSATION_SUB_TEXT": "Enable or disable multiple conversations for the same contact in this inbox", "INBOX_UPDATE_TITLE": "Innboksinnstillinger", "INBOX_UPDATE_SUB_TEXT": "<PERSON>p<PERSON><PERSON> innboksinnstillinger", "AUTO_ASSIGNMENT_SUB_TEXT": "Aktiver eller deaktiver automatisk tildeling av nye samtaler til agenter som er lagt til i denne innboksen.", "HMAC_VERIFICATION": "Brukeridentitetsvalidering", "HMAC_DESCRIPTION": "In order to validate the user's identity, you can pass an `identifier_hash` for each user. You can generate a HMAC sha256 hash using the `identifier` with the key shown here.", "HMAC_LINK_TO_DOCS": "You can read more here.", "HMAC_MANDATORY_VERIFICATION": "Enforce User Identity Validation", "HMAC_MANDATORY_DESCRIPTION": "If enabled, requests missing the `identifier_hash` will be rejected.", "INBOX_IDENTIFIER": "Inbox Identifier", "INBOX_IDENTIFIER_SUB_TEXT": "Use the `inbox_identifier` token shown here to authentication your API clients.", "FORWARD_EMAIL_TITLE": "Forward to Email", "FORWARD_EMAIL_SUB_TEXT": "Begynn å videresende e-post til følgende e-postadresse.", "ALLOW_MESSAGES_AFTER_RESOLVED": "Allow messages after conversation resolved", "ALLOW_MESSAGES_AFTER_RESOLVED_SUB_TEXT": "Allow the end-users to send messages even after the conversation is resolved.", "WHATSAPP_SECTION_SUBHEADER": "This API Key is used for the integration with the WhatsApp APIs.", "WHATSAPP_SECTION_UPDATE_SUBHEADER": "Enter the new API key to be used for the integration with the WhatsApp APIs.", "WHATSAPP_SECTION_TITLE": "API Key", "WHATSAPP_SECTION_UPDATE_TITLE": "Update API Key", "WHATSAPP_SECTION_UPDATE_PLACEHOLDER": "Enter the new API Key here", "WHATSAPP_SECTION_UPDATE_BUTTON": "<PERSON><PERSON><PERSON><PERSON>", "WHATSAPP_WEBHOOK_TITLE": "Webhook Verification Token", "WHATSAPP_WEBHOOK_SUBHEADER": "This token is used to verify the authenticity of the webhook endpoint.", "UPDATE_PRE_CHAT_FORM_SETTINGS": "Update Pre Chat Form Settings"}, "HELP_CENTER": {"LABEL": "Help Center", "PLACEHOLDER": "Select Help Center", "SELECT_PLACEHOLDER": "Select Help Center", "REMOVE": "Remove Help Center", "SUB_TEXT": "Attach a Help Center with the inbox"}, "AUTO_ASSIGNMENT": {"MAX_ASSIGNMENT_LIMIT": "Auto assignment limit", "MAX_ASSIGNMENT_LIMIT_RANGE_ERROR": "Please enter a value greater than 0", "MAX_ASSIGNMENT_LIMIT_SUB_TEXT": "Limit the maximum number of conversations from this inbox that can be auto assigned to an agent"}, "FACEBOOK_REAUTHORIZE": {"TITLE": "Reautoriser", "SUBTITLE": "Facebook-tilkoblingen din er utløpt, koble til Facebook-siden din for å fortsette tjenester", "MESSAGE_SUCCESS": "<PERSON><PERSON> tilko<PERSON> vellykket", "MESSAGE_ERROR": "Det oppstod en feil. Prøv igjen"}, "PRE_CHAT_FORM": {"DESCRIPTION": "Pre chat forms enable you to capture user information before they start conversation with you.", "SET_FIELDS": "Pre chat form fields", "SET_FIELDS_HEADER": {"FIELDS": "Fields", "LABEL": "Label", "PLACE_HOLDER": "Placeholder", "KEY": "Key", "TYPE": "Type", "REQUIRED": "Required"}, "ENABLE": {"LABEL": "Enable pre chat form", "OPTIONS": {"ENABLED": "Yes", "DISABLED": "No"}}, "PRE_CHAT_MESSAGE": {"LABEL": "Pre chat message", "PLACEHOLDER": "This message would be visible to the users along with the form"}, "REQUIRE_EMAIL": {"LABEL": "Visitors should provide their name and email address before starting the chat"}}, "CSAT": {"TITLE": "Enable CSAT", "SUBTITLE": "Automatically trigger CSAT surveys at the end of conversations to understand how customers feel about their support experience. Track satisfaction trends and identify areas for improvement over time.", "DISPLAY_TYPE": {"LABEL": "Display type"}, "MESSAGE": {"LABEL": "Melding", "PLACEHOLDER": "Please enter a message to show users with the form"}, "SURVEY_RULE": {"LABEL": "Survey rule", "DESCRIPTION_PREFIX": "Send the survey if the conversation", "DESCRIPTION_SUFFIX": "any of the labels", "OPERATOR": {"CONTAINS": "contains", "DOES_NOT_CONTAINS": "does not contain"}, "SELECT_PLACEHOLDER": "select labels"}, "NOTE": "Note: CSAT surveys are sent only once per conversation", "API": {"SUCCESS_MESSAGE": "CSAT settings updated successfully", "ERROR_MESSAGE": "We couldn't update CSAT settings. Please try again later."}}, "BUSINESS_HOURS": {"TITLE": "Set your availability", "SUBTITLE": "Set your availability on your livechat widget", "WEEKLY_TITLE": "Set your weekly hours", "TIMEZONE_LABEL": "Select timezone", "UPDATE": "Update business hours settings", "TOGGLE_AVAILABILITY": "Enable business availability for this inbox", "UNAVAILABLE_MESSAGE_LABEL": "Unavailable message for visitors", "TOGGLE_HELP": "Enabling business availability will show the available hours on live chat widget even if all the agents are offline. Outside available hours visitors can be warned with a message and a pre-chat form.", "DAY": {"ENABLE": "Enable availability for this day", "UNAVAILABLE": "Unavailable", "HOURS": "hours", "VALIDATION_ERROR": "Starting time should be before closing time.", "CHOOSE": "<PERSON><PERSON>"}, "ALL_DAY": "All-Day"}, "IMAP": {"TITLE": "IMAP", "SUBTITLE": "Set your IMAP details", "NOTE_TEXT": "To enable SMTP, please configure IMAP.", "UPDATE": "Update IMAP settings", "TOGGLE_AVAILABILITY": "Enable IMAP configuration for this inbox", "TOGGLE_HELP": "Enabling IMAP will help the user to receive email", "EDIT": {"SUCCESS_MESSAGE": "IMAP settings updated successfully", "ERROR_MESSAGE": "Unable to update IMAP settings"}, "ADDRESS": {"LABEL": "Address", "PLACE_HOLDER": "Address (Eg: imap.gmail.com)"}, "PORT": {"LABEL": "Port", "PLACE_HOLDER": "Port"}, "LOGIN": {"LABEL": "Logg inn", "PLACE_HOLDER": "Logg inn"}, "PASSWORD": {"LABEL": "Passord", "PLACE_HOLDER": "Passord"}, "ENABLE_SSL": "Enable SSL"}, "MICROSOFT": {"TITLE": "Microsoft", "SUBTITLE": "Reauthorize your MICROSOFT account"}, "SMTP": {"TITLE": "SMTP", "SUBTITLE": "Set your SMTP details", "UPDATE": "Update SMTP settings", "TOGGLE_AVAILABILITY": "Enable SMTP configuration for this inbox", "TOGGLE_HELP": "Enabling SMTP will help the user to send email", "EDIT": {"SUCCESS_MESSAGE": "SMTP settings updated successfully", "ERROR_MESSAGE": "Unable to update SMTP settings"}, "ADDRESS": {"LABEL": "Address", "PLACE_HOLDER": "Address (Eg: smtp.gmail.com)"}, "PORT": {"LABEL": "Port", "PLACE_HOLDER": "Port"}, "LOGIN": {"LABEL": "Logg inn", "PLACE_HOLDER": "Logg inn"}, "PASSWORD": {"LABEL": "Passord", "PLACE_HOLDER": "Passord"}, "DOMAIN": {"LABEL": "Domain", "PLACE_HOLDER": "Domain"}, "ENCRYPTION": "Encryption", "SSL_TLS": "SSL/TLS", "START_TLS": "STARTTLS", "OPEN_SSL_VERIFY_MODE": "Open SSL Verify Mode", "AUTH_MECHANISM": "Authentication"}, "NOTE": "Note: ", "WIDGET_BUILDER": {"WIDGET_OPTIONS": {"AVATAR": {"LABEL": "Website Avatar", "DELETE": {"API": {"SUCCESS_MESSAGE": "Avatar deleted successfully", "ERROR_MESSAGE": "Det oppstod en feil. Prøv igjen"}}}, "WEBSITE_NAME": {"LABEL": "Nettstedsnavn", "PLACE_HOLDER": "Angi navnet på nettsiden (f. eks.: Acme Inc)", "ERROR": "Please enter a valid website name"}, "WELCOME_HEADING": {"LABEL": "Velkomsttittel", "PLACE_HOLDER": "Hi there!"}, "WELCOME_TAGLINE": {"LABEL": "Velkomsthilsen undertittel", "PLACE_HOLDER": "Det skal være lett å kontakte oss. Spør oss om hva som helst, eller del din tilbakemelding."}, "REPLY_TIME": {"LABEL": "Reply Time", "IN_A_FEW_MINUTES": "Om noen få minutter", "IN_A_FEW_HOURS": "Om noen få timer", "IN_A_DAY": "Om en dag"}, "WIDGET_COLOR_LABEL": "Widget farge", "WIDGET_BUBBLE_POSITION_LABEL": "Widget Bubble Position", "WIDGET_BUBBLE_TYPE_LABEL": "Widget Bubble Type", "WIDGET_BUBBLE_LAUNCHER_TITLE": {"DEFAULT": "Chat med oss", "LABEL": "Widget Bubble Launcher Title", "PLACE_HOLDER": "Chat med oss"}, "UPDATE": {"BUTTON_TEXT": "Update Widget Settings", "API": {"SUCCESS_MESSAGE": "Widget settings updated successfully", "ERROR_MESSAGE": "Unable to update widget settings"}}, "WIDGET_VIEW_OPTION": {"PREVIEW": "Preview", "SCRIPT": "<PERSON><PERSON><PERSON>"}, "WIDGET_BUBBLE_POSITION": {"LEFT": "Left", "RIGHT": "Right"}, "WIDGET_BUBBLE_TYPE": {"STANDARD": "Standard", "EXPANDED_BUBBLE": "Expanded Bubble"}}, "WIDGET_SCREEN": {"DEFAULT": "<PERSON><PERSON><PERSON>", "CHAT": "Cha<PERSON>"}, "REPLY_TIME": {"IN_A_FEW_MINUTES": "<PERSON><PERSON><PERSON> innen et par timer", "IN_A_FEW_HOURS": "<PERSON><PERSON><PERSON> innen et par timer", "IN_A_DAY": "<PERSON><PERSON><PERSON> innen en dag"}, "FOOTER": {"START_CONVERSATION_BUTTON_TEXT": "Start samtale", "CHAT_INPUT_PLACEHOLDER": "Skriv inn meldingen"}, "BODY": {"TEAM_AVAILABILITY": {"ONLINE": "We are Online", "OFFLINE": "Vi er for øyeblikket borte"}, "USER_MESSAGE": "Hi", "AGENT_MESSAGE": "Hello"}, "BRANDING_TEXT": "Drevet av Chatwoot", "SCRIPT_SETTINGS": "\n      window.chatwootSettings = {options};"}, "EMAIL_PROVIDERS": {"MICROSOFT": "Microsoft", "GOOGLE": "Google", "OTHER_PROVIDERS": "Other Providers"}, "CHANNELS": {"MESSENGER": "<PERSON>", "WEB_WIDGET": "Website", "TWITTER_PROFILE": "Twitter", "TWILIO_SMS": "<PERSON><PERSON><PERSON>", "WHATSAPP": "WhatsApp", "SMS": "SMS", "EMAIL": "E-post", "TELEGRAM": "Telegram", "LINE": "Line", "API": "API Kanal", "INSTAGRAM": "Instagram"}}}