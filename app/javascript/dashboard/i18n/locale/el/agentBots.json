{"AGENT_BOTS": {"HEADER": "<PERSON><PERSON>", "LOADING_EDITOR": "Loading editor...", "DESCRIPTION": "Agent <PERSON><PERSON> are like the most fabulous members of your team. They can handle the small stuff, so you can focus on the stuff that matters. Give them a try. You can manage your bots from this page or create new ones using the 'Add Bot' button.", "LEARN_MORE": "Learn about agent bots", "GLOBAL_BOT": "System bot", "GLOBAL_BOT_BADGE": "System", "AVATAR": {"SUCCESS_DELETE": "<PERSON><PERSON> avatar deleted successfully", "ERROR_DELETE": "Error deleting bot avatar, please try again"}, "BOT_CONFIGURATION": {"TITLE": "Επιλέξτε ενός Agent <PERSON>", "DESC": "Assign an Agent Bot to your inbox. They can handle initial conversations and transfer them to a live agent when necessary.", "SUBMIT": "Ενημέρωση", "DISCONNECT": "Disconnect bot", "SUCCESS_MESSAGE": "Επιτυχής ενημέρωση του agent bot.", "DISCONNECTED_SUCCESS_MESSAGE": "Successfully disconnected the agent bot.", "ERROR_MESSAGE": "Could not update the agent bot. Please try again.", "DISCONNECTED_ERROR_MESSAGE": "Could not disconnect the agent bot. Please try again.", "SELECT_PLACEHOLDER": "Select bot"}, "ADD": {"TITLE": "<PERSON><PERSON>", "CANCEL_BUTTON_TEXT": "Άκυρο", "API": {"SUCCESS_MESSAGE": "Το bot προστέθηκε επιτυχώς.", "ERROR_MESSAGE": "Could not add bot. Please try again later."}}, "LIST": {"404": "No bots found. You can create a bot by clicking the 'Add Bot' button.", "LOADING": "Fetching bots...", "TABLE_HEADER": {"DETAILS": "Bot Details", "URL": "Σύνδε<PERSON><PERSON><PERSON> Webhook"}}, "DELETE": {"BUTTON_TEXT": "Διαγραφή", "TITLE": "Delete bot", "CONFIRM": {"TITLE": "Επιβεβαίωση Διαγραφής", "MESSAGE": "Are you sure you want to delete {name}?", "YES": "Ναι, Διέγραψε το", "NO": "Όχι, Διατήρηση"}, "API": {"SUCCESS_MESSAGE": "Το bot διαγράφηκε επιτυχώς.", "ERROR_MESSAGE": "Could not delete bot. Please try again."}}, "EDIT": {"BUTTON_TEXT": "Επεξεργασία", "TITLE": "Edit bot", "API": {"SUCCESS_MESSAGE": "Το bot ενημερώθηκε επιτυχώς.", "ERROR_MESSAGE": "Could not update bot. Please try again."}}, "FORM": {"AVATAR": {"LABEL": "<PERSON><PERSON> avatar"}, "NAME": {"LABEL": "Bot name", "PLACEHOLDER": "Enter bot name", "REQUIRED": "Το Όνομα Bot είναι απαραίτητο"}, "DESCRIPTION": {"LABEL": "Περιγραφή", "PLACEHOLDER": "Τι κάνει αυτό το bot?"}, "WEBHOOK_URL": {"LABEL": "Σύνδε<PERSON><PERSON><PERSON> Webhook", "PLACEHOLDER": "https://example.com/webhook", "REQUIRED": "Webhook URL is required"}, "ERRORS": {"NAME": "Το Όνομα Bot είναι απαραίτητο", "URL": "Webhook URL is required", "VALID_URL": "Please enter a valid URL starting with http:// or https://"}, "CANCEL": "Άκυρο", "CREATE": "Create <PERSON><PERSON>", "UPDATE": "Update Bot"}, "WEBHOOK": {"DESCRIPTION": "Configure a webhook bot to integrate with your custom services. The bot will receive and process events from conversations and can respond to them."}, "TYPES": {"WEBHOOK": "Webhook bot"}}}