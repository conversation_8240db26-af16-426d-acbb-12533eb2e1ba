{"CONTACTS_FILTER": {"TITLE": "Φίλ<PERSON>ρ<PERSON>φ<PERSON>", "SUBTITLE": "Προσθέστε φίλτρα παρακάτω και πατήστε 'Υποβολή' για να φιλτράρετε τις επαφές.", "EDIT_CUSTOM_SEGMENT": "Edit Segment", "CUSTOM_VIEWS_SUBTITLE": "Add or remove filters and update your segment.", "ADD_NEW_FILTER": "Προσθήκη Φίλτρου", "CLEAR_ALL_FILTERS": "Εκκαθάριση Όλων Των Φίλτρων", "FILTER_DELETE_ERROR": "Θα πρέπει να έχετε τουλάχιστον ένα φίλτρο για την αποθήκευση", "SUBMIT_BUTTON_LABEL": "Καταχώρηση", "UPDATE_BUTTON_LABEL": "Update Segment", "CANCEL_BUTTON_LABEL": "Άκυρο", "CLEAR_BUTTON_LABEL": "Εκκαθάριση Φίλτρων", "EMPTY_VALUE_ERROR": "Απαιτείται τιμή", "SEGMENT_LABEL": "Segment Name", "SEGMENT_QUERY_LABEL": "Segment Query", "TOOLTIP_LABEL": "Φιλτράρισμα επαφών", "QUERY_DROPDOWN_LABELS": {"AND": "AND", "OR": "OR"}, "OPERATOR_LABELS": {"equal_to": "Ίσο με", "not_equal_to": "Όχι ίσο με", "contains": "Περιέχει", "does_not_contain": "Δεν περιέχει", "is_present": "Υπάρχει", "is_not_present": "Δεν υπάρχει", "is_greater_than": "Είναι μεγαλύτερο από", "is_lesser_than": "Είναι μικρότερο από", "days_before": "Είναι x ημέρες πριν"}, "ERRORS": {"VALUE_REQUIRED": "Απαιτείται τιμή"}, "ATTRIBUTES": {"NAME": "Όνομα", "EMAIL": "Email", "PHONE_NUMBER": "Αριθμός τηλεφώνου", "IDENTIFIER": "Κω<PERSON>ικ<PERSON>ς", "CITY": "Π<PERSON><PERSON><PERSON>", "COUNTRY": "Χώ<PERSON><PERSON>", "CUSTOM_ATTRIBUTE_LIST": "Λίστα", "CUSTOM_ATTRIBUTE_TEXT": "Κείμενο", "CUSTOM_ATTRIBUTE_NUMBER": "Αριθμός", "CUSTOM_ATTRIBUTE_LINK": "Σύνδεσμος", "CUSTOM_ATTRIBUTE_CHECKBOX": "Checkbox", "CREATED_AT": "Δημιουργήθηκε στις", "LAST_ACTIVITY": "Τελευτα<PERSON>α Δραστηριότητα", "REFERER_LINK": "Σύνδεσ<PERSON>ος αναφορ<PERSON>ς", "BLOCKED": "Blocked"}, "GROUPS": {"STANDARD_FILTERS": "Τυπικά Φίλτρα", "ADDITIONAL_FILTERS": "Πρόσθετα Φίλτρα", "CUSTOM_ATTRIBUTES": "Προσα<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ες Ιδιότητες"}}}