{"FILTER": {"TITLE": "Tapiskan perbualan", "SUBTITLE": "Add your filters below and hit 'Apply filters' to cut through the chat clutter.", "EDIT_CUSTOM_FILTER": "Edit <PERSON>", "CUSTOM_VIEWS_SUBTITLE": "Add or remove filters and update your folder.", "ADD_NEW_FILTER": "Add filter", "FILTER_DELETE_ERROR": "Oops, looks like we can't save nothing! Please add at least one filter to save it.", "SUBMIT_BUTTON_LABEL": "Tetapkan tapisan", "UPDATE_BUTTON_LABEL": "Update folder", "CANCEL_BUTTON_LABEL": "Batalkan", "CLEAR_BUTTON_LABEL": "Clear filters", "FOLDER_LABEL": "Folder Name", "FOLDER_QUERY_LABEL": "Folder Query", "EMPTY_VALUE_ERROR": "<PERSON><PERSON>.", "TOOLTIP_LABEL": "Tapiskan perbualan", "QUERY_DROPDOWN_LABELS": {"AND": "DAN", "OR": "ATAU"}, "INPUT_PLACEHOLDER": "Enter value", "OPERATOR_LABELS": {"equal_to": "<PERSON><PERSON> den<PERSON>", "not_equal_to": "Tak sama dengan", "does_not_contain": "Tidak mengandungi", "is_present": "Sedia ada", "is_not_present": "Tidak sedia ada", "is_greater_than": "<PERSON><PERSON> le<PERSON> besar", "is_less_than": "<PERSON><PERSON> lebih kurang", "days_before": "<PERSON><PERSON> x hari sebelum", "starts_with": "Starts with", "equalTo": "<PERSON><PERSON> den<PERSON>", "notEqualTo": "Tak sama dengan", "contains": "Mengandungi", "doesNotContain": "Tidak mengandungi", "isPresent": "Sedia ada", "isNotPresent": "Tidak sedia ada", "isGreaterThan": "<PERSON><PERSON> le<PERSON> besar", "isLessThan": "<PERSON><PERSON> lebih kurang", "daysBefore": "<PERSON><PERSON> x hari sebelum", "startsWith": "Starts with"}, "ATTRIBUTE_LABELS": {"TRUE": "<PERSON><PERSON>", "FALSE": "T<PERSON>k <PERSON>ar"}, "ATTRIBUTES": {"STATUS": "Status", "ASSIGNEE_NAME": "Assignee name", "INBOX_NAME": "Inbox name", "TEAM_NAME": "Team name", "CONVERSATION_IDENTIFIER": "Conversation identifier", "CAMPAIGN_NAME": "Campaign name", "LABELS": "Label", "BROWSER_LANGUAGE": "Browser language", "PRIORITY": "Priority", "COUNTRY_NAME": "Country name", "REFERER_LINK": "<PERSON><PERSON><PERSON> p<PERSON>an", "CUSTOM_ATTRIBUTE_LIST": "<PERSON><PERSON><PERSON>", "CUSTOM_ATTRIBUTE_TEXT": "Teks", "CUSTOM_ATTRIBUTE_NUMBER": "Nombor", "CUSTOM_ATTRIBUTE_LINK": "<PERSON><PERSON><PERSON>", "CUSTOM_ATTRIBUTE_CHECKBOX": "Kotak Semak", "CREATED_AT": "Created at", "LAST_ACTIVITY": "Last activity"}, "ERRORS": {"VALUE_REQUIRED": "<PERSON><PERSON>", "ATTRIBUTE_KEY_REQUIRED": "Attribute key is required", "FILTER_OPERATOR_REQUIRED": "Filter operator is required", "VALUE_MUST_BE_BETWEEN_1_AND_998": "Value must be between 1 and 998"}, "GROUPS": {"STANDARD_FILTERS": "Standard filters", "ADDITIONAL_FILTERS": "Additional filters", "CUSTOM_ATTRIBUTES": "Custom attributes"}, "CUSTOM_VIEWS": {"ADD": {"TITLE": "<PERSON><PERSON>h kamu ingin menetapkan penapis ini?", "LABEL": "<PERSON><PERSON><PERSON> penapis ini", "PLACEHOLDER": "Name your filter to refer it later.", "ERROR_MESSAGE": "<PERSON><PERSON>.", "SAVE_BUTTON": "Tetapkan penapis", "CANCEL_BUTTON": "<PERSON><PERSON>", "API_FOLDERS": {"SUCCESS_MESSAGE": "Folder telah ditetapkan.", "ERROR_MESSAGE": "Ralat apabilia menetapkan folder ini."}, "API_SEGMENTS": {"SUCCESS_MESSAGE": "Segmen telah berjaya dite<PERSON>.", "ERROR_MESSAGE": "Segmen tidak berjaya dite<PERSON>."}}, "EDIT": {"EDIT_BUTTON": "Edit folder"}, "DELETE": {"DELETE_BUTTON": "Padam tapisan", "MODAL": {"CONFIRM": {"TITLE": "Confirm deletion", "MESSAGE": "<PERSON><PERSON><PERSON> anda pasti hendak padamkan tapisan ini ", "YES": "Yes, delete", "NO": "No, keep it"}}, "API_FOLDERS": {"SUCCESS_MESSAGE": "Folder ini sudah dipadamkan.", "ERROR_MESSAGE": "<PERSON><PERSON> apabila padamkan folder ini."}, "API_SEGMENTS": {"SUCCESS_MESSAGE": "Segmen berjaya dipadam.", "ERROR_MESSAGE": "<PERSON><PERSON> apabilia memadamkan segmen."}}}}}