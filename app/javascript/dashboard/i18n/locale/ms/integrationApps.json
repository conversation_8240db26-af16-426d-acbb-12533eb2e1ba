{"INTEGRATION_APPS": {"FETCHING": "Fetching Integrations", "NO_HOOK_CONFIGURED": "There are no {integrationId} integrations configured in this account.", "HEADER": "Applications", "STATUS": {"ENABLED": "Enabled", "DISABLED": "Disabled"}, "CONFIGURE": "Configure", "ADD_BUTTON": "Add a new hook", "DELETE": {"TITLE": {"INBOX": "Confirm deletion", "ACCOUNT": "Disconnect"}, "MESSAGE": {"INBOX": "Are you sure to delete?", "ACCOUNT": "Are you sure to disconnect?"}, "CONFIRM_BUTTON_TEXT": {"INBOX": "Yes, Delete", "ACCOUNT": "Yes, Disconnect"}, "CANCEL_BUTTON_TEXT": "Batalkan", "API": {"SUCCESS_MESSAGE": "Hook deleted successfully", "ERROR_MESSAGE": "Ma<PERSON>ah untuk hubungi Woot Server, <PERSON>la cuba sebentar lagi"}}, "LIST": {"FETCHING": "Fetching integration hooks", "INBOX": "Inbox", "DELETE": {"BUTTON_TEXT": "Padamkan"}}, "ADD": {"FORM": {"INBOX": {"LABEL": "Select Inbox", "PLACEHOLDER": "Select Inbox"}, "SUBMIT": "Create", "CANCEL": "Batalkan"}, "API": {"SUCCESS_MESSAGE": "Integration hook added successfully", "ERROR_MESSAGE": "Ma<PERSON>ah untuk hubungi Woot Server, <PERSON>la cuba sebentar lagi"}}, "CONNECT": {"BUTTON_TEXT": "Connect"}, "DISCONNECT": {"BUTTON_TEXT": "Disconnect"}, "SIDEBAR_DESCRIPTION": {"DIALOGFLOW": "Dialogflow is a natural language processing platform for building conversational interfaces. Integrating it with {installationName} lets bots handle queries first and transfer them to agents when needed. It helps qualify leads and reduce agent workload by answering FAQs. To add Dialogflow, create a Service Account in Google Console and share the credentials. Refer to the docs for details"}}}