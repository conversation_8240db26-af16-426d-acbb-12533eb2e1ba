{"MACROS": {"HEADER": "宏", "DESCRIPTION": "宏是一组预设操作，帮助客服人员快速完成任务。客服人员可以定义诸如标记对话、发送邮件记录、更新自定义属性等操作，并一键执行。", "LEARN_MORE": "了解更多关于宏的信息", "HEADER_BTN_TXT": "添加一个新宏文件", "HEADER_BTN_TXT_SAVE": "保存宏文件", "LOADING": "获取宏中", "ERROR": "出错了。请重试", "ORDER_INFO": "宏将按照您添加操作的顺序运行。您可以通过拖动每个节点旁边的手柄来重新排列它们。", "ADD": {"FORM": {"NAME": {"LABEL": "宏名称", "PLACEHOLDER": "为您的宏输入名称", "ERROR": "创建宏需要名称"}, "ACTIONS": {"LABEL": "操作"}}, "API": {"SUCCESS_MESSAGE": "宏添加成功", "ERROR_MESSAGE": "无法创建宏，请稍后再试。"}}, "LIST": {"TABLE_HEADER": {"NAME": "姓名：", "CREATED BY": "创建者", "LAST_UPDATED_BY": "最后更新者", "VISIBILITY": "可视性"}, "404": "未找到宏"}, "DELETE": {"TOOLTIP": "删除宏文件", "CONFIRM": {"MESSAGE": "您确定要删除吗？ ", "YES": "是，删除", "NO": "否"}, "API": {"SUCCESS_MESSAGE": "宏添加成功", "ERROR_MESSAGE": "删除宏时出错。请稍后再试"}}, "EDIT": {"TOOLTIP": "编辑宏文件", "API": {"SUCCESS_MESSAGE": "宏观已成功更新", "ERROR_MESSAGE": "无法更新宏，请稍后再试"}}, "EDITOR": {"START_FLOW": "启动流", "END_FLOW": "结束流", "LOADING": "正在获取宏文件", "ADD_BTN_TOOLTIP": "添加新操作", "DELETE_BTN_TOOLTIP": "删除", "VISIBILITY": {"LABEL": "宏可见性", "GLOBAL": {"LABEL": "公开的", "DESCRIPTION": "此宏对此帐户中的所有代理公开可用"}, "PERSONAL": {"LABEL": "私人的", "DESCRIPTION": "这个宏将私有化，其他人无法使用。"}}}, "EXECUTE": {"BUTTON_TOOLTIP": "执行", "PREVIEW": "预览宏...", "EXECUTED_SUCCESSFULLY": "宏成功执行"}, "ERRORS": {"ATTRIBUTE_KEY_REQUIRED": "属性键是必需的", "FILTER_OPERATOR_REQUIRED": "需要过滤器操作符", "VALUE_REQUIRED": "必须填写值", "VALUE_MUST_BE_BETWEEN_1_AND_998": "值必须介于 1 到 998 之间。", "ACTION_PARAMETERS_REQUIRED": "需要操作参数", "ATLEAST_ONE_CONDITION_REQUIRED": "至少需要一个条件", "ATLEAST_ONE_ACTION_REQUIRED": "至少需要一个动作"}, "ACTIONS": {"ASSIGN_TEAM": "Assign a Team", "ASSIGN_AGENT": "Assign an Agent", "ADD_LABEL": "Add a Label", "REMOVE_LABEL": "Remove a Label", "REMOVE_ASSIGNED_TEAM": "Remove Assigned Team", "SEND_EMAIL_TRANSCRIPT": "Send an Email Transcript", "MUTE_CONVERSATION": "开始会话", "SNOOZE_CONVERSATION": "暂停对话", "RESOLVE_CONVERSATION": "解决对话", "SEND_ATTACHMENT": "Send Attachment", "SEND_MESSAGE": "Send a Message", "CHANGE_PRIORITY": "更改优先级", "ADD_PRIVATE_NOTE": "Add a Private Note", "SEND_WEBHOOK_EVENT": "Send Webhook Event"}}}