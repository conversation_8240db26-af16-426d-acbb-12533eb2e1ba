{"ATTRIBUTES_MGMT": {"HEADER": "自定义属性", "HEADER_BTN_TXT": "添加自定义属性", "LOADING": "正在获取自定义属性", "DESCRIPTION": "自定义属性可跟踪有关您的联系人或对话的其他详细信息——例如订阅计划或首次购买日期。您可以添加不同类型的自定义属性，如文本、列表或数字，以捕获您需要的特定信息。", "LEARN_MORE": "自定义属性说明", "ADD": {"TITLE": "添加自定义属性", "SUBMIT": "创建", "CANCEL_BUTTON_TEXT": "取消", "FORM": {"NAME": {"LABEL": "展示名", "PLACEHOLDER": "输入自定义属性显示名称", "ERROR": "名字为必填项"}, "DESC": {"LABEL": "描述信息", "PLACEHOLDER": "输入自定义属性描述", "ERROR": "描述是必需的"}, "MODEL": {"LABEL": "应用到", "PLACEHOLDER": "请选择一个", "ERROR": "模型是必需的"}, "TYPE": {"LABEL": "类型", "PLACEHOLDER": "请选择一个类型", "ERROR": "类型是必需的", "LIST": {"LABEL": "列表值", "PLACEHOLDER": "请输入值并按回车键", "ERROR": "必须至少有一个值"}}, "KEY": {"LABEL": "键", "PLACEHOLDER": "输入自定义属性键", "ERROR": "键是必需的", "IN_VALID": "无效键"}, "REGEX_PATTERN": {"LABEL": "正则表达式模式", "PLACEHOLDER": "请输入自定义属性正则表达式模式。(可选)"}, "REGEX_CUE": {"LABEL": "正则表达式提示", "PLACEHOLDER": "请输入正则表达式模式提示。（可选）"}, "ENABLE_REGEX": {"LABEL": "启用正则表达式验证"}}, "API": {"SUCCESS_MESSAGE": "自定义属性添加成功!", "ERROR_MESSAGE": "无法创建自定义属性，请稍后再试。"}}, "DELETE": {"BUTTON_TEXT": "删除", "API": {"SUCCESS_MESSAGE": "自定义属性删除成功", "ERROR_MESSAGE": "无法删除自定义属性。请重试。"}, "CONFIRM": {"TITLE": "您确定要删除 - {attributeName}", "PLACE_HOLDER": "请输入 {attributeName} 以确认", "MESSAGE": "删除将删除自定义属性", "YES": "删除 ", "NO": "取消"}}, "EDIT": {"TITLE": "编辑自定义属性", "UPDATE_BUTTON_TEXT": "更新", "TYPE": {"LIST": {"LABEL": "列表值", "PLACEHOLDER": "请输入值并按输入键"}}, "API": {"SUCCESS_MESSAGE": "自定义属性更新成功", "ERROR_MESSAGE": "更新自定义属性时出错，请重试"}}, "TABS": {"HEADER": "自定义属性", "CONVERSATION": "会话", "CONTACT": "联系人"}, "LIST": {"TABLE_HEADER": {"NAME": "名称", "DESCRIPTION": "描述信息", "TYPE": "类型", "KEY": "键"}, "BUTTONS": {"EDIT": "编辑", "DELETE": "删除"}, "EMPTY_RESULT": {"404": "没有创建自定义属性", "NOT_FOUND": "没有配置自定义属性"}, "REGEX_PATTERN": {"LABEL": "正则表达式模式", "PLACEHOLDER": "请输入自定义属性的正则表达式模式。（可选）"}, "REGEX_CUE": {"LABEL": "正则表达式提示", "PLACEHOLDER": "请输入正则表达式提示。(可选)"}, "ENABLE_REGEX": {"LABEL": "启用正则表达式验证"}}}}