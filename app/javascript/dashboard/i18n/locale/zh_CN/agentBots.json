{"AGENT_BOTS": {"HEADER": "机器人", "LOADING_EDITOR": "正在加载编辑器...", "DESCRIPTION": "Agent <PERSON><PERSON> are like the most fabulous members of your team. They can handle the small stuff, so you can focus on the stuff that matters. Give them a try. You can manage your bots from this page or create new ones using the 'Add Bot' button.", "LEARN_MORE": "了解客服机器人", "GLOBAL_BOT": "System bot", "GLOBAL_BOT_BADGE": "系统", "AVATAR": {"SUCCESS_DELETE": "<PERSON><PERSON> avatar deleted successfully", "ERROR_DELETE": "Error deleting bot avatar, please try again"}, "BOT_CONFIGURATION": {"TITLE": "选择一个客服机器人", "DESC": "将代理机器人分配到您的收件箱。他们可以处理初始对话，并在必要时将其转移给真人客服。", "SUBMIT": "更新", "DISCONNECT": "断开机器人", "SUCCESS_MESSAGE": "成功更新代理机器人。", "DISCONNECTED_SUCCESS_MESSAGE": "成功断开代理机器人。", "ERROR_MESSAGE": "无法更新代理机器人。请再试一次。", "DISCONNECTED_ERROR_MESSAGE": "无法断开客服机器人。请再试一次。", "SELECT_PLACEHOLDER": "选择机器人"}, "ADD": {"TITLE": "<PERSON><PERSON>", "CANCEL_BUTTON_TEXT": "取消", "API": {"SUCCESS_MESSAGE": "机器人添加成功.", "ERROR_MESSAGE": "无法添加机器人。请稍后再试。"}}, "LIST": {"404": "No bots found. You can create a bot by clicking the 'Add Bot' button.", "LOADING": "正在获取机器人...", "TABLE_HEADER": {"DETAILS": "Bot Details", "URL": "Webhook 网址"}}, "DELETE": {"BUTTON_TEXT": "删除", "TITLE": "删除机器人", "CONFIRM": {"TITLE": "确认删除", "MESSAGE": "Are you sure you want to delete {name}?", "YES": "是，删除", "NO": "不，保留"}, "API": {"SUCCESS_MESSAGE": "成功删除机器人.", "ERROR_MESSAGE": "无法删除机器人。请再试一次。"}}, "EDIT": {"BUTTON_TEXT": "编辑", "TITLE": "编辑机器人", "API": {"SUCCESS_MESSAGE": "机器人更新成功.", "ERROR_MESSAGE": "无法更新机器人。请再试一次。"}}, "FORM": {"AVATAR": {"LABEL": "<PERSON><PERSON> avatar"}, "NAME": {"LABEL": "机器人名称", "PLACEHOLDER": "Enter bot name", "REQUIRED": "Bo<PERSON> name is required"}, "DESCRIPTION": {"LABEL": "描述信息", "PLACEHOLDER": "这个机器人的用途是？"}, "WEBHOOK_URL": {"LABEL": "Webhook 网址", "PLACEHOLDER": "https://example.com/webhook", "REQUIRED": "Webhook URL is required"}, "ERRORS": {"NAME": "Bo<PERSON> name is required", "URL": "Webhook URL is required", "VALID_URL": "Please enter a valid URL starting with http:// or https://"}, "CANCEL": "取消", "CREATE": "Create <PERSON><PERSON>", "UPDATE": "Update Bot"}, "WEBHOOK": {"DESCRIPTION": "Configure a webhook bot to integrate with your custom services. The bot will receive and process events from conversations and can respond to them."}, "TYPES": {"WEBHOOK": "Webhook 机器人"}}}