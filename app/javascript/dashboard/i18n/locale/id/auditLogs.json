{"AUDIT_LOGS": {"HEADER": "Catatan Audit", "HEADER_BTN_TXT": "Tambahkan Catatan Audit", "LOADING": "Mengambil Catatan Audit", "DESCRIPTION": "Audit Logs maintain a record of activities in your account, allowing you to track and audit your account, team, or services.", "LEARN_MORE": "Learn more about audit logs", "SEARCH_404": "Tidak ada item yang cocok dengan kueri ini", "SIDEBAR_TXT": "<p><b>Catatan Audit</b> </p><p> Catatan Audit adalah jejak untuk peristiwa dan tindakan dalam Sistem Chatwoot. </p>", "LIST": {"404": "Tidak ada Catatan Audit yang tersedia di akun ini.", "TITLE": "Kelola Catatan Audit", "DESC": "Catatan Audit adalah jejak untuk peristiwa dan tindakan dalam Sistem Chatwoot.", "TABLE_HEADER": {"ACTIVITY": "Pengguna", "TIME": "<PERSON><PERSON><PERSON>", "IP_ADDRESS": "Alamat IP"}}, "API": {"SUCCESS_MESSAGE": "Catatan Audit berhasil diambil", "ERROR_MESSAGE": "Tidak dapat terhubung ke Server Woot, <PERSON><PERSON><PERSON> coba lagi nanti"}, "DEFAULT_USER": "Sistem", "AUTOMATION_RULE": {"ADD": "{agentName} created a new automation rule (#{id})", "EDIT": "{agentName} updated an automation rule (#{id})", "DELETE": "{agent<PERSON>ame} deleted an automation rule (#{id})"}, "ACCOUNT_USER": {"ADD": "{agent<PERSON><PERSON>} mengundang {invitee} ke akun sebagai {role}", "EDIT": {"SELF": "{agentName} mengu<PERSON> {attributes} mereka menjadi {values}", "OTHER": "{agentName} mengu<PERSON> {attributes} dari {user} menjadi {values}", "DELETED": "{agent<PERSON><PERSON>} changed {attributes} of a deleted user to {values}"}}, "INBOX": {"ADD": "{agentName} created a new inbox (#{id})", "EDIT": "{agentName} updated an inbox (#{id})", "DELETE": "{agent<PERSON>ame} deleted an inbox (#{id})"}, "WEBHOOK": {"ADD": "{agent<PERSON>ame} created a new webhook (#{id})", "EDIT": "{agent<PERSON>ame} updated a webhook (#{id})", "DELETE": "{agent<PERSON><PERSON>} deleted a webhook (#{id})"}, "USER_ACTION": {"SIGN_IN": "{agent<PERSON><PERSON>} masuk", "SIGN_OUT": "{agent<PERSON><PERSON>} keluar"}, "TEAM": {"ADD": "{<PERSON><PERSON><PERSON>} created a new team (#{id})", "EDIT": "{<PERSON><PERSON><PERSON>} updated a team (#{id})", "DELETE": "{<PERSON><PERSON><PERSON>} deleted a team (#{id})"}, "MACRO": {"ADD": "{agent<PERSON>ame} created a new macro (#{id})", "EDIT": "{agent<PERSON>ame} updated a macro (#{id})", "DELETE": "{agent<PERSON>ame} deleted a macro (#{id})"}, "INBOX_MEMBER": {"ADD": "{agent<PERSON>ame} added {user} to the inbox(#{inbox_id})", "REMOVE": "{<PERSON><PERSON><PERSON>} removed {user} from the inbox(#{inbox_id})"}, "TEAM_MEMBER": {"ADD": "{<PERSON><PERSON><PERSON>} added {user} to the team(#{team_id})", "REMOVE": "{<PERSON><PERSON><PERSON>} removed {user} from the team(#{team_id})"}, "ACCOUNT": {"EDIT": "{<PERSON><PERSON><PERSON>} updated the account configuration (#{id})"}}}