{"REPORT": {"HEADER": "Percaka<PERSON>", "LOADING_CHART": "Memuat data grafik...", "NO_ENOUGH_DATA": "<PERSON><PERSON> belum menerima cukup data untuk membuat laporan, <PERSON><PERSON><PERSON> coba lagi nanti.", "DOWNLOAD_AGENT_REPORTS": "<PERSON>du<PERSON> laporan agen", "DATA_FETCHING_FAILED": "Gagal mengambil data, silakan coba lagi nanti.", "SUMMARY_FETCHING_FAILED": "<PERSON><PERSON> men<PERSON>, silakan coba lagi nanti.", "METRICS": {"CONVERSATIONS": {"NAME": "Percaka<PERSON>", "DESC": "( Total )"}, "INCOMING_MESSAGES": {"NAME": "<PERSON><PERSON>", "DESC": "( Total )"}, "OUTGOING_MESSAGES": {"NAME": "<PERSON><PERSON>", "DESC": "( Total )"}, "FIRST_RESPONSE_TIME": {"NAME": "<PERSON><PERSON><PERSON>", "DESC": "( <PERSON><PERSON><PERSON><PERSON><PERSON> )", "INFO_TEXT": "Total jumlah percakapan yang digunakan untuk perhitungan:", "TOOLTIP_TEXT": "<PERSON><PERSON><PERSON> Resp<PERSON> ad<PERSON> {metricValue} (berdasar<PERSON> {conversationCount} percakapan)"}, "RESOLUTION_TIME": {"NAME": "<PERSON><PERSON><PERSON>", "DESC": "( <PERSON><PERSON><PERSON><PERSON><PERSON> )", "INFO_TEXT": "Total jumlah percakapan yang digunakan untuk perhitungan:", "TOOLTIP_TEXT": "<PERSON><PERSON><PERSON> adala<PERSON> {metricValue} (berdasar<PERSON> {conversationCount} percakapan)"}, "RESOLUTION_COUNT": {"NAME": "<PERSON><PERSON><PERSON>", "DESC": "( Total )"}, "BOT_RESOLUTION_COUNT": {"NAME": "<PERSON><PERSON><PERSON>", "DESC": "( Total )"}, "BOT_HANDOFF_COUNT": {"NAME": "Handoff Count", "DESC": "( Total )"}, "REPLY_TIME": {"NAME": "Customer waiting time", "TOOLTIP_TEXT": "Waiting time is {metricValue} (based on {conversationCount} replies)", "DESC": ""}}, "DATE_RANGE_OPTIONS": {"LAST_7_DAYS": "7 hari terakhir", "LAST_30_DAYS": "30 hari terakhir", "LAST_3_MONTHS": "3 bulan te<PERSON>hir", "LAST_6_MONTHS": "6 bulan te<PERSON>hir", "LAST_YEAR": "<PERSON><PERSON>", "CUSTOM_DATE_RANGE": "Rentang tanggal kustom"}, "CUSTOM_DATE_RANGE": {"CONFIRM": "Terapkan", "PLACEHOLDER": "<PERSON><PERSON><PERSON> rentang tanggal"}, "GROUP_BY_FILTER_DROPDOWN_LABEL": "Kelompokkan Berdasarkan", "DURATION_FILTER_LABEL": "<PERSON><PERSON><PERSON>", "GROUPING_OPTIONS": {"DAY": "<PERSON>", "WEEK": "<PERSON><PERSON>", "MONTH": "<PERSON><PERSON><PERSON>", "YEAR": "<PERSON><PERSON>"}, "GROUP_BY_DAY_OPTIONS": [{"id": 1, "groupBy": "<PERSON>"}], "GROUP_BY_WEEK_OPTIONS": [{"id": 1, "groupBy": "<PERSON>"}, {"id": 2, "groupBy": "<PERSON><PERSON>"}], "GROUP_BY_MONTH_OPTIONS": [{"id": 1, "groupBy": "<PERSON>"}, {"id": 2, "groupBy": "<PERSON><PERSON>"}, {"id": 3, "groupBy": "<PERSON><PERSON><PERSON>"}], "GROUP_BY_YEAR_OPTIONS": [{"id": 1, "groupBy": "Day"}, {"id": 2, "groupBy": "Week"}, {"id": 3, "groupBy": "Month"}], "BUSINESS_HOURS": "<PERSON>", "FILTER_ACTIONS": {"CLEAR_FILTER": "Clear filter", "EMPTY_LIST": "Tidak ada hasil di<PERSON>n"}, "PAGINATION": {"RESULTS": "Showing {start} to {end} of {total} results", "PER_PAGE_TEMPLATE": "{size} / page"}}, "AGENT_REPORTS": {"HEADER": "Gambaran <PERSON>n", "DESCRIPTION": "Easily track agent performance with key metrics such as conversations, response times, resolution times, and resolved cases. Click an agent’s name to learn more.", "LOADING_CHART": "Memuat data grafik...", "NO_ENOUGH_DATA": "<PERSON><PERSON> belum menerima cukup data untuk membuat laporan, <PERSON><PERSON><PERSON> coba lagi nanti.", "DOWNLOAD_AGENT_REPORTS": "<PERSON>du<PERSON> laporan agen", "FILTER_DROPDOWN_LABEL": "<PERSON><PERSON><PERSON>", "METRICS": {"CONVERSATIONS": {"NAME": "Percaka<PERSON>", "DESC": "( Total )"}, "INCOMING_MESSAGES": {"NAME": "<PERSON><PERSON>", "DESC": "( Total )"}, "OUTGOING_MESSAGES": {"NAME": "<PERSON><PERSON>", "DESC": "( Total )"}, "FIRST_RESPONSE_TIME": {"NAME": "<PERSON><PERSON><PERSON>", "DESC": "( <PERSON><PERSON><PERSON><PERSON><PERSON> )", "INFO_TEXT": "Total jumlah percakapan yang digunakan untuk perhitungan:", "TOOLTIP_TEXT": "<PERSON><PERSON><PERSON> Resp<PERSON> ad<PERSON> {metricValue} (berdasar<PERSON> {conversationCount} percakapan)"}, "RESOLUTION_TIME": {"NAME": "<PERSON><PERSON><PERSON>", "DESC": "( <PERSON><PERSON><PERSON><PERSON><PERSON> )", "INFO_TEXT": "Total jumlah percakapan yang digunakan untuk perhitungan:", "TOOLTIP_TEXT": "<PERSON><PERSON><PERSON> adala<PERSON> {metricValue} (berdasar<PERSON> {conversationCount} percakapan)"}, "RESOLUTION_COUNT": {"NAME": "<PERSON><PERSON><PERSON>", "DESC": "( Total )"}}, "DATE_RANGE": [{"id": 0, "name": "7 hari terakhir"}, {"id": 1, "name": "30 hari terakhir"}, {"id": 2, "name": "3 bulan te<PERSON>hir"}, {"id": 3, "name": "6 bulan te<PERSON>hir"}, {"id": 4, "name": "<PERSON><PERSON>"}, {"id": 5, "name": "Rentang tanggal kustom"}], "CUSTOM_DATE_RANGE": {"CONFIRM": "Terapkan", "PLACEHOLDER": "<PERSON><PERSON><PERSON> rentang tanggal"}}, "LABEL_REPORTS": {"HEADER": "Gambaran Label", "LOADING_CHART": "Memuat data grafik...", "NO_ENOUGH_DATA": "<PERSON><PERSON> belum menerima cukup data untuk membuat laporan, <PERSON><PERSON><PERSON> coba lagi nanti.", "DOWNLOAD_LABEL_REPORTS": "Unduh laporan label", "FILTER_DROPDOWN_LABEL": "Pilih label", "METRICS": {"CONVERSATIONS": {"NAME": "Percaka<PERSON>", "DESC": "( Total )"}, "INCOMING_MESSAGES": {"NAME": "<PERSON><PERSON>", "DESC": "( Total )"}, "OUTGOING_MESSAGES": {"NAME": "<PERSON><PERSON>", "DESC": "( Total )"}, "FIRST_RESPONSE_TIME": {"NAME": "<PERSON><PERSON><PERSON>", "DESC": "( <PERSON><PERSON><PERSON><PERSON><PERSON> )", "INFO_TEXT": "Total jumlah percakapan yang digunakan untuk perhitungan:", "TOOLTIP_TEXT": "<PERSON><PERSON><PERSON> Resp<PERSON> ad<PERSON> {metricValue} (berdasar<PERSON> {conversationCount} percakapan)"}, "RESOLUTION_TIME": {"NAME": "<PERSON><PERSON><PERSON>", "DESC": "( <PERSON><PERSON><PERSON><PERSON><PERSON> )", "INFO_TEXT": "Total jumlah percakapan yang digunakan untuk perhitungan:", "TOOLTIP_TEXT": "<PERSON><PERSON><PERSON> adala<PERSON> {metricValue} (berdasar<PERSON> {conversationCount} percakapan)"}, "RESOLUTION_COUNT": {"NAME": "<PERSON><PERSON><PERSON>", "DESC": "( Total )"}}, "DATE_RANGE": [{"id": 0, "name": "7 hari terakhir"}, {"id": 1, "name": "30 hari terakhir"}, {"id": 2, "name": "3 bulan te<PERSON>hir"}, {"id": 3, "name": "6 bulan te<PERSON>hir"}, {"id": 4, "name": "<PERSON><PERSON>"}, {"id": 5, "name": "Rentang tanggal kustom"}], "CUSTOM_DATE_RANGE": {"CONFIRM": "Terapkan", "PLACEHOLDER": "<PERSON><PERSON><PERSON> rentang tanggal"}}, "INBOX_REPORTS": {"HEADER": "<PERSON><PERSON><PERSON><PERSON> kontak masuk", "DESCRIPTION": "Quickly view your inbox performance with key metrics like conversations, response times, resolution times, and resolved cases—all in one place. Click an inbox name for more details.", "LOADING_CHART": "Memuat data grafik...", "NO_ENOUGH_DATA": "<PERSON><PERSON> belum menerima cukup data untuk membuat laporan, <PERSON><PERSON><PERSON> coba lagi nanti.", "DOWNLOAD_INBOX_REPORTS": "Unduh laporan kotak masuk", "FILTER_DROPDOWN_LABEL": "Pilih kotak masuk", "METRICS": {"CONVERSATIONS": {"NAME": "Percaka<PERSON>", "DESC": "( Total )"}, "INCOMING_MESSAGES": {"NAME": "<PERSON><PERSON>", "DESC": "( Total )"}, "OUTGOING_MESSAGES": {"NAME": "<PERSON><PERSON>", "DESC": "( Total )"}, "FIRST_RESPONSE_TIME": {"NAME": "<PERSON><PERSON><PERSON>", "DESC": "( <PERSON><PERSON><PERSON><PERSON><PERSON> )", "INFO_TEXT": "Total jumlah percakapan yang digunakan untuk perhitungan:", "TOOLTIP_TEXT": "<PERSON><PERSON><PERSON> Resp<PERSON> ad<PERSON> {metricValue} (berdasar<PERSON> {conversationCount} percakapan)"}, "RESOLUTION_TIME": {"NAME": "<PERSON><PERSON><PERSON>", "DESC": "( <PERSON><PERSON><PERSON><PERSON><PERSON> )", "INFO_TEXT": "Total jumlah percakapan yang digunakan untuk perhitungan:", "TOOLTIP_TEXT": "<PERSON><PERSON><PERSON> adala<PERSON> {metricValue} (berdasar<PERSON> {conversationCount} percakapan)"}, "RESOLUTION_COUNT": {"NAME": "<PERSON><PERSON><PERSON>", "DESC": "( Total )"}}, "DATE_RANGE": [{"id": 0, "name": "7 hari terakhir"}, {"id": 1, "name": "30 hari terakhir"}, {"id": 2, "name": "3 bulan te<PERSON>hir"}, {"id": 3, "name": "6 bulan te<PERSON>hir"}, {"id": 4, "name": "<PERSON><PERSON>"}, {"id": 5, "name": "Rentang tanggal kustom"}], "CUSTOM_DATE_RANGE": {"CONFIRM": "Terapkan", "PLACEHOLDER": "<PERSON><PERSON><PERSON> rentang tanggal"}}, "TEAM_REPORTS": {"HEADER": "<PERSON><PERSON><PERSON><PERSON>", "DESCRIPTION": "Get a snapshot of your team’s performance with essential metrics, including conversations, response times, resolution times, and resolved cases. Click a team name for more details.", "LOADING_CHART": "Memuat data grafik...", "NO_ENOUGH_DATA": "<PERSON><PERSON> belum menerima cukup data untuk membuat laporan, <PERSON><PERSON><PERSON> coba lagi nanti.", "DOWNLOAD_TEAM_REPORTS": "<PERSON><PERSON><PERSON> laporan tim", "FILTER_DROPDOWN_LABEL": "<PERSON><PERSON><PERSON>", "METRICS": {"CONVERSATIONS": {"NAME": "Percaka<PERSON>", "DESC": "( Total )"}, "INCOMING_MESSAGES": {"NAME": "<PERSON><PERSON>", "DESC": "( Total )"}, "OUTGOING_MESSAGES": {"NAME": "<PERSON><PERSON>", "DESC": "( Total )"}, "FIRST_RESPONSE_TIME": {"NAME": "<PERSON><PERSON><PERSON>", "DESC": "( <PERSON><PERSON><PERSON><PERSON><PERSON> )", "INFO_TEXT": "Total jumlah percakapan yang digunakan untuk perhitungan:", "TOOLTIP_TEXT": "<PERSON><PERSON><PERSON> Resp<PERSON> ad<PERSON> {metricValue} (berdasar<PERSON> {conversationCount} percakapan)"}, "RESOLUTION_TIME": {"NAME": "<PERSON><PERSON><PERSON>", "DESC": "( <PERSON><PERSON><PERSON><PERSON><PERSON> )", "INFO_TEXT": "Total jumlah percakapan yang digunakan untuk perhitungan:", "TOOLTIP_TEXT": "<PERSON><PERSON><PERSON> adala<PERSON> {metricValue} (berdasar<PERSON> {conversationCount} percakapan)"}, "RESOLUTION_COUNT": {"NAME": "<PERSON><PERSON><PERSON>", "DESC": "( Total )"}}, "DATE_RANGE": [{"id": 0, "name": "7 hari terakhir"}, {"id": 1, "name": "30 hari terakhir"}, {"id": 2, "name": "3 bulan te<PERSON>hir"}, {"id": 3, "name": "6 bulan te<PERSON>hir"}, {"id": 4, "name": "<PERSON><PERSON>"}, {"id": 5, "name": "Rentang tanggal kustom"}], "CUSTOM_DATE_RANGE": {"CONFIRM": "Terapkan", "PLACEHOLDER": "<PERSON><PERSON><PERSON> rentang tanggal"}}, "CSAT_REPORTS": {"HEADER": "Laporan CSAT", "NO_RECORDS": "Tidak ada respons survey CSAT yang tersedia.", "DOWNLOAD": "Unduh Laporan CSAT", "DOWNLOAD_FAILED": "Gagal mengunduh Laporan CSAT", "FILTERS": {"AGENTS": {"PLACEHOLDER": "<PERSON><PERSON><PERSON>"}}, "TABLE": {"HEADER": {"CONTACT_NAME": "Kontak", "AGENT_NAME": "<PERSON>n yang di<PERSON>", "RATING": "<PERSON><PERSON><PERSON>", "FEEDBACK_TEXT": "<PERSON><PERSON><PERSON> umpan balik"}}, "METRIC": {"TOTAL_RESPONSES": {"LABEL": "Total respons", "TOOLTIP": "Total jumlah respons yang terkumpul"}, "SATISFACTION_SCORE": {"LABEL": "<PERSON><PERSON>", "TOOLTIP": "Total jumlah respons positif / Total jumlah respons * 100"}, "RESPONSE_RATE": {"LABEL": "<PERSON><PERSON><PERSON> respons", "TOOLTIP": "Total jumlah respons / Total jumlah pesan survey CSAT yang terkirim * 100"}}}, "BOT_REPORTS": {"HEADER": "Bot Reports", "METRIC": {"TOTAL_CONVERSATIONS": {"LABEL": "No. of Conversations", "TOOLTIP": "Total number of conversations handled by the bot"}, "TOTAL_RESPONSES": {"LABEL": "Total Responses", "TOOLTIP": "Total number of responses sent by the bot"}, "RESOLUTION_RATE": {"LABEL": "Resolution Rate", "TOOLTIP": "Total number of conversations resolved by the bot / Total number of conversations handled by the bot * 100"}, "HANDOFF_RATE": {"LABEL": "Handoff Rate", "TOOLTIP": "Total number of conversations handed off to agents / Total number of conversations handled by the bot * 100"}}}, "OVERVIEW_REPORTS": {"HEADER": "<PERSON><PERSON><PERSON><PERSON>", "LIVE": "Langsung", "ACCOUNT_CONVERSATIONS": {"HEADER": "Percaka<PERSON>", "LOADING_MESSAGE": "Memuat metrik per<PERSON>...", "OPEN": "<PERSON><PERSON><PERSON>", "UNATTENDED": "Tidak Ditangani", "UNASSIGNED": "Belum Ditugaskan", "PENDING": "<PERSON><PERSON><PERSON>"}, "CONVERSATION_HEATMAP": {"HEADER": "<PERSON><PERSON>", "NO_CONVERSATIONS": "Tidak ada perca<PERSON>pan", "CONVERSATION": "{count} percakapan", "CONVERSATIONS": "{count} percakapan", "DOWNLOAD_REPORT": "Download report"}, "AGENT_CONVERSATIONS": {"HEADER": "Percakapan o<PERSON>n", "LOADING_MESSAGE": "<PERSON><PERSON><PERSON> metrik agen...", "NO_AGENTS": "Tidak ada perca<PERSON>pan oleh agen", "TABLE_HEADER": {"AGENT": "Agen", "OPEN": "<PERSON><PERSON><PERSON>", "UNATTENDED": "Tidak Ditangani", "STATUS": "Status"}}, "TEAM_CONVERSATIONS": {"ALL_TEAMS": "All Teams", "HEADER": "Conversations by teams", "LOADING_MESSAGE": "Loading team metrics...", "NO_TEAMS": "There is no data available", "TABLE_HEADER": {"TEAM": "<PERSON>", "OPEN": "<PERSON><PERSON><PERSON>", "UNATTENDED": "Tidak Ditangani", "STATUS": "Status"}}, "AGENT_STATUS": {"HEADER": "Status Agen", "ONLINE": "Online", "BUSY": "Sibuk", "OFFLINE": "Offline"}}, "DAYS_OF_WEEK": {"SUNDAY": "<PERSON><PERSON>", "MONDAY": "<PERSON><PERSON>", "TUESDAY": "<PERSON><PERSON><PERSON>", "WEDNESDAY": "<PERSON><PERSON>", "THURSDAY": "<PERSON><PERSON>", "FRIDAY": "<PERSON><PERSON>", "SATURDAY": "Sabtu"}, "SLA_REPORTS": {"HEADER": "SLA Reports", "NO_RECORDS": "SLA applied conversations are not available.", "LOADING": "Loading SLA data...", "DOWNLOAD_SLA_REPORTS": "Download SLA reports", "DOWNLOAD_FAILED": "Failed to download SLA Reports", "DROPDOWN": {"ADD_FIlTER": "Tambah filter", "CLEAR_ALL": "Clear all", "CLEAR_FILTER": "Clear filter", "EMPTY_LIST": "Tidak ada hasil di<PERSON>n", "NO_FILTER": "No filters available", "SEARCH": "Search filter", "INPUT_PLACEHOLDER": {"SLA": "SLA name", "AGENTS": "<PERSON><PERSON> agen", "INBOXES": "Nama kotak masuk", "LABELS": "Nama label", "TEAMS": "<PERSON><PERSON>"}, "SLA": "SLA Policy", "INBOXES": "Kotak masuk", "AGENTS": "Agen", "LABELS": "Label", "TEAMS": "<PERSON>"}, "WITH": "with", "METRICS": {"HIT_RATE": {"LABEL": "Hit Rate", "TOOLTIP": "Percentage of SLAs created were completed successfully"}, "NO_OF_MISSES": {"LABEL": "Number of Misses", "TOOLTIP": "Total SLA misses in a certain period"}, "NO_OF_CONVERSATIONS": {"LABEL": "Number of Conversations", "TOOLTIP": "Total number of conversations with SLA"}}, "TABLE": {"HEADER": {"POLICY": "Policy", "CONVERSATION": "Percaka<PERSON>", "AGENT": "Agen"}, "VIEW_DETAILS": "View Details"}}, "SUMMARY_REPORTS": {"INBOX": "Kotak masuk", "AGENT": "Agen", "TEAM": "<PERSON>", "AVG_RESOLUTION_TIME": "Avg. Resolution Time", "AVG_FIRST_RESPONSE_TIME": "Avg. First Response Time", "AVG_REPLY_TIME": "Avg. Customer Waiting Time", "RESOLUTION_COUNT": "<PERSON><PERSON><PERSON>", "CONVERSATIONS": "<PERSON><PERSON><PERSON>"}}