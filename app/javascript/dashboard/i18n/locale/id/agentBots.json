{"AGENT_BOTS": {"HEADER": "Bot", "LOADING_EDITOR": "Me<PERSON>at editor...", "DESCRIPTION": "Agent <PERSON><PERSON> are like the most fabulous members of your team. They can handle the small stuff, so you can focus on the stuff that matters. Give them a try. You can manage your bots from this page or create new ones using the 'Add Bot' button.", "LEARN_MORE": "Learn about agent bots", "GLOBAL_BOT": "System bot", "GLOBAL_BOT_BADGE": "Sistem", "AVATAR": {"SUCCESS_DELETE": "<PERSON><PERSON> avatar deleted successfully", "ERROR_DELETE": "Error deleting bot avatar, please try again"}, "BOT_CONFIGURATION": {"TITLE": "<PERSON><PERSON><PERSON> bot agen", "DESC": "Tetapkan Bot Agen ke kotak masuk Anda. Mereka dapat menangani percakapan awal dan meneruskannya ke agen manusia jika diperlukan.", "SUBMIT": "<PERSON><PERSON><PERSON>", "DISCONNECT": "<PERSON><PERSON><PERSON> kone<PERSON>i", "SUCCESS_MESSAGE": "<PERSON><PERSON><PERSON><PERSON> bot agen.", "DISCONNECTED_SUCCESS_MESSAGE": "<PERSON><PERSON><PERSON><PERSON> memutuskan hubungan bot agen.", "ERROR_MESSAGE": "Could not update the agent bot. Please try again.", "DISCONNECTED_ERROR_MESSAGE": "Could not disconnect the agent bot. Please try again.", "SELECT_PLACEHOLDER": "Select bot"}, "ADD": {"TITLE": "<PERSON><PERSON>", "CANCEL_BUTTON_TEXT": "Batalkan", "API": {"SUCCESS_MESSAGE": "<PERSON><PERSON> be<PERSON><PERSON><PERSON> di<PERSON>.", "ERROR_MESSAGE": "Could not add bot. Please try again later."}}, "LIST": {"404": "No bots found. You can create a bot by clicking the 'Add Bot' button.", "LOADING": "Fetching bots...", "TABLE_HEADER": {"DETAILS": "Bot Details", "URL": "URL Webhook"}}, "DELETE": {"BUTTON_TEXT": "Hapus", "TITLE": "Delete bot", "CONFIRM": {"TITLE": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "MESSAGE": "Are you sure you want to delete {name}?", "YES": "Ya, Hapus", "NO": "Tidak, Simpan"}, "API": {"SUCCESS_MESSAGE": "<PERSON><PERSON> ber<PERSON><PERSON>.", "ERROR_MESSAGE": "Could not delete bot. Please try again."}}, "EDIT": {"BUTTON_TEXT": "Edit", "TITLE": "Edit bot", "API": {"SUCCESS_MESSAGE": "<PERSON><PERSON> ber<PERSON><PERSON>.", "ERROR_MESSAGE": "Could not update bot. Please try again."}}, "FORM": {"AVATAR": {"LABEL": "<PERSON><PERSON> avatar"}, "NAME": {"LABEL": "Bot name", "PLACEHOLDER": "Enter bot name", "REQUIRED": "<PERSON><PERSON> bot wajib diisi"}, "DESCRIPTION": {"LABEL": "<PERSON><PERSON><PERSON><PERSON>", "PLACEHOLDER": "Apa yang dilakukan bot ini?"}, "WEBHOOK_URL": {"LABEL": "URL Webhook", "PLACEHOLDER": "https://example.com/webhook", "REQUIRED": "Webhook URL is required"}, "ERRORS": {"NAME": "<PERSON><PERSON> bot wajib diisi", "URL": "Webhook URL is required", "VALID_URL": "Please enter a valid URL starting with http:// or https://"}, "CANCEL": "Batalkan", "CREATE": "Create <PERSON><PERSON>", "UPDATE": "Update Bot"}, "WEBHOOK": {"DESCRIPTION": "Configure a webhook bot to integrate with your custom services. The bot will receive and process events from conversations and can respond to them."}, "TYPES": {"WEBHOOK": "Webhook bot"}}}