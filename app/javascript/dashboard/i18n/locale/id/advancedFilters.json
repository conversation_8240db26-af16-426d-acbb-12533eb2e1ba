{"FILTER": {"TITLE": "Filter per<PERSON>", "SUBTITLE": "Add your filters below and hit 'Apply filters' to cut through the chat clutter.", "EDIT_CUSTOM_FILTER": "Edit <PERSON>", "CUSTOM_VIEWS_SUBTITLE": "Tambahkan atau hapus filter dan perbarui folder Anda.", "ADD_NEW_FILTER": "Tambah filter", "FILTER_DELETE_ERROR": "Oops, looks like we can't save nothing! Please add at least one filter to save it.", "SUBMIT_BUTTON_LABEL": "Terapkan filter", "UPDATE_BUTTON_LABEL": "Perbarui folder", "CANCEL_BUTTON_LABEL": "Batalkan", "CLEAR_BUTTON_LABEL": "Clear filters", "FOLDER_LABEL": "<PERSON>a <PERSON>", "FOLDER_QUERY_LABEL": "<PERSON><PERSON>", "EMPTY_VALUE_ERROR": "<PERSON><PERSON>.", "TOOLTIP_LABEL": "Filter per<PERSON>", "QUERY_DROPDOWN_LABELS": {"AND": "DAN", "OR": "ATAU"}, "INPUT_PLACEHOLDER": "Enter value", "OPERATOR_LABELS": {"equal_to": "<PERSON><PERSON> den<PERSON>", "not_equal_to": "Tidak sama dengan", "does_not_contain": "Tidak berisi", "is_present": "Ada", "is_not_present": "Tidak ada", "is_greater_than": "<PERSON><PERSON><PERSON> besar dari", "is_less_than": "<PERSON><PERSON><PERSON> kecil dari", "days_before": "X hari sebelum", "starts_with": "<PERSON><PERSON><PERSON>", "equalTo": "<PERSON><PERSON> den<PERSON>", "notEqualTo": "Tidak sama dengan", "contains": "<PERSON><PERSON><PERSON>", "doesNotContain": "Tidak berisi", "isPresent": "Ada", "isNotPresent": "Tidak ada", "isGreaterThan": "<PERSON><PERSON><PERSON> besar dari", "isLessThan": "<PERSON><PERSON><PERSON> kecil dari", "daysBefore": "X hari sebelum", "startsWith": "<PERSON><PERSON><PERSON>"}, "ATTRIBUTE_LABELS": {"TRUE": "<PERSON><PERSON>", "FALSE": "<PERSON><PERSON>"}, "ATTRIBUTES": {"STATUS": "Status", "ASSIGNEE_NAME": "Assignee name", "INBOX_NAME": "Nama kotak masuk", "TEAM_NAME": "<PERSON><PERSON>", "CONVERSATION_IDENTIFIER": "Conversation identifier", "CAMPAIGN_NAME": "Campaign name", "LABELS": "Label", "BROWSER_LANGUAGE": "Bahasa peramban", "PRIORITY": "Prioritas", "COUNTRY_NAME": "<PERSON><PERSON> negara", "REFERER_LINK": "<PERSON><PERSON>", "CUSTOM_ATTRIBUTE_LIST": "<PERSON><PERSON><PERSON>", "CUSTOM_ATTRIBUTE_TEXT": "Teks", "CUSTOM_ATTRIBUTE_NUMBER": "Nomor", "CUSTOM_ATTRIBUTE_LINK": "Tautan", "CUSTOM_ATTRIBUTE_CHECKBOX": "Kotak centang", "CREATED_AT": "Dibuat pada", "LAST_ACTIVITY": "Aktivitas terakhir"}, "ERRORS": {"VALUE_REQUIRED": "<PERSON><PERSON>", "ATTRIBUTE_KEY_REQUIRED": "Attribute key is required", "FILTER_OPERATOR_REQUIRED": "Filter operator is required", "VALUE_MUST_BE_BETWEEN_1_AND_998": "Value must be between 1 and 998"}, "GROUPS": {"STANDARD_FILTERS": "Standard filters", "ADDITIONAL_FILTERS": "Additional filters", "CUSTOM_ATTRIBUTES": "Atribut kostum"}, "CUSTOM_VIEWS": {"ADD": {"TITLE": "<PERSON><PERSON><PERSON><PERSON> Anda ingin menyimpan filter ini?", "LABEL": "Beri nama pada filter ini", "PLACEHOLDER": "Name your filter to refer it later.", "ERROR_MESSAGE": "<PERSON><PERSON>.", "SAVE_BUTTON": "Simpan filter", "CANCEL_BUTTON": "Batalkan", "API_FOLDERS": {"SUCCESS_MESSAGE": "Folder berhasil dibuat.", "ERROR_MESSAGE": "Galat saat membuat folder."}, "API_SEGMENTS": {"SUCCESS_MESSAGE": "Segment berhasil dibuat.", "ERROR_MESSAGE": "Galat saat membuat segment."}}, "EDIT": {"EDIT_BUTTON": "Edit folder"}, "DELETE": {"DELETE_BUTTON": "Hapus filter", "MODAL": {"CONFIRM": {"TITLE": "<PERSON>n<PERSON><PERSON><PERSON>", "MESSAGE": "<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin menghapus filter ", "YES": "Ya, hapus", "NO": "Tidak, simpan"}}, "API_FOLDERS": {"SUCCESS_MESSAGE": "Folder berhasil dihapus.", "ERROR_MESSAGE": "Galat saat menghapus folder."}, "API_SEGMENTS": {"SUCCESS_MESSAGE": "Segment berhasil dihapus.", "ERROR_MESSAGE": "Galat saat menghapus segment."}}}}}