{"AGENT_BOTS": {"HEADER": "機器人", "LOADING_EDITOR": "正在載入編輯器...", "DESCRIPTION": "Agent <PERSON><PERSON> are like the most fabulous members of your team. They can handle the small stuff, so you can focus on the stuff that matters. Give them a try. You can manage your bots from this page or create new ones using the 'Add Bot' button.", "LEARN_MORE": "Learn about agent bots", "GLOBAL_BOT": "System bot", "GLOBAL_BOT_BADGE": "System", "AVATAR": {"SUCCESS_DELETE": "<PERSON><PERSON> avatar deleted successfully", "ERROR_DELETE": "Error deleting bot avatar, please try again"}, "BOT_CONFIGURATION": {"TITLE": "選擇一個機器人", "DESC": "將機器人分配到您的收件匣中。它們可以處理初始對話，並在必要時轉接給真人客服", "SUBMIT": "更新", "DISCONNECT": "取消機器人連結", "SUCCESS_MESSAGE": "成功更新機器人", "DISCONNECTED_SUCCESS_MESSAGE": "成功解除機器人連結", "ERROR_MESSAGE": "無法更新機器人，請再試一次", "DISCONNECTED_ERROR_MESSAGE": "無法斷開機器人，請再試一次", "SELECT_PLACEHOLDER": "選擇機器人"}, "ADD": {"TITLE": "<PERSON><PERSON>", "CANCEL_BUTTON_TEXT": "取消", "API": {"SUCCESS_MESSAGE": "機器人新增成功.", "ERROR_MESSAGE": "無法新增機器人，請稍後再試."}}, "LIST": {"404": "No bots found. You can create a bot by clicking the 'Add Bot' button.", "LOADING": "正在取得機器人...", "TABLE_HEADER": {"DETAILS": "Bot Details", "URL": "Webhook 網址"}}, "DELETE": {"BUTTON_TEXT": "刪除", "TITLE": "刪除機器人", "CONFIRM": {"TITLE": "確認刪除", "MESSAGE": "Are you sure you want to delete {name}?", "YES": "是，刪除", "NO": "不，保留"}, "API": {"SUCCESS_MESSAGE": "機器人刪除成功", "ERROR_MESSAGE": "無法刪除機器人，請再試一次"}}, "EDIT": {"BUTTON_TEXT": "編輯", "TITLE": "編輯機器人", "API": {"SUCCESS_MESSAGE": "機器人更新成功.", "ERROR_MESSAGE": "無法更新機器人，請稍後再試"}}, "FORM": {"AVATAR": {"LABEL": "<PERSON><PERSON> avatar"}, "NAME": {"LABEL": "機器人名稱", "PLACEHOLDER": "Enter bot name", "REQUIRED": "機器人名稱為必填"}, "DESCRIPTION": {"LABEL": "描述資訊", "PLACEHOLDER": "這個機器人的作用是什麼"}, "WEBHOOK_URL": {"LABEL": "Webhook 網址", "PLACEHOLDER": "https://example.com/webhook", "REQUIRED": "Webhook URL is required"}, "ERRORS": {"NAME": "機器人名稱為必填", "URL": "Webhook URL is required", "VALID_URL": "Please enter a valid URL starting with http:// or https://"}, "CANCEL": "取消", "CREATE": "Create <PERSON><PERSON>", "UPDATE": "Update Bot"}, "WEBHOOK": {"DESCRIPTION": "Configure a webhook bot to integrate with your custom services. The bot will receive and process events from conversations and can respond to them."}, "TYPES": {"WEBHOOK": "Webhook 機器人"}}}