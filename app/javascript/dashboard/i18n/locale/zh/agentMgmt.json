{"AGENT_MGMT": {"HEADER": "Agents", "HEADER_BTN_TXT": "Add Agent", "LOADING": "Fetching Agent List", "SIDEBAR_TXT": "<p><b>Agents</b></p> <p> An <b>Agent</b> is a member of your Customer Support team. </p><p> Agents will be able to view and reply to messages from your users. The list shows all agents currently in your account. </p><p> Click on <b>Add Agent</b> to add a new agent. Agent you add will receive an email with a confirmation link to activate their account, after which they can access Chatwoot and respond to messages. </p><p> Access to Chatwoot's features are based on following roles. </p><p> <b>Agent</b> - Agents with this role can only access inboxes, reports and conversations. They can assign conversations to other agents or themselves and resolve conversations.</p><p> <b>Administrator</b> - Administrator will have access to all Chatwoot features enabled for your account, including settings, along with all of a normal agents' privileges.</p>", "AGENT_TYPES": {"ADMINISTRATOR": "Administrator", "AGENT": "Agent"}, "LIST": {"404": "There are no agents associated to this account", "TITLE": "Manage agents in your team", "DESC": "You can add/remove agents to/in your team.", "NAME": "Name", "EMAIL": "EMAIL", "STATUS": "Status", "ACTIONS": "Actions", "VERIFIED": "Verified", "VERIFICATION_PENDING": "Verification Pending"}, "ADD": {"TITLE": "Add agent to your team", "DESC": "You can add people who will be able to handle support for your inboxes.", "CANCEL_BUTTON_TEXT": "Cancel", "FORM": {"NAME": {"LABEL": "Agent Name", "PLACEHOLDER": "Please enter a name of the agent"}, "AGENT_TYPE": {"LABEL": "Agent Type", "PLACEHOLDER": "Please select a type", "ERROR": "Agent type is required"}, "EMAIL": {"LABEL": "Email Address", "PLACEHOLDER": "Please enter an email address of the agent"}, "SUBMIT": "Add Agent"}, "API": {"SUCCESS_MESSAGE": "Agent added successfully", "EXIST_MESSAGE": "Agent email already in use, Please try another email address", "ERROR_MESSAGE": "Could not connect to Woot Server, Please try again later"}}, "DELETE": {"BUTTON_TEXT": "Delete", "API": {"SUCCESS_MESSAGE": "Agent deleted successfully", "ERROR_MESSAGE": "Could not connect to Woot Server, Please try again later"}, "CONFIRM": {"TITLE": "Confirm Deletion", "MESSAGE": "Are you sure to delete ", "YES": "Yes, Delete ", "NO": "No, Keep "}}, "EDIT": {"TITLE": "Edit agent", "FORM": {"NAME": {"LABEL": "Agent Name", "PLACEHOLDER": "Please enter a name of the agent"}, "AGENT_TYPE": {"LABEL": "Agent Type", "PLACEHOLDER": "Please select a type", "ERROR": "Agent type is required"}, "EMAIL": {"LABEL": "Email Address", "PLACEHOLDER": "Please enter an email address of the agent"}, "SUBMIT": "Edit Agent"}, "BUTTON_TEXT": "Edit", "CANCEL_BUTTON_TEXT": "Cancel", "API": {"SUCCESS_MESSAGE": "Agent updated successfully", "ERROR_MESSAGE": "Could not connect to Woot Server, Please try again later"}, "PASSWORD_RESET": {"ADMIN_RESET_BUTTON": "Reset Password", "ADMIN_SUCCESS_MESSAGE": "An email with reset password instructions has been sent to the agent", "SUCCESS_MESSAGE": "Agent password reset successfully", "ERROR_MESSAGE": "Could not connect to Woot Server, Please try again later"}}, "SEARCH": {"NO_RESULTS": "No agents found."}}}