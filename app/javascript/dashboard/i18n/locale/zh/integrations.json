{"INTEGRATION_SETTINGS": {"HEADER": "Integrations", "WEBHOOK": {"TITLE": "Webhook", "CONFIGURE": "Configure", "HEADER": "Webhook settings", "HEADER_BTN_TXT": "Add new webhook", "INTEGRATION_TXT": "Webhook events provide you the realtime information about what's happening in your Chatwoot account. You can make use of the webhooks to communicate the events to your favourite apps like Slack or Github. Click on Configure to set up your webhooks.", "LOADING": "Fetching attached webhooks", "SEARCH_404": "There are no items matching this query", "SIDEBAR_TXT": "<p><b>Webhooks</b> </p> <p>Webhooks are HTTP callbacks which can be defined for every account. They are triggered by events like message creation in Chatwoot. You can create more than one webhook for this account. <br /><br /> For creating a <b>webhook</b>, click on the <b>Add new webhook</b> button. You can also remove any existing webhook by clicking on the Delete button.</p>", "LIST": {"404": "There are no webhooks configured for this account.", "TITLE": "Manage webhooks", "DESC": "Webhooks are predefined reply templates which can be used to quickly send out replies to tickets.", "TABLE_HEADER": ["Webhook endpoint", "Actions"]}, "ADD": {"CANCEL": "Cancel", "TITLE": "Add new webhook", "DESC": "Webhook events provide you the realtime information about what's happening in your Chatwoot account. Please enter a valid URL to configure a callback.", "FORM": {"END_POINT": {"LABEL": "Webhook URL", "PLACEHOLDER": "Example: https://example/api/webhook", "ERROR": "Please enter a valid URL"}, "SUBMIT": "Create webhook"}, "API": {"SUCCESS_MESSAGE": "Webhook added successfully", "ERROR_MESSAGE": "Could not connect to Woot Server, Please try again later"}}, "DELETE": {"BUTTON_TEXT": "Delete", "API": {"SUCCESS_MESSAGE": "Webhook deleted successfully", "ERROR_MESSAGE": "Could not connect to Woot Server, Please try again later"}, "CONFIRM": {"TITLE": "Confirm Deletion", "MESSAGE": "Are you sure to delete ", "YES": "Yes, Delete ", "NO": "No, Keep it"}}}, "DELETE": {"BUTTON_TEXT": "Delete", "API": {"SUCCESS_MESSAGE": "Integration deleted successfully"}}, "CONNECT": {"BUTTON_TEXT": "Connect"}}}