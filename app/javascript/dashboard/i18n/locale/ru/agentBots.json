{"AGENT_BOTS": {"HEADER": "Боты", "LOADING_EDITOR": "Загрузка редактора...", "DESCRIPTION": "Agent <PERSON><PERSON> are like the most fabulous members of your team. They can handle the small stuff, so you can focus on the stuff that matters. Give them a try. You can manage your bots from this page or create new ones using the 'Add Bot' button.", "LEARN_MORE": "Learn about agent bots", "GLOBAL_BOT": "System bot", "GLOBAL_BOT_BADGE": "Система", "AVATAR": {"SUCCESS_DELETE": "<PERSON><PERSON> avatar deleted successfully", "ERROR_DELETE": "Error deleting bot avatar, please try again"}, "BOT_CONFIGURATION": {"TITLE": "Выберите бота агента", "DESC": "Назначить бота агента на источник. Они могут отвечать на первоначальные сообщения и передавать их агенту в случае необходимости.", "SUBMIT": "Обновить", "DISCONNECT": "Отключить бота", "SUCCESS_MESSAGE": "Оператор успешно обновлен ботом.", "DISCONNECTED_SUCCESS_MESSAGE": "Успешное отключение робота агента.", "ERROR_MESSAGE": "Не удалось обновить бота агента. Пожалуйста, повторите попытку позже.", "DISCONNECTED_ERROR_MESSAGE": "Не удалось отключить бота агента. Пожалуйста, повторите попытку позже.", "SELECT_PLACEHOLDER": "Выбрать бота"}, "ADD": {"TITLE": "<PERSON><PERSON>", "CANCEL_BUTTON_TEXT": "Отменить", "API": {"SUCCESS_MESSAGE": "Бот успешно добавлен.", "ERROR_MESSAGE": "Не удалось добавить бота. Пожалуйста, повторите попытку позже."}}, "LIST": {"404": "No bots found. You can create a bot by clicking the 'Add Bot' button.", "LOADING": "Получение ботов...", "TABLE_HEADER": {"DETAILS": "Bot Details", "URL": "URL вебхука"}}, "DELETE": {"BUTTON_TEXT": "Удалить", "TITLE": "Удалить бота", "CONFIRM": {"TITLE": "Подтвердите удаление", "MESSAGE": "Are you sure you want to delete {name}?", "YES": "Да, удалить", "NO": "Нет, не удалять"}, "API": {"SUCCESS_MESSAGE": "Бот успешно удален.", "ERROR_MESSAGE": "Не удалось удалить бота. Пожалуйста, повторите попытку позже."}}, "EDIT": {"BUTTON_TEXT": "Редактировать", "TITLE": "Редактировать бота", "API": {"SUCCESS_MESSAGE": "Бот успешно обновлен.", "ERROR_MESSAGE": "Не удалось обновить бота. Пожалуйста, повторите попытку позже."}}, "FORM": {"AVATAR": {"LABEL": "<PERSON><PERSON> avatar"}, "NAME": {"LABEL": "Имя бота", "PLACEHOLDER": "Enter bot name", "REQUIRED": "Имя бота обязательно"}, "DESCRIPTION": {"LABEL": "Описание", "PLACEHOLDER": "Что делает этот бот?"}, "WEBHOOK_URL": {"LABEL": "URL вебхука", "PLACEHOLDER": "https://example.com/webhook", "REQUIRED": "Webhook URL is required"}, "ERRORS": {"NAME": "Имя бота обязательно", "URL": "Webhook URL is required", "VALID_URL": "Please enter a valid URL starting with http:// or https://"}, "CANCEL": "Отменить", "CREATE": "Create <PERSON><PERSON>", "UPDATE": "Update Bot"}, "WEBHOOK": {"DESCRIPTION": "Configure a webhook bot to integrate with your custom services. The bot will receive and process events from conversations and can respond to them."}, "TYPES": {"WEBHOOK": "Webhook бот"}}}