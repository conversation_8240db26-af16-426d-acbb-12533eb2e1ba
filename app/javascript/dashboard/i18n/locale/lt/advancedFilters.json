{"FILTER": {"TITLE": "Filtru<PERSON><PERSON> pokalbius", "SUBTITLE": "Add your filters below and hit 'Apply filters' to cut through the chat clutter.", "EDIT_CUSTOM_FILTER": "Redaguoti papkę", "CUSTOM_VIEWS_SUBTITLE": "Pridėkite arba pašalinkite filtrus ir atnaujinkite aplanką.", "ADD_NEW_FILTER": "<PERSON><PERSON><PERSON><PERSON>", "FILTER_DELETE_ERROR": "Oops, looks like we can't save nothing! Please add at least one filter to save it.", "SUBMIT_BUTTON_LABEL": "<PERSON><PERSON><PERSON> filtrus", "UPDATE_BUTTON_LABEL": "Atna<PERSON>jin<PERSON> a<PERSON>lank<PERSON>", "CANCEL_BUTTON_LABEL": "<PERSON><PERSON><PERSON><PERSON>", "CLEAR_BUTTON_LABEL": "Išvalyti filtrus", "FOLDER_LABEL": "<PERSON><PERSON><PERSON><PERSON>", "FOLDER_QUERY_LABEL": "Aplanko užklausa", "EMPTY_VALUE_ERROR": "Re<PERSON>linga vertė.", "TOOLTIP_LABEL": "Filtru<PERSON><PERSON> pokalbius", "QUERY_DROPDOWN_LABELS": {"AND": "IR", "OR": "AR"}, "INPUT_PLACEHOLDER": "Enter value", "OPERATOR_LABELS": {"equal_to": "Lygu", "not_equal_to": "<PERSON><PERSON><PERSON>", "does_not_contain": "Sudėtyje nėra", "is_present": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "is_not_present": "Nepateikiamas", "is_greater_than": "<PERSON><PERSON> nei", "is_less_than": "<PERSON><PERSON>i", "days_before": "<PERSON><PERSON> p<PERSON> x dienų", "starts_with": "<PERSON>ras<PERSON>a nuo", "equalTo": "Lygu", "notEqualTo": "<PERSON><PERSON><PERSON>", "contains": "Sudėtyje yra", "doesNotContain": "Sudėtyje nėra", "isPresent": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isNotPresent": "Nepateikiamas", "isGreaterThan": "<PERSON><PERSON> nei", "isLessThan": "<PERSON><PERSON>i", "daysBefore": "<PERSON><PERSON> p<PERSON> x dienų", "startsWith": "<PERSON>ras<PERSON>a nuo"}, "ATTRIBUTE_LABELS": {"TRUE": "<PERSON><PERSON><PERSON>", "FALSE": "Netiesa"}, "ATTRIBUTES": {"STATUS": "<PERSON><PERSON><PERSON><PERSON>", "ASSIGNEE_NAME": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "INBOX_NAME": "Gautų laiškų aplanko pavadinimas", "TEAM_NAME": "<PERSON><PERSON><PERSON> p<PERSON>", "CONVERSATION_IDENTIFIER": "Pokalbio identifikatorius", "CAMPAIGN_NAME": "<PERSON><PERSON><PERSON><PERSON>", "LABELS": "Etiketė<PERSON>", "BROWSER_LANGUAGE": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PRIORITY": "Prioritetas", "COUNTRY_NAME": "<PERSON><PERSON><PERSON>", "REFERER_LINK": "<PERSON><PERSON><PERSON> nuoroda", "CUSTOM_ATTRIBUTE_LIST": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "CUSTOM_ATTRIBUTE_TEXT": "Tekstas", "CUSTOM_ATTRIBUTE_NUMBER": "Numeris", "CUSTOM_ATTRIBUTE_LINK": "<PERSON><PERSON><PERSON><PERSON>", "CUSTOM_ATTRIBUTE_CHECKBOX": "<PERSON><PERSON><PERSON><PERSON> langelis", "CREATED_AT": "Sukurta", "LAST_ACTIVITY": "Paskutiniai veiksmai"}, "ERRORS": {"VALUE_REQUIRED": "Reikalinga vertė", "ATTRIBUTE_KEY_REQUIRED": "Attribute key is required", "FILTER_OPERATOR_REQUIRED": "Filter operator is required", "VALUE_MUST_BE_BETWEEN_1_AND_998": "Value must be between 1 and 998"}, "GROUPS": {"STANDARD_FILTERS": "<PERSON><PERSON><PERSON><PERSON>", "ADDITIONAL_FILTERS": "<PERSON><PERSON><PERSON><PERSON>", "CUSTOM_ATTRIBUTES": "Personalizuoti <PERSON>i"}, "CUSTOM_VIEWS": {"ADD": {"TITLE": "Ar nori iš<PERSON>ugoti šį filtrą?", "LABEL": "Suteikti filtrui pavadin<PERSON>", "PLACEHOLDER": "Name your filter to refer it later.", "ERROR_MESSAGE": "<PERSON><PERSON> vardas.", "SAVE_BUTTON": "Išsaugoti filtrą", "CANCEL_BUTTON": "<PERSON><PERSON><PERSON><PERSON>", "API_FOLDERS": {"SUCCESS_MESSAGE": "<PERSON><PERSON><PERSON><PERSON> sukurta<PERSON>.", "ERROR_MESSAGE": "<PERSON><PERSON>t aplanką įvyko klaida."}, "API_SEGMENTS": {"SUCCESS_MESSAGE": "Segmentas sėkmingai sukurtas.", "ERROR_MESSAGE": "Kuriant segmentą įvyko klaida."}}, "EDIT": {"EDIT_BUTTON": "Redaguoti aplanką"}, "DELETE": {"DELETE_BUTTON": "<PERSON><PERSON> fi<PERSON>", "MODAL": {"CONFIRM": {"TITLE": "<PERSON><PERSON><PERSON><PERSON>", "MESSAGE": "Ar tikrai norite ištrinti filtrą ", "YES": "<PERSON>p, Trinti", "NO": "Ne, Išsaugoti"}}, "API_FOLDERS": {"SUCCESS_MESSAGE": "Aplankas sėkmingai ištrintas.", "ERROR_MESSAGE": "Trinant aplanką įvyko klaida."}, "API_SEGMENTS": {"SUCCESS_MESSAGE": "Segmentas i<PERSON><PERSON>.", "ERROR_MESSAGE": "Trinant segmentą įvyko klaida."}}}}}