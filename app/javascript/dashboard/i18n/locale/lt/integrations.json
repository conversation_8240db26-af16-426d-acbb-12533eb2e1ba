{"INTEGRATION_SETTINGS": {"SHOPIFY": {"DELETE": {"TITLE": "Delete Shopify Integration", "MESSAGE": "Are you sure you want to delete the Shopify integration?"}, "STORE_URL": {"TITLE": "Connect Shopify Store", "LABEL": "Store URL", "PLACEHOLDER": "your-store.myshopify.com", "HELP": "Enter your Shopify store's myshopify.com URL", "CANCEL": "<PERSON><PERSON><PERSON><PERSON>", "SUBMIT": "Connect Store"}, "ERROR": "There was an error connecting to Shopify. Please try again or contact support if the issue persists."}, "HEADER": "Integracijos", "DESCRIPTION": "Chatwoot integrates with multiple tools and services to improve your team's efficiency. Explore the list below to configure your favorite apps.", "LEARN_MORE": "Learn more about integrations", "LOADING": "Fetching integrations", "CAPTAIN": {"DISABLED": "Captain is not enabled on your account.", "CLICK_HERE_TO_CONFIGURE": "Click here to configure", "LOADING_CONSOLE": "Loading Captain <PERSON><PERSON><PERSON>...", "FAILED_TO_LOAD_CONSOLE": "Failed to load Captain <PERSON><PERSON>. Please refresh and try again."}, "WEBHOOK": {"SUBSCRIBED_EVENTS": "Prenumeruoti įvykiai", "LEARN_MORE": "Learn more about webhooks", "FORM": {"CANCEL": "<PERSON><PERSON><PERSON><PERSON>", "DESC": "Webhook įvykiai suteikia jums realaus laiko informaciją apie tai, kas vyksta jūsų Chatwoot paskyroje. Norėdami sukonfigūruoti atgalinį skambinimą, įveskite tinkamą URL.", "SUBSCRIPTIONS": {"LABEL": "Įvykiai", "EVENTS": {"CONVERSATION_CREATED": "Pokalbis sukurtas", "CONVERSATION_STATUS_CHANGED": "<PERSON><PERSON><PERSON> b<PERSON>a pake<PERSON>", "CONVERSATION_UPDATED": "Pokalbis atnaujintas", "MESSAGE_CREATED": "Pranešimas sukurtas", "MESSAGE_UPDATED": "Pranešimas atnaujintas", "WEBWIDGET_TRIGGERED": "<PERSON><PERSON>iogin<PERSON>, kurį at<PERSON><PERSON><PERSON>", "CONTACT_CREATED": "<PERSON><PERSON><PERSON><PERSON> kont<PERSON>", "CONTACT_UPDATED": "Kontaktas <PERSON>na<PERSON>jin<PERSON>", "CONVERSATION_TYPING_ON": "Conversation Typing On", "CONVERSATION_TYPING_OFF": "Conversation Typing Off"}}, "END_POINT": {"LABEL": "Webhook URL", "PLACEHOLDER": "Example: {webhookExampleURL}", "ERROR": "Prašome įvesti tesingą URL adresą"}, "EDIT_SUBMIT": "<PERSON><PERSON><PERSON><PERSON><PERSON> webhook", "ADD_SUBMIT": "<PERSON><PERSON><PERSON><PERSON> webhook"}, "TITLE": "Webhook", "CONFIGURE": "Konfigūruoti", "HEADER": "Webhook nustatymai", "HEADER_BTN_TXT": "Pridėti naują webhook", "LOADING": "Pridėtų webhooks gavimas", "SEARCH_404": "<PERSON><PERSON><PERSON> už<PERSON>ą atitinkančių elementų nėra", "SIDEBAR_TXT": "<p><b>Webhooks</b> </p> <p>Webhooks yra HTTP už<PERSON>, kurias galima nustatyti kiekvienai paskyrai. <PERSON><PERSON> suakty<PERSON> tokie įvykiai kaip praneš<PERSON> kūrimas Chatwoot. Šiai paskyrai galite sukurti daugiau nei vieną webhook. <br /><br /> Norėdami sukurti <b>webhook</b>, spustelėkite mygtuką <b>Pridėti naują webhook</b>. Taip pat galite pašalinti bet kurį esamą webhook spustelėdami mygtuką Ištrinti.</p>", "LIST": {"404": "Šioje paskyroje nėra sukonfigūruotų webhook.", "TITLE": "Tvarkykite webhooks", "TABLE_HEADER": {"WEBHOOK_ENDPOINT": "Webhook endpoint", "ACTIONS": "Veiks<PERSON><PERSON>"}}, "EDIT": {"BUTTON_TEXT": "Red<PERSON><PERSON><PERSON>", "TITLE": "Redaguoti webhook", "API": {"SUCCESS_MESSAGE": "Webhook konfigūracija sėkmingai atnaujinta", "ERROR_MESSAGE": "Nepavyko prisijungti prie Woot serverio, bandykite dar kartą vėliau"}}, "ADD": {"CANCEL": "<PERSON><PERSON><PERSON><PERSON>", "TITLE": "Pridėti naują webhook", "API": {"SUCCESS_MESSAGE": "Webhook konfigūracija pridėta <PERSON>kmingai", "ERROR_MESSAGE": "Nepavyko prisijungti prie Woot serverio, bandykite dar kartą vėliau"}}, "DELETE": {"BUTTON_TEXT": "<PERSON><PERSON><PERSON><PERSON>", "API": {"SUCCESS_MESSAGE": "Webhook ištrintas <PERSON>", "ERROR_MESSAGE": "Nepavyko prisijungti prie Woot serverio, bandykite dar kartą vėliau"}, "CONFIRM": {"TITLE": "<PERSON><PERSON><PERSON><PERSON>", "MESSAGE": "Ar tikrai norite ištrinti webhook? ({webhookURL})", "YES": "<PERSON>p, Trinti ", "NO": "Ne, Išsaugoti"}}}, "SLACK": {"DELETE": "<PERSON><PERSON><PERSON><PERSON>", "DELETE_CONFIRMATION": {"TITLE": "Ištrinkite integraciją", "MESSAGE": "Ar tikrai norite ištrinti integraciją? Tai padarius prarasite prieigą prie pokalbių Slack darbo erdvėje."}, "HELP_TEXT": {"TITLE": "Slack integracijos naudo<PERSON>", "BODY": "With this integration, all of your incoming conversations will be synced to the ***{selectedChannelName}*** channel in your Slack workspace. You can manage all your customer conversations right within the channel and never miss a message.\n\nHere are the main features of the integration:\n\n**Respond to conversations from within Slack:** To respond to a conversation in the ***{selectedChannelName}*** Slack channel, simply type out your message and send it as a thread. This will create a response back to the customer through Chatwoot. It's that simple!\n\n **Create private notes:** If you want to create private notes instead of replies, start your message with ***`note:`***. This ensures that your message is kept private and won't be visible to the customer.\n\n**Associate an agent profile:** If the person who replied on Slack has an agent profile in Chatwoot under the same email, the replies will be associated with that agent profile automatically. This means you can easily track who said what and when. On the other hand, when the replier doesn't have an associated agent profile, the replies will appear from the bot profile to the customer.", "SELECTED": "pasirinkta"}, "SELECT_CHANNEL": {"OPTION_LABEL": "Pasirinkite kanalą", "UPDATE": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "BUTTON_TEXT": "Prijunkite kanalą", "DESCRIPTION": "Your Slack workspace is now linked with Chatwoot. However, the integration is currently inactive. To activate the integration and connect a channel to Chatwoot, please click the button below.\n\n**Note:** If you are attempting to connect a private channel, add the Chatwoot app to the Slack channel before proceeding with this step.", "ATTENTION_REQUIRED": "Reikalingas <PERSON>", "EXPIRED": "Jūsų Slack integracijos galiojimo laikas b<PERSON>. Norėdami ir toliau gauti p<PERSON>, ištrinkite integraciją ir vėl prijunkite savo darbo erdvę."}, "UPDATE_ERROR": "Atnaujinant integraciją įvyko klaida, bandykite dar kartą", "UPDATE_SUCCESS": "Kanalas sėkmingai prijungtas", "FAILED_TO_FETCH_CHANNELS": "Gaunant kanalus i<PERSON> įvyko klaida, bandykite dar kartą"}, "DYTE": {"CLICK_HERE_TO_JOIN": "Spausti čia kad prisijungti", "LEAVE_THE_ROOM": "Išeiti i<PERSON> kamba<PERSON>", "START_VIDEO_CALL_HELP_TEXT": "Pradėkite naują vaizdo skambutį su klientu", "JOIN_ERROR": "Prisijungiant prie skamb<PERSON><PERSON> įvyko klaida, bandykite dar kartą", "CREATE_ERROR": "Įvyko klaida kuriant susitikimo nuorod<PERSON>, pabandykite dar kartą"}, "OPEN_AI": {"AI_ASSIST": "AI pagalba", "WITH_AI": " {option} with AI ", "OPTIONS": {"REPLY_SUGGESTION": "<PERSON><PERSON><PERSON><PERSON>", "SUMMARIZE": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "REPHRASE": "Pagerinkite rašymą", "FIX_SPELLING_GRAMMAR": "Pataisykite rašybą ir gramatiką", "SHORTEN": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "EXPAND": "Išskleisti", "MAKE_FRIENDLY": "Pakeisti p<PERSON> į draugišką", "MAKE_FORMAL": "Naudokite oficialų toną", "SIMPLIFY": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "ASSISTANCE_MODAL": {"DRAFT_TITLE": "<PERSON><PERSON>", "GENERATED_TITLE": "<PERSON><PERSON><PERSON><PERSON><PERSON> turinys", "AI_WRITING": "AI rašo", "BUTTONS": {"APPLY": "<PERSON><PERSON><PERSON> šį pasiūly<PERSON>ą", "CANCEL": "<PERSON><PERSON><PERSON><PERSON>"}}, "CTA_MODAL": {"TITLE": "Integruoti su OpenAI", "DESC": "Bring advanced AI features to your dashboard with OpenAI's GPT models. To begin, enter the API key from your OpenAI account.", "KEY_PLACEHOLDER": "Įveskite savo OpenAI API raktą", "BUTTONS": {"NEED_HELP": "<PERSON><PERSON>a paga<PERSON>?", "DISMISS": "Atsisakyti", "FINISH": "<PERSON><PERSON><PERSON> nustatymus"}, "DISMISS_MESSAGE": "You can setup OpenAI integration later Whenever you want.", "SUCCESS_MESSAGE": "OpenAI integration setup successfully"}, "TITLE": "Tobulinti naudojant AI", "SUMMARY_TITLE": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "REPLY_TITLE": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>", "SUBTITLE": "Patobulintas atsakymas bus sukurtas naudojant AI, re<PERSON><PERSON> da<PERSON> juo<PERSON>.", "TONE": {"TITLE": "<PERSON><PERSON>", "OPTIONS": {"PROFESSIONAL": "Profesionalas", "FRIENDLY": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "BUTTONS": {"GENERATE": "Generu<PERSON><PERSON>", "GENERATING": "Sukurti...", "CANCEL": "<PERSON><PERSON><PERSON><PERSON>"}, "GENERATE_ERROR": "Įvyko k<PERSON>a a<PERSON> turinį, pabandykite dar kartą"}, "DELETE": {"BUTTON_TEXT": "<PERSON><PERSON><PERSON><PERSON>", "API": {"SUCCESS_MESSAGE": "Integracija sėkmingai ištrinta"}}, "CONNECT": {"BUTTON_TEXT": "<PERSON><PERSON><PERSON><PERSON>"}, "DASHBOARD_APPS": {"TITLE": "Informacinio skydelio programos", "HEADER_BTN_TXT": "Pridėkite naują informacinio skydelio programą", "SIDEBAR_TXT": "<p><b>Informacinio skydelio programos</b></p><p>Informacinio skydelio programos leidžia organizacijoms įterpti programą į Chatwood valdymo skydelį, kad klientų aptarnavimo agentams būtų suteiktas kontekstas. Ši funkcija leidžia savarankiškai kurti programą ir įterpti informaciją apie vartotoją, jo užsakymus ar mokėjimų istoriją.</p><p>Kai įterpiate programą naudodami Chatwood valdymo skydelį, programa gaus pokalbio kontekstą ir įvykio nuorodas. Įdiekite pranešimo įvykio nuorodas savo puslapyje, kad gautumėte kontekstą.</p><p>Jei norite pridėti naują informacinio skydelio programą, spustelėkite mygtuką \"Pridėti naują skydelio programą\".</p>", "DESCRIPTION": "Informacinio skydelio programos leidžia organizacijoms įterpti programą į valdymo skydelį, kad klientų aptarnavimo agentams būtų suteiktas kontekstas. Ši funkcija leidžia savarankiškai kurti programą ir įterpti informaciją apie vartotoją, jo užsakymus ar mokėjimų istoriją.", "LEARN_MORE": "Learn more about Dashboard Apps", "LIST": {"404": "Šioje paskyroje nėra sukonfigūruotų informacinio skydelio programų", "LOADING": "Gaunamos informacinio skydelio programos...", "TABLE_HEADER": {"NAME": "Vardas", "ENDPOINT": "Endpoint"}, "EDIT_TOOLTIP": "Redaguoti programą", "DELETE_TOOLTIP": "Ištrinti programą"}, "FORM": {"TITLE_LABEL": "Vardas", "TITLE_PLACEHOLDER": "Įveskite informacinio skydelio programos pavadinimą", "TITLE_ERROR": "Reikalingas informacinio skydelio programos pavadinimas", "URL_LABEL": "Endpoint", "URL_PLACEHOLDER": "Įveskite URL adresą, kuri<PERSON> pasiekiama jūsų programa", "URL_ERROR": "Reikalingas tinkamas URL"}, "CREATE": {"HEADER": "Pridėkite naują informacinio skydelio programą", "FORM_SUBMIT": "Pat<PERSON><PERSON><PERSON>", "FORM_CANCEL": "<PERSON><PERSON><PERSON><PERSON>", "API_SUCCESS": "Informacinio skydelio programa sėkmingai sukonfiguruota", "API_ERROR": "Nepavyko sukurti programos. Pabandykite dar kartą vėliau"}, "UPDATE": {"HEADER": "Tvarkyti informacinio skydelio programą", "FORM_SUBMIT": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FORM_CANCEL": "<PERSON><PERSON><PERSON><PERSON>", "API_SUCCESS": "Informacinio skydelio programa atnaujinta sėkmingai", "API_ERROR": "Nepavyko atnaujinti programos. Pabandykite dar kartą vėliau"}, "DELETE": {"CONFIRM_YES": "<PERSON>p, Trinti", "CONFIRM_NO": "Ne, Išsaugoti", "TITLE": "<PERSON><PERSON><PERSON><PERSON>", "MESSAGE": "Ar tikrai norite ištrinti programą - {appName}?", "API_SUCCESS": "Informacinio skydelio programa ištrinta sėkmingai", "API_ERROR": "Nepavyko ištrinti programos. Pabandykite dar kartą vėliau"}}, "LINEAR": {"ADD_OR_LINK_BUTTON": "Create/Link Linear Issue", "LOADING": "Fetching linear issues...", "LOADING_ERROR": "There was an error fetching the linear issues, please try again", "CREATE": "<PERSON><PERSON><PERSON><PERSON>", "LINK": {"SEARCH": "Search issues", "SELECT": "Select issue", "TITLE": "<PERSON><PERSON><PERSON><PERSON>", "EMPTY_LIST": "No linear issues found", "LOADING": "Loading", "ERROR": "There was an error fetching the linear issues, please try again", "LINK_SUCCESS": "Issue linked successfully", "LINK_ERROR": "There was an error linking the issue, please try again", "LINK_TITLE": "Conversation (#{conversationId}) with {name}"}, "ADD_OR_LINK": {"TITLE": "Create/link linear issue", "DESCRIPTION": "Create Linear issues from conversations, or link existing ones for seamless tracking.", "FORM": {"TITLE": {"LABEL": "Pavadinimas", "PLACEHOLDER": "Enter title", "REQUIRED_ERROR": "<PERSON><PERSON> re<PERSON> pavadin<PERSON>s"}, "DESCRIPTION": {"LABEL": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PLACEHOLDER": "Enter description"}, "TEAM": {"LABEL": "<PERSON><PERSON><PERSON>", "PLACEHOLDER": "Pasirinkite komandą", "SEARCH": "Search team", "REQUIRED_ERROR": "Team is required"}, "ASSIGNEE": {"LABEL": "Assignee", "PLACEHOLDER": "Select assignee", "SEARCH": "Search assignee"}, "PRIORITY": {"LABEL": "Prioritetas", "PLACEHOLDER": "Pasirinkti prioritetą", "SEARCH": "Search priority"}, "LABEL": {"LABEL": "Etiketė", "PLACEHOLDER": "Select label", "SEARCH": "Search label"}, "STATUS": {"LABEL": "<PERSON><PERSON><PERSON><PERSON>", "PLACEHOLDER": "Select status", "SEARCH": "Search status"}, "PROJECT": {"LABEL": "Project", "PLACEHOLDER": "Select project", "SEARCH": "Search project"}}, "CREATE": "<PERSON><PERSON><PERSON><PERSON>", "CANCEL": "<PERSON><PERSON><PERSON><PERSON>", "CREATE_SUCCESS": "Issue created successfully", "CREATE_ERROR": "There was an error creating the issue, please try again", "LOADING_TEAM_ERROR": "There was an error fetching the teams, please try again", "LOADING_TEAM_ENTITIES_ERROR": "There was an error fetching the team entities, please try again"}, "ISSUE": {"STATUS": "<PERSON><PERSON><PERSON><PERSON>", "PRIORITY": "Prioritetas", "ASSIGNEE": "Assignee", "LABELS": "Etiketė<PERSON>", "CREATED_AT": "Created at {createdAt}"}, "UNLINK": {"TITLE": "Unlink", "SUCCESS": "Issue unlinked successfully", "ERROR": "There was an error unlinking the issue, please try again"}, "DELETE": {"TITLE": "Are you sure you want to delete the integration?", "MESSAGE": "Are you sure you want to delete the integration?", "CONFIRM": "<PERSON>p, Trinti", "CANCEL": "<PERSON><PERSON><PERSON><PERSON>"}}}, "CAPTAIN": {"NAME": "Captain", "HEADER_KNOW_MORE": "Know more", "COPILOT": {"SEND_MESSAGE": "Išsiųsti pranešimą...", "EMPTY_MESSAGE": "There was an error generating the response. Please try again.", "LOADER": "Captain is thinking", "YOU": "<PERSON><PERSON><PERSON>", "USE": "Use this", "RESET": "Reset", "SELECT_ASSISTANT": "Select Assistant", "PROMPTS": {"SUMMARIZE": {"LABEL": "Summarize this conversation", "CONTENT": "Summarize the key points discussed between the customer and the support agent, including the customer's concerns, questions, and the solutions or responses provided by the support agent"}, "SUGGEST": {"LABEL": "Suggest an answer", "CONTENT": "Analyze the customer's inquiry, and draft a response that effectively addresses their concerns or questions. Ensure the reply is clear, concise, and provides helpful information."}, "RATE": {"LABEL": "Rate this conversation", "CONTENT": "Review the conversation to see how well it meets the customer's needs. Share a rating out of 5 based on tone, clarity, and effectiveness."}}}, "PLAYGROUND": {"USER": "<PERSON><PERSON><PERSON>", "ASSISTANT": "Assistant", "MESSAGE_PLACEHOLDER": "Parašykite pranešimą...", "HEADER": "Playground", "DESCRIPTION": "Use this playground to send messages to your assistant and check if it responds accurately, quickly, and in the tone you expect.", "CREDIT_NOTE": "Messages sent here will count toward your Captain credits."}, "PAYWALL": {"TITLE": "Upgrade to use Captain AI", "AVAILABLE_ON": "Captain is not available on the free plan.", "UPGRADE_PROMPT": "Upgrade your plan to get access to our assistants, copilot and more.", "UPGRADE_NOW": "Upgrade now", "CANCEL_ANYTIME": "You can change or cancel your plan anytime"}, "ENTERPRISE_PAYWALL": {"AVAILABLE_ON": "Captain AI feature is only available in a paid plan.", "UPGRADE_PROMPT": "Upgrade your plan to get access to our assistants, copilot and more.", "ASK_ADMIN": "Please reach out to your administrator for the upgrade."}, "BANNER": {"RESPONSES": "You've used over 80% of your response limit. To continue using Captain AI, please upgrade.", "DOCUMENTS": "Document limit reached. Upgrade to continue using Captain AI."}, "FORM": {"CANCEL": "<PERSON><PERSON><PERSON><PERSON>", "CREATE": "<PERSON><PERSON><PERSON><PERSON>", "EDIT": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "ASSISTANTS": {"HEADER": "Assistants", "ADD_NEW": "Create a new assistant", "DELETE": {"TITLE": "Are you sure to delete the assistant?", "DESCRIPTION": "This action is permanent. Deleting this assistant will remove it from all connected inboxes and permanently erase all generated knowledge.", "CONFIRM": "<PERSON>p, Trinti", "SUCCESS_MESSAGE": "The assistant has been successfully deleted", "ERROR_MESSAGE": "There was an error deleting the assistant, please try again."}, "FORM_DESCRIPTION": "Fill out the details below to name your assistant, describe its purpose, and specify the product it will support.", "CREATE": {"TITLE": "Create an assistant", "SUCCESS_MESSAGE": "The assistant has been successfully created", "ERROR_MESSAGE": "There was an error creating the assistant, please try again."}, "FORM": {"UPDATE": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SECTIONS": {"BASIC_INFO": "Basic Information", "SYSTEM_MESSAGES": "System Messages", "INSTRUCTIONS": "Instructions", "FEATURES": "Funkcijos", "TOOLS": "Tools "}, "NAME": {"LABEL": "Vardas", "PLACEHOLDER": "Enter assistant name", "ERROR": "The name is required"}, "DESCRIPTION": {"LABEL": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PLACEHOLDER": "Enter assistant description", "ERROR": "The description is required"}, "PRODUCT_NAME": {"LABEL": "Product Name", "PLACEHOLDER": "Enter product name", "ERROR": "The product name is required"}, "WELCOME_MESSAGE": {"LABEL": "Welcome Message", "PLACEHOLDER": "Enter welcome message"}, "HANDOFF_MESSAGE": {"LABEL": "Handoff Message", "PLACEHOLDER": "Enter handoff message"}, "RESOLUTION_MESSAGE": {"LABEL": "Resolution Message", "PLACEHOLDER": "Enter resolution message"}, "INSTRUCTIONS": {"LABEL": "Instructions", "PLACEHOLDER": "Enter instructions for the assistant"}, "FEATURES": {"TITLE": "Funkcijos", "ALLOW_CONVERSATION_FAQS": "Generate FAQs from resolved conversations", "ALLOW_MEMORIES": "Capture key details as memories from customer interactions."}}, "EDIT": {"TITLE": "Update the assistant", "SUCCESS_MESSAGE": "The assistant has been successfully updated", "ERROR_MESSAGE": "There was an error updating the assistant, please try again.", "NOT_FOUND": "Could not find the assistant. Please try again."}, "OPTIONS": {"EDIT_ASSISTANT": "Edit Assistant", "DELETE_ASSISTANT": "Delete Assistant", "VIEW_CONNECTED_INBOXES": "View connected inboxes"}, "EMPTY_STATE": {"TITLE": "No assistants available", "SUBTITLE": "Create an assistant to provide quick and accurate responses to your users. It can learn from your help articles and past conversations.", "FEATURE_SPOTLIGHT": {"TITLE": "Captain Assistant", "NOTE": "Captain Assistant engages directly with customers, learns from your help docs and past conversations, and delivers instant, accurate responses. It handles the initial queries, providing quick resolutions before  transferring to an agent when needed."}}}, "DOCUMENTS": {"HEADER": "Documents", "ADD_NEW": "Create a new document", "RELATED_RESPONSES": {"TITLE": "Related FAQs", "DESCRIPTION": "These FAQs are generated directly from the document."}, "FORM_DESCRIPTION": "Enter the URL of the document to add it as a knowledge source and choose the assistant to associate it with.", "CREATE": {"TITLE": "Add a document", "SUCCESS_MESSAGE": "The document has been successfully created", "ERROR_MESSAGE": "There was an error creating the document, please try again."}, "FORM": {"URL": {"LABEL": "URL", "PLACEHOLDER": "Enter the URL of the document", "ERROR": "Please provide a valid URL for the document"}, "ASSISTANT": {"LABEL": "Assistant", "PLACEHOLDER": "Select the assistant", "ERROR": "The assistant field is required"}}, "DELETE": {"TITLE": "Are you sure to delete the document?", "DESCRIPTION": "This action is permanent. Deleting this document will permanently erase all generated knowledge.", "CONFIRM": "<PERSON>p, Trinti", "SUCCESS_MESSAGE": "The document has been successfully deleted", "ERROR_MESSAGE": "There was an error deleting the document, please try again."}, "OPTIONS": {"VIEW_RELATED_RESPONSES": "View Related Responses", "DELETE_DOCUMENT": "Delete Document"}, "EMPTY_STATE": {"TITLE": "No documents available", "SUBTITLE": "Documents are used by your assistant to generate FAQs. You can import documents to provide context for your assistant.", "FEATURE_SPOTLIGHT": {"TITLE": "Captain <PERSON>ument", "NOTE": "A document in Captain serves as a knowledge resource for the assistant. By connecting your help center or guides, Captain can analyze the content and provide accurate responses for customer inquiries."}}}, "RESPONSES": {"HEADER": "FAQs", "ADD_NEW": "Create new FAQ", "DOCUMENTABLE": {"CONVERSATION": "Conversation #{id}"}, "SELECTED": "{count} selected", "BULK_APPROVE_BUTTON": "Approve", "BULK_DELETE_BUTTON": "<PERSON><PERSON><PERSON><PERSON>", "BULK_APPROVE": {"SUCCESS_MESSAGE": "FAQs approved successfully", "ERROR_MESSAGE": "There was an error approving the FAQs, please try again."}, "BULK_DELETE": {"TITLE": "Delete FAQs?", "DESCRIPTION": "Are you sure you want to delete the selected FAQs? This action cannot be undone.", "CONFIRM": "Yes, delete all", "SUCCESS_MESSAGE": "FAQs deleted successfully", "ERROR_MESSAGE": "There was an error deleting the FAQs, please try again."}, "DELETE": {"TITLE": "Are you sure to delete the FAQ?", "DESCRIPTION": "", "CONFIRM": "<PERSON>p, Trinti", "SUCCESS_MESSAGE": "FAQ deleted successfully", "ERROR_MESSAGE": "There was an error deleting the FAQ, please try again."}, "FILTER": {"ASSISTANT": "Assistant: {selected}", "STATUS": "Status: {selected}", "ALL_ASSISTANTS": "Visi"}, "STATUS": {"TITLE": "<PERSON><PERSON><PERSON><PERSON>", "PENDING": "<PERSON><PERSON><PERSON>", "APPROVED": "Approved", "ALL": "Visi"}, "FORM_DESCRIPTION": "Add a question and its corresponding answer to the knowledge base and select the assistant it should be associated with.", "CREATE": {"TITLE": "Add an FAQ", "SUCCESS_MESSAGE": "The response has been added successfully.", "ERROR_MESSAGE": "An error occurred while adding the response. Please try again."}, "FORM": {"QUESTION": {"LABEL": "Question", "PLACEHOLDER": "Enter the question here", "ERROR": "Please provide a valid question."}, "ANSWER": {"LABEL": "Answer", "PLACEHOLDER": "Enter the answer here", "ERROR": "Please provide a valid answer."}, "ASSISTANT": {"LABEL": "Assistant", "PLACEHOLDER": "Select an assistant", "ERROR": "Please select an assistant."}}, "EDIT": {"TITLE": "Update the FAQ", "SUCCESS_MESSAGE": "The FAQ has been successfully updated", "ERROR_MESSAGE": "There was an error updating the FAQ, please try again", "APPROVE_SUCCESS_MESSAGE": "The FAQ was marked as approved"}, "OPTIONS": {"APPROVE": "Mark as approved", "EDIT_RESPONSE": "Edit FAQ", "DELETE_RESPONSE": "Delete FAQ"}, "EMPTY_STATE": {"TITLE": "No FAQs Found", "SUBTITLE": "FAQs help your assistant provide quick and accurate answers to questions from your customers. They can be generated automatically from your content or can be added manually.", "FEATURE_SPOTLIGHT": {"TITLE": "Captain FAQ", "NOTE": "Captain <PERSON><PERSON><PERSON> detects common customer questions—whether missing from your knowledge base or frequently asked—and generates relevant FAQs to improve support. You can review each suggestion and decide whether to approve or reject it."}}}, "INBOXES": {"HEADER": "Connected Inboxes", "ADD_NEW": "Connect a new inbox", "OPTIONS": {"DISCONNECT": "<PERSON>si<PERSON><PERSON><PERSON>"}, "DELETE": {"TITLE": "Are you sure to disconnect the inbox?", "DESCRIPTION": "", "CONFIRM": "<PERSON>p, Trinti", "SUCCESS_MESSAGE": "The inbox was successfully disconnected.", "ERROR_MESSAGE": "There was an error disconnecting the inbox, please try again."}, "FORM_DESCRIPTION": "Choose an inbox to connect with the assistant.", "CREATE": {"TITLE": "Connect an Inbox", "SUCCESS_MESSAGE": "The inbox was successfully connected.", "ERROR_MESSAGE": "An error occurred while connecting the inbox. Please try again."}, "FORM": {"INBOX": {"LABEL": "Gautų laiškų aplankas", "PLACEHOLDER": "Choose the inbox to deploy the assistant.", "ERROR": "An inbox selection is required."}}, "EMPTY_STATE": {"TITLE": "No Connected Inboxes", "SUBTITLE": "Connecting an inbox allows the assistant to handle initial questions from your customers before transferring them to you."}}}}