{"CONTACTS_FILTER": {"TITLE": "Filtruoti Kontaktus", "SUBTITLE": "Toliau pridėkite filtrus ir paspauskite \"Pateikti\", kad filtruotum<PERSON><PERSON> kontaktus.", "EDIT_CUSTOM_SEGMENT": "Redaguoti segmentą", "CUSTOM_VIEWS_SUBTITLE": "Pridėkite arba pašalinkite filtrus ir atnaujinkite segmentą.", "ADD_NEW_FILTER": "<PERSON><PERSON><PERSON><PERSON>", "CLEAR_ALL_FILTERS": "<PERSON>š<PERSON><PERSON>i visus filtrus", "FILTER_DELETE_ERROR": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> turėti bent vieną filtrą, kad <PERSON> i<PERSON>i", "SUBMIT_BUTTON_LABEL": "Pat<PERSON><PERSON><PERSON>", "UPDATE_BUTTON_LABEL": "Atnaujinti segmentą", "CANCEL_BUTTON_LABEL": "<PERSON><PERSON><PERSON><PERSON>", "CLEAR_BUTTON_LABEL": "Išvalyti filtrus", "EMPTY_VALUE_ERROR": "Reikalinga vertė", "SEGMENT_LABEL": "Segmento <PERSON>", "SEGMENT_QUERY_LABEL": "Segmento užklausa", "TOOLTIP_LABEL": "<PERSON>lt<PERSON><PERSON><PERSON> kont<PERSON>", "QUERY_DROPDOWN_LABELS": {"AND": "IR", "OR": "AR"}, "OPERATOR_LABELS": {"equal_to": "Lygu", "not_equal_to": "<PERSON><PERSON><PERSON>", "contains": "Sudėtyje yra", "does_not_contain": "Sudėtyje nėra", "is_present": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "is_not_present": "Nepateikiamas", "is_greater_than": "<PERSON><PERSON> nei", "is_lesser_than": "<PERSON><PERSON>i", "days_before": "<PERSON><PERSON> p<PERSON> x dienų"}, "ERRORS": {"VALUE_REQUIRED": "Reikalinga vertė"}, "ATTRIBUTES": {"NAME": "Vardas", "EMAIL": "El. <PERSON>", "PHONE_NUMBER": "Telefono numeris", "IDENTIFIER": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "CITY": "Miestas", "COUNTRY": "<PERSON><PERSON>", "CUSTOM_ATTRIBUTE_LIST": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "CUSTOM_ATTRIBUTE_TEXT": "Tekstas", "CUSTOM_ATTRIBUTE_NUMBER": "Numeris", "CUSTOM_ATTRIBUTE_LINK": "<PERSON><PERSON><PERSON><PERSON>", "CUSTOM_ATTRIBUTE_CHECKBOX": "<PERSON><PERSON><PERSON><PERSON> langelis", "CREATED_AT": "Sukūrimo data", "LAST_ACTIVITY": "Paskutiniai veiksmai", "REFERER_LINK": "Rekomendacijos nuoroda", "BLOCKED": "Blocked"}, "GROUPS": {"STANDARD_FILTERS": "<PERSON><PERSON><PERSON><PERSON>", "ADDITIONAL_FILTERS": "<PERSON><PERSON><PERSON><PERSON>", "CUSTOM_ATTRIBUTES": "Personalizuoti <PERSON>i"}}}