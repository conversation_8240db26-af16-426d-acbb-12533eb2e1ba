{"INTEGRATION_SETTINGS": {"SHOPIFY": {"DELETE": {"TITLE": "Delete Shopify Integration", "MESSAGE": "Are you sure you want to delete the Shopify integration?"}, "STORE_URL": {"TITLE": "Connect Shopify Store", "LABEL": "Store URL", "PLACEHOLDER": "your-store.myshopify.com", "HELP": "Enter your Shopify store's myshopify.com URL", "CANCEL": "<PERSON><PERSON><PERSON><PERSON>", "SUBMIT": "Connect Store"}, "ERROR": "There was an error connecting to Shopify. Please try again or contact support if the issue persists."}, "HEADER": "Integrációk", "DESCRIPTION": "Chatwoot integrates with multiple tools and services to improve your team's efficiency. Explore the list below to configure your favorite apps.", "LEARN_MORE": "Learn more about integrations", "LOADING": "Fetching integrations", "CAPTAIN": {"DISABLED": "Captain is not enabled on your account.", "CLICK_HERE_TO_CONFIGURE": "Click here to configure", "LOADING_CONSOLE": "Loading Captain <PERSON><PERSON><PERSON>...", "FAILED_TO_LOAD_CONSOLE": "Failed to load Captain <PERSON><PERSON>. Please refresh and try again."}, "WEBHOOK": {"SUBSCRIBED_EVENTS": "Feliratkozott események", "LEARN_MORE": "Learn more about webhooks", "FORM": {"CANCEL": "<PERSON><PERSON><PERSON><PERSON>", "DESC": "Webhook események valós idejű információt adnak arról, hogy mi történik a Chatwoot fiókodban. Kérünk a visszahívás beállításánál egy helyes URL-t adj meg.", "SUBSCRIPTIONS": {"LABEL": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "EVENTS": {"CONVERSATION_CREATED": "Beszélgetés létrehozva", "CONVERSATION_STATUS_CHANGED": "Beszélgetés státusza megváltozott", "CONVERSATION_UPDATED": "Beszélgetés frissítve", "MESSAGE_CREATED": "Üzenet létrehozva", "MESSAGE_UPDATED": "Üzenet frissítve", "WEBWIDGET_TRIGGERED": "A felhasználó által megnyitott élő chat widget", "CONTACT_CREATED": "Kontakt létrehozva", "CONTACT_UPDATED": "Kontakt frissítve", "CONVERSATION_TYPING_ON": "Conversation Typing On", "CONVERSATION_TYPING_OFF": "Conversation Typing Off"}}, "END_POINT": {"LABEL": "Webhook URL", "PLACEHOLDER": "Example: {webhookExampleURL}", "ERROR": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> helyes URL-t adj meg"}, "EDIT_SUBMIT": "Webhook frissítése", "ADD_SUBMIT": "Webhook létrehozása"}, "TITLE": "Webhook", "CONFIGURE": "Beállítások", "HEADER": "Webhook beállítások", "HEADER_BTN_TXT": "Új webhook hozzáadása", "LOADING": "Kapcsolódó Webhookok betöltése", "SEARCH_404": "<PERSON><PERSON><PERSON> meg<PERSON> elem", "SIDEBAR_TXT": "<p><b>Webhook</b>-ok</p><p>A Webhook-ok HTTP visszahívások, melyek minden fiókhoz bállíthatóak. Chatwoot események, például beérkező üzenetek aktiválhatják a webhookokat. Több webhookot is beállíthatsz a fiókodhoz. <br /><br /> Egy <b>webhook</b> létrehozásához kattints a <b>Webhook hozzáadása</b> gombra. Le is tör<PERSON>lheted a létrejött webhookokat a Törlés gombra kattintva.</p>", "LIST": {"404": "Nincs a fiókhoz rendelt Webhook.", "TITLE": "Webhook kezelés", "TABLE_HEADER": {"WEBHOOK_ENDPOINT": "Webhook végpont", "ACTIONS": "Műveletek"}}, "EDIT": {"BUTTON_TEXT": "Szerkesztés", "TITLE": "Webhook szerkesztése", "API": {"SUCCESS_MESSAGE": "A Webhook konfigurációja sikeresen frissítve", "ERROR_MESSAGE": "<PERSON><PERSON> csatlakozni a Woot szerverhez, kérjük pr<PERSON><PERSON><PERSON><PERSON> k<PERSON>"}}, "ADD": {"CANCEL": "<PERSON><PERSON><PERSON><PERSON>", "TITLE": "Új webhook hozzáadása", "API": {"SUCCESS_MESSAGE": "A Webhook konfigurációja sikeresen hozzáadva", "ERROR_MESSAGE": "<PERSON><PERSON> csatlakozni a Woot szerverhez, kérjük pr<PERSON><PERSON><PERSON><PERSON> k<PERSON>"}}, "DELETE": {"BUTTON_TEXT": "Törlés", "API": {"SUCCESS_MESSAGE": "Webhook sikeresen törölve", "ERROR_MESSAGE": "<PERSON><PERSON> csatlakozni a Woot szerverhez, kérjük pr<PERSON><PERSON><PERSON><PERSON> k<PERSON>"}, "CONFIRM": {"TITLE": "Törlés megerősítése", "MESSAGE": "Biztosan törölni szeretnéd a webhookot? ({webhookURL})", "YES": "Igen, Törlés ", "NO": "<PERSON>em, tartsa meg"}}}, "SLACK": {"DELETE": "Törlés", "DELETE_CONFIRMATION": {"TITLE": "Az integráció törlése", "MESSAGE": "<PERSON><PERSON><PERSON>, hogy törölni szeretné az integrációt? Ha így tesz, <PERSON><PERSON><PERSON><PERSON> a Slack munkaterületén lévő beszé<PERSON>ez való hozzáférést."}, "HELP_TEXT": {"TITLE": "Using Slack Integration", "BODY": "With this integration, all of your incoming conversations will be synced to the ***{selectedChannelName}*** channel in your Slack workspace. You can manage all your customer conversations right within the channel and never miss a message.\n\nHere are the main features of the integration:\n\n**Respond to conversations from within Slack:** To respond to a conversation in the ***{selectedChannelName}*** Slack channel, simply type out your message and send it as a thread. This will create a response back to the customer through Chatwoot. It's that simple!\n\n **Create private notes:** If you want to create private notes instead of replies, start your message with ***`note:`***. This ensures that your message is kept private and won't be visible to the customer.\n\n**Associate an agent profile:** If the person who replied on Slack has an agent profile in Chatwoot under the same email, the replies will be associated with that agent profile automatically. This means you can easily track who said what and when. On the other hand, when the replier doesn't have an associated agent profile, the replies will appear from the bot profile to the customer.", "SELECTED": "kiválasztás"}, "SELECT_CHANNEL": {"OPTION_LABEL": "Csatorna kiválasztása", "UPDATE": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "BUTTON_TEXT": "Csatlakozás a csatornához", "DESCRIPTION": "A Slack munkaterülete mostantól összekapcsolódik a Chatwoot-tal. Az integráció azonban jelenleg inaktív. Az aktiválásához és egy Chatwoot-hoz való csatlakoztatásához kattintson az alábbi gombra.\n\n**Figyelem:** Ha egy privát csatornát próbál csatlakoztatni, akkor a Chatwoot alkalmazást adja hozzá a Slack-csatornához, mielőtt ezt a lépést folytatná.", "ATTENTION_REQUIRED": "Figyelem szükséges", "EXPIRED": "A Slack integr<PERSON><PERSON><PERSON> lej<PERSON>rt. Ha továbbra is szeretne üzeneteket kapni a Slacken, törölje az integrációt, és csatlakoztassa újra a munkaterületét."}, "UPDATE_ERROR": "Hiba történt az integrá<PERSON>ó frissítésében, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "UPDATE_SUCCESS": "A csatorna si<PERSON>esen c<PERSON><PERSON>", "FAILED_TO_FETCH_CHANNELS": "Hiba történt a csatornák lekérdezésében a Slackből, k<PERSON><PERSON><PERSON><PERSON><PERSON>, próbá<PERSON>ja meg újra"}, "DYTE": {"CLICK_HERE_TO_JOIN": "Kattintsd ide a csatlakozáshoz", "LEAVE_THE_ROOM": "Szoba elhagyása", "START_VIDEO_CALL_HELP_TEXT": "Új videóhívás indítása ügyféllel", "JOIN_ERROR": "Hiba történt az híváshoz való csatlakozás során, kérjük prób<PERSON>ld <PERSON>", "CREATE_ERROR": "Hiba történt a link létrehozása során, kérjük prób<PERSON>ld <PERSON>"}, "OPEN_AI": {"AI_ASSIST": "AI segítség", "WITH_AI": " {option} with AI ", "OPTIONS": {"REPLY_SUGGESTION": "Válasz le<PERSON>őségek", "SUMMARIZE": "Összefoglalás", "REPHRASE": "<PERSON><PERSON><PERSON>", "FIX_SPELLING_GRAMMAR": "Helyesírás és nyelvtan kijavítása", "SHORTEN": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EXPAND": "Kiegészítés", "MAKE_FRIENDLY": "<PERSON><PERSON><PERSON> s<PERSON><PERSON><PERSON><PERSON>", "MAKE_FORMAL": "<PERSON><PERSON><PERSON> hivat<PERSON>", "SIMPLIFY": "Egyszerűsít"}, "ASSISTANCE_MODAL": {"DRAFT_TITLE": "Vázlatos szöveg", "GENERATED_TITLE": "Gener<PERSON>lt szöveg", "AI_WRITING": "AI éppen ír", "BUTTONS": {"APPLY": "Használja ezt a javaslatot", "CANCEL": "<PERSON><PERSON><PERSON><PERSON>"}}, "CTA_MODAL": {"TITLE": "OpenAI-al integrálás", "DESC": "Az OpenAI GPT modelljeivel fejlett AI funkciókat építhet be a menüjébe. A kezdéshez adja meg az API kulcsot az OpenAI-fiókjából.", "KEY_PLACEHOLDER": "Adja meg az OpenAI API kulcsát", "BUTTONS": {"NEED_HELP": "Kell segítség?", "DISMISS": "Elutasítás", "FINISH": "Előkészületek befejezése"}, "DISMISS_MESSAGE": "Az OpenAI integrációt k<PERSON> is <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, amikor csak akarja.", "SUCCESS_MESSAGE": "OpenAI integráció sikeres beállítva"}, "TITLE": "Javítás AI segítségével", "SUMMARY_TITLE": "Összegzés Al segítségével", "REPLY_TITLE": "Válasz a javaslatra az AI-val", "SUBTITLE": "\nA jelenlegi piszkozat alapján továbbfejlesztett válasz jön létre mesterséges intelligencia használatával.", "TONE": {"TITLE": "Hangnem", "OPTIONS": {"PROFESSIONAL": "Professzionális", "FRIENDLY": "Barátságos"}}, "BUTTONS": {"GENERATE": "Gener<PERSON><PERSON><PERSON>", "GENERATING": "Generálás...", "CANCEL": "<PERSON><PERSON><PERSON><PERSON>"}, "GENERATE_ERROR": "There was an error processing the content, please try again"}, "DELETE": {"BUTTON_TEXT": "Törlés", "API": {"SUCCESS_MESSAGE": "Integr<PERSON><PERSON><PERSON> si<PERSON> tör<PERSON>"}}, "CONNECT": {"BUTTON_TEXT": "Ka<PERSON><PERSON>olódás"}, "DASHBOARD_APPS": {"TITLE": "Kezdőlap applikációi", "HEADER_BTN_TXT": "Új kezdőlapi applikáció hozzáadása", "SIDEBAR_TXT": "<p><b><PERSON><PERSON><PERSON><PERSON><PERSON>tópult-alkalmazások</b></p><p>Az irányítópult-alkalmazások segítségével a szervezetek beágyazhatnak egy alkalmazást a Chatwoot irányítópultjába, hogy kontextust biztosítsanak az ügyfélszolgálati ügynökök számára. Ezzel a funkcióval önállóan hozhat létre alkalmazást, és beágyazhatja azt az irányítópultba, hogy megadja a felhasználói információkat, rendeléseiket vagy korábbi fizetési előzményeiket.</p><p>Ha beágyazza alkalmazását a Chatwoot irányítópultjával, az alkalmazás ablakeseményként kapja meg a beszélgetés és a kapcsolatfelvétel kontextusát. He<PERSON><PERSON>zen el egy figyelőt az üzeneteseményhez az oldalon, hogy megkapja a kontextust.</p><p>Új irányítópult-alkalmazás hozzáadásához kattintson az „Új irányítópult-alkalmazás hozzáadása” gombra.</p>", "DESCRIPTION": "Az irányítópult-alkalmazások segítségével a szervezetek beágyazhatnak egy alkalmazást az irányítópultba, hogy kontextust biztosítsanak az ügyfélszolgálati ügynökök számára. Ez a funkció lehetővé teszi, hogy önállóan hozzon létre egy alkalmazást, és beágyazza azt, hogy megadja a felhasználói információkat, rendeléseiket vagy korábbi fizetési előzményeiket.\n", "LEARN_MORE": "Learn more about Dashboard Apps", "LIST": {"404": "<PERSON><PERSON>n a fiókban még nincsenek konfigurálva kezdőlapi-alkalmazások", "LOADING": "Kezdőlapi alkalmazások lekérése...", "TABLE_HEADER": {"NAME": "Név", "ENDPOINT": "<PERSON><PERSON>g<PERSON>"}, "EDIT_TOOLTIP": "<PERSON><PERSON>zté<PERSON>", "DELETE_TOOLTIP": "App törl<PERSON>e"}, "FORM": {"TITLE_LABEL": "Név", "TITLE_PLACEHOLDER": "Add meg a kezdőlapi applikáció nevét", "TITLE_ERROR": "A kezdőlapi applikáció nevének megadása kötelező", "URL_LABEL": "<PERSON><PERSON>g<PERSON>", "URL_PLACEHOLDER": "Add meg azt a végpont URL-jét, ahol az alkalmazást tárolják", "URL_ERROR": "Érvényes URL megadása kötelező"}, "CREATE": {"HEADER": "Új kezdőlapi applikáció hozzáadása", "FORM_SUBMIT": "Elküld<PERSON>", "FORM_CANCEL": "<PERSON><PERSON><PERSON><PERSON>", "API_SUCCESS": "Kezdőlapi applikációk sikeresen konfigurálva", "API_ERROR": "<PERSON>em tudtuk megjeleníteni az appot, kérlek pr<PERSON><PERSON><PERSON><PERSON>"}, "UPDATE": {"HEADER": "Kezdőlapi app szerkesztése", "FORM_SUBMIT": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FORM_CANCEL": "<PERSON><PERSON><PERSON><PERSON>", "API_SUCCESS": "Kezdőlapi applikációk sikeresen frissítve", "API_ERROR": "<PERSON>em tud<PERSON>k frissíteni az appot, k<PERSON>rlek pr<PERSON><PERSON><PERSON><PERSON>"}, "DELETE": {"CONFIRM_YES": "Igen, töröld", "CONFIRM_NO": "<PERSON>em, tartsd meg", "TITLE": "Törlés megerősítése", "MESSAGE": "Biztosan törölni szeretnéd az appot? ({appName}?", "API_SUCCESS": "Kezdőlapi applikációk sikeresen törölve", "API_ERROR": "<PERSON>em tudtuk törölni az appot, kérlek pr<PERSON><PERSON><PERSON><PERSON>"}}, "LINEAR": {"ADD_OR_LINK_BUTTON": "Create/Link Linear Issue", "LOADING": "Fetching linear issues...", "LOADING_ERROR": "There was an error fetching the linear issues, please try again", "CREATE": "Létrehozás", "LINK": {"SEARCH": "Search issues", "SELECT": "Select issue", "TITLE": "Link", "EMPTY_LIST": "No linear issues found", "LOADING": "Loading", "ERROR": "There was an error fetching the linear issues, please try again", "LINK_SUCCESS": "Issue linked successfully", "LINK_ERROR": "There was an error linking the issue, please try again", "LINK_TITLE": "Conversation (#{conversationId}) with {name}"}, "ADD_OR_LINK": {"TITLE": "Create/link linear issue", "DESCRIPTION": "Create Linear issues from conversations, or link existing ones for seamless tracking.", "FORM": {"TITLE": {"LABEL": "Cím", "PLACEHOLDER": "Enter title", "REQUIRED_ERROR": "Cím megadása kötelező"}, "DESCRIPTION": {"LABEL": "Le<PERSON><PERSON><PERSON>", "PLACEHOLDER": "Enter description"}, "TEAM": {"LABEL": "Csapat", "PLACEHOLDER": "Csapat kiválasztása", "SEARCH": "Search team", "REQUIRED_ERROR": "Team is required"}, "ASSIGNEE": {"LABEL": "Assignee", "PLACEHOLDER": "Select assignee", "SEARCH": "Search assignee"}, "PRIORITY": {"LABEL": "Prioritás", "PLACEHOLDER": "Prioritás megadása", "SEARCH": "Search priority"}, "LABEL": {"LABEL": "<PERSON><PERSON><PERSON>", "PLACEHOLDER": "Select label", "SEARCH": "Search label"}, "STATUS": {"LABEL": "<PERSON><PERSON><PERSON><PERSON>", "PLACEHOLDER": "Select status", "SEARCH": "Search status"}, "PROJECT": {"LABEL": "Project", "PLACEHOLDER": "Select project", "SEARCH": "Search project"}}, "CREATE": "Létrehozás", "CANCEL": "<PERSON><PERSON><PERSON><PERSON>", "CREATE_SUCCESS": "Issue created successfully", "CREATE_ERROR": "There was an error creating the issue, please try again", "LOADING_TEAM_ERROR": "There was an error fetching the teams, please try again", "LOADING_TEAM_ENTITIES_ERROR": "There was an error fetching the team entities, please try again"}, "ISSUE": {"STATUS": "<PERSON><PERSON><PERSON><PERSON>", "PRIORITY": "Prioritás", "ASSIGNEE": "Assignee", "LABELS": "Cimkék", "CREATED_AT": "Created at {createdAt}"}, "UNLINK": {"TITLE": "Unlink", "SUCCESS": "Issue unlinked successfully", "ERROR": "There was an error unlinking the issue, please try again"}, "DELETE": {"TITLE": "Are you sure you want to delete the integration?", "MESSAGE": "Are you sure you want to delete the integration?", "CONFIRM": "Igen, törlés", "CANCEL": "<PERSON><PERSON><PERSON><PERSON>"}}}, "CAPTAIN": {"NAME": "Captain", "HEADER_KNOW_MORE": "Know more", "COPILOT": {"SEND_MESSAGE": "Üzenet elküldése...", "EMPTY_MESSAGE": "There was an error generating the response. Please try again.", "LOADER": "Captain is thinking", "YOU": "<PERSON><PERSON>", "USE": "Use this", "RESET": "Reset", "SELECT_ASSISTANT": "Select Assistant", "PROMPTS": {"SUMMARIZE": {"LABEL": "Summarize this conversation", "CONTENT": "Summarize the key points discussed between the customer and the support agent, including the customer's concerns, questions, and the solutions or responses provided by the support agent"}, "SUGGEST": {"LABEL": "Suggest an answer", "CONTENT": "Analyze the customer's inquiry, and draft a response that effectively addresses their concerns or questions. Ensure the reply is clear, concise, and provides helpful information."}, "RATE": {"LABEL": "Rate this conversation", "CONTENT": "Review the conversation to see how well it meets the customer's needs. Share a rating out of 5 based on tone, clarity, and effectiveness."}}}, "PLAYGROUND": {"USER": "<PERSON><PERSON>", "ASSISTANT": "Assistant", "MESSAGE_PLACEHOLDER": "Gépeld be üzeneted...", "HEADER": "Playground", "DESCRIPTION": "Use this playground to send messages to your assistant and check if it responds accurately, quickly, and in the tone you expect.", "CREDIT_NOTE": "Messages sent here will count toward your Captain credits."}, "PAYWALL": {"TITLE": "Upgrade to use Captain AI", "AVAILABLE_ON": "Captain is not available on the free plan.", "UPGRADE_PROMPT": "Upgrade your plan to get access to our assistants, copilot and more.", "UPGRADE_NOW": "Upgrade now", "CANCEL_ANYTIME": "You can change or cancel your plan anytime"}, "ENTERPRISE_PAYWALL": {"AVAILABLE_ON": "Captain AI feature is only available in a paid plan.", "UPGRADE_PROMPT": "Upgrade your plan to get access to our assistants, copilot and more.", "ASK_ADMIN": "Please reach out to your administrator for the upgrade."}, "BANNER": {"RESPONSES": "You've used over 80% of your response limit. To continue using Captain AI, please upgrade.", "DOCUMENTS": "Document limit reached. Upgrade to continue using Captain AI."}, "FORM": {"CANCEL": "<PERSON><PERSON><PERSON><PERSON>", "CREATE": "Létrehozás", "EDIT": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "ASSISTANTS": {"HEADER": "Assistants", "ADD_NEW": "Create a new assistant", "DELETE": {"TITLE": "Are you sure to delete the assistant?", "DESCRIPTION": "This action is permanent. Deleting this assistant will remove it from all connected inboxes and permanently erase all generated knowledge.", "CONFIRM": "Igen, törlés", "SUCCESS_MESSAGE": "The assistant has been successfully deleted", "ERROR_MESSAGE": "There was an error deleting the assistant, please try again."}, "FORM_DESCRIPTION": "Fill out the details below to name your assistant, describe its purpose, and specify the product it will support.", "CREATE": {"TITLE": "Create an assistant", "SUCCESS_MESSAGE": "The assistant has been successfully created", "ERROR_MESSAGE": "There was an error creating the assistant, please try again."}, "FORM": {"UPDATE": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SECTIONS": {"BASIC_INFO": "Basic Information", "SYSTEM_MESSAGES": "System Messages", "INSTRUCTIONS": "Instructions", "FEATURES": "Lehetőségek", "TOOLS": "Tools "}, "NAME": {"LABEL": "Név", "PLACEHOLDER": "Enter assistant name", "ERROR": "The name is required"}, "DESCRIPTION": {"LABEL": "Le<PERSON><PERSON><PERSON>", "PLACEHOLDER": "Enter assistant description", "ERROR": "The description is required"}, "PRODUCT_NAME": {"LABEL": "Product Name", "PLACEHOLDER": "Enter product name", "ERROR": "The product name is required"}, "WELCOME_MESSAGE": {"LABEL": "Welcome Message", "PLACEHOLDER": "Enter welcome message"}, "HANDOFF_MESSAGE": {"LABEL": "Handoff Message", "PLACEHOLDER": "Enter handoff message"}, "RESOLUTION_MESSAGE": {"LABEL": "Resolution Message", "PLACEHOLDER": "Enter resolution message"}, "INSTRUCTIONS": {"LABEL": "Instructions", "PLACEHOLDER": "Enter instructions for the assistant"}, "FEATURES": {"TITLE": "Lehetőségek", "ALLOW_CONVERSATION_FAQS": "Generate FAQs from resolved conversations", "ALLOW_MEMORIES": "Capture key details as memories from customer interactions."}}, "EDIT": {"TITLE": "Update the assistant", "SUCCESS_MESSAGE": "The assistant has been successfully updated", "ERROR_MESSAGE": "There was an error updating the assistant, please try again.", "NOT_FOUND": "Could not find the assistant. Please try again."}, "OPTIONS": {"EDIT_ASSISTANT": "Edit Assistant", "DELETE_ASSISTANT": "Delete Assistant", "VIEW_CONNECTED_INBOXES": "View connected inboxes"}, "EMPTY_STATE": {"TITLE": "No assistants available", "SUBTITLE": "Create an assistant to provide quick and accurate responses to your users. It can learn from your help articles and past conversations.", "FEATURE_SPOTLIGHT": {"TITLE": "Captain Assistant", "NOTE": "Captain Assistant engages directly with customers, learns from your help docs and past conversations, and delivers instant, accurate responses. It handles the initial queries, providing quick resolutions before  transferring to an agent when needed."}}}, "DOCUMENTS": {"HEADER": "Documents", "ADD_NEW": "Create a new document", "RELATED_RESPONSES": {"TITLE": "Related FAQs", "DESCRIPTION": "These FAQs are generated directly from the document."}, "FORM_DESCRIPTION": "Enter the URL of the document to add it as a knowledge source and choose the assistant to associate it with.", "CREATE": {"TITLE": "Add a document", "SUCCESS_MESSAGE": "The document has been successfully created", "ERROR_MESSAGE": "There was an error creating the document, please try again."}, "FORM": {"URL": {"LABEL": "URL", "PLACEHOLDER": "Enter the URL of the document", "ERROR": "Please provide a valid URL for the document"}, "ASSISTANT": {"LABEL": "Assistant", "PLACEHOLDER": "Select the assistant", "ERROR": "The assistant field is required"}}, "DELETE": {"TITLE": "Are you sure to delete the document?", "DESCRIPTION": "This action is permanent. Deleting this document will permanently erase all generated knowledge.", "CONFIRM": "Igen, törlés", "SUCCESS_MESSAGE": "The document has been successfully deleted", "ERROR_MESSAGE": "There was an error deleting the document, please try again."}, "OPTIONS": {"VIEW_RELATED_RESPONSES": "View Related Responses", "DELETE_DOCUMENT": "Delete Document"}, "EMPTY_STATE": {"TITLE": "No documents available", "SUBTITLE": "Documents are used by your assistant to generate FAQs. You can import documents to provide context for your assistant.", "FEATURE_SPOTLIGHT": {"TITLE": "Captain <PERSON>ument", "NOTE": "A document in Captain serves as a knowledge resource for the assistant. By connecting your help center or guides, Captain can analyze the content and provide accurate responses for customer inquiries."}}}, "RESPONSES": {"HEADER": "FAQs", "ADD_NEW": "Create new FAQ", "DOCUMENTABLE": {"CONVERSATION": "Conversation #{id}"}, "SELECTED": "{count} selected", "BULK_APPROVE_BUTTON": "Approve", "BULK_DELETE_BUTTON": "Törlés", "BULK_APPROVE": {"SUCCESS_MESSAGE": "FAQs approved successfully", "ERROR_MESSAGE": "There was an error approving the FAQs, please try again."}, "BULK_DELETE": {"TITLE": "Delete FAQs?", "DESCRIPTION": "Are you sure you want to delete the selected FAQs? This action cannot be undone.", "CONFIRM": "Yes, delete all", "SUCCESS_MESSAGE": "FAQs deleted successfully", "ERROR_MESSAGE": "There was an error deleting the FAQs, please try again."}, "DELETE": {"TITLE": "Are you sure to delete the FAQ?", "DESCRIPTION": "", "CONFIRM": "Igen, törlés", "SUCCESS_MESSAGE": "FAQ deleted successfully", "ERROR_MESSAGE": "There was an error deleting the FAQ, please try again."}, "FILTER": {"ASSISTANT": "Assistant: {selected}", "STATUS": "Status: {selected}", "ALL_ASSISTANTS": "Mind"}, "STATUS": {"TITLE": "<PERSON><PERSON><PERSON><PERSON>", "PENDING": "Függőben lévő", "APPROVED": "Approved", "ALL": "Mind"}, "FORM_DESCRIPTION": "Add a question and its corresponding answer to the knowledge base and select the assistant it should be associated with.", "CREATE": {"TITLE": "Add an FAQ", "SUCCESS_MESSAGE": "The response has been added successfully.", "ERROR_MESSAGE": "An error occurred while adding the response. Please try again."}, "FORM": {"QUESTION": {"LABEL": "Question", "PLACEHOLDER": "Enter the question here", "ERROR": "Please provide a valid question."}, "ANSWER": {"LABEL": "Answer", "PLACEHOLDER": "Enter the answer here", "ERROR": "Please provide a valid answer."}, "ASSISTANT": {"LABEL": "Assistant", "PLACEHOLDER": "Select an assistant", "ERROR": "Please select an assistant."}}, "EDIT": {"TITLE": "Update the FAQ", "SUCCESS_MESSAGE": "The FAQ has been successfully updated", "ERROR_MESSAGE": "There was an error updating the FAQ, please try again", "APPROVE_SUCCESS_MESSAGE": "The FAQ was marked as approved"}, "OPTIONS": {"APPROVE": "Mark as approved", "EDIT_RESPONSE": "Edit FAQ", "DELETE_RESPONSE": "Delete FAQ"}, "EMPTY_STATE": {"TITLE": "No FAQs Found", "SUBTITLE": "FAQs help your assistant provide quick and accurate answers to questions from your customers. They can be generated automatically from your content or can be added manually.", "FEATURE_SPOTLIGHT": {"TITLE": "Captain FAQ", "NOTE": "Captain <PERSON><PERSON><PERSON> detects common customer questions—whether missing from your knowledge base or frequently asked—and generates relevant FAQs to improve support. You can review each suggestion and decide whether to approve or reject it."}}}, "INBOXES": {"HEADER": "Connected Inboxes", "ADD_NEW": "Connect a new inbox", "OPTIONS": {"DISCONNECT": "Leválasztás"}, "DELETE": {"TITLE": "Are you sure to disconnect the inbox?", "DESCRIPTION": "", "CONFIRM": "Igen, törlés", "SUCCESS_MESSAGE": "The inbox was successfully disconnected.", "ERROR_MESSAGE": "There was an error disconnecting the inbox, please try again."}, "FORM_DESCRIPTION": "Choose an inbox to connect with the assistant.", "CREATE": {"TITLE": "Connect an Inbox", "SUCCESS_MESSAGE": "The inbox was successfully connected.", "ERROR_MESSAGE": "An error occurred while connecting the inbox. Please try again."}, "FORM": {"INBOX": {"LABEL": "<PERSON>ók", "PLACEHOLDER": "Choose the inbox to deploy the assistant.", "ERROR": "An inbox selection is required."}}, "EMPTY_STATE": {"TITLE": "No Connected Inboxes", "SUBTITLE": "Connecting an inbox allows the assistant to handle initial questions from your customers before transferring them to you."}}}}