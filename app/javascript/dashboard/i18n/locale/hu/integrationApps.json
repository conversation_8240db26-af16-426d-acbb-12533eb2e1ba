{"INTEGRATION_APPS": {"FETCHING": "Integrációk lekérése", "NO_HOOK_CONFIGURED": "<PERSON><PERSON>n a fi<PERSON>kban nincs be<PERSON> {integrationId} integráció", "HEADER": "Alkalmazások", "STATUS": {"ENABLED": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DISABLED": "<PERSON><PERSON><PERSON>"}, "CONFIGURE": "Beállítások", "ADD_BUTTON": "<PERSON><PERSON> <PERSON> hozz<PERSON><PERSON><PERSON><PERSON>", "DELETE": {"TITLE": {"INBOX": "Törlés megerősítése", "ACCOUNT": "Leválasztás"}, "MESSAGE": {"INBOX": "<PERSON><PERSON><PERSON> abban, hogy törli?", "ACCOUNT": "Biztosan le<PERSON>álasztja?"}, "CONFIRM_BUTTON_TEXT": {"INBOX": "Igen, Törlés", "ACCOUNT": "Igen, leválasztás"}, "CANCEL_BUTTON_TEXT": "<PERSON><PERSON><PERSON><PERSON>", "API": {"SUCCESS_MESSAGE": "<PERSON> törl<PERSON><PERSON> k<PERSON>", "ERROR_MESSAGE": "<PERSON><PERSON> csatlakozni a Woot szerverhez, kérjük pr<PERSON><PERSON><PERSON><PERSON> k<PERSON>"}}, "LIST": {"FETCHING": "Integrációs hookok betöltése", "INBOX": "<PERSON>ók", "DELETE": {"BUTTON_TEXT": "Törlés"}}, "ADD": {"FORM": {"INBOX": {"LABEL": "<PERSON><PERSON><PERSON><PERSON>", "PLACEHOLDER": "<PERSON><PERSON><PERSON><PERSON>"}, "SUBMIT": "Létrehozás", "CANCEL": "<PERSON><PERSON><PERSON><PERSON>"}, "API": {"SUCCESS_MESSAGE": "Integrációs hookok hozzáadásra kerültek", "ERROR_MESSAGE": "<PERSON><PERSON> csatlakozni a Woot szerverhez, kérjük pr<PERSON><PERSON><PERSON><PERSON> k<PERSON>"}}, "CONNECT": {"BUTTON_TEXT": "Ka<PERSON><PERSON>olódás"}, "DISCONNECT": {"BUTTON_TEXT": "Leválasztás"}, "SIDEBAR_DESCRIPTION": {"DIALOGFLOW": "Dialogflow is a natural language processing platform for building conversational interfaces. Integrating it with {installationName} lets bots handle queries first and transfer them to agents when needed. It helps qualify leads and reduce agent workload by answering FAQs. To add Dialogflow, create a Service Account in Google Console and share the credentials. Refer to the docs for details"}}}