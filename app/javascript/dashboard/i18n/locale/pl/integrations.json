{"INTEGRATION_SETTINGS": {"SHOPIFY": {"DELETE": {"TITLE": "Delete Shopify Integration", "MESSAGE": "Are you sure you want to delete the Shopify integration?"}, "STORE_URL": {"TITLE": "Connect Shopify Store", "LABEL": "Store URL", "PLACEHOLDER": "your-store.myshopify.com", "HELP": "Enter your Shopify store's myshopify.com URL", "CANCEL": "<PERSON><PERSON><PERSON>", "SUBMIT": "Connect Store"}, "ERROR": "There was an error connecting to Shopify. Please try again or contact support if the issue persists."}, "HEADER": "Integracje", "DESCRIPTION": "Chatwoot integrates with multiple tools and services to improve your team's efficiency. Explore the list below to configure your favorite apps.", "LEARN_MORE": "Learn more about integrations", "LOADING": "Fetching integrations", "CAPTAIN": {"DISABLED": "Captain is not enabled on your account.", "CLICK_HERE_TO_CONFIGURE": "Click here to configure", "LOADING_CONSOLE": "Loading Captain <PERSON><PERSON><PERSON>...", "FAILED_TO_LOAD_CONSOLE": "Failed to load Captain <PERSON><PERSON>. Please refresh and try again."}, "WEBHOOK": {"SUBSCRIBED_EVENTS": "Subskrybowane wydarzenia", "LEARN_MORE": "Learn more about webhooks", "FORM": {"CANCEL": "<PERSON><PERSON><PERSON>", "DESC": "Webhooki dostarczają informacje o tym, co dzieje się w Twoim koncie Chatwoot. Wprowadź poprawny adres URL, aby skon<PERSON><PERSON><PERSON><PERSON>ć webhook.", "SUBSCRIPTIONS": {"LABEL": "Wyd<PERSON>zen<PERSON>", "EVENTS": {"CONVERSATION_CREATED": "Rozpoczęcie rozmowy", "CONVERSATION_STATUS_CHANGED": "Zmiana statusu rozmowy", "CONVERSATION_UPDATED": "Aktualizacja rozmowy", "MESSAGE_CREATED": "Utworzenie wiadomości", "MESSAGE_UPDATED": "Aktualizacja wiadomości", "WEBWIDGET_TRIGGERED": "Użytkownik otworzył czat na żywo", "CONTACT_CREATED": "Utworzenie kontaktu", "CONTACT_UPDATED": "Aktualizacja kontaktu", "CONVERSATION_TYPING_ON": "Conversation Typing On", "CONVERSATION_TYPING_OFF": "Conversation Typing Off"}}, "END_POINT": {"LABEL": "Adres URL webhooka", "PLACEHOLDER": "Example: {webhookExampleURL}", "ERROR": "Wprowadź poprawny adres URL"}, "EDIT_SUBMIT": "Aktualizuj webhook", "ADD_SUBMIT": "Utwórz webhook"}, "TITLE": "Webhook", "CONFIGURE": "Skonfiguruj", "HEADER": "Ustawienia webhooka", "HEADER_BTN_TXT": "<PERSON><PERSON><PERSON> nowy webhook", "LOADING": "Pobieranie webhooków", "SEARCH_404": "Brak wyników pasujących do wyszukiwania", "SIDEBAR_TXT": "<p><b><PERSON><PERSON>oki</b> </p> <p>Webhooki są wywołaniami zwrotnymi HTTP, które mogą być zdefiniowane dla każdego konta. Są wywoływane przez zdarzenia takie jak utworzenie wiadomości w Chatwoot. Możesz utworzyć więcej niż jeden webhook dla tego konta. <br /><br /> Aby ut<PERSON><PERSON> <b>webhook</b>, klik<PERSON>j przycisk <b>Dodaj nowy webhook</b>. Możesz również usunąć istniejący webhook, klikając przycisk Usuń.</p>", "LIST": {"404": "Brak skonfigurowanych webhooków dla tego konta.", "TITLE": "Zarządza<PERSON>", "TABLE_HEADER": {"WEBHOOK_ENDPOINT": "Adres URL webhooka", "ACTIONS": "<PERSON><PERSON><PERSON><PERSON>"}}, "EDIT": {"BUTTON_TEXT": "<PERSON><PERSON><PERSON><PERSON>", "TITLE": "<PERSON><PERSON><PERSON><PERSON>", "API": {"SUCCESS_MESSAGE": "Konfiguracja webhooka została pomyślnie zaktualizowana", "ERROR_MESSAGE": "Nie można połączyć się z serwerem Woot, spróbuj ponownie później"}}, "ADD": {"CANCEL": "<PERSON><PERSON><PERSON>", "TITLE": "<PERSON><PERSON><PERSON> nowy webhook", "API": {"SUCCESS_MESSAGE": "Konfiguracja webhooka została pomyślnie dodana", "ERROR_MESSAGE": "Nie można połączyć się z serwerem Woot, spróbuj ponownie później"}}, "DELETE": {"BUTTON_TEXT": "Usuń", "API": {"SUCCESS_MESSAGE": "Webhook został pomyślnie usunięty", "ERROR_MESSAGE": "Nie można połączyć się z serwerem Woot, spróbuj ponownie później"}, "CONFIRM": {"TITLE": "Potwierdź usunięcie", "MESSAGE": "<PERSON>zy na pewno chcesz usunąć webhook? ({webhookURL})", "YES": "Tak, usuń ", "NO": "Nie, zostaw"}}}, "SLACK": {"DELETE": "Usuń", "DELETE_CONFIRMATION": {"TITLE": "Delete the integration", "MESSAGE": "Are you sure you want to delete the integration? Doing so will result in the loss of access to conversations on your Slack workspace."}, "HELP_TEXT": {"TITLE": "Using Slack Integration", "BODY": "With this integration, all of your incoming conversations will be synced to the ***{selectedChannelName}*** channel in your Slack workspace. You can manage all your customer conversations right within the channel and never miss a message.\n\nHere are the main features of the integration:\n\n**Respond to conversations from within Slack:** To respond to a conversation in the ***{selectedChannelName}*** Slack channel, simply type out your message and send it as a thread. This will create a response back to the customer through Chatwoot. It's that simple!\n\n **Create private notes:** If you want to create private notes instead of replies, start your message with ***`note:`***. This ensures that your message is kept private and won't be visible to the customer.\n\n**Associate an agent profile:** If the person who replied on Slack has an agent profile in Chatwoot under the same email, the replies will be associated with that agent profile automatically. This means you can easily track who said what and when. On the other hand, when the replier doesn't have an associated agent profile, the replies will appear from the bot profile to the customer.", "SELECTED": "selected"}, "SELECT_CHANNEL": {"OPTION_LABEL": "Select a channel", "UPDATE": "Aktualizuj", "BUTTON_TEXT": "Connect channel", "DESCRIPTION": "Your Slack workspace is now linked with Chatwoot. However, the integration is currently inactive. To activate the integration and connect a channel to Chatwoot, please click the button below.\n\n**Note:** If you are attempting to connect a private channel, add the Chatwoot app to the Slack channel before proceeding with this step.", "ATTENTION_REQUIRED": "Attention required", "EXPIRED": "Your Slack integration has expired. To continue receiving messages on Slack, please delete the integration and connect your workspace again."}, "UPDATE_ERROR": "There was an error updating the integration, please try again", "UPDATE_SUCCESS": "The channel is connected successfully", "FAILED_TO_FETCH_CHANNELS": "There was an error fetching the channels from Slack, please try again"}, "DYTE": {"CLICK_HERE_TO_JOIN": "<PERSON><PERSON><PERSON><PERSON> tutaj, aby dołącz<PERSON>", "LEAVE_THE_ROOM": "Opuść pokój", "START_VIDEO_CALL_HELP_TEXT": "Rozpocznij nową wideorozmowę z klientem", "JOIN_ERROR": "Wystąpił błąd podczas dołączania do rozmowy, spróbuj ponownie", "CREATE_ERROR": "Wystą<PERSON>ł błąd podczas tworzenia linku spotkania, spróbuj ponownie"}, "OPEN_AI": {"AI_ASSIST": "AI Assist", "WITH_AI": " {option} with AI ", "OPTIONS": {"REPLY_SUGGESTION": "Reply Suggestion", "SUMMARIZE": "Summarize", "REPHRASE": "Improve Writing", "FIX_SPELLING_GRAMMAR": "Fix Spelling and Grammar", "SHORTEN": "<PERSON>en", "EXPAND": "Expand", "MAKE_FRIENDLY": "Change message tone to friendly", "MAKE_FORMAL": "Use formal tone", "SIMPLIFY": "Simplify"}, "ASSISTANCE_MODAL": {"DRAFT_TITLE": "Draft content", "GENERATED_TITLE": "Generated content", "AI_WRITING": "AI is writing", "BUTTONS": {"APPLY": "Use this suggestion", "CANCEL": "<PERSON><PERSON><PERSON>"}}, "CTA_MODAL": {"TITLE": "Integrate with OpenAI", "DESC": "Bring advanced AI features to your dashboard with OpenAI's GPT models. To begin, enter the API key from your OpenAI account.", "KEY_PLACEHOLDER": "Enter your OpenAI API key", "BUTTONS": {"NEED_HELP": "Potrzebujesz pomocy?", "DISMISS": "<PERSON><PERSON><PERSON><PERSON>", "FINISH": "Finish Setup"}, "DISMISS_MESSAGE": "You can setup OpenAI integration later Whenever you want.", "SUCCESS_MESSAGE": "OpenAI integration setup successfully"}, "TITLE": "Ulepszanie przy użyciu sztucznej inteligencji", "SUMMARY_TITLE": "Podsumowanie z użyciem sztucznej inteligencji", "REPLY_TITLE": "Sugerowanie odpowiedzi z użyciem sztucznej inteligencji", "SUBTITLE": "Na podstawie bieżącej wersji roboczej zostanie wygenerowana ulepszona odpowiedź przy użyciu sztucznej inteligencji.", "TONE": {"TITLE": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "OPTIONS": {"PROFESSIONAL": "Profesjonalna", "FRIENDLY": "Przyjazna"}}, "BUTTONS": {"GENERATE": "Generuj", "GENERATING": "Generowanie...", "CANCEL": "<PERSON><PERSON><PERSON>"}, "GENERATE_ERROR": "There was an error processing the content, please try again"}, "DELETE": {"BUTTON_TEXT": "Usuń", "API": {"SUCCESS_MESSAGE": "Integracja została pomyślnie usunięta"}}, "CONNECT": {"BUTTON_TEXT": "Połącz"}, "DASHBOARD_APPS": {"TITLE": "Aplikacje na pulpicie", "HEADER_BTN_TXT": "Dodaj nową aplikację na pulpicie", "SIDEBAR_TXT": "<p><b>Aplikacje na pulpicie</b></p><p>Aplikacje na pulpicie umożliwiają organizacjom osadzenie aplikacji wewnątrz panelu Chatwoot w celu dostarczenia kontekstu dla agentów obsługi klienta. Ta funkcja umożliwia tworzenie niezależnej aplikacji i osadzanie jej w celu dostarczania informacji o użytkowniku, jego zamówieniach lub historii płatności.</p><p>Kiedy osadzisz swoją aplikację za pomocą pulpitu Chatwoot, Twoja aplikacja otrzyma kontekst rozmowy i kontaktu jako zdarzenie okna. W swojej stronie zaimplementuj nasłuchiwanie zdarzenia wiadomości, aby otr<PERSON><PERSON><PERSON> kontekst.</p><p>Aby dodać nową aplikację na pulpicie, kliknij przycisk 'Dodaj nową aplikację na pulpicie'.</p>", "DESCRIPTION": "Aplikacje na pulpicie umożliwiają organizacjom osadzenie aplikacji wewnątrz panelu w celu dostarczenia kontekstu dla agentów obsługi klienta. Ta funkcja umożliwia tworzenie niezależnej aplikacji i osadzanie jej w celu dostarczania informacji o użytkowniku, jego zamówieniach lub historii płatności.", "LEARN_MORE": "Learn more about Dashboard Apps", "LIST": {"404": "Na tym koncie nie skonfigurowano jeszcze aplikacji na pulpicie", "LOADING": "Pobieranie aplikacji na pulpicie...", "TABLE_HEADER": {"NAME": "<PERSON><PERSON><PERSON>", "ENDPOINT": "Punkt końcowy"}, "EDIT_TOOLTIP": "<PERSON><PERSON><PERSON><PERSON>", "DELETE_TOOLTIP": "<PERSON><PERSON><PERSON> aplikację"}, "FORM": {"TITLE_LABEL": "Nazwa", "TITLE_PLACEHOLDER": "Wprowadź nazwę dla aplikacji na pulpicie", "TITLE_ERROR": "Nazwa aplikacji na pulpicie jest wymagana", "URL_LABEL": "Punkt końcowy", "URL_PLACEHOLDER": "Wprowadź adres URL punktu koń<PERSON>wego, gdzie jest hostowana Twoja aplikacja", "URL_ERROR": "Wymagany jest poprawny adres URL"}, "CREATE": {"HEADER": "Dodaj nową aplikację na pulpicie", "FORM_SUBMIT": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FORM_CANCEL": "<PERSON><PERSON><PERSON>", "API_SUCCESS": "Aplikacja na pulpicie została pomyślnie skonfigurowana", "API_ERROR": "Nie udało się utworzyć aplikacji. Spróbuj ponownie później"}, "UPDATE": {"HEADER": "Edytuj aplikację na pulpicie", "FORM_SUBMIT": "Aktualizuj", "FORM_CANCEL": "<PERSON><PERSON><PERSON>", "API_SUCCESS": "Aplikacja na pulpicie została pomyślnie zaktualizowana", "API_ERROR": "Nie udało się zaktualizować aplikacji. Spróbuj ponownie później"}, "DELETE": {"CONFIRM_YES": "Tak, usuń", "CONFIRM_NO": "Nie, zostaw", "TITLE": "Potwierdzenie usunięcia", "MESSAGE": "<PERSON>zy na pewno chcesz usunąć aplikację - {appName}?", "API_SUCCESS": "Aplikacja na pulpicie została pomyślnie usunięta", "API_ERROR": "Nie udało się usunąć aplikacji. Spróbuj ponownie później"}}, "LINEAR": {"ADD_OR_LINK_BUTTON": "Create/Link Linear Issue", "LOADING": "Fetching linear issues...", "LOADING_ERROR": "There was an error fetching the linear issues, please try again", "CREATE": "Stwórz", "LINK": {"SEARCH": "Search issues", "SELECT": "Select issue", "TITLE": "Link", "EMPTY_LIST": "No linear issues found", "LOADING": "Loading", "ERROR": "There was an error fetching the linear issues, please try again", "LINK_SUCCESS": "Issue linked successfully", "LINK_ERROR": "There was an error linking the issue, please try again", "LINK_TITLE": "Conversation (#{conversationId}) with {name}"}, "ADD_OR_LINK": {"TITLE": "Create/link linear issue", "DESCRIPTION": "Create Linear issues from conversations, or link existing ones for seamless tracking.", "FORM": {"TITLE": {"LABEL": "<PERSON><PERSON><PERSON>", "PLACEHOLDER": "Enter title", "REQUIRED_ERROR": "<PERSON><PERSON><PERSON> jest wymagany"}, "DESCRIPTION": {"LABEL": "Opis", "PLACEHOLDER": "Enter description"}, "TEAM": {"LABEL": "Zespół", "PLACEHOLDER": "<PERSON><PERSON><PERSON><PERSON>", "SEARCH": "Search team", "REQUIRED_ERROR": "Team is required"}, "ASSIGNEE": {"LABEL": "Assignee", "PLACEHOLDER": "Select assignee", "SEARCH": "Search assignee"}, "PRIORITY": {"LABEL": "Priorytet", "PLACEHOLDER": "Wybierz priorytet", "SEARCH": "Search priority"}, "LABEL": {"LABEL": "Etykieta", "PLACEHOLDER": "Select label", "SEARCH": "Search label"}, "STATUS": {"LABEL": "Status", "PLACEHOLDER": "Select status", "SEARCH": "Search status"}, "PROJECT": {"LABEL": "Project", "PLACEHOLDER": "Select project", "SEARCH": "Search project"}}, "CREATE": "Stwórz", "CANCEL": "<PERSON><PERSON><PERSON>", "CREATE_SUCCESS": "Issue created successfully", "CREATE_ERROR": "There was an error creating the issue, please try again", "LOADING_TEAM_ERROR": "There was an error fetching the teams, please try again", "LOADING_TEAM_ENTITIES_ERROR": "There was an error fetching the team entities, please try again"}, "ISSUE": {"STATUS": "Status", "PRIORITY": "Priorytet", "ASSIGNEE": "Assignee", "LABELS": "Etykiety", "CREATED_AT": "Created at {createdAt}"}, "UNLINK": {"TITLE": "Unlink", "SUCCESS": "Issue unlinked successfully", "ERROR": "There was an error unlinking the issue, please try again"}, "DELETE": {"TITLE": "Are you sure you want to delete the integration?", "MESSAGE": "Are you sure you want to delete the integration?", "CONFIRM": "Tak, usuń", "CANCEL": "<PERSON><PERSON><PERSON>"}}}, "CAPTAIN": {"NAME": "Captain", "HEADER_KNOW_MORE": "Know more", "COPILOT": {"SEND_MESSAGE": "<PERSON><PERSON><PERSON><PERSON><PERSON> w<PERSON>...", "EMPTY_MESSAGE": "There was an error generating the response. Please try again.", "LOADER": "Captain is thinking", "YOU": "You", "USE": "Use this", "RESET": "Reset", "SELECT_ASSISTANT": "Select Assistant", "PROMPTS": {"SUMMARIZE": {"LABEL": "Summarize this conversation", "CONTENT": "Summarize the key points discussed between the customer and the support agent, including the customer's concerns, questions, and the solutions or responses provided by the support agent"}, "SUGGEST": {"LABEL": "Suggest an answer", "CONTENT": "Analyze the customer's inquiry, and draft a response that effectively addresses their concerns or questions. Ensure the reply is clear, concise, and provides helpful information."}, "RATE": {"LABEL": "Rate this conversation", "CONTENT": "Review the conversation to see how well it meets the customer's needs. Share a rating out of 5 based on tone, clarity, and effectiveness."}}}, "PLAYGROUND": {"USER": "You", "ASSISTANT": "Assistant", "MESSAGE_PLACEHOLDER": "<PERSON><PERSON><PERSON> tre<PERSON> wiadom<PERSON>...", "HEADER": "Playground", "DESCRIPTION": "Use this playground to send messages to your assistant and check if it responds accurately, quickly, and in the tone you expect.", "CREDIT_NOTE": "Messages sent here will count toward your Captain credits."}, "PAYWALL": {"TITLE": "Upgrade to use Captain AI", "AVAILABLE_ON": "Captain is not available on the free plan.", "UPGRADE_PROMPT": "Upgrade your plan to get access to our assistants, copilot and more.", "UPGRADE_NOW": "Upgrade now", "CANCEL_ANYTIME": "You can change or cancel your plan anytime"}, "ENTERPRISE_PAYWALL": {"AVAILABLE_ON": "Captain AI feature is only available in a paid plan.", "UPGRADE_PROMPT": "Upgrade your plan to get access to our assistants, copilot and more.", "ASK_ADMIN": "Please reach out to your administrator for the upgrade."}, "BANNER": {"RESPONSES": "You've used over 80% of your response limit. To continue using Captain AI, please upgrade.", "DOCUMENTS": "Document limit reached. Upgrade to continue using Captain AI."}, "FORM": {"CANCEL": "<PERSON><PERSON><PERSON>", "CREATE": "Stwórz", "EDIT": "Aktualizuj"}, "ASSISTANTS": {"HEADER": "Assistants", "ADD_NEW": "Create a new assistant", "DELETE": {"TITLE": "Are you sure to delete the assistant?", "DESCRIPTION": "This action is permanent. Deleting this assistant will remove it from all connected inboxes and permanently erase all generated knowledge.", "CONFIRM": "Tak, usuń", "SUCCESS_MESSAGE": "The assistant has been successfully deleted", "ERROR_MESSAGE": "There was an error deleting the assistant, please try again."}, "FORM_DESCRIPTION": "Fill out the details below to name your assistant, describe its purpose, and specify the product it will support.", "CREATE": {"TITLE": "Create an assistant", "SUCCESS_MESSAGE": "The assistant has been successfully created", "ERROR_MESSAGE": "There was an error creating the assistant, please try again."}, "FORM": {"UPDATE": "Aktualizuj", "SECTIONS": {"BASIC_INFO": "Basic Information", "SYSTEM_MESSAGES": "System Messages", "INSTRUCTIONS": "Instructions", "FEATURES": "<PERSON><PERSON><PERSON>", "TOOLS": "Tools "}, "NAME": {"LABEL": "<PERSON><PERSON><PERSON>", "PLACEHOLDER": "Enter assistant name", "ERROR": "The name is required"}, "DESCRIPTION": {"LABEL": "Opis", "PLACEHOLDER": "Enter assistant description", "ERROR": "The description is required"}, "PRODUCT_NAME": {"LABEL": "Product Name", "PLACEHOLDER": "Enter product name", "ERROR": "The product name is required"}, "WELCOME_MESSAGE": {"LABEL": "Welcome Message", "PLACEHOLDER": "Enter welcome message"}, "HANDOFF_MESSAGE": {"LABEL": "Handoff Message", "PLACEHOLDER": "Enter handoff message"}, "RESOLUTION_MESSAGE": {"LABEL": "Resolution Message", "PLACEHOLDER": "Enter resolution message"}, "INSTRUCTIONS": {"LABEL": "Instructions", "PLACEHOLDER": "Enter instructions for the assistant"}, "FEATURES": {"TITLE": "<PERSON><PERSON><PERSON>", "ALLOW_CONVERSATION_FAQS": "Generate FAQs from resolved conversations", "ALLOW_MEMORIES": "Capture key details as memories from customer interactions."}}, "EDIT": {"TITLE": "Update the assistant", "SUCCESS_MESSAGE": "The assistant has been successfully updated", "ERROR_MESSAGE": "There was an error updating the assistant, please try again.", "NOT_FOUND": "Could not find the assistant. Please try again."}, "OPTIONS": {"EDIT_ASSISTANT": "Edit Assistant", "DELETE_ASSISTANT": "Delete Assistant", "VIEW_CONNECTED_INBOXES": "View connected inboxes"}, "EMPTY_STATE": {"TITLE": "No assistants available", "SUBTITLE": "Create an assistant to provide quick and accurate responses to your users. It can learn from your help articles and past conversations.", "FEATURE_SPOTLIGHT": {"TITLE": "Captain Assistant", "NOTE": "Captain Assistant engages directly with customers, learns from your help docs and past conversations, and delivers instant, accurate responses. It handles the initial queries, providing quick resolutions before  transferring to an agent when needed."}}}, "DOCUMENTS": {"HEADER": "Documents", "ADD_NEW": "Create a new document", "RELATED_RESPONSES": {"TITLE": "Related FAQs", "DESCRIPTION": "These FAQs are generated directly from the document."}, "FORM_DESCRIPTION": "Enter the URL of the document to add it as a knowledge source and choose the assistant to associate it with.", "CREATE": {"TITLE": "Add a document", "SUCCESS_MESSAGE": "The document has been successfully created", "ERROR_MESSAGE": "There was an error creating the document, please try again."}, "FORM": {"URL": {"LABEL": "Adres URL", "PLACEHOLDER": "Enter the URL of the document", "ERROR": "Please provide a valid URL for the document"}, "ASSISTANT": {"LABEL": "Assistant", "PLACEHOLDER": "Select the assistant", "ERROR": "The assistant field is required"}}, "DELETE": {"TITLE": "Are you sure to delete the document?", "DESCRIPTION": "This action is permanent. Deleting this document will permanently erase all generated knowledge.", "CONFIRM": "Tak, usuń", "SUCCESS_MESSAGE": "The document has been successfully deleted", "ERROR_MESSAGE": "There was an error deleting the document, please try again."}, "OPTIONS": {"VIEW_RELATED_RESPONSES": "View Related Responses", "DELETE_DOCUMENT": "Delete Document"}, "EMPTY_STATE": {"TITLE": "No documents available", "SUBTITLE": "Documents are used by your assistant to generate FAQs. You can import documents to provide context for your assistant.", "FEATURE_SPOTLIGHT": {"TITLE": "Captain <PERSON>ument", "NOTE": "A document in Captain serves as a knowledge resource for the assistant. By connecting your help center or guides, Captain can analyze the content and provide accurate responses for customer inquiries."}}}, "RESPONSES": {"HEADER": "FAQs", "ADD_NEW": "Create new FAQ", "DOCUMENTABLE": {"CONVERSATION": "Conversation #{id}"}, "SELECTED": "{count} selected", "BULK_APPROVE_BUTTON": "Approve", "BULK_DELETE_BUTTON": "Usuń", "BULK_APPROVE": {"SUCCESS_MESSAGE": "FAQs approved successfully", "ERROR_MESSAGE": "There was an error approving the FAQs, please try again."}, "BULK_DELETE": {"TITLE": "Delete FAQs?", "DESCRIPTION": "Are you sure you want to delete the selected FAQs? This action cannot be undone.", "CONFIRM": "Yes, delete all", "SUCCESS_MESSAGE": "FAQs deleted successfully", "ERROR_MESSAGE": "There was an error deleting the FAQs, please try again."}, "DELETE": {"TITLE": "Are you sure to delete the FAQ?", "DESCRIPTION": "", "CONFIRM": "Tak, usuń", "SUCCESS_MESSAGE": "FAQ deleted successfully", "ERROR_MESSAGE": "There was an error deleting the FAQ, please try again."}, "FILTER": {"ASSISTANT": "Assistant: {selected}", "STATUS": "Status: {selected}", "ALL_ASSISTANTS": "Wszystkie"}, "STATUS": {"TITLE": "Status", "PENDING": "Oczek<PERSON><PERSON><PERSON><PERSON>", "APPROVED": "Approved", "ALL": "Wszystkie"}, "FORM_DESCRIPTION": "Add a question and its corresponding answer to the knowledge base and select the assistant it should be associated with.", "CREATE": {"TITLE": "Add an FAQ", "SUCCESS_MESSAGE": "The response has been added successfully.", "ERROR_MESSAGE": "An error occurred while adding the response. Please try again."}, "FORM": {"QUESTION": {"LABEL": "Question", "PLACEHOLDER": "Enter the question here", "ERROR": "Please provide a valid question."}, "ANSWER": {"LABEL": "Answer", "PLACEHOLDER": "Enter the answer here", "ERROR": "Please provide a valid answer."}, "ASSISTANT": {"LABEL": "Assistant", "PLACEHOLDER": "Select an assistant", "ERROR": "Please select an assistant."}}, "EDIT": {"TITLE": "Update the FAQ", "SUCCESS_MESSAGE": "The FAQ has been successfully updated", "ERROR_MESSAGE": "There was an error updating the FAQ, please try again", "APPROVE_SUCCESS_MESSAGE": "The FAQ was marked as approved"}, "OPTIONS": {"APPROVE": "Mark as approved", "EDIT_RESPONSE": "Edit FAQ", "DELETE_RESPONSE": "Delete FAQ"}, "EMPTY_STATE": {"TITLE": "No FAQs Found", "SUBTITLE": "FAQs help your assistant provide quick and accurate answers to questions from your customers. They can be generated automatically from your content or can be added manually.", "FEATURE_SPOTLIGHT": {"TITLE": "Captain FAQ", "NOTE": "Captain <PERSON><PERSON><PERSON> detects common customer questions—whether missing from your knowledge base or frequently asked—and generates relevant FAQs to improve support. You can review each suggestion and decide whether to approve or reject it."}}}, "INBOXES": {"HEADER": "Connected Inboxes", "ADD_NEW": "Connect a new inbox", "OPTIONS": {"DISCONNECT": "Rozłącz"}, "DELETE": {"TITLE": "Are you sure to disconnect the inbox?", "DESCRIPTION": "", "CONFIRM": "Tak, usuń", "SUCCESS_MESSAGE": "The inbox was successfully disconnected.", "ERROR_MESSAGE": "There was an error disconnecting the inbox, please try again."}, "FORM_DESCRIPTION": "Choose an inbox to connect with the assistant.", "CREATE": {"TITLE": "Connect an Inbox", "SUCCESS_MESSAGE": "The inbox was successfully connected.", "ERROR_MESSAGE": "An error occurred while connecting the inbox. Please try again."}, "FORM": {"INBOX": {"LABEL": "Skrzynka odbiorcza", "PLACEHOLDER": "Choose the inbox to deploy the assistant.", "ERROR": "An inbox selection is required."}}, "EMPTY_STATE": {"TITLE": "No Connected Inboxes", "SUBTITLE": "Connecting an inbox allows the assistant to handle initial questions from your customers before transferring them to you."}}}}