{"AGENT_BOTS": {"HEADER": "<PERSON><PERSON>", "LOADING_EDITOR": "Ładowanie edytora...", "DESCRIPTION": "Agent <PERSON><PERSON> are like the most fabulous members of your team. They can handle the small stuff, so you can focus on the stuff that matters. Give them a try. You can manage your bots from this page or create new ones using the 'Add Bot' button.", "LEARN_MORE": "Learn about agent bots", "GLOBAL_BOT": "System bot", "GLOBAL_BOT_BADGE": "System", "AVATAR": {"SUCCESS_DELETE": "<PERSON><PERSON> avatar deleted successfully", "ERROR_DELETE": "Error deleting bot avatar, please try again"}, "BOT_CONFIGURATION": {"TITLE": "<PERSON><PERSON><PERSON>rz bota agenta", "DESC": "Przypisz Bota Agenta do swojej skrzynki odbiorczej. Mogą oni obsługiwać wstępne rozmowy i przekazywać je do żywego agenta, gdy jest to konieczne.", "SUBMIT": "Aktualizuj", "DISCONNECT": "Odłącz bota", "SUCCESS_MESSAGE": "Pomyślnie zaktualizowano bota agenta.", "DISCONNECTED_SUCCESS_MESSAGE": "Pomyślnie odłączono bota agenta.", "ERROR_MESSAGE": "<PERSON>e udało się zaktualizować bota agenta. Proszę spróbować ponownie.", "DISCONNECTED_ERROR_MESSAGE": "Nie udało się odłączyć bota agenta. Proszę spróbować ponownie.", "SELECT_PLACEHOLDER": "<PERSON><PERSON><PERSON><PERSON> bota"}, "ADD": {"TITLE": "<PERSON><PERSON>", "CANCEL_BUTTON_TEXT": "<PERSON><PERSON><PERSON>", "API": {"SUCCESS_MESSAGE": "<PERSON><PERSON> dodany p<PERSON>lnie.", "ERROR_MESSAGE": "<PERSON>e udało się dodać bota. Proszę spróbować później."}}, "LIST": {"404": "No bots found. You can create a bot by clicking the 'Add Bot' button.", "LOADING": "Pobieranie botów...", "TABLE_HEADER": {"DETAILS": "Bot Details", "URL": "Adres URL webhooka"}}, "DELETE": {"BUTTON_TEXT": "Usuń", "TITLE": "Usuń bota", "CONFIRM": {"TITLE": "Potwierdź usunięcie", "MESSAGE": "Are you sure you want to delete {name}?", "YES": "Tak, usuń", "NO": "<PERSON><PERSON>, anuluj"}, "API": {"SUCCESS_MESSAGE": "Bot usunięty pomyślnie.", "ERROR_MESSAGE": "<PERSON>e udało się usunąć bota. Proszę spróbować ponownie."}}, "EDIT": {"BUTTON_TEXT": "<PERSON><PERSON><PERSON><PERSON>", "TITLE": "<PERSON><PERSON><PERSON><PERSON> bota", "API": {"SUCCESS_MESSAGE": "Bot zaktualizowany pomyślnie.", "ERROR_MESSAGE": "Nie udało się zaktualizować bota. Proszę spróbować ponownie."}}, "FORM": {"AVATAR": {"LABEL": "<PERSON><PERSON> avatar"}, "NAME": {"LABEL": "Nazwa bota", "PLACEHOLDER": "Enter bot name", "REQUIRED": "Nazwa bota jest wymagana"}, "DESCRIPTION": {"LABEL": "Opis", "PLACEHOLDER": "Co robi ten bot?"}, "WEBHOOK_URL": {"LABEL": "Adres URL webhooka", "PLACEHOLDER": "https://example.com/webhook", "REQUIRED": "Webhook URL is required"}, "ERRORS": {"NAME": "Nazwa bota jest wymagana", "URL": "Webhook URL is required", "VALID_URL": "Please enter a valid URL starting with http:// or https://"}, "CANCEL": "<PERSON><PERSON><PERSON>", "CREATE": "Create <PERSON><PERSON>", "UPDATE": "Update Bot"}, "WEBHOOK": {"DESCRIPTION": "Configure a webhook bot to integrate with your custom services. The bot will receive and process events from conversations and can respond to them."}, "TYPES": {"WEBHOOK": "Bot webhook"}}}