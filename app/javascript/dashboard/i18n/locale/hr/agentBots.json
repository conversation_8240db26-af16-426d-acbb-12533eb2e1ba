{"AGENT_BOTS": {"HEADER": "<PERSON><PERSON><PERSON>", "LOADING_EDITOR": "Otvaranje Editora...", "DESCRIPTION": "Agent <PERSON><PERSON> are like the most fabulous members of your team. They can handle the small stuff, so you can focus on the stuff that matters. Give them a try. You can manage your bots from this page or create new ones using the 'Add Bot' button.", "LEARN_MORE": "Learn about agent bots", "GLOBAL_BOT": "System bot", "GLOBAL_BOT_BADGE": "System", "AVATAR": {"SUCCESS_DELETE": "<PERSON><PERSON> avatar deleted successfully", "ERROR_DELETE": "Error deleting bot avatar, please try again"}, "BOT_CONFIGURATION": {"TITLE": "Izaberi agentskog bota", "DESC": "Dodijelite Agent <PERSON><PERSON> svojoj pristigloj pošti. Oni mogu voditi početne razgovore i prebaciti ih živom agentu kada je to potrebno.", "SUBMIT": "<PERSON><PERSON><PERSON><PERSON>", "DISCONNECT": "Isključi Bota", "SUCCESS_MESSAGE": "Uspješno ažuriran agentski bot.", "DISCONNECTED_SUCCESS_MESSAGE": "Uspješno isključen agentski bot.", "ERROR_MESSAGE": "<PERSON><PERSON> mog<PERSON>e ažurirati agentskog bota, molimo pokušajte ponovno.", "DISCONNECTED_ERROR_MESSAGE": "<PERSON><PERSON> mog<PERSON>e ažurirati agentskog bota, molimo pokušajte ponovno.", "SELECT_PLACEHOLDER": "<PERSON><PERSON><PERSON><PERSON>"}, "ADD": {"TITLE": "<PERSON><PERSON>", "CANCEL_BUTTON_TEXT": "Odustani", "API": {"SUCCESS_MESSAGE": "Uspješno dodan Bot.", "ERROR_MESSAGE": "<PERSON><PERSON> mogu<PERSON>e do<PERSON>ti bota, molimo pokušajte kasnije."}}, "LIST": {"404": "No bots found. You can create a bot by clicking the 'Add Bot' button.", "LOADING": "<PERSON><PERSON><PERSON>...", "TABLE_HEADER": {"DETAILS": "Bot Details", "URL": "Webhook URL"}}, "DELETE": {"BUTTON_TEXT": "Izbriši", "TITLE": "Izbriši Bo<PERSON>", "CONFIRM": {"TITLE": "Potvrdi brisanje", "MESSAGE": "Are you sure you want to delete {name}?", "YES": "Yes, Delete", "NO": "No, Keep"}, "API": {"SUCCESS_MESSAGE": "Bot uspješno izbrisan.", "ERROR_MESSAGE": "<PERSON><PERSON> mogu<PERSON>e izbrisati bota, molimo pokušajte ponovno."}}, "EDIT": {"BUTTON_TEXT": "<PERSON><PERSON><PERSON>", "TITLE": "<PERSON><PERSON><PERSON>", "API": {"SUCCESS_MESSAGE": "Bot uspješno izbrisan.", "ERROR_MESSAGE": "<PERSON><PERSON> mog<PERSON> ažuri<PERSON> bota, molimo pokušajte ponovno."}}, "FORM": {"AVATAR": {"LABEL": "<PERSON><PERSON> avatar"}, "NAME": {"LABEL": "<PERSON><PERSON>", "PLACEHOLDER": "Enter bot name", "REQUIRED": "Potrebno je unijeti ime Bota"}, "DESCRIPTION": {"LABEL": "Description", "PLACEHOLDER": "<PERSON><PERSON> ovaj Bot radi?"}, "WEBHOOK_URL": {"LABEL": "Webhook URL", "PLACEHOLDER": "https://example.com/webhook", "REQUIRED": "Webhook URL is required"}, "ERRORS": {"NAME": "Potrebno je unijeti ime Bota", "URL": "Webhook URL is required", "VALID_URL": "Please enter a valid URL starting with http:// or https://"}, "CANCEL": "Odustani", "CREATE": "Create <PERSON><PERSON>", "UPDATE": "Update Bot"}, "WEBHOOK": {"DESCRIPTION": "Configure a webhook bot to integrate with your custom services. The bot will receive and process events from conversations and can respond to them."}, "TYPES": {"WEBHOOK": "Webhook Bot"}}}