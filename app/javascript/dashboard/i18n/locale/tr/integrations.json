{"INTEGRATION_SETTINGS": {"SHOPIFY": {"DELETE": {"TITLE": "Delete Shopify Integration", "MESSAGE": "Are you sure you want to delete the Shopify integration?"}, "STORE_URL": {"TITLE": "Connect Shopify Store", "LABEL": "Store URL", "PLACEHOLDER": "your-store.myshopify.com", "HELP": "Enter your Shopify store's myshopify.com URL", "CANCEL": "İptal Et", "SUBMIT": "Connect Store"}, "ERROR": "There was an error connecting to Shopify. Please try again or contact support if the issue persists."}, "HEADER": "Entegrasyonlar", "DESCRIPTION": "Chatwoot integrates with multiple tools and services to improve your team's efficiency. Explore the list below to configure your favorite apps.", "LEARN_MORE": "Learn more about integrations", "LOADING": "Fetching integrations", "CAPTAIN": {"DISABLED": "Captain is not enabled on your account.", "CLICK_HERE_TO_CONFIGURE": "Click here to configure", "LOADING_CONSOLE": "Loading Captain <PERSON><PERSON><PERSON>...", "FAILED_TO_LOAD_CONSOLE": "Failed to load Captain <PERSON><PERSON>. Please refresh and try again."}, "WEBHOOK": {"SUBSCRIBED_EVENTS": "<PERSON><PERSON>", "LEARN_MORE": "Learn more about webhooks", "FORM": {"CANCEL": "İptal Et", "DESC": "Web kancası etkinlikleri, Chatwoot hesabınızdaki olaylar hakkında gerçek zamanlı bilgi sağlar. Bir geri aramayı yapılandırmak için lütfen geçerli bir URL girin.", "SUBSCRIPTIONS": {"LABEL": "<PERSON><PERSON><PERSON>", "EVENTS": {"CONVERSATION_CREATED": "G<PERSON>r<PERSON>ş<PERSON>", "CONVERSATION_STATUS_CHANGED": "Görüşme <PERSON>", "CONVERSATION_UPDATED": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "MESSAGE_CREATED": "<PERSON><PERSON>", "MESSAGE_UPDATED": "<PERSON><PERSON>", "WEBWIDGET_TRIGGERED": "Kullanıcı tarafından canlı sohbet widget'ı açıldı", "CONTACT_CREATED": "<PERSON><PERSON><PERSON>", "CONTACT_UPDATED": "<PERSON><PERSON><PERSON>", "CONVERSATION_TYPING_ON": "Conversation Typing On", "CONVERSATION_TYPING_OFF": "Conversation Typing Off"}}, "END_POINT": {"LABEL": "Web kancası URL'si", "PLACEHOLDER": "Example: {webhookExampleURL}", "ERROR": "Lütfen geçerli bir adres girin"}, "EDIT_SUBMIT": "Web kancasını güncelle", "ADD_SUBMIT": "Web kancası oluştur"}, "TITLE": "Web Kancası", "CONFIGURE": "Yapılandır", "HEADER": "Web Kancası Ayarları", "HEADER_BTN_TXT": "Yeni Web Kancası Ekle", "LOADING": "Ekli web kancaları getiriliyor", "SEARCH_404": "Bu sorgu ile eşleşen öğe yok", "SIDEBAR_TXT": "<p> <b> Web Kancaları </b> </p> <p> Web Kancaları, her hesap için tanımlanabilen HTTP geri aramalarıdır. Chatwoot'ta mesaj oluşturma gibi olaylar tarafından tetiklenirler. Bu hesap için birden fazla web kancası oluşturabilirsiniz. <br /> <br /> Bir <b> web kancası </b> oluşturmak için, <b> Yeni Web Kancası Ekle </b> düğmesini tıklayın. Ayrıca, Sil düğmesini tıklayarak mevcut herhangi bir web kancasını kaldırabilirsiniz. </p>", "LIST": {"404": "Bu hesap için yapılandırılmış web kancası yok.", "TITLE": "Web Kancalarını Yönet", "TABLE_HEADER": {"WEBHOOK_ENDPOINT": "Web Kancası Uç Noktası", "ACTIONS": "<PERSON><PERSON><PERSON>"}}, "EDIT": {"BUTTON_TEXT": "<PERSON><PERSON><PERSON><PERSON>", "TITLE": "Web Kancasını Düzenle", "API": {"SUCCESS_MESSAGE": "Web Kancası yapılandırması başarıyla güncellendi", "ERROR_MESSAGE": "Woot Sunucusuna b<PERSON>ğlanılamadı, Lütfen daha sonra tekrar deneyin"}}, "ADD": {"CANCEL": "İptal Et", "TITLE": "Yeni Web Kancası Ekle", "API": {"SUCCESS_MESSAGE": "Web Kancası yapılandırması başarıyla eklendi", "ERROR_MESSAGE": "Woot Sunucusuna b<PERSON>ğlanılamadı, Lütfen daha sonra tekrar deneyin"}}, "DELETE": {"BUTTON_TEXT": "Sil", "API": {"SUCCESS_MESSAGE": "Web Kancası başarı<PERSON> silindi", "ERROR_MESSAGE": "Woot Sunucusuna b<PERSON>ğlanılamadı, Lütfen daha sonra tekrar deneyin"}, "CONFIRM": {"TITLE": "<PERSON><PERSON><PERSON><PERSON>", "MESSAGE": "Web Kancasını silmek istediğinizden emin misiniz? ({webhookURL})", "YES": "Evet, Sil ", "NO": "Hayır, Kalsın"}}}, "SLACK": {"DELETE": "Sil", "DELETE_CONFIRMATION": {"TITLE": "Entegrasyonu <PERSON>", "MESSAGE": "Entegrasyonu silmek istediğinizden emin misiniz? <PERSON><PERSON> işlem, Slack iş alanınızdaki konuşmalara erişim kaybına neden olacaktır."}, "HELP_TEXT": {"TITLE": "Slack Entegrasyonu <PERSON>ı", "BODY": "With this integration, all of your incoming conversations will be synced to the ***{selectedChannelName}*** channel in your Slack workspace. You can manage all your customer conversations right within the channel and never miss a message.\n\nHere are the main features of the integration:\n\n**Respond to conversations from within Slack:** To respond to a conversation in the ***{selectedChannelName}*** Slack channel, simply type out your message and send it as a thread. This will create a response back to the customer through Chatwoot. It's that simple!\n\n **Create private notes:** If you want to create private notes instead of replies, start your message with ***`note:`***. This ensures that your message is kept private and won't be visible to the customer.\n\n**Associate an agent profile:** If the person who replied on Slack has an agent profile in Chatwoot under the same email, the replies will be associated with that agent profile automatically. This means you can easily track who said what and when. On the other hand, when the replier doesn't have an associated agent profile, the replies will appear from the bot profile to the customer.", "SELECTED": "seç<PERSON>"}, "SELECT_CHANNEL": {"OPTION_LABEL": "Bir kanal seçin", "UPDATE": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "BUTTON_TEXT": "Kanalı Bağla", "DESCRIPTION": "Slack iş alanınız şimdi Chatwoot ile bağlantılı. Ancak entegrasyon şu anda etkin değil. Entegrasyonu etkinleştirmek ve bir kanalı Chatwoot'a bağlamak için lütfen aşağıdaki düğmeye tıklayın.\n\n**Not:** Eğer özel bir kanal bağlamaya çalışıyorsanız, bu adımdan önce Slack kanalına Chatwoot uygulamasını ekleyin.", "ATTENTION_REQUIRED": "<PERSON><PERSON><PERSON> gere<PERSON>r", "EXPIRED": "Slack entegrasyonunuz süresi dolmuştur. Slack üzerinde mesaj almaya devam etmek için lütfen entegrasyonu silip iş alanınızı tekrar bağlayın."}, "UPDATE_ERROR": "Entegrasyonu güncelleme hat<PERSON>ı oluş<PERSON>, lütfen tekrar deneyin", "UPDATE_SUCCESS": "Kanal başarıyla bağlandı", "FAILED_TO_FETCH_CHANNELS": "Slack'ten kanallar alınırken bir hata o<PERSON>, l<PERSON><PERSON><PERSON> tekrar deneyin"}, "DYTE": {"CLICK_HERE_TO_JOIN": "Katılmak için buraya tıkla", "LEAVE_THE_ROOM": "<PERSON><PERSON><PERSON>", "START_VIDEO_CALL_HELP_TEXT": "Müşteri ile yeni bir video görüşmesi başlatın", "JOIN_ERROR": "<PERSON><PERSON>ya katılırken bir hata o<PERSON>, lüt<PERSON> tekrar deneyin", "CREATE_ERROR": "Toplantı bağlantısı oluşturulurken bir hata oluştu, lütfen tekrar deneyin"}, "OPEN_AI": {"AI_ASSIST": "AI Yardımı", "WITH_AI": " {option} with AI ", "OPTIONS": {"REPLY_SUGGESTION": "Yanıt Önerisi", "SUMMARIZE": "Özetle", "REPHRASE": "Yazımı İyileştir", "FIX_SPELLING_GRAMMAR": "Ya<PERSON><PERSON>m ve Dilbilgisini Düzelt", "SHORTEN": "Kısalt", "EXPAND": "Genişlet", "MAKE_FRIENDLY": "<PERSON><PERSON> yap", "MAKE_FORMAL": "<PERSON><PERSON><PERSON> ton kullan", "SIMPLIFY": "Basitleştir"}, "ASSISTANCE_MODAL": {"DRAFT_TITLE": "Taslak içeriği", "GENERATED_TITLE": "Oluşturulan içerik", "AI_WRITING": "AI yazıyor", "BUTTONS": {"APPLY": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> kullan", "CANCEL": "İptal Et"}}, "CTA_MODAL": {"TITLE": "OpenAI ile Entegre Olun", "DESC": "OpenAI'nın GPT modelleri ile panonuza gelişmiş AI özellikleri getirin. Başlamak için OpenAI hesabınızdan API anahtarını girin.", "KEY_PLACEHOLDER": "OpenAI API anahtarınızı girin", "BUTTONS": {"NEED_HELP": "Yardıma mı ihtiyacınız var?", "DISMISS": "Ka<PERSON><PERSON>", "FINISH": "<PERSON><PERSON><PERSON><PERSON>"}, "DISMISS_MESSAGE": "OpenAI entegrasyonunu daha sonra istediğiniz zaman kurabilirsiniz.", "SUCCESS_MESSAGE": "OpenAI entegrasyonu başarıyla kuruldu"}, "TITLE": "AI ile İyileştir", "SUMMARY_TITLE": "AI ile Özetle", "REPLY_TITLE": "AI ile Yanıt Önerisi", "SUBTITLE": "Mevcut taslağınıza dayanarak AI tarafından geliştirilmiş bir yanıt oluşturulacaktır.", "TONE": {"TITLE": "Ton", "OPTIONS": {"PROFESSIONAL": "Profesyonel", "FRIENDLY": "Dost<PERSON>"}}, "BUTTONS": {"GENERATE": "Oluştur", "GENERATING": "Oluşturuluyor...", "CANCEL": "İptal Et"}, "GENERATE_ERROR": "İçerik işlenirken bir hata olu<PERSON>, lütfen tekrar deneyin"}, "DELETE": {"BUTTON_TEXT": "Sil", "API": {"SUCCESS_MESSAGE": "Entegrasyon ba<PERSON><PERSON><PERSON><PERSON> si<PERSON>"}}, "CONNECT": {"BUTTON_TEXT": "Bağlan"}, "DASHBOARD_APPS": {"TITLE": "Panel Uygulamaları", "HEADER_BTN_TXT": "Yeni bir panel uygulaması ekle", "SIDEBAR_TXT": "<p><b>Panel Uygulamaları</b></p><p>Panel Uygulamaları, organizasyonlara destek uzmanları için bağlam sağlamak amacıyla bir uygulamayı Chatwoot paneli içine yerleştirmelerine izin verir. <PERSON><PERSON> özellik, bağımsız bir uygulama oluşturmanıza ve bunu panel içine yerleştirmenize olanak tanır, b<PERSON><PERSON><PERSON> kullanıcı bilgilerini, siparişlerini veya önceki ödeme geçmişini sağlayabilirsiniz.</p><p>Chatwoot'taki panel üzerine uygulamanızı yerleştirdiğinizde, uygulamanız konuşma ve iletişim bağlamını pencere olayı olarak alır. Bağlamı almak için sayfanızda bir message olayı için bir dinleyici uygulayın.</p><p>Yeni bir panel uygulaması eklemek için 'Yeni bir panel uygulaması ekle' düğmesine tıklayın.</p>", "DESCRIPTION": "Panel Uygulamaları, organizasyonlara destek uzmanları için bağlam sağlamak amacıyla bir uygulamayı panel içine yerleştirmelerine izin verir. <PERSON><PERSON> ö<PERSON>, bağımsız bir uygulama oluşturmanıza ve bunu panel içine yerleştirmenize olanak tanır, bö<PERSON>ce kullanıcı bilgilerini, siparişlerini veya önceki ödeme geçmişini sağlayabilirsiniz.", "LEARN_MORE": "Learn more about Dashboard Apps", "LIST": {"404": "Bu hesapta henüz yapılandırılmış panel uygulamaları yok", "LOADING": "Panel uygulamaları alınıyor...", "TABLE_HEADER": {"NAME": "İsim", "ENDPOINT": "Endpoint"}, "EDIT_TOOLTIP": "Uygulamayı Düzenle", "DELETE_TOOLTIP": "Uygulamayı Sil"}, "FORM": {"TITLE_LABEL": "İsim", "TITLE_PLACEHOLDER": "Panel uygulamanız için bir isim girin", "TITLE_ERROR": "Panel uygulaması için bir isim gere<PERSON>lidir", "URL_LABEL": "Endpoint", "URL_PLACEHOLDER": "Uygulamanızın barındırıldığı endpoint URL'sini girin", "URL_ERROR": "Geçerli bir URL gereklidir"}, "CREATE": {"HEADER": "Yeni bir panel uygulaması ekle", "FORM_SUBMIT": "<PERSON><PERSON><PERSON>", "FORM_CANCEL": "İptal Et", "API_SUCCESS": "Panel uygulaması başarıyla yapılandırıldı", "API_ERROR": "Uygulama oluşturamadık. Lütfen daha sonra tekrar deneyin"}, "UPDATE": {"HEADER": "Panel uygulamasını düzenle", "FORM_SUBMIT": "<PERSON><PERSON><PERSON><PERSON>", "FORM_CANCEL": "İptal Et", "API_SUCCESS": "Panel uygulaması başarıyla güncellendi", "API_ERROR": "Uygulamayı güncelleyemedik. Lütfen daha sonra tekrar deneyin"}, "DELETE": {"CONFIRM_YES": "Eve<PERSON>, sil", "CONFIRM_NO": "Hayır, sakla", "TITLE": "<PERSON><PERSON><PERSON>", "MESSAGE": "Uygulamayı silmek istediğinizden emin misiniz - {appName}?", "API_SUCCESS": "Panel uygulaması başarıyla silindi", "API_ERROR": "Uygulamayı silemedik. Lütfen daha sonra tekrar deneyin"}}, "LINEAR": {"ADD_OR_LINK_BUTTON": "Create/Link Linear Issue", "LOADING": "Fetching linear issues...", "LOADING_ERROR": "There was an error fetching the linear issues, please try again", "CREATE": "<PERSON><PERSON>", "LINK": {"SEARCH": "Search issues", "SELECT": "Select issue", "TITLE": "Bağlantı", "EMPTY_LIST": "No linear issues found", "LOADING": "Loading", "ERROR": "There was an error fetching the linear issues, please try again", "LINK_SUCCESS": "Issue linked successfully", "LINK_ERROR": "There was an error linking the issue, please try again", "LINK_TITLE": "Conversation (#{conversationId}) with {name}"}, "ADD_OR_LINK": {"TITLE": "Create/link linear issue", "DESCRIPTION": "Create Linear issues from conversations, or link existing ones for seamless tracking.", "FORM": {"TITLE": {"LABEL": "Başlık", "PLACEHOLDER": "Enter title", "REQUIRED_ERROR": "Başlık gerekli"}, "DESCRIPTION": {"LABEL": "<PERSON><PERSON>ı<PERSON><PERSON>", "PLACEHOLDER": "Enter description"}, "TEAM": {"LABEL": "Ekip", "PLACEHOLDER": "<PERSON><PERSON><PERSON><PERSON> se<PERSON>", "SEARCH": "Search team", "REQUIRED_ERROR": "Team is required"}, "ASSIGNEE": {"LABEL": "Assignee", "PLACEHOLDER": "Select assignee", "SEARCH": "Search assignee"}, "PRIORITY": {"LABEL": "Öncelik", "PLACEHOLDER": "<PERSON>ncelik seçin", "SEARCH": "Search priority"}, "LABEL": {"LABEL": "Etiket", "PLACEHOLDER": "Select label", "SEARCH": "Search label"}, "STATUS": {"LABEL": "Durum", "PLACEHOLDER": "Select status", "SEARCH": "Search status"}, "PROJECT": {"LABEL": "Project", "PLACEHOLDER": "Select project", "SEARCH": "Search project"}}, "CREATE": "<PERSON><PERSON>", "CANCEL": "İptal Et", "CREATE_SUCCESS": "Issue created successfully", "CREATE_ERROR": "There was an error creating the issue, please try again", "LOADING_TEAM_ERROR": "There was an error fetching the teams, please try again", "LOADING_TEAM_ENTITIES_ERROR": "There was an error fetching the team entities, please try again"}, "ISSUE": {"STATUS": "Durum", "PRIORITY": "Öncelik", "ASSIGNEE": "Assignee", "LABELS": "<PERSON><PERSON><PERSON><PERSON>", "CREATED_AT": "Created at {createdAt}"}, "UNLINK": {"TITLE": "Unlink", "SUCCESS": "Issue unlinked successfully", "ERROR": "There was an error unlinking the issue, please try again"}, "DELETE": {"TITLE": "Are you sure you want to delete the integration?", "MESSAGE": "Are you sure you want to delete the integration?", "CONFIRM": "Eve<PERSON>, sil", "CANCEL": "İptal Et"}}}, "CAPTAIN": {"NAME": "Captain", "HEADER_KNOW_MORE": "Know more", "COPILOT": {"SEND_MESSAGE": "Mesajı Gönder...", "EMPTY_MESSAGE": "There was an error generating the response. Please try again.", "LOADER": "Captain is thinking", "YOU": "<PERSON>", "USE": "Use this", "RESET": "Reset", "SELECT_ASSISTANT": "Select Assistant", "PROMPTS": {"SUMMARIZE": {"LABEL": "Summarize this conversation", "CONTENT": "Summarize the key points discussed between the customer and the support agent, including the customer's concerns, questions, and the solutions or responses provided by the support agent"}, "SUGGEST": {"LABEL": "Suggest an answer", "CONTENT": "Analyze the customer's inquiry, and draft a response that effectively addresses their concerns or questions. Ensure the reply is clear, concise, and provides helpful information."}, "RATE": {"LABEL": "Rate this conversation", "CONTENT": "Review the conversation to see how well it meets the customer's needs. Share a rating out of 5 based on tone, clarity, and effectiveness."}}}, "PLAYGROUND": {"USER": "<PERSON>", "ASSISTANT": "Assistant", "MESSAGE_PLACEHOLDER": "Mesajınız...", "HEADER": "Playground", "DESCRIPTION": "Use this playground to send messages to your assistant and check if it responds accurately, quickly, and in the tone you expect.", "CREDIT_NOTE": "Messages sent here will count toward your Captain credits."}, "PAYWALL": {"TITLE": "Upgrade to use Captain AI", "AVAILABLE_ON": "Captain is not available on the free plan.", "UPGRADE_PROMPT": "Upgrade your plan to get access to our assistants, copilot and more.", "UPGRADE_NOW": "Upgrade now", "CANCEL_ANYTIME": "You can change or cancel your plan anytime"}, "ENTERPRISE_PAYWALL": {"AVAILABLE_ON": "Captain AI feature is only available in a paid plan.", "UPGRADE_PROMPT": "Upgrade your plan to get access to our assistants, copilot and more.", "ASK_ADMIN": "Please reach out to your administrator for the upgrade."}, "BANNER": {"RESPONSES": "You've used over 80% of your response limit. To continue using Captain AI, please upgrade.", "DOCUMENTS": "Document limit reached. Upgrade to continue using Captain AI."}, "FORM": {"CANCEL": "İptal Et", "CREATE": "<PERSON><PERSON>", "EDIT": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "ASSISTANTS": {"HEADER": "Assistants", "ADD_NEW": "Create a new assistant", "DELETE": {"TITLE": "Are you sure to delete the assistant?", "DESCRIPTION": "This action is permanent. Deleting this assistant will remove it from all connected inboxes and permanently erase all generated knowledge.", "CONFIRM": "Eve<PERSON>, sil", "SUCCESS_MESSAGE": "The assistant has been successfully deleted", "ERROR_MESSAGE": "There was an error deleting the assistant, please try again."}, "FORM_DESCRIPTION": "Fill out the details below to name your assistant, describe its purpose, and specify the product it will support.", "CREATE": {"TITLE": "Create an assistant", "SUCCESS_MESSAGE": "The assistant has been successfully created", "ERROR_MESSAGE": "There was an error creating the assistant, please try again."}, "FORM": {"UPDATE": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SECTIONS": {"BASIC_INFO": "Basic Information", "SYSTEM_MESSAGES": "System Messages", "INSTRUCTIONS": "Instructions", "FEATURES": "Özellikleri", "TOOLS": "Tools "}, "NAME": {"LABEL": "İsim", "PLACEHOLDER": "Enter assistant name", "ERROR": "The name is required"}, "DESCRIPTION": {"LABEL": "<PERSON><PERSON>ı<PERSON><PERSON>", "PLACEHOLDER": "Enter assistant description", "ERROR": "The description is required"}, "PRODUCT_NAME": {"LABEL": "Product Name", "PLACEHOLDER": "Enter product name", "ERROR": "The product name is required"}, "WELCOME_MESSAGE": {"LABEL": "Welcome Message", "PLACEHOLDER": "Enter welcome message"}, "HANDOFF_MESSAGE": {"LABEL": "Handoff Message", "PLACEHOLDER": "Enter handoff message"}, "RESOLUTION_MESSAGE": {"LABEL": "Resolution Message", "PLACEHOLDER": "Enter resolution message"}, "INSTRUCTIONS": {"LABEL": "Instructions", "PLACEHOLDER": "Enter instructions for the assistant"}, "FEATURES": {"TITLE": "Özellikleri", "ALLOW_CONVERSATION_FAQS": "Generate FAQs from resolved conversations", "ALLOW_MEMORIES": "Capture key details as memories from customer interactions."}}, "EDIT": {"TITLE": "Update the assistant", "SUCCESS_MESSAGE": "The assistant has been successfully updated", "ERROR_MESSAGE": "There was an error updating the assistant, please try again.", "NOT_FOUND": "Could not find the assistant. Please try again."}, "OPTIONS": {"EDIT_ASSISTANT": "Edit Assistant", "DELETE_ASSISTANT": "Delete Assistant", "VIEW_CONNECTED_INBOXES": "View connected inboxes"}, "EMPTY_STATE": {"TITLE": "No assistants available", "SUBTITLE": "Create an assistant to provide quick and accurate responses to your users. It can learn from your help articles and past conversations.", "FEATURE_SPOTLIGHT": {"TITLE": "Captain Assistant", "NOTE": "Captain Assistant engages directly with customers, learns from your help docs and past conversations, and delivers instant, accurate responses. It handles the initial queries, providing quick resolutions before  transferring to an agent when needed."}}}, "DOCUMENTS": {"HEADER": "Documents", "ADD_NEW": "Create a new document", "RELATED_RESPONSES": {"TITLE": "Related FAQs", "DESCRIPTION": "These FAQs are generated directly from the document."}, "FORM_DESCRIPTION": "Enter the URL of the document to add it as a knowledge source and choose the assistant to associate it with.", "CREATE": {"TITLE": "Add a document", "SUCCESS_MESSAGE": "The document has been successfully created", "ERROR_MESSAGE": "There was an error creating the document, please try again."}, "FORM": {"URL": {"LABEL": "URL", "PLACEHOLDER": "Enter the URL of the document", "ERROR": "Please provide a valid URL for the document"}, "ASSISTANT": {"LABEL": "Assistant", "PLACEHOLDER": "Select the assistant", "ERROR": "The assistant field is required"}}, "DELETE": {"TITLE": "Are you sure to delete the document?", "DESCRIPTION": "This action is permanent. Deleting this document will permanently erase all generated knowledge.", "CONFIRM": "Eve<PERSON>, sil", "SUCCESS_MESSAGE": "The document has been successfully deleted", "ERROR_MESSAGE": "There was an error deleting the document, please try again."}, "OPTIONS": {"VIEW_RELATED_RESPONSES": "View Related Responses", "DELETE_DOCUMENT": "Delete Document"}, "EMPTY_STATE": {"TITLE": "No documents available", "SUBTITLE": "Documents are used by your assistant to generate FAQs. You can import documents to provide context for your assistant.", "FEATURE_SPOTLIGHT": {"TITLE": "Captain <PERSON>ument", "NOTE": "A document in Captain serves as a knowledge resource for the assistant. By connecting your help center or guides, Captain can analyze the content and provide accurate responses for customer inquiries."}}}, "RESPONSES": {"HEADER": "FAQs", "ADD_NEW": "Create new FAQ", "DOCUMENTABLE": {"CONVERSATION": "Conversation #{id}"}, "SELECTED": "{count} selected", "BULK_APPROVE_BUTTON": "Approve", "BULK_DELETE_BUTTON": "Sil", "BULK_APPROVE": {"SUCCESS_MESSAGE": "FAQs approved successfully", "ERROR_MESSAGE": "There was an error approving the FAQs, please try again."}, "BULK_DELETE": {"TITLE": "Delete FAQs?", "DESCRIPTION": "Are you sure you want to delete the selected FAQs? This action cannot be undone.", "CONFIRM": "Yes, delete all", "SUCCESS_MESSAGE": "FAQs deleted successfully", "ERROR_MESSAGE": "There was an error deleting the FAQs, please try again."}, "DELETE": {"TITLE": "Are you sure to delete the FAQ?", "DESCRIPTION": "", "CONFIRM": "Eve<PERSON>, sil", "SUCCESS_MESSAGE": "FAQ deleted successfully", "ERROR_MESSAGE": "There was an error deleting the FAQ, please try again."}, "FILTER": {"ASSISTANT": "Assistant: {selected}", "STATUS": "Status: {selected}", "ALL_ASSISTANTS": "<PERSON><PERSON><PERSON>"}, "STATUS": {"TITLE": "Durum", "PENDING": "Bekliyor", "APPROVED": "Approved", "ALL": "<PERSON><PERSON><PERSON>"}, "FORM_DESCRIPTION": "Add a question and its corresponding answer to the knowledge base and select the assistant it should be associated with.", "CREATE": {"TITLE": "Add an FAQ", "SUCCESS_MESSAGE": "The response has been added successfully.", "ERROR_MESSAGE": "An error occurred while adding the response. Please try again."}, "FORM": {"QUESTION": {"LABEL": "Question", "PLACEHOLDER": "Enter the question here", "ERROR": "Please provide a valid question."}, "ANSWER": {"LABEL": "Answer", "PLACEHOLDER": "Enter the answer here", "ERROR": "Please provide a valid answer."}, "ASSISTANT": {"LABEL": "Assistant", "PLACEHOLDER": "Select an assistant", "ERROR": "Please select an assistant."}}, "EDIT": {"TITLE": "Update the FAQ", "SUCCESS_MESSAGE": "The FAQ has been successfully updated", "ERROR_MESSAGE": "There was an error updating the FAQ, please try again", "APPROVE_SUCCESS_MESSAGE": "The FAQ was marked as approved"}, "OPTIONS": {"APPROVE": "Mark as approved", "EDIT_RESPONSE": "Edit FAQ", "DELETE_RESPONSE": "Delete FAQ"}, "EMPTY_STATE": {"TITLE": "No FAQs Found", "SUBTITLE": "FAQs help your assistant provide quick and accurate answers to questions from your customers. They can be generated automatically from your content or can be added manually.", "FEATURE_SPOTLIGHT": {"TITLE": "Captain FAQ", "NOTE": "Captain <PERSON><PERSON><PERSON> detects common customer questions—whether missing from your knowledge base or frequently asked—and generates relevant FAQs to improve support. You can review each suggestion and decide whether to approve or reject it."}}}, "INBOXES": {"HEADER": "Connected Inboxes", "ADD_NEW": "Connect a new inbox", "OPTIONS": {"DISCONNECT": "Bağlantıyı Kes"}, "DELETE": {"TITLE": "Are you sure to disconnect the inbox?", "DESCRIPTION": "", "CONFIRM": "Eve<PERSON>, sil", "SUCCESS_MESSAGE": "The inbox was successfully disconnected.", "ERROR_MESSAGE": "There was an error disconnecting the inbox, please try again."}, "FORM_DESCRIPTION": "Choose an inbox to connect with the assistant.", "CREATE": {"TITLE": "Connect an Inbox", "SUCCESS_MESSAGE": "The inbox was successfully connected.", "ERROR_MESSAGE": "An error occurred while connecting the inbox. Please try again."}, "FORM": {"INBOX": {"LABEL": "<PERSON><PERSON><PERSON>", "PLACEHOLDER": "Choose the inbox to deploy the assistant.", "ERROR": "An inbox selection is required."}}, "EMPTY_STATE": {"TITLE": "No Connected Inboxes", "SUBTITLE": "Connecting an inbox allows the assistant to handle initial questions from your customers before transferring them to you."}}}}