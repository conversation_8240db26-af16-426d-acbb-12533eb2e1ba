{"AUDIT_LOGS": {"HEADER": "Denetim <PERSON>ıtları", "HEADER_BTN_TXT": "Denetim Kaydı Ekle", "LOADING": "Denetim Kayıtları Alınıyor", "DESCRIPTION": "Audit Logs maintain a record of activities in your account, allowing you to track and audit your account, team, or services.", "LEARN_MORE": "Learn more about audit logs", "SEARCH_404": "Bu sorgu ile eşleşen öğe yok", "SIDEBAR_TXT": "<p><b><PERSON><PERSON><PERSON></b></p><p><PERSON><PERSON><PERSON>, Chat<PERSON>ot Sistemi'ndeki olaylar ve eylemler için izlerdir.</p>", "LIST": {"404": "Bu hesapta kullanılabilir denetim kaydı yok.", "TITLE": "Denetim Kayıtlarını Yönet", "DESC": "<PERSON><PERSON><PERSON>, Chatwoot Sistemi'ndeki o<PERSON> ve e<PERSON>mler için izlerdir.", "TABLE_HEADER": {"ACTIVITY": "Kullanıcı", "TIME": "<PERSON><PERSON><PERSON>", "IP_ADDRESS": "IP Adresi"}}, "API": {"SUCCESS_MESSAGE": "Denetim Kayıtları başarıyla alındı", "ERROR_MESSAGE": "Woot Sunucusuna b<PERSON>ğlanılamadı, Lütfen daha sonra tekrar deneyin"}, "DEFAULT_USER": "Sistem", "AUTOMATION_RULE": {"ADD": "{agentName} created a new automation rule (#{id})", "EDIT": "{agentName} updated an automation rule (#{id})", "DELETE": "{agent<PERSON>ame} deleted an automation rule (#{id})"}, "ACCOUNT_USER": {"ADD": "{agent<PERSON><PERSON>}, {invitee}'yi {role} olarak hesaba davet etti", "EDIT": {"SELF": "{agentName}, {attributes}ını {values} olarak değiştirdi", "OTHER": "{agentName}, {user}'ın {attributes}ını {values} o<PERSON><PERSON>", "DELETED": "{agent<PERSON><PERSON>} changed {attributes} of a deleted user to {values}"}}, "INBOX": {"ADD": "{agentName} created a new inbox (#{id})", "EDIT": "{agentName} updated an inbox (#{id})", "DELETE": "{agent<PERSON>ame} deleted an inbox (#{id})"}, "WEBHOOK": {"ADD": "{agent<PERSON>ame} created a new webhook (#{id})", "EDIT": "{agent<PERSON>ame} updated a webhook (#{id})", "DELETE": "{agent<PERSON><PERSON>} deleted a webhook (#{id})"}, "USER_ACTION": {"SIGN_IN": "{agentName} giri<PERSON> yaptı", "SIGN_OUT": "{agentName} ç<PERSON><PERSON><PERSON><PERSON> yaptı"}, "TEAM": {"ADD": "{<PERSON><PERSON><PERSON>} created a new team (#{id})", "EDIT": "{<PERSON><PERSON><PERSON>} updated a team (#{id})", "DELETE": "{<PERSON><PERSON><PERSON>} deleted a team (#{id})"}, "MACRO": {"ADD": "{agent<PERSON>ame} created a new macro (#{id})", "EDIT": "{agent<PERSON>ame} updated a macro (#{id})", "DELETE": "{agent<PERSON>ame} deleted a macro (#{id})"}, "INBOX_MEMBER": {"ADD": "{agent<PERSON>ame} added {user} to the inbox(#{inbox_id})", "REMOVE": "{<PERSON><PERSON><PERSON>} removed {user} from the inbox(#{inbox_id})"}, "TEAM_MEMBER": {"ADD": "{<PERSON><PERSON><PERSON>} added {user} to the team(#{team_id})", "REMOVE": "{<PERSON><PERSON><PERSON>} removed {user} from the team(#{team_id})"}, "ACCOUNT": {"EDIT": "{<PERSON><PERSON><PERSON>} updated the account configuration (#{id})"}}}