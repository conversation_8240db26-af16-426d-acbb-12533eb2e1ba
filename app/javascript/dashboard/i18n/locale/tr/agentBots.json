{"AGENT_BOTS": {"HEADER": "<PERSON><PERSON><PERSON>", "LOADING_EDITOR": "<PERSON><PERSON><PERSON>...", "DESCRIPTION": "Agent <PERSON><PERSON> are like the most fabulous members of your team. They can handle the small stuff, so you can focus on the stuff that matters. Give them a try. You can manage your bots from this page or create new ones using the 'Add Bot' button.", "LEARN_MORE": "Learn about agent bots", "GLOBAL_BOT": "System bot", "GLOBAL_BOT_BADGE": "Sistem", "AVATAR": {"SUCCESS_DELETE": "<PERSON><PERSON> avatar deleted successfully", "ERROR_DELETE": "Error deleting bot avatar, please try again"}, "BOT_CONFIGURATION": {"TITLE": "Bir temsilci botu seçin", "DESC": "Gelen kutunuza bir temsilci Botu atayın. Küçük konuşmaları ele alabilir ve gerekli olduğunda canlı bir temsilciın devralmasını sağlayabilirler.", "SUBMIT": "<PERSON><PERSON><PERSON><PERSON>", "DISCONNECT": "Botu bağlantısını kes", "SUCCESS_MESSAGE": "Temsilci botu başarıyla güncellendi.", "DISCONNECTED_SUCCESS_MESSAGE": "Temsilci botu başarıyla bağlantısı kesildi.", "ERROR_MESSAGE": "Temsilci botu güncellenemedi. Lütfen tekrar deneyin.", "DISCONNECTED_ERROR_MESSAGE": "Temsilci botu bağlantısı kesilemedi. Lütfen tekrar deneyin.", "SELECT_PLACEHOLDER": "<PERSON><PERSON>"}, "ADD": {"TITLE": "<PERSON><PERSON>", "CANCEL_BUTTON_TEXT": "İptal Et", "API": {"SUCCESS_MESSAGE": "Bot başarıyla eklendi.", "ERROR_MESSAGE": "Bot eklenemedi. Lütfen daha sonra tekrar deneyin."}}, "LIST": {"404": "No bots found. You can create a bot by clicking the 'Add Bot' button.", "LOADING": "Bo<PERSON>ar alınıyor...", "TABLE_HEADER": {"DETAILS": "Bot Details", "URL": "Webhook URL'si"}}, "DELETE": {"BUTTON_TEXT": "Sil", "TITLE": "<PERSON><PERSON> sil", "CONFIRM": {"TITLE": "<PERSON><PERSON><PERSON><PERSON> on<PERSON>la", "MESSAGE": "Are you sure you want to delete {name}?", "YES": "Evet, Sil", "NO": "Hayır, Sakla"}, "API": {"SUCCESS_MESSAGE": "<PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON><PERSON>.", "ERROR_MESSAGE": "Bot silinemedi. Lütfen tekrar deneyin."}}, "EDIT": {"BUTTON_TEXT": "<PERSON><PERSON><PERSON><PERSON>", "TITLE": "<PERSON><PERSON>", "API": {"SUCCESS_MESSAGE": "<PERSON><PERSON> baş<PERSON><PERSON><PERSON> gü<PERSON>.", "ERROR_MESSAGE": "Bot güncellenemedi. Lütfen tekrar deneyin."}}, "FORM": {"AVATAR": {"LABEL": "<PERSON><PERSON> avatar"}, "NAME": {"LABEL": "Bot Adı", "PLACEHOLDER": "Enter bot name", "REQUIRED": "Bot adı zorunludur"}, "DESCRIPTION": {"LABEL": "<PERSON><PERSON>ı<PERSON><PERSON>", "PLACEHOLDER": "Bu bot ne iş yapar?"}, "WEBHOOK_URL": {"LABEL": "Webhook URL'si", "PLACEHOLDER": "https://example.com/webhook", "REQUIRED": "Webhook URL is required"}, "ERRORS": {"NAME": "Bot adı zorunludur", "URL": "Webhook URL is required", "VALID_URL": "Please enter a valid URL starting with http:// or https://"}, "CANCEL": "İptal Et", "CREATE": "Create <PERSON><PERSON>", "UPDATE": "Update Bot"}, "WEBHOOK": {"DESCRIPTION": "Configure a webhook bot to integrate with your custom services. The bot will receive and process events from conversations and can respond to them."}, "TYPES": {"WEBHOOK": "Webhook botu"}}}