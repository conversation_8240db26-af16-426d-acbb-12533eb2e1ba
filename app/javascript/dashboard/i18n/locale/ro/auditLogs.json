{"AUDIT_LOGS": {"HEADER": "Jurnale de audit", "HEADER_BTN_TXT": "Adăugarea jurnalelor de audit", "LOADING": "Preluarea jurnalelor de audit", "DESCRIPTION": "Audit Logs maintain a record of activities in your account, allowing you to track and audit your account, team, or services.", "LEARN_MORE": "Learn more about audit logs", "SEARCH_404": "Nu există elemente care să corespundă acestei interogări", "SIDEBAR_TXT": "<p><b>Jurnale de audit</b> </p><p> Jurnalele de audit sunt piste pentru evenimente și acțiuni într-un sistem Chatwoot. </p>", "LIST": {"404": "Nu există jurnale de audit disponibile în acest cont.", "TITLE": "Gestionarea jurnalelor de audit", "DESC": "Jurnalele de audit sunt piste pentru evenimente și acțiuni într-un sistem Chatwoot.", "TABLE_HEADER": {"ACTIVITY": "Activitate", "TIME": "<PERSON><PERSON>", "IP_ADDRESS": "Adresă IP"}}, "API": {"SUCCESS_MESSAGE": "AuditLogs preluat cu succes", "ERROR_MESSAGE": "Nu s-a putut conecta la Woot Server, încercați din nou mai târziu"}, "DEFAULT_USER": "Sistem", "AUTOMATION_RULE": {"ADD": "{agentName} created a new automation rule (#{id})", "EDIT": "{agentName} updated an automation rule (#{id})", "DELETE": "{agent<PERSON>ame} deleted an automation rule (#{id})"}, "ACCOUNT_USER": {"ADD": "{<PERSON><PERSON><PERSON>} invited {invitee} to the account as an {role}", "EDIT": {"SELF": "{agent<PERSON><PERSON>} changed their {attributes} to {values}", "OTHER": "{agent<PERSON><PERSON>} changed {attributes} of {user} to {values}", "DELETED": "{agent<PERSON><PERSON>} changed {attributes} of a deleted user to {values}"}}, "INBOX": {"ADD": "{agentName} created a new inbox (#{id})", "EDIT": "{agentName} updated an inbox (#{id})", "DELETE": "{agent<PERSON>ame} deleted an inbox (#{id})"}, "WEBHOOK": {"ADD": "{agent<PERSON>ame} created a new webhook (#{id})", "EDIT": "{agent<PERSON>ame} updated a webhook (#{id})", "DELETE": "{agent<PERSON><PERSON>} deleted a webhook (#{id})"}, "USER_ACTION": {"SIGN_IN": "{agent<PERSON>ame} s-a autentificat", "SIGN_OUT": "{agentName} s-a deconectat"}, "TEAM": {"ADD": "{<PERSON><PERSON><PERSON>} created a new team (#{id})", "EDIT": "{<PERSON><PERSON><PERSON>} updated a team (#{id})", "DELETE": "{<PERSON><PERSON><PERSON>} deleted a team (#{id})"}, "MACRO": {"ADD": "{agent<PERSON>ame} created a new macro (#{id})", "EDIT": "{agent<PERSON>ame} updated a macro (#{id})", "DELETE": "{agent<PERSON>ame} deleted a macro (#{id})"}, "INBOX_MEMBER": {"ADD": "{agent<PERSON>ame} added {user} to the inbox(#{inbox_id})", "REMOVE": "{<PERSON><PERSON><PERSON>} removed {user} from the inbox(#{inbox_id})"}, "TEAM_MEMBER": {"ADD": "{<PERSON><PERSON><PERSON>} added {user} to the team(#{team_id})", "REMOVE": "{<PERSON><PERSON><PERSON>} removed {user} from the team(#{team_id})"}, "ACCOUNT": {"EDIT": "{<PERSON><PERSON><PERSON>} updated the account configuration (#{id})"}}}