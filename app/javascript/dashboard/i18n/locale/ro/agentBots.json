{"AGENT_BOTS": {"HEADER": "<PERSON><PERSON><PERSON>", "LOADING_EDITOR": "Loading editor...", "DESCRIPTION": "Agent <PERSON><PERSON> are like the most fabulous members of your team. They can handle the small stuff, so you can focus on the stuff that matters. Give them a try. You can manage your bots from this page or create new ones using the 'Add Bot' button.", "LEARN_MORE": "Learn about agent bots", "GLOBAL_BOT": "System bot", "GLOBAL_BOT_BADGE": "Sistem", "AVATAR": {"SUCCESS_DELETE": "<PERSON><PERSON> avatar deleted successfully", "ERROR_DELETE": "Error deleting bot avatar, please try again"}, "BOT_CONFIGURATION": {"TITLE": "Selectați un bot de agent", "DESC": "Assign an Agent Bot to your inbox. They can handle initial conversations and transfer them to a live agent when necessary.", "SUBMIT": "Actualizare", "DISCONNECT": "Disconnect bot", "SUCCESS_MESSAGE": "A actualizat cu succes botul agentului.", "DISCONNECTED_SUCCESS_MESSAGE": "Deconectat cu succes botul agentului.", "ERROR_MESSAGE": "Could not update the agent bot. Please try again.", "DISCONNECTED_ERROR_MESSAGE": "Could not disconnect the agent bot. Please try again.", "SELECT_PLACEHOLDER": "Select bot"}, "ADD": {"TITLE": "<PERSON><PERSON>", "CANCEL_BUTTON_TEXT": "<PERSON><PERSON><PERSON><PERSON>", "API": {"SUCCESS_MESSAGE": "Bot adăugat cu succes.", "ERROR_MESSAGE": "Could not add bot. Please try again later."}}, "LIST": {"404": "No bots found. You can create a bot by clicking the 'Add Bot' button.", "LOADING": "Fetching bots...", "TABLE_HEADER": {"DETAILS": "Bot Details", "URL": "URL Webhook"}}, "DELETE": {"BUTTON_TEXT": "Şterge", "TITLE": "Delete bot", "CONFIRM": {"TITLE": "Confirmă ș<PERSON>ea", "MESSAGE": "Are you sure you want to delete {name}?", "YES": "Da, șterge", "NO": "Nu, păstreaza"}, "API": {"SUCCESS_MESSAGE": "Agent sters cu succes.", "ERROR_MESSAGE": "Could not delete bot. Please try again."}}, "EDIT": {"BUTTON_TEXT": "Editare", "TITLE": "Edit bot", "API": {"SUCCESS_MESSAGE": "Bot actualizat cu succes.", "ERROR_MESSAGE": "Could not update bot. Please try again."}}, "FORM": {"AVATAR": {"LABEL": "<PERSON><PERSON> avatar"}, "NAME": {"LABEL": "Bot name", "PLACEHOLDER": "Enter bot name", "REQUIRED": "Numele botului este necesar"}, "DESCRIPTION": {"LABEL": "Des<PERSON><PERSON><PERSON>", "PLACEHOLDER": "Ce face acest bot?"}, "WEBHOOK_URL": {"LABEL": "URL Webhook", "PLACEHOLDER": "https://example.com/webhook", "REQUIRED": "Webhook URL is required"}, "ERRORS": {"NAME": "Numele botului este necesar", "URL": "Webhook URL is required", "VALID_URL": "Please enter a valid URL starting with http:// or https://"}, "CANCEL": "<PERSON><PERSON><PERSON><PERSON>", "CREATE": "Create <PERSON><PERSON>", "UPDATE": "Update Bot"}, "WEBHOOK": {"DESCRIPTION": "Configure a webhook bot to integrate with your custom services. The bot will receive and process events from conversations and can respond to them."}, "TYPES": {"WEBHOOK": "Webhook bot"}}}