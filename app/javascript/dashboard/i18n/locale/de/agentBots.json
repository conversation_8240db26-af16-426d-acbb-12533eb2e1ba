{"AGENT_BOTS": {"HEADER": "<PERSON><PERSON>", "LOADING_EDITOR": "Lade Editor...", "DESCRIPTION": "Agent <PERSON><PERSON> are like the most fabulous members of your team. They can handle the small stuff, so you can focus on the stuff that matters. Give them a try. You can manage your bots from this page or create new ones using the 'Add Bot' button.", "LEARN_MORE": "Learn about agent bots", "GLOBAL_BOT": "System bot", "GLOBAL_BOT_BADGE": "System", "AVATAR": {"SUCCESS_DELETE": "<PERSON><PERSON> avatar deleted successfully", "ERROR_DELETE": "Error deleting bot avatar, please try again"}, "BOT_CONFIGURATION": {"TITLE": "Agenten-Bot auswählen", "DESC": "Weise deinem Posteingang einen Agent Bot zu. Er kann die ersten Konversationen bearbeiten und sie bei Bedarf an einen echten Agenten übertragen.", "SUBMIT": "Aktualisieren", "DISCONNECT": "Bot-Verbindung trennen", "SUCCESS_MESSAGE": "Agenten-Bot erfolgreich aktualisiert.", "DISCONNECTED_SUCCESS_MESSAGE": "Agent-<PERSON><PERSON> er<PERSON><PERSON>g<PERSON>ich getrennt.", "ERROR_MESSAGE": "Der Agent Bot konnte nicht aktualisiert werden, bitte versuche es später erneut.", "DISCONNECTED_ERROR_MESSAGE": "Der Agent Bot konnte nicht entfernt werden, bitte versuchen Sie es später erneut.", "SELECT_PLACEHOLDER": "Bot auswählen"}, "ADD": {"TITLE": "<PERSON><PERSON>", "CANCEL_BUTTON_TEXT": "Stornieren", "API": {"SUCCESS_MESSAGE": "<PERSON><PERSON> erfolgreich hinzugefügt.", "ERROR_MESSAGE": "<PERSON>t konnte nicht hinzugefügt werden, bitte versuchen Sie es später erneut."}}, "LIST": {"404": "No bots found. You can create a bot by clicking the 'Add Bot' button.", "LOADING": "Bots werden geladen...", "TABLE_HEADER": {"DETAILS": "Bot Details", "URL": "Webhook-URL"}}, "DELETE": {"BUTTON_TEXT": "Löschen", "TITLE": "Bot löschen", "CONFIRM": {"TITLE": "Löschung bestätigen", "MESSAGE": "Are you sure you want to delete {name}?", "YES": "Ja, löschen", "NO": "<PERSON>ein, behalten"}, "API": {"SUCCESS_MESSAGE": "Bot erfolgreich gelö<PERSON>t.", "ERROR_MESSAGE": "Der Bot konnte nicht gelöscht werden, bitte versuche es später erneut."}}, "EDIT": {"BUTTON_TEXT": "<PERSON><PERSON><PERSON>", "TITLE": "<PERSON><PERSON> bear<PERSON>ten", "API": {"SUCCESS_MESSAGE": "Bot erfolgreich aktualisiert.", "ERROR_MESSAGE": "Der Bot konnte nicht aktualisiert werden, bitte versuche es später erneut."}}, "FORM": {"AVATAR": {"LABEL": "<PERSON><PERSON> avatar"}, "NAME": {"LABEL": "Bot Name", "PLACEHOLDER": "Enter bot name", "REQUIRED": "Bot Name ist erforderlich"}, "DESCRIPTION": {"LABEL": "Beschreibung", "PLACEHOLDER": "Was macht dieser <PERSON><PERSON>?"}, "WEBHOOK_URL": {"LABEL": "Webhook-URL", "PLACEHOLDER": "https://example.com/webhook", "REQUIRED": "Webhook URL is required"}, "ERRORS": {"NAME": "Bot Name ist erforderlich", "URL": "Webhook URL is required", "VALID_URL": "Please enter a valid URL starting with http:// or https://"}, "CANCEL": "Stornieren", "CREATE": "Create <PERSON><PERSON>", "UPDATE": "Update Bot"}, "WEBHOOK": {"DESCRIPTION": "Configure a webhook bot to integrate with your custom services. The bot will receive and process events from conversations and can respond to them."}, "TYPES": {"WEBHOOK": "Webhook Bot"}}}