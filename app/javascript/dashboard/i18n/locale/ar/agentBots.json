{"AGENT_BOTS": {"HEADER": "الروبوتات", "LOADING_EDITOR": "جار جلب المحرر...", "DESCRIPTION": "Agent <PERSON><PERSON> are like the most fabulous members of your team. They can handle the small stuff, so you can focus on the stuff that matters. Give them a try. You can manage your bots from this page or create new ones using the 'Add Bot' button.", "LEARN_MORE": "Learn about agent bots", "GLOBAL_BOT": "System bot", "GLOBAL_BOT_BADGE": "النظام", "AVATAR": {"SUCCESS_DELETE": "<PERSON><PERSON> avatar deleted successfully", "ERROR_DELETE": "Error deleting bot avatar, please try again"}, "BOT_CONFIGURATION": {"TITLE": "اختر الروبوت", "DESC": "قم بتعيين روبوت لصندوق الوارد الخاص بك. يمكنهم التعامل مع المحادثات الأولية ونقلها إلى وكيل مباشر عند الضرورة.", "SUBMIT": "تحديث", "DISCONNECT": "قطع اتصال الروبوت", "SUCCESS_MESSAGE": "تم تحديث الروبوت بنجاح.", "DISCONNECTED_SUCCESS_MESSAGE": "تم فصل الروبوت بنجاح.", "ERROR_MESSAGE": "تعذر تحديث الروبوت. يرجى المحاولة مرة أخرى.", "DISCONNECTED_ERROR_MESSAGE": "تعذر فصل الروبوت. يرجى المحاولة مرة أخرى.", "SELECT_PLACEHOLDER": "اختر الروبوت"}, "ADD": {"TITLE": "<PERSON><PERSON>", "CANCEL_BUTTON_TEXT": "إلغاء", "API": {"SUCCESS_MESSAGE": "تمت إضافة الروبوت بنجاح.", "ERROR_MESSAGE": "تعذر إضافة الروبوت. يرجى المحاولة مرة أخرى لاحقًا."}}, "LIST": {"404": "No bots found. You can create a bot by clicking the 'Add Bot' button.", "LOADING": "جار جلب الروبوتات...", "TABLE_HEADER": {"DETAILS": "Bot Details", "URL": "رابط Webhook"}}, "DELETE": {"BUTTON_TEXT": "<PERSON><PERSON><PERSON>", "TITLE": "<PERSON>ذ<PERSON> الروبوت", "CONFIRM": {"TITLE": "تأكيد الحذف", "MESSAGE": "Are you sure you want to delete {name}?", "YES": "نعم، احذف", "NO": "لا، احتفظ"}, "API": {"SUCCESS_MESSAGE": "تم حذف الروبوت بنجاح.", "ERROR_MESSAGE": "تعذر حذف الروبوت. يرجى المحاولة مرة أخرى."}}, "EDIT": {"BUTTON_TEXT": "تعديل", "TITLE": "تعديل الروبوت", "API": {"SUCCESS_MESSAGE": "تم تحديث الروبوت بنجاح.", "ERROR_MESSAGE": "تعذر تحديث الروبوت. يرجى المحاولة مرة أخرى."}}, "FORM": {"AVATAR": {"LABEL": "<PERSON><PERSON> avatar"}, "NAME": {"LABEL": "اسم الروبوت", "PLACEHOLDER": "Enter bot name", "REQUIRED": "اسم الروبوت مطلوب"}, "DESCRIPTION": {"LABEL": "الوصف", "PLACEHOLDER": "ماذا يفعل هذا الروبوت؟"}, "WEBHOOK_URL": {"LABEL": "رابط Webhook", "PLACEHOLDER": "https://example.com/webhook", "REQUIRED": "Webhook URL is required"}, "ERRORS": {"NAME": "اسم الروبوت مطلوب", "URL": "Webhook URL is required", "VALID_URL": "Please enter a valid URL starting with http:// or https://"}, "CANCEL": "إلغاء", "CREATE": "Create <PERSON><PERSON>", "UPDATE": "Update Bot"}, "WEBHOOK": {"DESCRIPTION": "Configure a webhook bot to integrate with your custom services. The bot will receive and process events from conversations and can respond to them."}, "TYPES": {"WEBHOOK": "روبوت الـWebhook"}}}