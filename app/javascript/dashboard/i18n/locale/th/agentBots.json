{"AGENT_BOTS": {"HEADER": "บอท", "LOADING_EDITOR": "Loading editor...", "DESCRIPTION": "Agent <PERSON><PERSON> are like the most fabulous members of your team. They can handle the small stuff, so you can focus on the stuff that matters. Give them a try. You can manage your bots from this page or create new ones using the 'Add Bot' button.", "LEARN_MORE": "Learn about agent bots", "GLOBAL_BOT": "System bot", "GLOBAL_BOT_BADGE": "System", "AVATAR": {"SUCCESS_DELETE": "<PERSON><PERSON> avatar deleted successfully", "ERROR_DELETE": "Error deleting bot avatar, please try again"}, "BOT_CONFIGURATION": {"TITLE": "Select an agent bot", "DESC": "Assign an Agent Bot to your inbox. They can handle initial conversations and transfer them to a live agent when necessary.", "SUBMIT": "อัพเดท", "DISCONNECT": "Disconnect bot", "SUCCESS_MESSAGE": "Successfully updated the agent bot.", "DISCONNECTED_SUCCESS_MESSAGE": "Successfully disconnected the agent bot.", "ERROR_MESSAGE": "Could not update the agent bot. Please try again.", "DISCONNECTED_ERROR_MESSAGE": "Could not disconnect the agent bot. Please try again.", "SELECT_PLACEHOLDER": "Select bot"}, "ADD": {"TITLE": "<PERSON><PERSON>", "CANCEL_BUTTON_TEXT": "ยกเลิก", "API": {"SUCCESS_MESSAGE": "<PERSON><PERSON> added successfully.", "ERROR_MESSAGE": "Could not add bot. Please try again later."}}, "LIST": {"404": "No bots found. You can create a bot by clicking the 'Add Bot' button.", "LOADING": "Fetching bots...", "TABLE_HEADER": {"DETAILS": "Bot Details", "URL": "ลิ้ง Webhook"}}, "DELETE": {"BUTTON_TEXT": "ลบ", "TITLE": "Delete bot", "CONFIRM": {"TITLE": "ยืนยันการลบ", "MESSAGE": "Are you sure you want to delete {name}?", "YES": "เอาเลย", "NO": "ไม่"}, "API": {"SUCCESS_MESSAGE": "<PERSON><PERSON> deleted successfully.", "ERROR_MESSAGE": "Could not delete bot. Please try again."}}, "EDIT": {"BUTTON_TEXT": "เเก้ไข", "TITLE": "Edit bot", "API": {"SUCCESS_MESSAGE": "Bo<PERSON> updated successfully.", "ERROR_MESSAGE": "Could not update bot. Please try again."}}, "FORM": {"AVATAR": {"LABEL": "<PERSON><PERSON> avatar"}, "NAME": {"LABEL": "Bot name", "PLACEHOLDER": "Enter bot name", "REQUIRED": "กรุณากรอกชื่อของบอท"}, "DESCRIPTION": {"LABEL": "คำอธิบาย", "PLACEHOLDER": "What does this bot do?"}, "WEBHOOK_URL": {"LABEL": "ลิ้ง Webhook", "PLACEHOLDER": "https://example.com/webhook", "REQUIRED": "Webhook URL is required"}, "ERRORS": {"NAME": "กรุณากรอกชื่อของบอท", "URL": "Webhook URL is required", "VALID_URL": "Please enter a valid URL starting with http:// or https://"}, "CANCEL": "ยกเลิก", "CREATE": "Create <PERSON><PERSON>", "UPDATE": "Update Bot"}, "WEBHOOK": {"DESCRIPTION": "Configure a webhook bot to integrate with your custom services. The bot will receive and process events from conversations and can respond to them."}, "TYPES": {"WEBHOOK": "Webhook bot"}}}