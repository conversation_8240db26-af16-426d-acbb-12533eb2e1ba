{"INTEGRATION_APPS": {"FETCHING": "שליפת אינטגרציות", "NO_HOOK_CONFIGURED": "אין {integrationId} אינטגרציות מוגדרות בחשבון זה.", "HEADER": "יישומים", "STATUS": {"ENABLED": "מופעל", "DISABLED": "כב<PERSON>י"}, "CONFIGURE": "הג<PERSON>ר", "ADD_BUTTON": "הוסף קרס חדש", "DELETE": {"TITLE": {"INBOX": "א<PERSON>ר מחי<PERSON>ה", "ACCOUNT": "התנתק"}, "MESSAGE": {"INBOX": "האם אתה בטוח שברצונך למחוק?", "ACCOUNT": "אתה בטוח שאתה מתנתק?"}, "CONFIRM_BUTTON_TEXT": {"INBOX": "כן, מחק", "ACCOUNT": "כן, התנתק"}, "CANCEL_BUTTON_TEXT": "ביטול", "API": {"SUCCESS_MESSAGE": "ה-Hook נמחק בהצלחה", "ERROR_MESSAGE": "לא ניתן להתחבר לשרת Woot, נסה שוב מאוחר יותר"}}, "LIST": {"FETCHING": "הוצאת קרס אינטגרציה", "INBOX": "תיבת הדואר הנכנס", "DELETE": {"BUTTON_TEXT": "מחק"}}, "ADD": {"FORM": {"INBOX": {"LABEL": "<PERSON><PERSON>ר תי<PERSON>ת דואר", "PLACEHOLDER": "<PERSON><PERSON>ר תי<PERSON>ת דואר"}, "SUBMIT": "צור", "CANCEL": "ביטול"}, "API": {"SUCCESS_MESSAGE": "קרס אינטגרציה נוסף בהצלחה", "ERROR_MESSAGE": "לא ניתן להתחבר לשרת Woot, נסה שוב מאוחר יותר"}}, "CONNECT": {"BUTTON_TEXT": "התח<PERSON>ר"}, "DISCONNECT": {"BUTTON_TEXT": "התנתק"}, "SIDEBAR_DESCRIPTION": {"DIALOGFLOW": "Dialogflow is a natural language processing platform for building conversational interfaces. Integrating it with {installationName} lets bots handle queries first and transfer them to agents when needed. It helps qualify leads and reduce agent workload by answering FAQs. To add Dialogflow, create a Service Account in Google Console and share the credentials. Refer to the docs for details"}}}