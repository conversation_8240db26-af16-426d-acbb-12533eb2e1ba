{"AGENT_BOTS": {"HEADER": "בוטים", "LOADING_EDITOR": "Loading editor...", "DESCRIPTION": "Agent <PERSON><PERSON> are like the most fabulous members of your team. They can handle the small stuff, so you can focus on the stuff that matters. Give them a try. You can manage your bots from this page or create new ones using the 'Add Bot' button.", "LEARN_MORE": "Learn about agent bots", "GLOBAL_BOT": "System bot", "GLOBAL_BOT_BADGE": "System", "AVATAR": {"SUCCESS_DELETE": "<PERSON><PERSON> avatar deleted successfully", "ERROR_DELETE": "Error deleting bot avatar, please try again"}, "BOT_CONFIGURATION": {"TITLE": "<PERSON><PERSON>ר סו<PERSON>ן בוט", "DESC": "Assign an Agent Bot to your inbox. They can handle initial conversations and transfer them to a live agent when necessary.", "SUBMIT": "עד<PERSON><PERSON>", "DISCONNECT": "Disconnect bot", "SUCCESS_MESSAGE": "סוכן הבוט עודכן בהצלחה.", "DISCONNECTED_SUCCESS_MESSAGE": "סוכן הבוט נותק בהצלחה.", "ERROR_MESSAGE": "Could not update the agent bot. Please try again.", "DISCONNECTED_ERROR_MESSAGE": "Could not disconnect the agent bot. Please try again.", "SELECT_PLACEHOLDER": "Select bot"}, "ADD": {"TITLE": "<PERSON><PERSON>", "CANCEL_BUTTON_TEXT": "ביטול", "API": {"SUCCESS_MESSAGE": "הבוט התווסף בהצלחה.", "ERROR_MESSAGE": "Could not add bot. Please try again later."}}, "LIST": {"404": "No bots found. You can create a bot by clicking the 'Add Bot' button.", "LOADING": "Fetching bots...", "TABLE_HEADER": {"DETAILS": "Bot Details", "URL": "כתובת אתר של Webhook"}}, "DELETE": {"BUTTON_TEXT": "מחק", "TITLE": "Delete bot", "CONFIRM": {"TITLE": "א<PERSON>ר מחי<PERSON>ה", "MESSAGE": "Are you sure you want to delete {name}?", "YES": "כן, מחק", "NO": "לא, השאר"}, "API": {"SUCCESS_MESSAGE": "הבוט נמחק בהצלחה.", "ERROR_MESSAGE": "Could not delete bot. Please try again."}}, "EDIT": {"BUTTON_TEXT": "ערוך", "TITLE": "Edit bot", "API": {"SUCCESS_MESSAGE": "הבוט עודכן בהצלחה.", "ERROR_MESSAGE": "Could not update bot. Please try again."}}, "FORM": {"AVATAR": {"LABEL": "<PERSON><PERSON> avatar"}, "NAME": {"LABEL": "Bot name", "PLACEHOLDER": "Enter bot name", "REQUIRED": "חובה לתת שם לבוט"}, "DESCRIPTION": {"LABEL": "תיאור", "PLACEHOLDER": "מה הבוט הזה עושה?"}, "WEBHOOK_URL": {"LABEL": "כתובת אתר של Webhook", "PLACEHOLDER": "https://example.com/webhook", "REQUIRED": "Webhook URL is required"}, "ERRORS": {"NAME": "חובה לתת שם לבוט", "URL": "Webhook URL is required", "VALID_URL": "Please enter a valid URL starting with http:// or https://"}, "CANCEL": "ביטול", "CREATE": "Create <PERSON><PERSON>", "UPDATE": "Update Bot"}, "WEBHOOK": {"DESCRIPTION": "Configure a webhook bot to integrate with your custom services. The bot will receive and process events from conversations and can respond to them."}, "TYPES": {"WEBHOOK": "Webhook bot"}}}