{"INTEGRATION_APPS": {"FETCHING": "Fetching Integrations", "NO_HOOK_CONFIGURED": "There are no {integrationId} integrations configured in this account.", "HEADER": "Applications", "STATUS": {"ENABLED": "Käytössä", "DISABLED": "<PERSON><PERSON>"}, "CONFIGURE": "Määrittele", "ADD_BUTTON": "Add a new hook", "DELETE": {"TITLE": {"INBOX": "Confirm deletion", "ACCOUNT": "Disconnect"}, "MESSAGE": {"INBOX": "<PERSON><PERSON><PERSON> varma että haluat poistaa?", "ACCOUNT": "Are you sure to disconnect?"}, "CONFIRM_BUTTON_TEXT": {"INBOX": "<PERSON><PERSON><PERSON>ä, poista", "ACCOUNT": "Yes, Disconnect"}, "CANCEL_BUTTON_TEXT": "Peruuta", "API": {"SUCCESS_MESSAGE": "Hook deleted successfully", "ERROR_MESSAGE": "<PERSON><PERSON><PERSON><PERSON> muodostaminen Woot-palvelimelle ei onnist<PERSON>ut, yrit<PERSON> my<PERSON><PERSON> uudelleen"}}, "LIST": {"FETCHING": "Fetching integration hooks", "INBOX": "Inbox", "DELETE": {"BUTTON_TEXT": "Poista"}}, "ADD": {"FORM": {"INBOX": {"LABEL": "Select Inbox", "PLACEHOLDER": "Select Inbox"}, "SUBMIT": "<PERSON><PERSON>", "CANCEL": "Peruuta"}, "API": {"SUCCESS_MESSAGE": "Integration hook added successfully", "ERROR_MESSAGE": "<PERSON><PERSON><PERSON><PERSON> muodostaminen Woot-palvelimelle ei onnist<PERSON>ut, yrit<PERSON> my<PERSON><PERSON> uudelleen"}}, "CONNECT": {"BUTTON_TEXT": "Yhdistä"}, "DISCONNECT": {"BUTTON_TEXT": "Disconnect"}, "SIDEBAR_DESCRIPTION": {"DIALOGFLOW": "Dialogflow is a natural language processing platform for building conversational interfaces. Integrating it with {installationName} lets bots handle queries first and transfer them to agents when needed. It helps qualify leads and reduce agent workload by answering FAQs. To add Dialogflow, create a Service Account in Google Console and share the credentials. Refer to the docs for details"}}}