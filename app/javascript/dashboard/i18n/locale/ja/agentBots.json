{"AGENT_BOTS": {"HEADER": "ボット", "LOADING_EDITOR": "エディターを読み込んでいます...", "DESCRIPTION": "Agent <PERSON><PERSON> are like the most fabulous members of your team. They can handle the small stuff, so you can focus on the stuff that matters. Give them a try. You can manage your bots from this page or create new ones using the 'Add Bot' button.", "LEARN_MORE": "Learn about agent bots", "GLOBAL_BOT": "System bot", "GLOBAL_BOT_BADGE": "システム", "AVATAR": {"SUCCESS_DELETE": "<PERSON><PERSON> avatar deleted successfully", "ERROR_DELETE": "Error deleting bot avatar, please try again"}, "BOT_CONFIGURATION": {"TITLE": "エージェントボットを選択", "DESC": "エージェントボットを受信トレイに割り当てます。初期の会話を処理し、必要に応じてライブエージェントに転送することができます。", "SUBMIT": "更新", "DISCONNECT": "ボットを切断", "SUCCESS_MESSAGE": "エージェントボットが正常に更新されました。", "DISCONNECTED_SUCCESS_MESSAGE": "エージェントボットが正常に切断されました。", "ERROR_MESSAGE": "エージェントボットを更新できませんでした。再試行してください。", "DISCONNECTED_ERROR_MESSAGE": "エージェントボットを切断できませんでした。再試行してください。", "SELECT_PLACEHOLDER": "ボットを選択"}, "ADD": {"TITLE": "<PERSON><PERSON>", "CANCEL_BUTTON_TEXT": "キャンセル", "API": {"SUCCESS_MESSAGE": "ボットが正常に追加されました。", "ERROR_MESSAGE": "ボットを追加できませんでした。後でもう一度お試しください。"}}, "LIST": {"404": "No bots found. You can create a bot by clicking the 'Add Bot' button.", "LOADING": "ボットを取得中...", "TABLE_HEADER": {"DETAILS": "Bot Details", "URL": "Webhook URL"}}, "DELETE": {"BUTTON_TEXT": "削除", "TITLE": "ボットを削除", "CONFIRM": {"TITLE": "削除の確認", "MESSAGE": "Are you sure you want to delete {name}?", "YES": "削除する", "NO": "いいえ"}, "API": {"SUCCESS_MESSAGE": "ボットが正常に削除されました。", "ERROR_MESSAGE": "ボットを削除できませんでした。再試行してください。"}}, "EDIT": {"BUTTON_TEXT": "編集", "TITLE": "ボットを編集", "API": {"SUCCESS_MESSAGE": "ボットが正常に更新されました。", "ERROR_MESSAGE": "ボットを更新できませんでした。再試行してください。"}}, "FORM": {"AVATAR": {"LABEL": "<PERSON><PERSON> avatar"}, "NAME": {"LABEL": "ボット名", "PLACEHOLDER": "Enter bot name", "REQUIRED": "Bo<PERSON> name is required"}, "DESCRIPTION": {"LABEL": "説明", "PLACEHOLDER": "このボットは何をしますか？"}, "WEBHOOK_URL": {"LABEL": "Webhook URL", "PLACEHOLDER": "https://example.com/webhook", "REQUIRED": "Webhook URL is required"}, "ERRORS": {"NAME": "Bo<PERSON> name is required", "URL": "Webhook URL is required", "VALID_URL": "Please enter a valid URL starting with http:// or https://"}, "CANCEL": "キャンセル", "CREATE": "Create <PERSON><PERSON>", "UPDATE": "Update Bot"}, "WEBHOOK": {"DESCRIPTION": "Configure a webhook bot to integrate with your custom services. The bot will receive and process events from conversations and can respond to them."}, "TYPES": {"WEBHOOK": "Webhookボット"}}}