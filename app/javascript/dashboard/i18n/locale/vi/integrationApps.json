{"INTEGRATION_APPS": {"FETCHING": "<PERSON><PERSON>", "NO_HOOK_CONFIGURED": "<PERSON><PERSON><PERSON><PERSON> có tích hợp {integrationId} đ<PERSON><PERSON><PERSON> cấu hình đối với tài kho<PERSON>n này.", "HEADER": "Ứng dụng", "STATUS": {"ENABLED": "<PERSON><PERSON><PERSON>", "DISABLED": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>t"}, "CONFIGURE": "<PERSON><PERSON><PERSON> h<PERSON>nh", "ADD_BUTTON": "<PERSON><PERSON><PERSON><PERSON> một hook mới", "DELETE": {"TITLE": {"INBOX": "<PERSON><PERSON><PERSON> n<PERSON>n xoá", "ACCOUNT": "<PERSON><PERSON><PERSON> kế<PERSON> n<PERSON>"}, "MESSAGE": {"INBOX": "Bạn có chắc muốn xoá?", "ACCOUNT": "Bạn có chắc muốn ngắt kết nối?"}, "CONFIRM_BUTTON_TEXT": {"INBOX": "Có, Xoá", "ACCOUNT": "<PERSON><PERSON>, ngắt kết nối"}, "CANCEL_BUTTON_TEXT": "Huỷ", "API": {"SUCCESS_MESSAGE": "<PERSON> đã đư<PERSON><PERSON> xoá thành công", "ERROR_MESSAGE": "<PERSON><PERSON><PERSON><PERSON> thể kết nối với <PERSON> chủ Woot, <PERSON><PERSON> lòng thử lại sau"}}, "LIST": {"FETCHING": "<PERSON><PERSON> l<PERSON>y hook tích hợp", "INBOX": "<PERSON><PERSON><PERSON> thư đ<PERSON>n", "DELETE": {"BUTTON_TEXT": "Xoá"}}, "ADD": {"FORM": {"INBOX": {"LABEL": "<PERSON><PERSON><PERSON> thư đến", "PLACEHOLDER": "<PERSON><PERSON><PERSON> thư đến"}, "SUBMIT": "Tạo", "CANCEL": "Huỷ"}, "API": {"SUCCESS_MESSAGE": "<PERSON> tích hợp đã đ<PERSON><PERSON><PERSON> thêm thành công", "ERROR_MESSAGE": "<PERSON><PERSON><PERSON><PERSON> thể kết nối với <PERSON> chủ Woot, <PERSON><PERSON> lòng thử lại sau"}}, "CONNECT": {"BUTTON_TEXT": "<PERSON><PERSON><PERSON>"}, "DISCONNECT": {"BUTTON_TEXT": "<PERSON><PERSON><PERSON> kế<PERSON> n<PERSON>"}, "SIDEBAR_DESCRIPTION": {"DIALOGFLOW": "Dialogflow is a natural language processing platform for building conversational interfaces. Integrating it with {installationName} lets bots handle queries first and transfer them to agents when needed. It helps qualify leads and reduce agent workload by answering FAQs. To add Dialogflow, create a Service Account in Google Console and share the credentials. Refer to the docs for details"}}}