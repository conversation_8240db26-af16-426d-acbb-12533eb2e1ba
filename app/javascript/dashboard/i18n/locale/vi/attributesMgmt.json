{"ATTRIBUTES_MGMT": {"HEADER": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>h tùy chỉnh", "HEADER_BTN_TXT": "<PERSON><PERSON><PERSON><PERSON> thu<PERSON><PERSON> t<PERSON>h <PERSON> chỉnh", "LOADING": "<PERSON><PERSON> l<PERSON>y thu<PERSON><PERSON> t<PERSON>h tuỳ chỉnh", "DESCRIPTION": "A custom attribute tracks additional details about your contacts or conversations—such as the subscription plan or the date of their first purchase. You can add different types of custom attributes, such as text, lists, or numbers, to capture the specific information you need.", "LEARN_MORE": "Learn more about custom attributes", "ADD": {"TITLE": "<PERSON><PERSON><PERSON><PERSON> thu<PERSON><PERSON> t<PERSON>h <PERSON> chỉnh", "SUBMIT": "Tạo", "CANCEL_BUTTON_TEXT": "Huỷ", "FORM": {"NAME": {"LABEL": "<PERSON><PERSON><PERSON> hiển thị", "PLACEHOLDER": "<PERSON><PERSON><PERSON><PERSON> tên hiển thị của thuộc tính tuỳ chỉnh", "ERROR": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> b<PERSON> có"}, "DESC": {"LABEL": "<PERSON><PERSON>", "PLACEHOLDER": "Đ<PERSON>ền mô tả của thuộc tính tuỳ chỉnh", "ERROR": "<PERSON><PERSON> t<PERSON> b<PERSON><PERSON> bu<PERSON> có"}, "MODEL": {"LABEL": "<PERSON><PERSON> d<PERSON>ng cho", "PLACEHOLDER": "<PERSON><PERSON> lòng chọn một", "ERROR": "<PERSON><PERSON> h<PERSON> b<PERSON><PERSON> bu<PERSON> có"}, "TYPE": {"LABEL": "<PERSON><PERSON><PERSON>", "PLACEHOLDER": "<PERSON><PERSON> lòng chọn một kiểu", "ERROR": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> bu<PERSON> có", "LIST": {"LABEL": "<PERSON><PERSON><PERSON> tr<PERSON> của danh s<PERSON>ch", "PLACEHOLDER": "<PERSON><PERSON> lòng nhập giá trị và ấn phím enter", "ERROR": "Phải có <PERSON>t nhất 1 giá trị"}}, "KEY": {"LABEL": "Khoá", "PLACEHOLDER": "<PERSON><PERSON><PERSON><PERSON> kho<PERSON> của thuộc tính tuỳ chỉnh", "ERROR": "<PERSON>\u001d<PERSON><PERSON> c<PERSON> k<PERSON>", "IN_VALID": "<PERSON><PERSON><PERSON> kh<PERSON>ng hợp lệ"}, "REGEX_PATTERN": {"LABEL": "Regex Pattern", "PLACEHOLDER": "Please enter custom attribute regex pattern. (Optional)"}, "REGEX_CUE": {"LABEL": "Regex Cue", "PLACEHOLDER": "Please enter regex pattern hint. (Optional)"}, "ENABLE_REGEX": {"LABEL": "Enable regex validation"}}, "API": {"SUCCESS_MESSAGE": "<PERSON><PERSON><PERSON><PERSON> tính tuỳ chỉnh được thêm thành công!", "ERROR_MESSAGE": "Could not create a Custom Attribute. Please try again later."}}, "DELETE": {"BUTTON_TEXT": "Xoá", "API": {"SUCCESS_MESSAGE": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>h tuỳ chỉnh bị xoá thành công.", "ERROR_MESSAGE": "<PERSON><PERSON><PERSON><PERSON> thể xoá thuộc tính tuỳ chỉnh. <PERSON>h<PERSON> lại."}, "CONFIRM": {"TITLE": "Bạn có chắc muốn xoá - {attributeName}", "PLACE_HOLDER": "<PERSON><PERSON> lòng điền {attributeName} để xác nhận", "MESSAGE": "Xoá sẽ loại bỏ thuộc tính tuỳ chỉnh", "YES": "Xoá ", "NO": "Huỷ"}}, "EDIT": {"TITLE": "<PERSON><PERSON><PERSON> t<PERSON> chỉnh", "UPDATE_BUTTON_TEXT": "<PERSON><PERSON><PERSON>", "TYPE": {"LIST": {"LABEL": "<PERSON><PERSON><PERSON> tr<PERSON> của danh s<PERSON>ch", "PLACEHOLDER": "<PERSON><PERSON> lòng nhập giá trị và ấn phím enter"}}, "API": {"SUCCESS_MESSAGE": "<PERSON><PERSON><PERSON><PERSON> tính tuỳ chỉnh được sửa thành công", "ERROR_MESSAGE": "<PERSON><PERSON> xảy ra lỗi khi cập nhật \bthu<PERSON><PERSON> tính tuỳ chỉnh, vui lòng thử lại"}}, "TABS": {"HEADER": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>h tùy chỉnh", "CONVERSATION": "<PERSON><PERSON><PERSON><PERSON> hội tho<PERSON>i", "CONTACT": "<PERSON><PERSON><PERSON>"}, "LIST": {"TABLE_HEADER": {"NAME": "<PERSON><PERSON><PERSON>", "DESCRIPTION": "<PERSON><PERSON>", "TYPE": "<PERSON><PERSON><PERSON>", "KEY": "Khoá"}, "BUTTONS": {"EDIT": "Chỉnh sửa", "DELETE": "Xoá"}, "EMPTY_RESULT": {"404": "<PERSON><PERSON><PERSON><PERSON> có thuộc tính tuỳ chỉnh nào được tạo", "NOT_FOUND": "<PERSON><PERSON><PERSON><PERSON> có thuộc tính tuỳ chỉnh nào được cấu hình"}, "REGEX_PATTERN": {"LABEL": "Regex Pattern", "PLACEHOLDER": "Please enter custom attribute regex pattern. (Optional)"}, "REGEX_CUE": {"LABEL": "Regex Cue", "PLACEHOLDER": "Please enter regex pattern hint. (Optional)"}, "ENABLE_REGEX": {"LABEL": "Enable regex validation"}}}}