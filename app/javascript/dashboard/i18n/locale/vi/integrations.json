{"INTEGRATION_SETTINGS": {"SHOPIFY": {"DELETE": {"TITLE": "Delete Shopify Integration", "MESSAGE": "Are you sure you want to delete the Shopify integration?"}, "STORE_URL": {"TITLE": "Connect Shopify Store", "LABEL": "Store URL", "PLACEHOLDER": "your-store.myshopify.com", "HELP": "Enter your Shopify store's myshopify.com URL", "CANCEL": "Huỷ", "SUBMIT": "Connect Store"}, "ERROR": "There was an error connecting to Shopify. Please try again or contact support if the issue persists."}, "HEADER": "<PERSON><PERSON><PERSON>", "DESCRIPTION": "Chatwoot integrates with multiple tools and services to improve your team's efficiency. Explore the list below to configure your favorite apps.", "LEARN_MORE": "Learn more about integrations", "LOADING": "Fetching integrations", "CAPTAIN": {"DISABLED": "Captain is not enabled on your account.", "CLICK_HERE_TO_CONFIGURE": "Click here to configure", "LOADING_CONSOLE": "Loading Captain <PERSON><PERSON><PERSON>...", "FAILED_TO_LOAD_CONSOLE": "Failed to load Captain <PERSON><PERSON>. Please refresh and try again."}, "WEBHOOK": {"SUBSCRIBED_EVENTS": "Những sự kiện đư<PERSON><PERSON> theo dõi", "LEARN_MORE": "Learn more about webhooks", "FORM": {"CANCEL": "Huỷ", "DESC": "Webhook events cung cấp cho bạn thông tin thời gian thực về những gì đang xảy ra trong tài khoản Chatwoot của bạn. Vui lòng nhập một URL hợp lệ để định cấu hình một cuộc gọi lại.", "SUBSCRIPTIONS": {"LABEL": "<PERSON><PERSON><PERSON> s<PERSON> kiện", "EVENTS": {"CONVERSATION_CREATED": "<PERSON><PERSON><PERSON><PERSON> trò chuyện đã đ<PERSON><PERSON><PERSON> tạo", "CONVERSATION_STATUS_CHANGED": "Tr<PERSON>ng thái cuộc trò chuyện đã thay đổi", "CONVERSATION_UPDATED": "<PERSON><PERSON><PERSON><PERSON> trò chuyện đã đ<PERSON><PERSON><PERSON> cập nhật", "MESSAGE_CREATED": "<PERSON> nhắn đã đ<PERSON><PERSON><PERSON> tạo", "MESSAGE_UPDATED": "<PERSON> nhắn đã đ<PERSON><PERSON><PERSON> cập nhật", "WEBWIDGET_TRIGGERED": "Tiện ích trò chuyện trực tuyến do người dùng mở", "CONTACT_CREATED": "Contact created", "CONTACT_UPDATED": "Contact updated", "CONVERSATION_TYPING_ON": "Conversation Typing On", "CONVERSATION_TYPING_OFF": "Conversation Typing Off"}}, "END_POINT": {"LABEL": "Webhook URL", "PLACEHOLDER": "Example: {webhookExampleURL}", "ERROR": "<PERSON><PERSON> lòng nhậ<PERSON> một URL hợp lệ"}, "EDIT_SUBMIT": "<PERSON><PERSON><PERSON> nh<PERSON><PERSON> webhook", "ADD_SUBMIT": "<PERSON><PERSON><PERSON> webhook"}, "TITLE": "Webhook", "CONFIGURE": "<PERSON><PERSON><PERSON> h<PERSON>nh", "HEADER": "Cài đặt Webhook", "HEADER_BTN_TXT": "<PERSON><PERSON><PERSON><PERSON> mới webhook", "LOADING": "<PERSON><PERSON> tải về các webhooks", "SEARCH_404": "<PERSON><PERSON><PERSON><PERSON> có kết quả nào đ<PERSON><PERSON><PERSON> tìm thấy", "SIDEBAR_TXT": "<p><b>Webhooks</b> </p> <p>Webhook là các lệnh gọi lại HTTP có thể được xác định cho mọi tài khoản. Chúng được kích hoạt bởi các sự kiện như tạo tin nhắn trong Chatwoot. Bạn có thể tạo nhiều webhook cho tài khoản này. <br /><br /> Đ<PERSON> tạo một <b>webhook</b>, click vào <b>Tạo mới</b>. Bạn cũng có thể xóa bất kỳ webhook hiện có nào bằng cách nhấp vào nút Xóa.</p>", "LIST": {"404": "<PERSON><PERSON><PERSON><PERSON> có webhook nào đ<PERSON><PERSON><PERSON> định cấu hình cho tài khoản này.", "TITLE": "<PERSON><PERSON><PERSON><PERSON> l<PERSON> webhooks", "TABLE_HEADER": {"WEBHOOK_ENDPOINT": "Webhook endpoint", "ACTIONS": "<PERSON><PERSON><PERSON> đ<PERSON>"}}, "EDIT": {"BUTTON_TEXT": "Chỉnh sửa", "TITLE": "<PERSON><PERSON><PERSON> webhook", "API": {"SUCCESS_MESSAGE": "<PERSON><PERSON> cập nhật cấu hình <PERSON>hook thành công", "ERROR_MESSAGE": "<PERSON><PERSON><PERSON><PERSON> thể kết nối với <PERSON> chủ Woot, <PERSON><PERSON> lòng thử lại sau"}}, "ADD": {"CANCEL": "Xoá", "TITLE": "<PERSON><PERSON><PERSON><PERSON> mới webhook", "API": {"SUCCESS_MESSAGE": "<PERSON><PERSON> thêm <PERSON>ok thành công", "ERROR_MESSAGE": "<PERSON><PERSON><PERSON><PERSON> thể kết nối với <PERSON> chủ Woot, <PERSON><PERSON> lòng thử lại sau"}}, "DELETE": {"BUTTON_TEXT": "Xoá", "API": {"SUCCESS_MESSAGE": "Webhook đã đư<PERSON><PERSON> xoá thành công", "ERROR_MESSAGE": "<PERSON><PERSON><PERSON><PERSON> thể kết nối với <PERSON> chủ Woot, <PERSON><PERSON> lòng thử lại sau"}, "CONFIRM": {"TITLE": "<PERSON><PERSON><PERSON>", "MESSAGE": "Bạn có chắc chắn muốn xóa webhook không? ({webhookURL})", "YES": "Có, Xoá ", "NO": "Không, Giữ"}}}, "SLACK": {"DELETE": "Xoá", "DELETE_CONFIRMATION": {"TITLE": "Delete the integration", "MESSAGE": "Are you sure you want to delete the integration? Doing so will result in the loss of access to conversations on your Slack workspace."}, "HELP_TEXT": {"TITLE": "<PERSON><PERSON><PERSON> t<PERSON><PERSON> h<PERSON><PERSON>", "BODY": "With this integration, all of your incoming conversations will be synced to the ***{selectedChannelName}*** channel in your Slack workspace. You can manage all your customer conversations right within the channel and never miss a message.\n\nHere are the main features of the integration:\n\n**Respond to conversations from within Slack:** To respond to a conversation in the ***{selectedChannelName}*** Slack channel, simply type out your message and send it as a thread. This will create a response back to the customer through Chatwoot. It's that simple!\n\n **Create private notes:** If you want to create private notes instead of replies, start your message with ***`note:`***. This ensures that your message is kept private and won't be visible to the customer.\n\n**Associate an agent profile:** If the person who replied on Slack has an agent profile in Chatwoot under the same email, the replies will be associated with that agent profile automatically. This means you can easily track who said what and when. On the other hand, when the replier doesn't have an associated agent profile, the replies will appear from the bot profile to the customer.", "SELECTED": "selected"}, "SELECT_CHANNEL": {"OPTION_LABEL": "Select a channel", "UPDATE": "<PERSON><PERSON><PERSON>", "BUTTON_TEXT": "Connect channel", "DESCRIPTION": "Your Slack workspace is now linked with Chatwoot. However, the integration is currently inactive. To activate the integration and connect a channel to Chatwoot, please click the button below.\n\n**Note:** If you are attempting to connect a private channel, add the Chatwoot app to the Slack channel before proceeding with this step.", "ATTENTION_REQUIRED": "Attention required", "EXPIRED": "Your Slack integration has expired. To continue receiving messages on Slack, please delete the integration and connect your workspace again."}, "UPDATE_ERROR": "There was an error updating the integration, please try again", "UPDATE_SUCCESS": "The channel is connected successfully", "FAILED_TO_FETCH_CHANNELS": "There was an error fetching the channels from Slack, please try again"}, "DYTE": {"CLICK_HERE_TO_JOIN": "Click here to join", "LEAVE_THE_ROOM": "Leave the room", "START_VIDEO_CALL_HELP_TEXT": "Start a new video call with the customer", "JOIN_ERROR": "There was an error joining the call, please try again", "CREATE_ERROR": "There was an error creating a meeting link, please try again"}, "OPEN_AI": {"AI_ASSIST": "AI Assist", "WITH_AI": " {option} with AI ", "OPTIONS": {"REPLY_SUGGESTION": "Reply Suggestion", "SUMMARIZE": "Summarize", "REPHRASE": "Improve Writing", "FIX_SPELLING_GRAMMAR": "Fix Spelling and Grammar", "SHORTEN": "<PERSON>en", "EXPAND": "Expand", "MAKE_FRIENDLY": "Change message tone to friendly", "MAKE_FORMAL": "Use formal tone", "SIMPLIFY": "Simplify"}, "ASSISTANCE_MODAL": {"DRAFT_TITLE": "Draft content", "GENERATED_TITLE": "Generated content", "AI_WRITING": "AI is writing", "BUTTONS": {"APPLY": "Use this suggestion", "CANCEL": "Huỷ"}}, "CTA_MODAL": {"TITLE": "Integrate with OpenAI", "DESC": "Bring advanced AI features to your dashboard with OpenAI's GPT models. To begin, enter the API key from your OpenAI account.", "KEY_PLACEHOLDER": "Enter your OpenAI API key", "BUTTONS": {"NEED_HELP": "Cần hỗ trợ?", "DISMISS": "<PERSON><PERSON><PERSON>", "FINISH": "Finish Setup"}, "DISMISS_MESSAGE": "You can setup OpenAI integration later Whenever you want.", "SUCCESS_MESSAGE": "OpenAI integration setup successfully"}, "TITLE": "Improve With AI", "SUMMARY_TITLE": "Summary with AI", "REPLY_TITLE": "Reply suggestion with AI", "SUBTITLE": "An improved reply will be generated using AI, based on your current draft.", "TONE": {"TITLE": "<PERSON><PERSON>", "OPTIONS": {"PROFESSIONAL": "Professional", "FRIENDLY": "Friendly"}}, "BUTTONS": {"GENERATE": "Generate", "GENERATING": "Generating...", "CANCEL": "Huỷ"}, "GENERATE_ERROR": "There was an error processing the content, please try again"}, "DELETE": {"BUTTON_TEXT": "Xoá", "API": {"SUCCESS_MESSAGE": "<PERSON><PERSON><PERSON> hợp đã đư<PERSON><PERSON> xóa thành công"}}, "CONNECT": {"BUTTON_TEXT": "<PERSON><PERSON><PERSON>"}, "DASHBOARD_APPS": {"TITLE": "Ứng dụng bảng điều khiển", "HEADER_BTN_TXT": "<PERSON>h<PERSON><PERSON> mới một Ứng dụng bảng điều khiển", "SIDEBAR_TXT": "<p> <b> Ứng dụng trên bảng điều khiển</b> </p> <p>Ứng dụng bảng điều khiển cho phép nhúng ứng dụng vào trang bảng điều khiển để cung cấp ngữ cảnh cho các tổng đài viên hỗ trợ khách hàng. Tính năng này cho phép bạn tạo ứng dụng một cách độc lập và nhúng ứng dụng đó vào bên trong trang điều khiển để cung cấp thông tin người dùng, đơn đặt hàng hoặc lịch sử thanh toán trước đó của họ. </p> <p> Khi bạn nhúng ứng dụng của mình bằng điều khiển, ứng dụng của bạn sẽ lấy bối cảnh của cuộc trò chuyện và liên hệ dưới dạng sự kiện cửa sổ. Triển khai trình nghe cho sự kiện thông báo trên trang của bạn để nhận ngữ cảnh. </p> <p> Để thêm ứng dụng trang điều khiển mới, hãy nhấp vào nút 'Thêm ứng dụng bảng điều khiển mới'. </p>", "DESCRIPTION": "Ứng dụng bảng điều khiển cho phép các tổ chức nhúng một ứng dụng bên trong trang điều khiển để cung cấp ngữ cảnh cho các tổng đài viên hỗ trợ khách hàng. Tính năng này cho phép bạn tạo ứng dụng một cách độc lập và nhúng ứng dụng đó để cung cấp thông tin người dùng, đơn đặt hàng hoặc lịch sử thanh toán trước đó của họ.", "LEARN_MORE": "Learn more about Dashboard Apps", "LIST": {"404": "<PERSON><PERSON><PERSON> có ứng dụng trang \u001dđiều khiển nào đư<PERSON><PERSON> định cấu hình trên tài khoản này", "LOADING": "<PERSON><PERSON> tìm nạp <PERSON>ng dụng bảng điều khiển...", "TABLE_HEADER": {"NAME": "<PERSON><PERSON><PERSON>", "ENDPOINT": "Endpoint"}, "EDIT_TOOLTIP": "Sửa app", "DELETE_TOOLTIP": "Xoá app"}, "FORM": {"TITLE_LABEL": "<PERSON><PERSON><PERSON>", "TITLE_PLACEHOLDER": "<PERSON><PERSON><PERSON><PERSON> tên cho ứng dụng bảng điều khiển", "TITLE_ERROR": "<PERSON><PERSON><PERSON> có tên cho ứng dụng bảng điều khiển", "URL_LABEL": "Endpoint", "URL_PLACEHOLDER": "Nhập URL endpoint <PERSON><PERSON><PERSON><PERSON> dụng của bạn đ<PERSON><PERSON><PERSON> lưu trữ", "URL_ERROR": "<PERSON><PERSON>n một URL hợp lệ"}, "CREATE": {"HEADER": "<PERSON>h<PERSON><PERSON> mới một Ứng dụng bảng điều khiển", "FORM_SUBMIT": "<PERSON><PERSON><PERSON>", "FORM_CANCEL": "Huỷ", "API_SUCCESS": "Ứng dụng trang điều khiển được định cấu hình thành công", "API_ERROR": "<PERSON><PERSON>g tôi không thể tạo ứng dụng. <PERSON><PERSON> lòng thử lại sau"}, "UPDATE": {"HEADER": "Sửa app bảng điều khiển", "FORM_SUBMIT": "<PERSON><PERSON><PERSON>", "FORM_CANCEL": "Huỷ", "API_SUCCESS": "<PERSON><PERSON> cập nhật ứng dụng trang điều khiển thành công", "API_ERROR": "<PERSON><PERSON>g tôi không thể cập nhật ứng dụng. <PERSON><PERSON> lòng thử lại sau"}, "DELETE": {"CONFIRM_YES": "<PERSON><PERSON>, xóa nó", "CONFIRM_NO": "<PERSON><PERSON><PERSON><PERSON>, g<PERSON><PERSON> nó", "TITLE": "<PERSON><PERSON><PERSON> n<PERSON>n xoá", "MESSAGE": "Bạn có chắc chắn xóa ứng dụng - {appName}?", "API_SUCCESS": "<PERSON><PERSON> xóa ứng dụng trang điều khiển thành công", "API_ERROR": "<PERSON><PERSON>g tôi không thể xóa ứng dụng. <PERSON><PERSON> lòng thử lại sau"}}, "LINEAR": {"ADD_OR_LINK_BUTTON": "Create/Link Linear Issue", "LOADING": "Fetching linear issues...", "LOADING_ERROR": "There was an error fetching the linear issues, please try again", "CREATE": "Tạo", "LINK": {"SEARCH": "Search issues", "SELECT": "Select issue", "TITLE": "<PERSON><PERSON><PERSON>", "EMPTY_LIST": "No linear issues found", "LOADING": "Loading", "ERROR": "There was an error fetching the linear issues, please try again", "LINK_SUCCESS": "Issue linked successfully", "LINK_ERROR": "There was an error linking the issue, please try again", "LINK_TITLE": "Conversation (#{conversationId}) with {name}"}, "ADD_OR_LINK": {"TITLE": "Create/link linear issue", "DESCRIPTION": "Create Linear issues from conversations, or link existing ones for seamless tracking.", "FORM": {"TITLE": {"LABEL": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>", "PLACEHOLDER": "Enter title", "REQUIRED_ERROR": "<PERSON><PERSON><PERSON><PERSON> đề b<PERSON><PERSON> bu<PERSON> có"}, "DESCRIPTION": {"LABEL": "<PERSON><PERSON>", "PLACEHOLDER": "Enter description"}, "TEAM": {"LABEL": "Nhóm", "PLACEHOLDER": "<PERSON><PERSON><PERSON>", "SEARCH": "Search team", "REQUIRED_ERROR": "Team is required"}, "ASSIGNEE": {"LABEL": "Assignee", "PLACEHOLDER": "Select assignee", "SEARCH": "Search assignee"}, "PRIORITY": {"LABEL": "<PERSON><PERSON><PERSON> đ<PERSON>u tiên", "PLACEHOLDER": "<PERSON><PERSON><PERSON> mức <PERSON> tiên", "SEARCH": "Search priority"}, "LABEL": {"LABEL": "<PERSON><PERSON>ã<PERSON>", "PLACEHOLDER": "Select label", "SEARCH": "Search label"}, "STATUS": {"LABEL": "<PERSON><PERSON><PERSON><PERSON> thái", "PLACEHOLDER": "Select status", "SEARCH": "Search status"}, "PROJECT": {"LABEL": "Project", "PLACEHOLDER": "Select project", "SEARCH": "Search project"}}, "CREATE": "Tạo", "CANCEL": "Huỷ", "CREATE_SUCCESS": "Issue created successfully", "CREATE_ERROR": "There was an error creating the issue, please try again", "LOADING_TEAM_ERROR": "There was an error fetching the teams, please try again", "LOADING_TEAM_ENTITIES_ERROR": "There was an error fetching the team entities, please try again"}, "ISSUE": {"STATUS": "<PERSON><PERSON><PERSON><PERSON> thái", "PRIORITY": "<PERSON><PERSON><PERSON> đ<PERSON>u tiên", "ASSIGNEE": "Assignee", "LABELS": "<PERSON><PERSON>ã<PERSON>", "CREATED_AT": "Created at {createdAt}"}, "UNLINK": {"TITLE": "Unlink", "SUCCESS": "Issue unlinked successfully", "ERROR": "There was an error unlinking the issue, please try again"}, "DELETE": {"TITLE": "Are you sure you want to delete the integration?", "MESSAGE": "Are you sure you want to delete the integration?", "CONFIRM": "Có, xoá", "CANCEL": "Huỷ"}}}, "CAPTAIN": {"NAME": "Captain", "HEADER_KNOW_MORE": "Know more", "COPILOT": {"SEND_MESSAGE": "<PERSON><PERSON><PERSON> tin nhắn...", "EMPTY_MESSAGE": "There was an error generating the response. Please try again.", "LOADER": "Captain is thinking", "YOU": "You", "USE": "Use this", "RESET": "Reset", "SELECT_ASSISTANT": "Select Assistant", "PROMPTS": {"SUMMARIZE": {"LABEL": "Summarize this conversation", "CONTENT": "Summarize the key points discussed between the customer and the support agent, including the customer's concerns, questions, and the solutions or responses provided by the support agent"}, "SUGGEST": {"LABEL": "Suggest an answer", "CONTENT": "Analyze the customer's inquiry, and draft a response that effectively addresses their concerns or questions. Ensure the reply is clear, concise, and provides helpful information."}, "RATE": {"LABEL": "Rate this conversation", "CONTENT": "Review the conversation to see how well it meets the customer's needs. Share a rating out of 5 based on tone, clarity, and effectiveness."}}}, "PLAYGROUND": {"USER": "You", "ASSISTANT": "Assistant", "MESSAGE_PLACEHOLDER": "<PERSON><PERSON> tin nhắn của bạn...", "HEADER": "Playground", "DESCRIPTION": "Use this playground to send messages to your assistant and check if it responds accurately, quickly, and in the tone you expect.", "CREDIT_NOTE": "Messages sent here will count toward your Captain credits."}, "PAYWALL": {"TITLE": "Upgrade to use Captain AI", "AVAILABLE_ON": "Captain is not available on the free plan.", "UPGRADE_PROMPT": "Upgrade your plan to get access to our assistants, copilot and more.", "UPGRADE_NOW": "Upgrade now", "CANCEL_ANYTIME": "You can change or cancel your plan anytime"}, "ENTERPRISE_PAYWALL": {"AVAILABLE_ON": "Captain AI feature is only available in a paid plan.", "UPGRADE_PROMPT": "Upgrade your plan to get access to our assistants, copilot and more.", "ASK_ADMIN": "Please reach out to your administrator for the upgrade."}, "BANNER": {"RESPONSES": "You've used over 80% of your response limit. To continue using Captain AI, please upgrade.", "DOCUMENTS": "Document limit reached. Upgrade to continue using Captain AI."}, "FORM": {"CANCEL": "Huỷ", "CREATE": "Tạo", "EDIT": "<PERSON><PERSON><PERSON>"}, "ASSISTANTS": {"HEADER": "Assistants", "ADD_NEW": "Create a new assistant", "DELETE": {"TITLE": "Are you sure to delete the assistant?", "DESCRIPTION": "This action is permanent. Deleting this assistant will remove it from all connected inboxes and permanently erase all generated knowledge.", "CONFIRM": "Có, xoá", "SUCCESS_MESSAGE": "The assistant has been successfully deleted", "ERROR_MESSAGE": "There was an error deleting the assistant, please try again."}, "FORM_DESCRIPTION": "Fill out the details below to name your assistant, describe its purpose, and specify the product it will support.", "CREATE": {"TITLE": "Create an assistant", "SUCCESS_MESSAGE": "The assistant has been successfully created", "ERROR_MESSAGE": "There was an error creating the assistant, please try again."}, "FORM": {"UPDATE": "<PERSON><PERSON><PERSON>", "SECTIONS": {"BASIC_INFO": "Basic Information", "SYSTEM_MESSAGES": "System Messages", "INSTRUCTIONS": "Instructions", "FEATURES": "<PERSON><PERSON><PERSON>", "TOOLS": "Tools "}, "NAME": {"LABEL": "<PERSON><PERSON><PERSON>", "PLACEHOLDER": "Enter assistant name", "ERROR": "The name is required"}, "DESCRIPTION": {"LABEL": "<PERSON><PERSON>", "PLACEHOLDER": "Enter assistant description", "ERROR": "The description is required"}, "PRODUCT_NAME": {"LABEL": "Product Name", "PLACEHOLDER": "Enter product name", "ERROR": "The product name is required"}, "WELCOME_MESSAGE": {"LABEL": "Welcome Message", "PLACEHOLDER": "Enter welcome message"}, "HANDOFF_MESSAGE": {"LABEL": "Handoff Message", "PLACEHOLDER": "Enter handoff message"}, "RESOLUTION_MESSAGE": {"LABEL": "Resolution Message", "PLACEHOLDER": "Enter resolution message"}, "INSTRUCTIONS": {"LABEL": "Instructions", "PLACEHOLDER": "Enter instructions for the assistant"}, "FEATURES": {"TITLE": "<PERSON><PERSON><PERSON>", "ALLOW_CONVERSATION_FAQS": "Generate FAQs from resolved conversations", "ALLOW_MEMORIES": "Capture key details as memories from customer interactions."}}, "EDIT": {"TITLE": "Update the assistant", "SUCCESS_MESSAGE": "The assistant has been successfully updated", "ERROR_MESSAGE": "There was an error updating the assistant, please try again.", "NOT_FOUND": "Could not find the assistant. Please try again."}, "OPTIONS": {"EDIT_ASSISTANT": "Edit Assistant", "DELETE_ASSISTANT": "Delete Assistant", "VIEW_CONNECTED_INBOXES": "View connected inboxes"}, "EMPTY_STATE": {"TITLE": "No assistants available", "SUBTITLE": "Create an assistant to provide quick and accurate responses to your users. It can learn from your help articles and past conversations.", "FEATURE_SPOTLIGHT": {"TITLE": "Captain Assistant", "NOTE": "Captain Assistant engages directly with customers, learns from your help docs and past conversations, and delivers instant, accurate responses. It handles the initial queries, providing quick resolutions before  transferring to an agent when needed."}}}, "DOCUMENTS": {"HEADER": "Documents", "ADD_NEW": "Create a new document", "RELATED_RESPONSES": {"TITLE": "Related FAQs", "DESCRIPTION": "These FAQs are generated directly from the document."}, "FORM_DESCRIPTION": "Enter the URL of the document to add it as a knowledge source and choose the assistant to associate it with.", "CREATE": {"TITLE": "Add a document", "SUCCESS_MESSAGE": "The document has been successfully created", "ERROR_MESSAGE": "There was an error creating the document, please try again."}, "FORM": {"URL": {"LABEL": "URL", "PLACEHOLDER": "Enter the URL of the document", "ERROR": "Please provide a valid URL for the document"}, "ASSISTANT": {"LABEL": "Assistant", "PLACEHOLDER": "Select the assistant", "ERROR": "The assistant field is required"}}, "DELETE": {"TITLE": "Are you sure to delete the document?", "DESCRIPTION": "This action is permanent. Deleting this document will permanently erase all generated knowledge.", "CONFIRM": "Có, xoá", "SUCCESS_MESSAGE": "The document has been successfully deleted", "ERROR_MESSAGE": "There was an error deleting the document, please try again."}, "OPTIONS": {"VIEW_RELATED_RESPONSES": "View Related Responses", "DELETE_DOCUMENT": "Delete Document"}, "EMPTY_STATE": {"TITLE": "No documents available", "SUBTITLE": "Documents are used by your assistant to generate FAQs. You can import documents to provide context for your assistant.", "FEATURE_SPOTLIGHT": {"TITLE": "Captain <PERSON>ument", "NOTE": "A document in Captain serves as a knowledge resource for the assistant. By connecting your help center or guides, Captain can analyze the content and provide accurate responses for customer inquiries."}}}, "RESPONSES": {"HEADER": "FAQs", "ADD_NEW": "Create new FAQ", "DOCUMENTABLE": {"CONVERSATION": "Conversation #{id}"}, "SELECTED": "{count} selected", "BULK_APPROVE_BUTTON": "Approve", "BULK_DELETE_BUTTON": "Xoá", "BULK_APPROVE": {"SUCCESS_MESSAGE": "FAQs approved successfully", "ERROR_MESSAGE": "There was an error approving the FAQs, please try again."}, "BULK_DELETE": {"TITLE": "Delete FAQs?", "DESCRIPTION": "Are you sure you want to delete the selected FAQs? This action cannot be undone.", "CONFIRM": "Yes, delete all", "SUCCESS_MESSAGE": "FAQs deleted successfully", "ERROR_MESSAGE": "There was an error deleting the FAQs, please try again."}, "DELETE": {"TITLE": "Are you sure to delete the FAQ?", "DESCRIPTION": "", "CONFIRM": "Có, xoá", "SUCCESS_MESSAGE": "FAQ deleted successfully", "ERROR_MESSAGE": "There was an error deleting the FAQ, please try again."}, "FILTER": {"ASSISTANT": "Assistant: {selected}", "STATUS": "Status: {selected}", "ALL_ASSISTANTS": "<PERSON><PERSON><PERSON> c<PERSON>"}, "STATUS": {"TITLE": "<PERSON><PERSON><PERSON><PERSON> thái", "PENDING": "<PERSON><PERSON> gi<PERSON>i quyết", "APPROVED": "Approved", "ALL": "<PERSON><PERSON><PERSON> c<PERSON>"}, "FORM_DESCRIPTION": "Add a question and its corresponding answer to the knowledge base and select the assistant it should be associated with.", "CREATE": {"TITLE": "Add an FAQ", "SUCCESS_MESSAGE": "The response has been added successfully.", "ERROR_MESSAGE": "An error occurred while adding the response. Please try again."}, "FORM": {"QUESTION": {"LABEL": "Question", "PLACEHOLDER": "Enter the question here", "ERROR": "Please provide a valid question."}, "ANSWER": {"LABEL": "Answer", "PLACEHOLDER": "Enter the answer here", "ERROR": "Please provide a valid answer."}, "ASSISTANT": {"LABEL": "Assistant", "PLACEHOLDER": "Select an assistant", "ERROR": "Please select an assistant."}}, "EDIT": {"TITLE": "Update the FAQ", "SUCCESS_MESSAGE": "The FAQ has been successfully updated", "ERROR_MESSAGE": "There was an error updating the FAQ, please try again", "APPROVE_SUCCESS_MESSAGE": "The FAQ was marked as approved"}, "OPTIONS": {"APPROVE": "Mark as approved", "EDIT_RESPONSE": "Edit FAQ", "DELETE_RESPONSE": "Delete FAQ"}, "EMPTY_STATE": {"TITLE": "No FAQs Found", "SUBTITLE": "FAQs help your assistant provide quick and accurate answers to questions from your customers. They can be generated automatically from your content or can be added manually.", "FEATURE_SPOTLIGHT": {"TITLE": "Captain FAQ", "NOTE": "Captain <PERSON><PERSON><PERSON> detects common customer questions—whether missing from your knowledge base or frequently asked—and generates relevant FAQs to improve support. You can review each suggestion and decide whether to approve or reject it."}}}, "INBOXES": {"HEADER": "Connected Inboxes", "ADD_NEW": "Connect a new inbox", "OPTIONS": {"DISCONNECT": "<PERSON><PERSON><PERSON> kế<PERSON> n<PERSON>"}, "DELETE": {"TITLE": "Are you sure to disconnect the inbox?", "DESCRIPTION": "", "CONFIRM": "Có, xoá", "SUCCESS_MESSAGE": "The inbox was successfully disconnected.", "ERROR_MESSAGE": "There was an error disconnecting the inbox, please try again."}, "FORM_DESCRIPTION": "Choose an inbox to connect with the assistant.", "CREATE": {"TITLE": "Connect an Inbox", "SUCCESS_MESSAGE": "The inbox was successfully connected.", "ERROR_MESSAGE": "An error occurred while connecting the inbox. Please try again."}, "FORM": {"INBOX": {"LABEL": "<PERSON><PERSON><PERSON> thư đ<PERSON>n", "PLACEHOLDER": "Choose the inbox to deploy the assistant.", "ERROR": "An inbox selection is required."}}, "EMPTY_STATE": {"TITLE": "No Connected Inboxes", "SUBTITLE": "Connecting an inbox allows the assistant to handle initial questions from your customers before transferring them to you."}}}}