{"AUDIT_LOGS": {"HEADER": "<PERSON><PERSON>", "HEADER_BTN_TXT": "Audit-logs toevoegen", "LOADING": "<PERSON><PERSON><PERSON>", "DESCRIPTION": "Audit Logs maintain a record of activities in your account, allowing you to track and audit your account, team, or services.", "LEARN_MORE": "Learn more about audit logs", "SEARCH_404": "Er zijn geen items die overeenkomen met deze z<PERSON>cht", "SIDEBAR_TXT": "<p><b><PERSON><PERSON></b> </p><p> <PERSON>t Logs zijn sporen voor gebeurtenissen en acties in een Chatwoot Systeem. </p>", "LIST": {"404": "<PERSON>r zijn geen Audit Logs beschikbaar in dit account.", "TITLE": "<PERSON><PERSON><PERSON>", "DESC": "Audit Logs zijn sporen voor gebeurtenissen en acties in een Chatwoot Systeem.", "TABLE_HEADER": {"ACTIVITY": "User", "TIME": "Tijd", "IP_ADDRESS": "IP-adres"}}, "API": {"SUCCESS_MESSAGE": "AuditLogs succesvol opgehaald", "ERROR_MESSAGE": "Kan geen verbinding maken met <PERSON><PERSON> <PERSON>, probeer het later opnieuw"}, "DEFAULT_USER": "Systeem", "AUTOMATION_RULE": {"ADD": "{agentName} created a new automation rule (#{id})", "EDIT": "{agentName} updated an automation rule (#{id})", "DELETE": "{agent<PERSON>ame} deleted an automation rule (#{id})"}, "ACCOUNT_USER": {"ADD": "{agent<PERSON><PERSON>} heeft {invitee} uitgenodigd voor het account als een {role}", "EDIT": {"SELF": "{agentName} heeft zijn {attributes} gewij<PERSON>d naar {values}", "OTHER": "{agentName} heeft {attributes} van {user} gewijzigd naar {values}", "DELETED": "{agent<PERSON><PERSON>} changed {attributes} of a deleted user to {values}"}}, "INBOX": {"ADD": "{agentName} created a new inbox (#{id})", "EDIT": "{agentName} updated an inbox (#{id})", "DELETE": "{agent<PERSON>ame} deleted an inbox (#{id})"}, "WEBHOOK": {"ADD": "{agent<PERSON>ame} created a new webhook (#{id})", "EDIT": "{agent<PERSON>ame} updated a webhook (#{id})", "DELETE": "{agent<PERSON><PERSON>} deleted a webhook (#{id})"}, "USER_ACTION": {"SIGN_IN": "{agent<PERSON><PERSON>} is ingelogd", "SIGN_OUT": "{agent<PERSON><PERSON>} heeft zich afgemeld"}, "TEAM": {"ADD": "{<PERSON><PERSON><PERSON>} created a new team (#{id})", "EDIT": "{<PERSON><PERSON><PERSON>} updated a team (#{id})", "DELETE": "{<PERSON><PERSON><PERSON>} deleted a team (#{id})"}, "MACRO": {"ADD": "{agent<PERSON>ame} created a new macro (#{id})", "EDIT": "{agent<PERSON>ame} updated a macro (#{id})", "DELETE": "{agent<PERSON>ame} deleted a macro (#{id})"}, "INBOX_MEMBER": {"ADD": "{agent<PERSON>ame} added {user} to the inbox(#{inbox_id})", "REMOVE": "{<PERSON><PERSON><PERSON>} removed {user} from the inbox(#{inbox_id})"}, "TEAM_MEMBER": {"ADD": "{<PERSON><PERSON><PERSON>} added {user} to the team(#{team_id})", "REMOVE": "{<PERSON><PERSON><PERSON>} removed {user} from the team(#{team_id})"}, "ACCOUNT": {"EDIT": "{<PERSON><PERSON><PERSON>} updated the account configuration (#{id})"}}}