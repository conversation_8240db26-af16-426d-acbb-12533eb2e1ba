{"AUDIT_LOGS": {"HEADER": "Логи аудиту", "HEADER_BTN_TXT": "Додати логи", "LOADING": "Завантаження логів", "DESCRIPTION": "Audit Logs maintain a record of activities in your account, allowing you to track and audit your account, team, or services.", "LEARN_MORE": "Learn more about audit logs", "SEARCH_404": "Немає елементів, що відповідають запиту", "SIDEBAR_TXT": "<p><b><PERSON><PERSON><PERSON><PERSON> аудиту</b> </p><p> Логи - це історія всіх подій в системі Chatwoot. </p>", "LIST": {"404": "У цьому акаунті немає логів.", "TITLE": "Керувати логами", "DESC": " Логи - це історія всіх подій в системі Chatwoot.", "TABLE_HEADER": {"ACTIVITY": "Користув<PERSON><PERSON>", "TIME": "Дія", "IP_ADDRESS": "IP-адреса"}}, "API": {"SUCCESS_MESSAGE": "Логи успішно отримано", "ERROR_MESSAGE": "Не вдалося підключитися до Woot Server, спробуйте ще раз пізніше"}, "DEFAULT_USER": "Система", "AUTOMATION_RULE": {"ADD": "{agentName} created a new automation rule (#{id})", "EDIT": "{agentName} updated an automation rule (#{id})", "DELETE": "{agent<PERSON>ame} deleted an automation rule (#{id})"}, "ACCOUNT_USER": {"ADD": "{agentName} запросив {invitee} на акаунт як {role}", "EDIT": {"SELF": "{agentName} змінив свій {attributes} на {values}", "OTHER": "{agentName} змінив {attributes} {user} на {values}", "DELETED": "{agent<PERSON><PERSON>} changed {attributes} of a deleted user to {values}"}}, "INBOX": {"ADD": "{agentName} created a new inbox (#{id})", "EDIT": "{agentName} updated an inbox (#{id})", "DELETE": "{agent<PERSON>ame} deleted an inbox (#{id})"}, "WEBHOOK": {"ADD": "{agent<PERSON>ame} created a new webhook (#{id})", "EDIT": "{agent<PERSON>ame} updated a webhook (#{id})", "DELETE": "{agent<PERSON><PERSON>} deleted a webhook (#{id})"}, "USER_ACTION": {"SIGN_IN": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {agentName}", "SIGN_OUT": "<PERSON><PERSON><PERSON><PERSON> {agent<PERSON>ame}"}, "TEAM": {"ADD": "{<PERSON><PERSON><PERSON>} created a new team (#{id})", "EDIT": "{<PERSON><PERSON><PERSON>} updated a team (#{id})", "DELETE": "{<PERSON><PERSON><PERSON>} deleted a team (#{id})"}, "MACRO": {"ADD": "{agent<PERSON>ame} created a new macro (#{id})", "EDIT": "{agent<PERSON>ame} updated a macro (#{id})", "DELETE": "{agent<PERSON>ame} deleted a macro (#{id})"}, "INBOX_MEMBER": {"ADD": "{agent<PERSON>ame} added {user} to the inbox(#{inbox_id})", "REMOVE": "{<PERSON><PERSON><PERSON>} removed {user} from the inbox(#{inbox_id})"}, "TEAM_MEMBER": {"ADD": "{<PERSON><PERSON><PERSON>} added {user} to the team(#{team_id})", "REMOVE": "{<PERSON><PERSON><PERSON>} removed {user} from the team(#{team_id})"}, "ACCOUNT": {"EDIT": "{<PERSON><PERSON><PERSON>} updated the account configuration (#{id})"}}}