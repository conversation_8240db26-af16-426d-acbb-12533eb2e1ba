{"AGENT_BOTS": {"HEADER": "<PERSON><PERSON>", "LOADING_EDITOR": "A carregar editor...", "DESCRIPTION": "Agent <PERSON><PERSON> are like the most fabulous members of your team. They can handle the small stuff, so you can focus on the stuff that matters. Give them a try. You can manage your bots from this page or create new ones using the 'Add Bot' button.", "LEARN_MORE": "Learn about agent bots", "GLOBAL_BOT": "System bot", "GLOBAL_BOT_BADGE": "Sistema", "AVATAR": {"SUCCESS_DELETE": "<PERSON><PERSON> avatar deleted successfully", "ERROR_DELETE": "Error deleting bot avatar, please try again"}, "BOT_CONFIGURATION": {"TITLE": "Selecione um agente bot", "DESC": "Atribua um agente bot à sua caixa de entrada. Eles podem lidar com conversas iniciais e transferi-las para um agente humano quando necessário.", "SUBMIT": "Atualização", "DISCONNECT": "Des<PERSON><PERSON> bot", "SUCCESS_MESSAGE": "Agente bot atualizado com sucesso.", "DISCONNECTED_SUCCESS_MESSAGE": "O agente bot foi desligado com sucesso.", "ERROR_MESSAGE": "Não foi possível atualizar o agente bot. Por favor, tente novamente.", "DISCONNECTED_ERROR_MESSAGE": "Não foi possível desligar o agente bot. Por favor, tente novamente.", "SELECT_PLACEHOLDER": "Selecionar bot"}, "ADD": {"TITLE": "<PERSON><PERSON>", "CANCEL_BUTTON_TEXT": "<PERSON><PERSON><PERSON>", "API": {"SUCCESS_MESSAGE": "Bot adicionado com sucesso.", "ERROR_MESSAGE": "Não foi possível adicionar o bot. Por favor, tente novamente mais tarde."}}, "LIST": {"404": "No bots found. You can create a bot by clicking the 'Add Bot' button.", "LOADING": "A carregar bots...", "TABLE_HEADER": {"DETAILS": "Bot Details", "URL": "URL do Webhook"}}, "DELETE": {"BUTTON_TEXT": "Excluir", "TITLE": "Apagar bot", "CONFIRM": {"TITLE": "Confirmar exclusão", "MESSAGE": "Are you sure you want to delete {name}?", "YES": "Sim, excluir", "NO": "Não, manter"}, "API": {"SUCCESS_MESSAGE": "Bot apagado com sucesso.", "ERROR_MESSAGE": "Não foi possível apagar o bot. Por favor, tente novamente."}}, "EDIT": {"BUTTON_TEXT": "<PERSON><PERSON>", "TITLE": "<PERSON><PERSON> bot", "API": {"SUCCESS_MESSAGE": "Bot atualizado com sucesso.", "ERROR_MESSAGE": "Não foi possível atualizar o bot. Por favor, tente novamente."}}, "FORM": {"AVATAR": {"LABEL": "<PERSON><PERSON> avatar"}, "NAME": {"LABEL": "Nome do bot", "PLACEHOLDER": "Enter bot name", "REQUIRED": "O nome do bot é obrigatório"}, "DESCRIPTION": {"LABEL": "Descrição", "PLACEHOLDER": "O que faz este bot?"}, "WEBHOOK_URL": {"LABEL": "URL do Webhook", "PLACEHOLDER": "https://example.com/webhook", "REQUIRED": "Webhook URL is required"}, "ERRORS": {"NAME": "O nome do bot é obrigatório", "URL": "Webhook URL is required", "VALID_URL": "Please enter a valid URL starting with http:// or https://"}, "CANCEL": "<PERSON><PERSON><PERSON>", "CREATE": "Create <PERSON><PERSON>", "UPDATE": "Update Bot"}, "WEBHOOK": {"DESCRIPTION": "Configure a webhook bot to integrate with your custom services. The bot will receive and process events from conversations and can respond to them."}, "TYPES": {"WEBHOOK": "Webhook bot"}}}