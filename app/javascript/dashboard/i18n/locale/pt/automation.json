{"AUTOMATION": {"HEADER": "Automatização", "DESCRIPTION": "A automação pode substituir e racionalizar os processos existentes que requerem esforço manual, como a adição de etiquetas e a atribuição de conversas ao agente mais adequado. Isto permite que a equipa se foque em tarefas maiores, reduzindo o tempo gasto em tarefas rotineiras.", "LEARN_MORE": "Saber mais sobre automação", "HEADER_BTN_TXT": "Adicionar regra de automação", "LOADING": "A procurar regras de automação", "ADD": {"TITLE": "Adicionar regra de automação", "SUBMIT": "<PERSON><PERSON><PERSON>", "CANCEL_BUTTON_TEXT": "<PERSON><PERSON><PERSON>", "FORM": {"NAME": {"LABEL": "Nome da regra", "PLACEHOLDER": "Insira o nome da regra", "ERROR": "Nome é obrigatório"}, "DESC": {"LABEL": "Descrição", "PLACEHOLDER": "Insira descrição da regra", "ERROR": "Descrição é obrigatória"}, "EVENT": {"LABEL": "Evento", "PLACEHOLDER": "Por favor, selecione um", "ERROR": "Evento é necessário"}, "CONDITIONS": {"LABEL": "Condições"}, "ACTIONS": {"LABEL": "Ações"}}, "CONDITION_BUTTON_LABEL": "Adicionar condi<PERSON>", "ACTION_BUTTON_LABEL": "Adicionar ação", "API": {"SUCCESS_MESSAGE": "Regra de automação adicionada com sucesso", "ERROR_MESSAGE": "Não foi possível criar uma regra de automação, por favor, tente novamente mais tarde"}}, "LIST": {"TABLE_HEADER": {"NAME": "Nome:", "DESCRIPTION": "Descrição", "ACTIVE": "Ativa", "CREATED_ON": "C<PERSON><PERSON> em"}, "404": "Nenhuma regra de automação encontrada"}, "DELETE": {"TITLE": "Apagar regra de automação", "SUBMIT": "Excluir", "CANCEL_BUTTON_TEXT": "<PERSON><PERSON><PERSON>", "CONFIRM": {"TITLE": "Confirmar exclusão", "MESSAGE": "Tem a certeza que pretende excluir a automação ", "YES": "Sim, excluir ", "NO": "Não, manter "}, "API": {"SUCCESS_MESSAGE": "Regra de automação excluída com sucesso", "ERROR_MESSAGE": "Não foi possível excluir a regra de automação, por favor, tente novamente mais tarde"}}, "EDIT": {"TITLE": "Editar regra de automação", "SUBMIT": "Atualização", "CANCEL_BUTTON_TEXT": "<PERSON><PERSON><PERSON>", "API": {"SUCCESS_MESSAGE": "Regra de automação atualizada com sucesso", "ERROR_MESSAGE": "Não foi possível atualizar a regra de automação, por favor, tente novamente mais tarde"}}, "CLONE": {"TOOLTIP": "<PERSON><PERSON>", "API": {"SUCCESS_MESSAGE": "Automação clonada com sucesso", "ERROR_MESSAGE": "Não foi possível clonar a regra de automação, por favor, tente novamente mais tarde"}}, "FORM": {"EDIT": "<PERSON><PERSON>", "CREATE": "<PERSON><PERSON><PERSON>", "DELETE": "Excluir", "CANCEL": "<PERSON><PERSON><PERSON>", "RESET_MESSAGE": "Alterar o tipo de evento irá redefinir as condições e eventos adicionados abaixo"}, "CONDITION": {"DELETE_MESSAGE": "É necessário ter, pelo menos, uma condição para guardar", "CONTACT_CUSTOM_ATTR_LABEL": "Atributos personalizados do contacto", "CONVERSATION_CUSTOM_ATTR_LABEL": "Atributos personalizados da conversa"}, "ACTION": {"DELETE_MESSAGE": "É necessário ter, pelo menos, uma ação para guardar", "TEAM_MESSAGE_INPUT_PLACEHOLDER": "Introduza aqui a sua mensagem", "TEAM_DROPDOWN_PLACEHOLDER": "Selecionar equipas", "EMAIL_INPUT_PLACEHOLDER": "Inserir e-mail", "URL_INPUT_PLACEHOLDER": "Inserir URL"}, "TOGGLE": {"ACTIVATION_TITLE": "Ativar regra de automação", "DEACTIVATION_TITLE": "Desativar regra de automação", "ACTIVATION_DESCRIPTION": "Esta ação irá ativar a regra de automação '{automationName}'. Tem a certeza que pretende continuar?", "DEACTIVATION_DESCRIPTION": "Esta ação irá desativar a regra de automação '{automationName}'. Tem a certeza que pretende continuar?", "ACTIVATION_SUCCESFUL": "Regra de automação ativada com sucesso", "DEACTIVATION_SUCCESFUL": "Regra de automação desativada com sucesso", "ACTIVATION_ERROR": "Não foi possível ativar a automação. Por favor, tente novamente mais tarde", "DEACTIVATION_ERROR": "Não foi possível desativar a automação. Por favor, tente novamente mais tarde", "CONFIRMATION_LABEL": "<PERSON>m", "CANCEL_LABEL": "Não"}, "ATTACHMENT": {"UPLOAD_ERROR": "Não foi possível carregar anexo, por favor, tente novamente", "LABEL_IDLE": "Carregar anexo", "LABEL_UPLOADING": "A carregar...", "LABEL_UPLOADED": "Anexo carregado com sucesso", "LABEL_UPLOAD_FAILED": "Falha ao carregar anexo"}, "ERRORS": {"ATTRIBUTE_KEY_REQUIRED": "A chave do atributo é necessária", "FILTER_OPERATOR_REQUIRED": "Operador do filtro é necessário", "VALUE_REQUIRED": "Valor obrigatório", "VALUE_MUST_BE_BETWEEN_1_AND_998": "O valor deve ser entre 1 e 998", "ACTION_PARAMETERS_REQUIRED": "Os parâmetros de ação são obrigatórios", "ATLEAST_ONE_CONDITION_REQUIRED": "Pelo menos uma condição é obrigatória", "ATLEAST_ONE_ACTION_REQUIRED": "Pelo menos uma ação é obrigatória"}, "NONE_OPTION": "<PERSON><PERSON><PERSON><PERSON>", "EVENTS": {"CONVERSATION_CREATED": "Conversa criada", "CONVERSATION_UPDATED": "Conversa atualizada", "MESSAGE_CREATED": "Message Created", "CONVERSATION_OPENED": "Conversation Opened"}, "ACTIONS": {"ASSIGN_AGENT": "Assign to Agent", "ASSIGN_TEAM": "Assign a Team", "ADD_LABEL": "Add a Label", "REMOVE_LABEL": "Remove a Label", "SEND_EMAIL_TO_TEAM": "Send an Email to Team", "SEND_EMAIL_TRANSCRIPT": "Send an Email Transcript", "MUTE_CONVERSATION": "<PERSON><PERSON><PERSON>r Con<PERSON>a", "SNOOZE_CONVERSATION": "Adiar conversa", "RESOLVE_CONVERSATION": "Resolver conversa", "SEND_WEBHOOK_EVENT": "Send Webhook Event", "SEND_ATTACHMENT": "Send Attachment", "SEND_MESSAGE": "Send a Message", "CHANGE_PRIORITY": "Alterar prioridade", "ADD_SLA": "Adicionar SLA"}, "ATTRIBUTES": {"MESSAGE_TYPE": "Message Type", "MESSAGE_CONTAINS": "Message Contains", "EMAIL": "E-mail", "INBOX": "Caixa de entrada", "CONVERSATION_LANGUAGE": "Conversation Language", "PHONE_NUMBER": "Número de telefone", "STATUS": "Situação", "BROWSER_LANGUAGE": "Idioma do navegador", "MAIL_SUBJECT": "Email Subject", "COUNTRY_NAME": "<PERSON><PERSON>", "REFERER_LINK": "Referrer Link", "ASSIGNEE_NAME": "Atribu<PERSON><PERSON>", "TEAM_NAME": "Equipa", "PRIORITY": "Prioridade"}}}