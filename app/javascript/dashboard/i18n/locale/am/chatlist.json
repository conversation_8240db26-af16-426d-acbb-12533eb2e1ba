{"CHAT_LIST": {"LOADING": "Fetching conversations", "LOAD_MORE_CONVERSATIONS": "Load more conversations", "EOF": "All conversations loaded 🎉", "LIST": {"404": "There are no active conversations in this group."}, "FAILED_TO_SEND": "Failed to send", "TAB_HEADING": "Conversations", "MENTION_HEADING": "Mentions", "UNATTENDED_HEADING": "Unattended", "SEARCH": {"INPUT": "Search for People, <PERSON><PERSON>, Saved Replies .."}, "FILTER_ALL": "All", "ASSIGNEE_TYPE_TABS": {"me": "Mine", "unassigned": "Unassigned", "all": "All"}, "CHAT_STATUS_FILTER_ITEMS": {"open": {"TEXT": "Open"}, "resolved": {"TEXT": "Resolved"}, "pending": {"TEXT": "Pending"}, "snoozed": {"TEXT": "Snoozed"}, "all": {"TEXT": "All"}}, "VIEW_FILTER": "View", "SORT_TOOLTIP_LABEL": "Sort conversations", "CHAT_SORT": {"STATUS": "Status", "ORDER_BY": "Order by"}, "CHAT_TIME_STAMP": {"CREATED": {"LATEST": "Created", "OLDEST": "Created at:"}, "LAST_ACTIVITY": {"NOT_ACTIVE": "Last activity:", "ACTIVE": "Last activity"}}, "SORT_ORDER_ITEMS": {"last_activity_at_asc": {"TEXT": "Last activity: Oldest first"}, "last_activity_at_desc": {"TEXT": "Last activity: Newest first"}, "created_at_desc": {"TEXT": "Created at: Newest first"}, "created_at_asc": {"TEXT": "Created at: Oldest first"}, "priority_desc": {"TEXT": "Priority: Highest first"}, "priority_asc": {"TEXT": "Priority: <PERSON>st first"}, "waiting_since_asc": {"TEXT": "Pending Response: Longest first"}, "waiting_since_desc": {"TEXT": "Pending Response: Shortest first"}}, "ATTACHMENTS": {"image": {"CONTENT": "Picture message"}, "audio": {"CONTENT": "Audio message"}, "video": {"CONTENT": "Video message"}, "file": {"CONTENT": "File Attachment"}, "location": {"CONTENT": "Location"}, "ig_reel": {"CONTENT": "Instagram Reel"}, "fallback": {"CONTENT": "has shared a url"}, "contact": {"CONTENT": "Shared contact"}}, "CHAT_SORT_BY_FILTER": {"TITLE": "Sort conversation", "DROPDOWN_TITLE": "Sort by", "ITEMS": {"LATEST": {"NAME": "Last activity at", "LABEL": "Last activity"}, "CREATED_AT": {"NAME": "Created at", "LABEL": "Created at"}, "LAST_USER_MESSAGE_AT": {"NAME": "Last user message at", "LABEL": "Last message"}}}, "RECEIVED_VIA_EMAIL": "Received via email", "VIEW_TWEET_IN_TWITTER": "View tweet in Twitter", "REPLY_TO_TWEET": "Reply to this tweet", "LINK_TO_STORY": "Go to instagram story", "SENT": "<PERSON><PERSON> successfully", "READ": "Read successfully", "DELIVERED": "Delivered successfully", "NO_MESSAGES": "No Messages", "NO_CONTENT": "No content available", "HIDE_QUOTED_TEXT": "<PERSON><PERSON> Quoted Text", "SHOW_QUOTED_TEXT": "Show Quoted Text", "MESSAGE_READ": "Read", "SENDING": "Sending"}}