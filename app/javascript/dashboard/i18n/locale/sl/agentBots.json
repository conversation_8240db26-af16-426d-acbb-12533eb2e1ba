{"AGENT_BOTS": {"HEADER": "<PERSON><PERSON>", "LOADING_EDITOR": "Nalaganje urejevalnika ...", "DESCRIPTION": "Agent <PERSON><PERSON> are like the most fabulous members of your team. They can handle the small stuff, so you can focus on the stuff that matters. Give them a try. You can manage your bots from this page or create new ones using the 'Add Bot' button.", "LEARN_MORE": "Learn about agent bots", "GLOBAL_BOT": "System bot", "GLOBAL_BOT_BADGE": "System", "AVATAR": {"SUCCESS_DELETE": "<PERSON><PERSON> avatar deleted successfully", "ERROR_DELETE": "Error deleting bot avatar, please try again"}, "BOT_CONFIGURATION": {"TITLE": "Izberite agentskega bota", "DESC": "Dodelite Agentskega Bota v svoj nabiralnik. Ti lahko vodijo začetne pogovore in jih po potrebi prenesejo na agenta v živo.", "SUBMIT": "<PERSON><PERSON><PERSON><PERSON>", "DISCONNECT": "Odklopi bota", "SUCCESS_MESSAGE": "Agentski bot je bil uspešno posodobljen.", "DISCONNECTED_SUCCESS_MESSAGE": "Povezava z agentskega bota je bila uspešno prekinjena.", "ERROR_MESSAGE": "Agentskega bota ni bilo mogoče posodobiti. Prosimo poskusite ponovno.", "DISCONNECTED_ERROR_MESSAGE": "Ni bilo mogoče prekiniti povezave z <PERSON>kim botom. Prosimo poskusite ponovno.", "SELECT_PLACEHOLDER": "Izberi bota"}, "ADD": {"TITLE": "<PERSON><PERSON>", "CANCEL_BUTTON_TEXT": "Prekliči", "API": {"SUCCESS_MESSAGE": "<PERSON>t je bil us<PERSON><PERSON> dodan.", "ERROR_MESSAGE": "Bota ni bilo mogoče dodati. Poskusite znova pozneje."}}, "LIST": {"404": "No bots found. You can create a bot by clicking the 'Add Bot' button.", "LOADING": "Pridobivanje botov ...", "TABLE_HEADER": {"DETAILS": "Bot Details", "URL": "Webhook URL"}}, "DELETE": {"BUTTON_TEXT": "Izbriši", "TITLE": "Izbriši bota", "CONFIRM": {"TITLE": "Confirm Deletion", "MESSAGE": "Are you sure you want to delete {name}?", "YES": "Yes, Delete", "NO": "No, Keep"}, "API": {"SUCCESS_MESSAGE": "Bot uspešno izbrisan.", "ERROR_MESSAGE": "Bota ni bilo mogoče izbrisati. Prosimo poskusite ponovno."}}, "EDIT": {"BUTTON_TEXT": "<PERSON><PERSON><PERSON>", "TITLE": "Uredi bota", "API": {"SUCCESS_MESSAGE": "<PERSON>t je bil us<PERSON>š<PERSON> posodobljen.", "ERROR_MESSAGE": "Bota ni bilo mogoče posodobiti. Prosimo poskusite ponovno."}}, "FORM": {"AVATAR": {"LABEL": "<PERSON><PERSON> avatar"}, "NAME": {"LABEL": "<PERSON>me bota", "PLACEHOLDER": "Enter bot name", "REQUIRED": "Zah<PERSON>vano je ime bota"}, "DESCRIPTION": {"LABEL": "Description", "PLACEHOLDER": "<PERSON>j po<PERSON>ne ta bot?"}, "WEBHOOK_URL": {"LABEL": "Webhook URL", "PLACEHOLDER": "https://example.com/webhook", "REQUIRED": "Webhook URL is required"}, "ERRORS": {"NAME": "Zah<PERSON>vano je ime bota", "URL": "Webhook URL is required", "VALID_URL": "Please enter a valid URL starting with http:// or https://"}, "CANCEL": "Cancel", "CREATE": "Create <PERSON><PERSON>", "UPDATE": "Update Bot"}, "WEBHOOK": {"DESCRIPTION": "Configure a webhook bot to integrate with your custom services. The bot will receive and process events from conversations and can respond to them."}, "TYPES": {"WEBHOOK": "Webhook bot"}}}