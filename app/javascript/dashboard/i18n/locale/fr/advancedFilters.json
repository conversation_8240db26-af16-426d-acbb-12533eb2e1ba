{"FILTER": {"TITLE": "Filtrer les conversations", "SUBTITLE": "Ajoutez vos filtres ci-dessous et cliquez sur \"Appliquer les filtres\" pour désencombrer les discussions.", "EDIT_CUSTOM_FILTER": "Modifier le dossier", "CUSTOM_VIEWS_SUBTITLE": "A<PERSON>ter ou supprimer des filtres et mettre à jour votre dossier.", "ADD_NEW_FILTER": "Ajouter un filtre", "FILTER_DELETE_ERROR": "<PERSON><PERSON>, on dirait que nous ne pouvons rien enregistrer ! Veuillez ajouter au moins un filtre pour l'enregistrer.", "SUBMIT_BUTTON_LABEL": "Appliquer les filtres", "UPDATE_BUTTON_LABEL": "Mettre à jour le dossier", "CANCEL_BUTTON_LABEL": "Annuler", "CLEAR_BUTTON_LABEL": "Effacer les filtres", "FOLDER_LABEL": "Nom du dossier", "FOLDER_QUERY_LABEL": "<PERSON><PERSON><PERSON><PERSON><PERSON> de dossier", "EMPTY_VALUE_ERROR": "La valeur est requise.", "TOOLTIP_LABEL": "Filtrer les conversations", "QUERY_DROPDOWN_LABELS": {"AND": "ET", "OR": "OU"}, "INPUT_PLACEHOLDER": "Enter value", "OPERATOR_LABELS": {"equal_to": "Égal à", "not_equal_to": "Pas <PERSON>", "does_not_contain": "Ne contient pas", "is_present": "Est présent", "is_not_present": "N'est pas présent", "is_greater_than": "Est plus grand que", "is_less_than": "Est inférieur à", "days_before": "Est x jours avant", "starts_with": "Commence par", "equalTo": "Égal à", "notEqualTo": "Pas <PERSON>", "contains": "Contient", "doesNotContain": "Ne contient pas", "isPresent": "Est présent", "isNotPresent": "N'est pas présent", "isGreaterThan": "Est plus grand que", "isLessThan": "Est inférieur à", "daysBefore": "Est x jours avant", "startsWith": "Commence par"}, "ATTRIBUTE_LABELS": {"TRUE": "Vrai", "FALSE": "Faux"}, "ATTRIBUTES": {"STATUS": "État", "ASSIGNEE_NAME": "Nom du responsable", "INBOX_NAME": "Nom de la boîte de réception", "TEAM_NAME": "Nom de l'équipe", "CONVERSATION_IDENTIFIER": "Identifiant de la conversation", "CAMPAIGN_NAME": "Nom de la campagne", "LABELS": "Étiquettes", "BROWSER_LANGUAGE": "<PERSON><PERSON>", "PRIORITY": "Priorité", "COUNTRY_NAME": "Nom du pays", "REFERER_LINK": "<PERSON><PERSON> de réfé<PERSON>ce", "CUSTOM_ATTRIBUTE_LIST": "Liste", "CUSTOM_ATTRIBUTE_TEXT": "Texte", "CUSTOM_ATTRIBUTE_NUMBER": "Nombre", "CUSTOM_ATTRIBUTE_LINK": "<PERSON><PERSON>", "CUSTOM_ATTRIBUTE_CHECKBOX": "Case à cocher", "CREATED_AT": "<PERSON><PERSON><PERSON>", "LAST_ACTIVITY": "Dernière activité"}, "ERRORS": {"VALUE_REQUIRED": "La valeur est requise", "ATTRIBUTE_KEY_REQUIRED": "Attribute key is required", "FILTER_OPERATOR_REQUIRED": "Filter operator is required", "VALUE_MUST_BE_BETWEEN_1_AND_998": "Value must be between 1 and 998"}, "GROUPS": {"STANDARD_FILTERS": "Filtres standards", "ADDITIONAL_FILTERS": "Filtres supplémentaires", "CUSTOM_ATTRIBUTES": "Attributs personnalisés"}, "CUSTOM_VIEWS": {"ADD": {"TITLE": "Voulez-vous enregistrer ce filtre ?", "LABEL": "Nommer ce filtre", "PLACEHOLDER": "Nommez votre filtre pour le référencer plus tard.", "ERROR_MESSAGE": "Le nom est requis.", "SAVE_BUTTON": "Enregistrer le filtre", "CANCEL_BUTTON": "Annuler", "API_FOLDERS": {"SUCCESS_MESSAGE": "Dossier c<PERSON>é avec succès.", "ERROR_MESSAGE": "Erreur lors de la création du dossier."}, "API_SEGMENTS": {"SUCCESS_MESSAGE": "Segment créé avec succès.", "ERROR_MESSAGE": "Erreur lors de la création du segment."}}, "EDIT": {"EDIT_BUTTON": "Modifier le dossier"}, "DELETE": {"DELETE_BUTTON": "Supp<PERSON>er le filtre", "MODAL": {"CONFIRM": {"TITLE": "Confirmer la <PERSON>", "MESSAGE": "Êtes-vous sûr de vouloir supprimer le filtre ", "YES": "<PERSON><PERSON>, supprimer", "NO": "Non, conservez-le"}}, "API_FOLDERS": {"SUCCESS_MESSAGE": "Dossier supprimé avec succès.", "ERROR_MESSAGE": "Erreur lors de la suppression du dossier."}, "API_SEGMENTS": {"SUCCESS_MESSAGE": "Segment supprimé avec succès.", "ERROR_MESSAGE": "Erreur lors de la suppression du segment."}}}}}