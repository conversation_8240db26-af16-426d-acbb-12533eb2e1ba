{"CANNED_MGMT": {"HEADER": "Konz<PERSON><PERSON><PERSON><PERSON>", "LEARN_MORE": "Learn more about canned responses", "DESCRIPTION": "Canned Responses are pre-written reply templates that help you quickly respond to a conversation. Agents can type the '/' character followed by the shortcode to insert a canned response during a conversation. ", "HEADER_BTN_TXT": "Add canned response", "LOADING": "Fetching canned responses...", "SEARCH_404": "Neexistují <PERSON><PERSON> odpovídající tomuto <PERSON>.", "LIST": {"404": "V tomto účtu nejsou k dispozici žádné konzervované odpovědi.", "TITLE": "Spravovat konzervované od<PERSON>vědi", "DESC": "Canned Responses are predefined reply templates which can be used to quickly send out replies to conversations.", "TABLE_HEADER": {"SHORT_CODE": "Short code", "CONTENT": "<PERSON><PERSON><PERSON>", "ACTIONS": "Ak<PERSON>"}}, "ADD": {"TITLE": "Add canned response", "DESC": "Canned Responses are predefined reply templates which can be used to quickly send out replies to conversations.", "CANCEL_BUTTON_TEXT": "Zrušit", "FORM": {"SHORT_CODE": {"LABEL": "Short code", "PLACEHOLDER": "Please enter a short code.", "ERROR": "Short Code is required."}, "CONTENT": {"LABEL": "Zpráva", "PLACEHOLDER": "Please write the message you want to save as a template to use later.", "ERROR": "Message is required."}, "SUBMIT": "<PERSON><PERSON><PERSON>"}, "API": {"SUCCESS_MESSAGE": "Canned response added successfully.", "ERROR_MESSAGE": "<PERSON><PERSON><PERSON> se připojit k <PERSON>oot serveru, opakujte akci později"}}, "EDIT": {"TITLE": "Edit canned response", "CANCEL_BUTTON_TEXT": "Zrušit", "FORM": {"SHORT_CODE": {"LABEL": "Short code", "PLACEHOLDER": "Please enter a shortcode.", "ERROR": "Short code is required."}, "CONTENT": {"LABEL": "Zpráva", "PLACEHOLDER": "Please write the message you want to save as a template to use later.", "ERROR": "Message is required."}, "SUBMIT": "<PERSON><PERSON><PERSON>"}, "BUTTON_TEXT": "<PERSON><PERSON><PERSON><PERSON>", "API": {"SUCCESS_MESSAGE": "Canned response is updated successfully.", "ERROR_MESSAGE": "<PERSON><PERSON><PERSON> se připojit k <PERSON>oot serveru, opakujte akci později"}}, "DELETE": {"BUTTON_TEXT": "Vymazat", "API": {"SUCCESS_MESSAGE": "Canned response deleted successfully.", "ERROR_MESSAGE": "<PERSON><PERSON><PERSON> se připojit k <PERSON>oot serveru, opakujte akci později"}, "CONFIRM": {"TITLE": "Confirm deletion", "MESSAGE": "Opravdu chcete odstranit ", "YES": "Yes, delete ", "NO": "No, keep "}}}}