{"FILTER": {"TITLE": "Filtrar conversaciones", "SUBTITLE": "Añada sus filtros a continuación y pulse 'Aplicar filtros' para cortar el caos del chat.", "EDIT_CUSTOM_FILTER": "<PERSON><PERSON>", "CUSTOM_VIEWS_SUBTITLE": "Agregar o eliminar filtros y actualizar la carpeta.", "ADD_NEW_FILTER": "<PERSON><PERSON><PERSON>", "FILTER_DELETE_ERROR": "¡Vaya, parece que no podemos guardar nada! Por favor, añade al menos un filtro para guardarlo.", "SUBMIT_BUTTON_LABEL": "Aplicar filtros", "UPDATE_BUTTON_LABEL": "<PERSON>ual<PERSON><PERSON>a", "CANCEL_BUTTON_LABEL": "<PERSON><PERSON><PERSON>", "CLEAR_BUTTON_LABEL": "Limpiar filtros", "FOLDER_LABEL": "Nombre de la carpeta", "FOLDER_QUERY_LABEL": "Consulta de carpetas", "EMPTY_VALUE_ERROR": "El valor es requerido.", "TOOLTIP_LABEL": "Filtrar conversaciones", "QUERY_DROPDOWN_LABELS": {"AND": "Y", "OR": "O"}, "INPUT_PLACEHOLDER": "Introducir valor", "OPERATOR_LABELS": {"equal_to": "Igual a", "not_equal_to": "No igual a", "does_not_contain": "No contiene", "is_present": "Está presente", "is_not_present": "No está presente", "is_greater_than": "Es mayor que", "is_less_than": "Es menor que", "days_before": "Es X días antes", "starts_with": "Empieza con", "equalTo": "Igual a", "notEqualTo": "No igual a", "contains": "<PERSON><PERSON><PERSON>", "doesNotContain": "No contiene", "isPresent": "Está presente", "isNotPresent": "No está presente", "isGreaterThan": "Es mayor que", "isLessThan": "Es menor que", "daysBefore": "Es X días antes", "startsWith": "Empieza con"}, "ATTRIBUTE_LABELS": {"TRUE": "Verdadero", "FALSE": "<PERSON><PERSON><PERSON>"}, "ATTRIBUTES": {"STATUS": "Estado", "ASSIGNEE_NAME": "Nombre Asignado", "INBOX_NAME": "Nombre de la bandeja de entrada", "TEAM_NAME": "Nombre del equipo", "CONVERSATION_IDENTIFIER": "Identificador de conversacion", "CAMPAIGN_NAME": "Nombre de Campaña", "LABELS": "Etiquetas", "BROWSER_LANGUAGE": "Idioma del navegador", "PRIORITY": "Prioridad", "COUNTRY_NAME": "Nombre del país", "REFERER_LINK": "<PERSON>lace de referencia", "CUSTOM_ATTRIBUTE_LIST": "Lista", "CUSTOM_ATTRIBUTE_TEXT": "Texto", "CUSTOM_ATTRIBUTE_NUMBER": "Número", "CUSTOM_ATTRIBUTE_LINK": "Enlace", "CUSTOM_ATTRIBUTE_CHECKBOX": "<PERSON><PERSON><PERSON>", "CREATED_AT": "Creado el", "LAST_ACTIVITY": "Última actividad"}, "ERRORS": {"VALUE_REQUIRED": "El valor es requerido", "ATTRIBUTE_KEY_REQUIRED": "Clave de atributo es requerida", "FILTER_OPERATOR_REQUIRED": "El operador de filtro es requerido", "VALUE_MUST_BE_BETWEEN_1_AND_998": "El valor debe ser entre 1 y 998"}, "GROUPS": {"STANDARD_FILTERS": "<PERSON>ltros estándar", "ADDITIONAL_FILTERS": "Filtros adicionales", "CUSTOM_ATTRIBUTES": "Atributos personalizados"}, "CUSTOM_VIEWS": {"ADD": {"TITLE": "¿Desea guardar este filtro?", "LABEL": "Nombre de este filtro", "PLACEHOLDER": "Nombre su filtro para referirse más tarde.", "ERROR_MESSAGE": "El nombre es requerido.", "SAVE_BUTTON": "Guardar filtro", "CANCEL_BUTTON": "<PERSON><PERSON><PERSON>", "API_FOLDERS": {"SUCCESS_MESSAGE": "Carpeta creada con éxito.", "ERROR_MESSAGE": "Error al crear la carpeta."}, "API_SEGMENTS": {"SUCCESS_MESSAGE": "Segmento creado con éxito.", "ERROR_MESSAGE": "Error al crear segmento."}}, "EDIT": {"EDIT_BUTTON": "<PERSON><PERSON>"}, "DELETE": {"DELETE_BUTTON": "Eliminar filtro", "MODAL": {"CONFIRM": {"TITLE": "Confirme eliminación", "MESSAGE": "¿Está seguro de que desea eliminar el filtro ", "YES": "Sí, eliminar", "NO": "No, manten<PERSON>lo"}}, "API_FOLDERS": {"SUCCESS_MESSAGE": "Carpeta eliminada con éxito.", "ERROR_MESSAGE": "Error al eliminar carpeta."}, "API_SEGMENTS": {"SUCCESS_MESSAGE": "Segmento eliminado con éxito.", "ERROR_MESSAGE": "Error al eliminar segmento."}}}}}