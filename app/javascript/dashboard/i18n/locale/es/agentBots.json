{"AGENT_BOTS": {"HEADER": "<PERSON><PERSON>", "LOADING_EDITOR": "Cargando el editor...", "DESCRIPTION": "Agent <PERSON><PERSON> are like the most fabulous members of your team. They can handle the small stuff, so you can focus on the stuff that matters. Give them a try. You can manage your bots from this page or create new ones using the 'Add Bot' button.", "LEARN_MORE": "Learn about agent bots", "GLOBAL_BOT": "System bot", "GLOBAL_BOT_BADGE": "Sistema", "AVATAR": {"SUCCESS_DELETE": "<PERSON><PERSON> avatar deleted successfully", "ERROR_DELETE": "Error deleting bot avatar, please try again"}, "BOT_CONFIGURATION": {"TITLE": "Seleccione un bot de agente", "DESC": "Asigne un bot de agente a su bandeja de entrada. <PERSON><PERSON><PERSON> manejar conversaciones iniciales y transferirlas a un agente en vivo cuando sea necesario.", "SUBMIT": "Actualizar", "DISCONNECT": "Desconectar Bot", "SUCCESS_MESSAGE": "El bot del agente se ha actualizado con éxito.", "DISCONNECTED_SUCCESS_MESSAGE": "Desconectado correctamente el bot agente.", "ERROR_MESSAGE": "No se pudo actualizar el bot de agente, por favor inténtalo de nuevo más tarde.", "DISCONNECTED_ERROR_MESSAGE": "No se pudo desconectar el bot de agente, por favor inténtalo de nuevo más tarde.", "SELECT_PLACEHOLDER": "<PERSON><PERSON><PERSON><PERSON><PERSON> bot"}, "ADD": {"TITLE": "<PERSON><PERSON>", "CANCEL_BUTTON_TEXT": "<PERSON><PERSON><PERSON>", "API": {"SUCCESS_MESSAGE": "<PERSON><PERSON>.", "ERROR_MESSAGE": "No se pudo agregar el bot, Inténtalo de nuevo más tarde."}}, "LIST": {"404": "No bots found. You can create a bot by clicking the 'Add Bot' button.", "LOADING": "Obteniendo bots...", "TABLE_HEADER": {"DETAILS": "Bot Details", "URL": "URL de Webhook"}}, "DELETE": {"BUTTON_TEXT": "Eliminar", "TITLE": "Eliminar bot", "CONFIRM": {"TITLE": "Confirmar eliminación", "MESSAGE": "Are you sure you want to delete {name}?", "YES": "Sí, eliminar", "NO": "No, manten<PERSON>lo"}, "API": {"SUCCESS_MESSAGE": "Bot eliminado correctamente.", "ERROR_MESSAGE": "No se pudo eliminar el bot. Por favor, inténtalo de nuevo."}}, "EDIT": {"BUTTON_TEXT": "<PERSON><PERSON>", "TITLE": "<PERSON><PERSON> bot", "API": {"SUCCESS_MESSAGE": "Bot actualizado correctamente.", "ERROR_MESSAGE": "No se pudo actualizar el bot, por favor inténtalo de nuevo más tarde."}}, "FORM": {"AVATAR": {"LABEL": "<PERSON><PERSON> avatar"}, "NAME": {"LABEL": "Nombre del bot", "PLACEHOLDER": "Enter bot name", "REQUIRED": "El nombre del bot es obligatorio"}, "DESCRIPTION": {"LABEL": "Descripción", "PLACEHOLDER": "¿Qué hace este bot?"}, "WEBHOOK_URL": {"LABEL": "URL de Webhook", "PLACEHOLDER": "https://example.com/webhook", "REQUIRED": "Webhook URL is required"}, "ERRORS": {"NAME": "El nombre del bot es obligatorio", "URL": "Webhook URL is required", "VALID_URL": "Please enter a valid URL starting with http:// or https://"}, "CANCEL": "<PERSON><PERSON><PERSON>", "CREATE": "Create <PERSON><PERSON>", "UPDATE": "Update Bot"}, "WEBHOOK": {"DESCRIPTION": "Configure a webhook bot to integrate with your custom services. The bot will receive and process events from conversations and can respond to them."}, "TYPES": {"WEBHOOK": "Webhook Bot"}}}