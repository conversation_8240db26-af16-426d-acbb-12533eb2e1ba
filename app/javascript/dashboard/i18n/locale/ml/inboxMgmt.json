{"INBOX_MGMT": {"HEADER": "ഇൻ‌ബോക്സുകൾ", "DESCRIPTION": "A channel is the mode of communication your customer chooses to interact with you. An inbox is where you manage interactions for a specific channel. It can include communications from various sources such as email, live chat, and social media.", "LEARN_MORE": "Learn more about inboxes", "RECONNECTION_REQUIRED": "Your inbox is disconnected. You won't receive new messages until you reauthorize it.", "CLICK_TO_RECONNECT": "Click here to reconnect.", "LIST": {"404": "ഈ അക്കൗണ്ടിലേക്കു ഇൻ‌ബോക്സുകളൊന്നും ബന്ധിപ്പിച്ചിട്ടില്ല."}, "CREATE_FLOW": {"CHANNEL": {"TITLE": "ചാനൽ തിരഞ്ഞെടുക്കുക", "BODY": "ചാറ്റ് വൂട്ടുമായി സംയോജിപ്പിക്കാൻ നിങ്ങൾ ആഗ്രഹിക്കുന്ന ദാതാവിനെ തിരഞ്ഞെടുക്കുക."}, "INBOX": {"TITLE": "ഇൻ‌ബോക്സ് സൃഷ്ടിക്കുക", "BODY": "നിങ്ങളുടെ അക്കൗണ്ട്  പ്രാമാണീകരിക്കുകയും ഇൻ‌ബോക്സ് സൃഷ്ടിക്കുകയും ചെയ്യുക."}, "AGENT": {"TITLE": "ഏജന്റുമാരെ ചേർക്കുക", "BODY": "സൃഷ്ടിച്ച ഇൻ‌ബോക്സിലേക്ക് ഏജന്റുമാരെ ചേർക്കുക."}, "FINISH": {"TITLE": "Voilà!", "BODY": "എല്ലാം ഭംഗിയായി പാപര്യവസാനിച്ചിരിക്കുന്നു. വരൂ നമുക്ക്‌ പോകാം!"}}, "ADD": {"CHANNEL_NAME": {"LABEL": "ഇൻ‌ബോക്സ് നാമം", "PLACEHOLDER": "Enter your inbox name (eg: Acme Inc)", "ERROR": "Please enter a valid inbox name"}, "WEBSITE_NAME": {"LABEL": "വെബ്‌സൈറ്റിന്റെ പേര്", "PLACEHOLDER": "നിങ്ങളുടെ വെബ്‌സൈറ്റിന്റെ പേര് നൽകുക (ഉദാ: പുണ്ണ്യാളൻ അഗർബത്തീസ്)"}, "FB": {"HELP": "സൈൻ ഇൻ ചെയ്യുന്നതിലൂടെ, നിങ്ങളുടെ പേജിന്റെ സന്ദേശങ്ങളിലേക്ക് മാത്രമേ ഞങ്ങൾക്ക് പ്രവേശനം ലഭിക്കൂ. നിങ്ങളുടെ സ്വകാര്യ സന്ദേശങ്ങൾ ഒരിക്കലും ചാറ്റ് വൂട്ട് ഉപയോഗിച്ച് ആക്സസ് ചെയ്യാൻ കഴിയില്ല.", "CHOOSE_PAGE": "പേജ് തിരഞ്ഞെടുക്കുക", "CHOOSE_PLACEHOLDER": "ലിസ്റ്റിൽ നിന്ന് ഒരു പേജ് തിരഞ്ഞെടുക്കുക", "INBOX_NAME": "ഇൻ‌ബോക്സ് നാമം", "ADD_NAME": "നിങ്ങളുടെ ഇൻ‌ബോക്‌സിനായി ഒരു പേര് ചേർക്കുക", "PICK_NAME": "Pick a Name for your Inbox", "PICK_A_VALUE": "ഒരു മൂല്യം തിരഞ്ഞെടുക്കുക", "CREATE_INBOX": "ഇൻ‌ബോക്സ് സൃഷ്ടിക്കുക"}, "INSTAGRAM": {"CONTINUE_WITH_INSTAGRAM": "Continue with Instagram", "CONNECT_YOUR_INSTAGRAM_PROFILE": "Connect your Instagram Profile", "HELP": "To add your Instagram profile as a channel, you need to authenticate your Instagram Profile by clicking on 'Continue with Instagram' ", "ERROR_MESSAGE": "There was an error connecting to Instagram, please try again", "ERROR_AUTH": "There was an error connecting to Instagram, please try again", "NEW_INBOX_SUGGESTION": "This Instagram account was previously linked to a different inbox and has now been migrated here. All new messages will appear here. The old inbox will no longer be able to send or receive messages for this account.", "DUPLICATE_INBOX_BANNER": "This Instagram account was migrated to the new Instagram channel inbox. You won’t be able to send/receive Instagram messages from this inbox anymore."}, "TWITTER": {"HELP": "നിങ്ങളുടെ ട്വിറ്റർ പ്രൊഫൈൽ ഒരു ചാനലായി ചേർക്കുന്നതിന്, 'ട്വിറ്ററിനൊപ്പം പ്രവേശിക്കുക' ക്ലിക്കുചെയ്ത് നിങ്ങളുടെ ട്വിറ്റർ പ്രൊഫൈൽ പ്രാമാണീകരിക്കേണ്ടതുണ്ട് ", "ERROR_MESSAGE": "There was an error connecting to Twitter, please try again", "TWEETS": {"ENABLE": "സൂചിപ്പിച്ച ട്വീറ്റുകളിൽ നിന്ന് സംഭാഷണങ്ങൾ സൃഷ്ടിക്കുക"}}, "WEBSITE_CHANNEL": {"TITLE": "വെബ്‌സൈറ്റ് ചാനൽ", "DESC": "നിങ്ങളുടെ വെബ്‌സൈറ്റിനായി ഒരു ചാനൽ സൃഷ്‌ടിച്ച് ഞങ്ങളുടെ വെബ്‌സൈറ്റ് വിജറ്റ് വഴി ഉപഭോക്താക്കളെ പിന്തുണയ്ക്കാൻ ആരംഭിക്കുക.", "LOADING_MESSAGE": "വെബ്‌സൈറ്റ് സപ്പോർട്ട് ചാനൽ സൃഷ്‌ടിക്കുന്നു", "CHANNEL_AVATAR": {"LABEL": "ചാനൽ അവതാർ"}, "CHANNEL_WEBHOOK_URL": {"LABEL": "വെബ്‌ഹുക്ക് യുആർഎൽ", "PLACEHOLDER": "Please enter your Webhook URL", "ERROR": "ദയവായി സാധുവായ ഒരു യുആർഎൽ നൽകുക"}, "CHANNEL_DOMAIN": {"LABEL": "വെബ്സൈറ്റ് ഡൊമെയ്ൻ", "PLACEHOLDER": "നിങ്ങളുടെ വെബ്‌സൈറ്റ് ഡൊമെയ്ൻ നൽകുക (ഉദാ: punnyalan.com)"}, "CHANNEL_WELCOME_TITLE": {"LABEL": "സ്വാഗത തലക്കെട്ട്", "PLACEHOLDER": "ഹേയ്, അവിടെയുണ്ടോ!"}, "CHANNEL_WELCOME_TAGLINE": {"LABEL": "ടാഗ്‌ലൈൻ സ്വാഗതം", "PLACEHOLDER": "ഞങ്ങളുമായി കണക്റ്റുചെയ്യുന്നത് ഞങ്ങൾ ലളിതമാക്കുന്നു. ഞങ്ങളോട് എന്തും ചോദിക്കുക, അല്ലെങ്കിൽ നിങ്ങളുടെ ഫീഡ്‌ബാക്ക് പങ്കിടുക."}, "CHANNEL_GREETING_MESSAGE": {"LABEL": "ചാനൽ അഭിവാദ്യ സന്ദേശം", "PLACEHOLDER": "Acme Inc സാധാരണയായി കുറച്ച് മണിക്കൂറിനുള്ളിൽ മറുപടി നൽകുന്നു."}, "CHANNEL_GREETING_TOGGLE": {"LABEL": "ചാനൽ അഭിവാദ്യം പ്രവർത്തനക്ഷമമാക്കുക", "HELP_TEXT": "Automatically send a greeting message when a new conversation is created.", "ENABLED": "പ്രവർത്തനക്ഷമമാക്കി", "DISABLED": "പ്രവർത്തനരഹിതമാക്കി"}, "REPLY_TIME": {"TITLE": "Set Reply time", "IN_A_FEW_MINUTES": "In a few minutes", "IN_A_FEW_HOURS": "In a few hours", "IN_A_DAY": "In a day", "HELP_TEXT": "This reply time will be displayed on the live chat widget"}, "WIDGET_COLOR": {"LABEL": "വിജറ്റ് നിറം", "PLACEHOLDER": "വിജറ്റിൽ ഉപയോഗിച്ച വിജറ്റ് നിറം അപ്‌ഡേറ്റ് ചെയ്യുക"}, "SUBMIT_BUTTON": "ഇൻ‌ബോക്സ് സൃഷ്ടിക്കുക", "API": {"ERROR_MESSAGE": "We were not able to create a website channel, please try again"}}, "TWILIO": {"TITLE": "Twilio SMS/WhatsApp Channel", "DESC": "Integrate Twilio and start supporting your customers via SMS or WhatsApp.", "ACCOUNT_SID": {"LABEL": "അക്കൗണ്ട് എസ്ഐഡി", "PLACEHOLDER": "ദയവായി നിങ്ങളുടെ ട്വിലിയോ അക്കൗണ്ട് എസ്ഐഡി നൽകുക", "ERROR": "ഈ ഫീൽഡ് ആവശ്യമാണ്"}, "API_KEY": {"USE_API_KEY": "Use API Key Authentication", "LABEL": "API Key SID", "PLACEHOLDER": "Please enter your API Key SID", "ERROR": "ഈ ഫീൽഡ് ആവശ്യമാണ്"}, "API_KEY_SECRET": {"LABEL": "API Key Secret", "PLACEHOLDER": "Please enter your API Key Secret", "ERROR": "ഈ ഫീൽഡ് ആവശ്യമാണ്"}, "MESSAGING_SERVICE_SID": {"LABEL": "Messaging Service SID", "PLACEHOLDER": "Please enter your Twilio Messaging Service SID", "ERROR": "ഈ ഫീൽഡ് ആവശ്യമാണ്", "USE_MESSAGING_SERVICE": "Use a Twilio Messaging Service"}, "CHANNEL_TYPE": {"LABEL": "ചാനൽ തരം", "ERROR": "നിങ്ങളുടെ ചാനൽ തരം തിരഞ്ഞെടുക്കുക"}, "AUTH_TOKEN": {"LABEL": "ഓത്ത് ടോക്കൺ", "PLACEHOLDER": "ദയവായി നിങ്ങളുടെ ട്വിലിയോ ഓത്ത് ടോക്കൺ നൽകുക", "ERROR": "ഈ ഫീൽഡ് ആവശ്യമാണ്"}, "CHANNEL_NAME": {"LABEL": "ഇൻ‌ബോക്സ് നാമം", "PLACEHOLDER": "Please enter a inbox name", "ERROR": "ഈ ഫീൽഡ് ആവശ്യമാണ്"}, "PHONE_NUMBER": {"LABEL": "ഫോൺ നമ്പർ", "PLACEHOLDER": "ദയവായി സന്ദേശം അയയ്‌ക്കുന്ന ഫോൺ നമ്പർ നൽകുക.", "ERROR": "Please provide a valid phone number that starts with a `+` sign and does not contain any spaces."}, "API_CALLBACK": {"TITLE": "Callback URL", "SUBTITLE": "You have to configure the message callback URL in Twilio with the URL mentioned here."}, "SUBMIT_BUTTON": "ട്വിലിയോ ചാനൽ സൃഷ്ടിക്കുക", "API": {"ERROR_MESSAGE": "ഞങ്ങൾക്ക് ട്വിലിയോ ക്രെഡൻഷ്യലുകൾ പ്രാമാണീകരിക്കാൻ കഴിഞ്ഞില്ല, ദയവായി വീണ്ടും ശ്രമിക്കുക"}}, "SMS": {"TITLE": "SMS Channel", "DESC": "Start supporting your customers via SMS.", "PROVIDERS": {"LABEL": "API Provider", "TWILIO": "<PERSON><PERSON><PERSON>", "BANDWIDTH": "Bandwidth"}, "API": {"ERROR_MESSAGE": "We were not able to save the SMS channel"}, "BANDWIDTH": {"ACCOUNT_ID": {"LABEL": "Account ID", "PLACEHOLDER": "Please enter your Bandwidth Account ID", "ERROR": "ഈ ഫീൽഡ് ആവശ്യമാണ്"}, "API_KEY": {"LABEL": "API Key", "PLACEHOLDER": "Please enter your Bandwidth API Key", "ERROR": "ഈ ഫീൽഡ് ആവശ്യമാണ്"}, "API_SECRET": {"LABEL": "API Secret", "PLACEHOLDER": "Please enter your Bandwidth API Secret", "ERROR": "ഈ ഫീൽഡ് ആവശ്യമാണ്"}, "APPLICATION_ID": {"LABEL": "Application ID", "PLACEHOLDER": "Please enter your Bandwidth Application ID", "ERROR": "ഈ ഫീൽഡ് ആവശ്യമാണ്"}, "INBOX_NAME": {"LABEL": "ഇൻ‌ബോക്സ് നാമം", "PLACEHOLDER": "Please enter a inbox name", "ERROR": "ഈ ഫീൽഡ് ആവശ്യമാണ്"}, "PHONE_NUMBER": {"LABEL": "ഫോൺ നമ്പർ", "PLACEHOLDER": "ദയവായി സന്ദേശം അയയ്‌ക്കുന്ന ഫോൺ നമ്പർ നൽകുക.", "ERROR": "Please provide a valid phone number that starts with a `+` sign and does not contain any spaces."}, "SUBMIT_BUTTON": "Create Bandwidth Channel", "API": {"ERROR_MESSAGE": "We were not able to authenticate Bandwidth credentials, please try again"}, "API_CALLBACK": {"TITLE": "Callback URL", "SUBTITLE": "You have to configure the message callback URL in Bandwidth with the URL mentioned here."}}}, "WHATSAPP": {"TITLE": "WhatsApp Channel", "DESC": "Start supporting your customers via WhatsApp.", "PROVIDERS": {"LABEL": "API Provider", "TWILIO": "<PERSON><PERSON><PERSON>", "WHATSAPP_CLOUD": "WhatsApp Cloud", "360_DIALOG": "360Dialog"}, "INBOX_NAME": {"LABEL": "ഇൻ‌ബോക്സ് നാമം", "PLACEHOLDER": "Please enter an inbox name", "ERROR": "ഈ ഫീൽഡ് ആവശ്യമാണ്"}, "PHONE_NUMBER": {"LABEL": "ഫോൺ നമ്പർ", "PLACEHOLDER": "ദയവായി സന്ദേശം അയയ്‌ക്കുന്ന ഫോൺ നമ്പർ നൽകുക.", "ERROR": "Please provide a valid phone number that starts with a `+` sign and does not contain any spaces."}, "PHONE_NUMBER_ID": {"LABEL": "Phone number ID", "PLACEHOLDER": "Please enter the Phone number ID obtained from Facebook developer dashboard.", "ERROR": "Please enter a valid value."}, "BUSINESS_ACCOUNT_ID": {"LABEL": "Business Account ID", "PLACEHOLDER": "Please enter the Business Account ID obtained from Facebook developer dashboard.", "ERROR": "Please enter a valid value."}, "WEBHOOK_VERIFY_TOKEN": {"LABEL": "Webhook Verify Token", "PLACEHOLDER": "Enter a verify token which you want to configure for Facebook webhooks.", "ERROR": "Please enter a valid value."}, "API_KEY": {"LABEL": "API key", "SUBTITLE": "Configure the WhatsApp API key.", "PLACEHOLDER": "API key", "ERROR": "Please enter a valid value."}, "API_CALLBACK": {"TITLE": "Callback URL", "SUBTITLE": "You have to configure the webhook URL and the verification token in the Facebook Developer portal with the values shown below.", "WEBHOOK_URL": "വെബ്‌ഹുക്ക് യുആർഎൽ", "WEBHOOK_VERIFICATION_TOKEN": "Webhook Verification Token"}, "SUBMIT_BUTTON": "Create WhatsApp Channel", "API": {"ERROR_MESSAGE": "We were not able to save the WhatsApp channel"}}, "API_CHANNEL": {"TITLE": "API Channel", "DESC": "Integrate with API channel and start supporting your customers.", "CHANNEL_NAME": {"LABEL": "ചാനലിന്റെ പേര്", "PLACEHOLDER": "ഈ ചാനലിനു ദയവായി ഒരു പേര് നൽകുക", "ERROR": "ഈ ഫീൽഡ് ആവശ്യമാണ്"}, "WEBHOOK_URL": {"LABEL": "വെബ്‌ഹുക്ക് യുആർഎൽ", "SUBTITLE": "Configure the URL where you want to receive callbacks on events.", "PLACEHOLDER": "വെബ്‌ഹുക്ക് യുആർഎൽ"}, "SUBMIT_BUTTON": "Create API Channel", "API": {"ERROR_MESSAGE": "We were not able to save the api channel"}}, "EMAIL_CHANNEL": {"TITLE": "Email Channel", "DESC": "Integrate your email inbox.", "CHANNEL_NAME": {"LABEL": "ചാനലിന്റെ പേര്", "PLACEHOLDER": "ഈ ചാനലിനു ദയവായി ഒരു പേര് നൽകുക", "ERROR": "ഈ ഫീൽഡ് ആവശ്യമാണ്"}, "EMAIL": {"LABEL": "ഇമെയിൽ", "SUBTITLE": "Email where your customers sends you support tickets", "PLACEHOLDER": "ഇമെയിൽ"}, "SUBMIT_BUTTON": "Create Email Channel", "API": {"ERROR_MESSAGE": "We were not able to save the email channel"}, "FINISH_MESSAGE": "Start forwarding your emails to the following email address."}, "LINE_CHANNEL": {"TITLE": "LINE Channel", "DESC": "Integrate with LINE channel and start supporting your customers.", "CHANNEL_NAME": {"LABEL": "ചാനലിന്റെ പേര്", "PLACEHOLDER": "ഈ ചാനലിനു ദയവായി ഒരു പേര് നൽകുക", "ERROR": "ഈ ഫീൽഡ് ആവശ്യമാണ്"}, "LINE_CHANNEL_ID": {"LABEL": "LINE Channel ID", "PLACEHOLDER": "LINE Channel ID"}, "LINE_CHANNEL_SECRET": {"LABEL": "LINE Channel Secret", "PLACEHOLDER": "LINE Channel Secret"}, "LINE_CHANNEL_TOKEN": {"LABEL": "LINE Channel Token", "PLACEHOLDER": "LINE Channel Token"}, "SUBMIT_BUTTON": "Create LINE Channel", "API": {"ERROR_MESSAGE": "We were not able to save the LINE channel"}, "API_CALLBACK": {"TITLE": "Callback URL", "SUBTITLE": "You have to configure the webhook URL in LINE application with the URL mentioned here."}}, "TELEGRAM_CHANNEL": {"TITLE": "Telegram Channel", "DESC": "Integrate with Telegram channel and start supporting your customers.", "BOT_TOKEN": {"LABEL": "Bot <PERSON>", "SUBTITLE": "Configure the bot token you have obtained from Telegram BotFather.", "PLACEHOLDER": "Bot <PERSON>"}, "SUBMIT_BUTTON": "Create Telegram Channel", "API": {"ERROR_MESSAGE": "We were not able to save the telegram channel"}}, "AUTH": {"TITLE": "Choose a channel", "DESC": "Chatwoot supports live-chat widgets, Facebook Messenger, Twitter profiles, WhatsApp, Emails, etc., as channels. If you want to build a custom channel, you can create it using the API channel. To get started, choose one of the channels below."}, "AGENTS": {"TITLE": "ഏജന്റുമാർ", "DESC": "നിങ്ങളുടെ പുതുതായി സൃഷ്ടിച്ച ഇൻ‌ബോക്സ് മാനേജു ചെയ്യുന്നതിന് ഇവിടെ നിങ്ങൾക്ക് ഏജന്റുമാരെ ചേർക്കാൻ‌ കഴിയും. ഈ തിരഞ്ഞെടുത്ത ഏജന്റുമാർ‌ക്ക് മാത്രമേ നിങ്ങളുടെ ഇൻ‌ബോക്സിലേക്ക് ആക്‌സസ് ഉണ്ടായിരിക്കുകയുള്ളൂ. ഈ ഇൻ‌ബോക്സിന്റെ ഭാഗമല്ലാത്ത ഏജന്റുമാർ‌ക്ക് ഈ ഇൻ‌ബോക്സിലെ സന്ദേശങ്ങൾ‌ കാണാനോ പ്രതികരിക്കാനോ കഴിയില്ല. <br> ഒരു അഡ്മിനിസ്ട്രേറ്റർ എന്ന നിലയിൽ, നിങ്ങൾക്ക് എല്ലാ ഇൻ‌ബോക്സുകളിലേക്കും ആക്സസ് ആവശ്യമുണ്ടെങ്കിൽ, നിങ്ങൾ സൃഷ്ടിക്കുന്ന എല്ലാ ഇൻ‌ബോക്സുകളിലേക്കും നിങ്ങൾ സ്വയം ഏജന്റായി ചേർക്കണം.", "VALIDATION_ERROR": "Add at least one agent to your new Inbox", "PICK_AGENTS": "Pick agents for the inbox"}, "DETAILS": {"TITLE": "ഇൻ‌ബോക്സ് വിശദാംശങ്ങൾ", "DESC": "ചുവടെയുള്ള ഡ്രോപ്പ്ഡൗണിൽ നിന്ന്, നിങ്ങൾക്ക് ചാറ്റ് വൂട്ടിലേക്ക് കണക്റ്റുചെയ്യാൻ ആഗ്രഹിക്കുന്ന ഫേസ്ബുക്ക് പേജ് തിരഞ്ഞെടുക്കുക. തിരിച്ചറിയലിനായി നിങ്ങളുടെ ഇൻബോക്സിനു ഒരു ഇച്ഛാനുസൃത പേര് നല്കാൻ കഴിയും."}, "FINISH": {"TITLE": "പൊളിച്ചു അടുക്കി!", "DESC": "നിങ്ങളുടെ ഫേസ്ബുക്ക് പേജ് ചാറ്റ് വൂട്ടുമായി സമന്വയിപ്പിക്കുന്നത് നിങ്ങൾ വിജയകരമായി പൂർത്തിയാക്കി. അടുത്ത തവണ ഒരു ഉപയോക്താവ് നിങ്ങളുടെ പേജിലേക്ക് സന്ദേശമയയ്ക്കുമ്പോൾ, സംഭാഷണം ഓട്ടോമാറ്റിക്കലി നിങ്ങളുടെ ഇൻ‌ബോക്സിൽ ദൃശ്യമാകും. <br> നിങ്ങൾക്ക് എളുപ്പത്തിൽ സംയോജിപ്പിക്കാൻ കഴിയുന്ന ഒരു വിജറ്റ് സ്ക്രിപ്റ്റും ഞങ്ങൾ നിങ്ങൾക്ക് നൽകുന്നു. ഇത് നിങ്ങളുടെ വെബ്‌സൈറ്റിലേക്ക് ചേർക്കുക. ഇത് നിങ്ങളുടെ വെബ്‌സൈറ്റിൽ തത്സമയമായിക്കഴിഞ്ഞാൽ, ഉപയോക്താക്കൾക്ക് നിങ്ങളുടെ വെബ്‌സൈറ്റിൽ നിന്ന് നിങ്ങൾക്ക് സന്ദേശം അയയ്‌ക്കാൻ കഴിയും, ഒപ്പം സംഭാഷണം ചാറ്റ് വൂട്ടിൽ തന്നെ ദൃശ്യമാകും. <br> കൊള്ളാം, അല്ലേ? :)"}, "EMAIL_PROVIDER": {"TITLE": "Select your email provider", "DESCRIPTION": "Select an email provider from the list below. If you don't see your email provider in the list, you can select the other provider option and provide the IMAP and SMTP Credentials."}, "MICROSOFT": {"TITLE": "Microsoft Email", "DESCRIPTION": "Click on the Sign in with Microsoft button to get started. You will redirected to the email sign in page. Once you accept the requested permissions, you would be redirected back to the inbox creation step.", "EMAIL_PLACEHOLDER": "Enter email address", "SIGN_IN": "Sign in with Microsoft", "ERROR_MESSAGE": "There was an error connecting to Microsoft, please try again"}, "GOOGLE": {"TITLE": "Google Email", "DESCRIPTION": "Click on the Sign in with Google button to get started. You will redirected to the email sign in page. Once you accept the requested permissions, you would be redirected back to the inbox creation step.", "SIGN_IN": "Sign in with Google", "EMAIL_PLACEHOLDER": "Enter email address", "ERROR_MESSAGE": "There was an error connecting to Google, please try again"}}, "DETAILS": {"LOADING_FB": "ഫേസ്ബുക് ഉപയോഗിച്ച് നിങ്ങളെ പ്രാമാണീകരിക്കുന്നു...", "ERROR_FB_LOADING": "Error loading Facebook SDK. Please disable any ad-blockers and try again from a different browser.", "ERROR_FB_AUTH": "എന്തോ കുഴപ്പം സംഭവിച്ചു, ദയവായി പേജ് പുതുക്കുക...", "ERROR_FB_UNAUTHORIZED": "You're not authorized to perform this action. ", "ERROR_FB_UNAUTHORIZED_HELP": "Please ensure you have access to the Facebook page with full control. You can read more about Facebook roles <a href=\" https://www.facebook.com/help/187316341316631\">here</a>.", "CREATING_CHANNEL": "നിങ്ങളുടെ ഇൻ‌ബോക്സ് സൃഷ്ടിച്ചു കൊണ്ട് ഇരിക്കുകയാണ്...", "TITLE": "ഇൻ‌ബോക്സ് വിശദാംശങ്ങൾ‌ കോൺഫിഗർ ചെയ്യുക", "DESC": ""}, "AGENTS": {"BUTTON_TEXT": "ഏജന്റുമാരെ ചേർക്കുക", "ADD_AGENTS": "നിങ്ങളുടെ ഇൻ‌ബോക്സിലേക്ക് ഏജന്റുമാരെ ചേർക്കുകയാണ്..."}, "FINISH": {"TITLE": "നിങ്ങളുടെ ഇൻ‌ബോക്സ് തയ്യാറാണ്!", "MESSAGE": "You can now engage with your customers through your new Channel. Happy supporting", "BUTTON_TEXT": "എന്നെ അവിടേക്ക് കൊണ്ടുപോകുക", "MORE_SETTINGS": "More settings", "WEBSITE_SUCCESS": "നിങ്ങൾ ഒരു വെബ്‌സൈറ്റ് ചാനൽ സൃഷ്ടിക്കുന്നത് വിജയകരമായി പൂർത്തിയാക്കി. ചുവടെ കാണിച്ചിരിക്കുന്ന കോഡ് പകർത്തി നിങ്ങളുടെ വെബ്‌സൈറ്റിൽ ചേർക്കുക. അടുത്ത തവണ ഒരു ഉപഭോക്താവ് തത്സമയ ചാറ്റ് ഉപയോഗിക്കുമ്പോൾ, സംഭാഷണം ഓട്ടോമാറ്റിക് ആയി  നിങ്ങളുടെ ഇൻ‌ബോക്സിൽ ദൃശ്യമാകും."}, "REAUTH": "വീണ്ടും അംഗീകാരം നൽകുക", "VIEW": "കാണുക", "EDIT": {"API": {"SUCCESS_MESSAGE": "വിജറ്റ് നിറം വിജയകരമായി അപ്‌ഡേറ്റു ചെയ്‌തു", "AUTO_ASSIGNMENT_SUCCESS_MESSAGE": "ഓട്ടോമാറ്റിക് അസൈൻമെന്റ് വിജയകരമായി അപ്‌ഡേറ്റുചെയ്‌തു", "ERROR_MESSAGE": "We couldn't update inbox settings. Please try again later."}, "EMAIL_COLLECT_BOX": {"ENABLED": "പ്രവർത്തനക്ഷമമാക്കി", "DISABLED": "പ്രവർത്തനരഹിതമാക്കി"}, "ENABLE_CSAT": {"ENABLED": "പ്രവർത്തനക്ഷമമാക്കി", "DISABLED": "പ്രവർത്തനരഹിതമാക്കി"}, "SENDER_NAME_SECTION": {"TITLE": "Sender name", "SUB_TEXT": "Select the name shown to your customer when they receive emails from your agents.", "FOR_EG": "For eg:", "FRIENDLY": {"TITLE": "Friendly", "FROM": "നിന്ന്", "SUBTITLE": "Add the name of the agent who sent the reply in the sender name to make it friendly."}, "PROFESSIONAL": {"TITLE": "Professional", "SUBTITLE": "Use only the configured business name as the sender name in the email header."}, "BUSINESS_NAME": {"BUTTON_TEXT": "+ Configure your business name", "PLACEHOLDER": "Enter your business name", "SAVE_BUTTON_TEXT": "Save"}}, "ALLOW_MESSAGES_AFTER_RESOLVED": {"ENABLED": "പ്രവർത്തനക്ഷമമാക്കി", "DISABLED": "പ്രവർത്തനരഹിതമാക്കി"}, "ENABLE_CONTINUITY_VIA_EMAIL": {"ENABLED": "പ്രവർത്തനക്ഷമമാക്കി", "DISABLED": "പ്രവർത്തനരഹിതമാക്കി"}, "LOCK_TO_SINGLE_CONVERSATION": {"ENABLED": "പ്രവർത്തനക്ഷമമാക്കി", "DISABLED": "പ്രവർത്തനരഹിതമാക്കി"}, "ENABLE_HMAC": {"LABEL": "Enable"}}, "DELETE": {"BUTTON_TEXT": "ഇല്ലാതാക്കുക", "AVATAR_DELETE_BUTTON_TEXT": "Delete Avatar", "CONFIRM": {"TITLE": "ഇല്ലാതാക്കൽ സ്ഥിരീകരിക്കുക", "MESSAGE": "ഇല്ലാതാക്കണമെന്നു ഉറപ്പാണോ ", "PLACE_HOLDER": "Please type {inboxName} to confirm", "YES": "അതെ, ഇല്ലാതാക്കുക ", "NO": "ഇല്ല, സൂക്ഷിക്കുക"}, "API": {"SUCCESS_MESSAGE": "ഇൻ‌ബോക്സ് വിജയകരമായി ഇല്ലാതാക്കിയിരിക്കുന്നു", "ERROR_MESSAGE": "ഇൻ‌ബോക്സ് ഇല്ലാതാക്കാൻ‌ കഴിഞ്ഞില്ല. ദയവായി പിന്നീട് വീണ്ടും ശ്രമിക്കുക.", "AVATAR_SUCCESS_MESSAGE": "Inbox avatar deleted successfully", "AVATAR_ERROR_MESSAGE": "Could not delete the inbox avatar. Please try again later."}}, "TABS": {"SETTINGS": "ക്രമീകരണങ്ങൾ", "COLLABORATORS": "Collaborators", "CONFIGURATION": "Configuration", "CAMPAIGN": "പ്രചാരണങ്ങൾ", "PRE_CHAT_FORM": "Pre Chat Form", "BUSINESS_HOURS": "Business Hours", "WIDGET_BUILDER": "Widget Builder", "BOT_CONFIGURATION": "Bot Configuration", "CSAT": "CSAT"}, "SETTINGS": "ക്രമീകരണങ്ങൾ", "FEATURES": {"LABEL": "Features", "DISPLAY_FILE_PICKER": "Display file picker on the widget", "DISPLAY_EMOJI_PICKER": "Display emoji picker on the widget", "ALLOW_END_CONVERSATION": "Allow users to end conversation from the widget", "USE_INBOX_AVATAR_FOR_BOT": "Use inbox name and avatar for the bot"}, "SETTINGS_POPUP": {"MESSENGER_HEADING": "മെസഞ്ചർ സ്ക്രിപ്റ്റ്", "MESSENGER_SUB_HEAD": "ഈ ബട്ടൺ നിങ്ങളുടെ ബോഡി ടാഗിനുള്ളിൽ സ്ഥാപിക്കുക", "INBOX_AGENTS": "ഏജന്റുമാർ", "INBOX_AGENTS_SUB_TEXT": "ഈ ഇൻ‌ബോക്സിൽ നിന്ന് ഏജന്റുമാരെ ചേർക്കുക അല്ലെങ്കിൽ നീക്കംചെയ്യുക", "AGENT_ASSIGNMENT": "Conversation Assignment", "AGENT_ASSIGNMENT_SUB_TEXT": "Update conversation assignment settings", "UPDATE": "അപ്‌ഡേറ്റ്", "ENABLE_EMAIL_COLLECT_BOX": "Enable email collect box", "ENABLE_EMAIL_COLLECT_BOX_SUB_TEXT": "Enable or disable email collect box on new conversation", "AUTO_ASSIGNMENT": "ഓട്ടോ അസൈൻമെന്റ് പ്രവർത്തനക്ഷമമാക്കുക", "SENDER_NAME_SECTION": "Enable Agent Name in Email", "SENDER_NAME_SECTION_TEXT": "Enable/Disable showing Agent's name in email, if disabled it will show business name", "ENABLE_CONTINUITY_VIA_EMAIL": "ഇമെയിൽ വഴി സംഭാഷണ തുടർച്ച പ്രവർത്തനക്ഷമമാക്കുക", "ENABLE_CONTINUITY_VIA_EMAIL_SUB_TEXT": "ബന്ധപ്പെടാനുള്ള ഇമെയിൽ വിലാസം ലഭ്യമാണെങ്കിൽ സംഭാഷണങ്ങൾ ഇമെയിൽ വഴി തുടരും.", "LOCK_TO_SINGLE_CONVERSATION": "Lock to single conversation", "LOCK_TO_SINGLE_CONVERSATION_SUB_TEXT": "Enable or disable multiple conversations for the same contact in this inbox", "INBOX_UPDATE_TITLE": "Inbox Settings", "INBOX_UPDATE_SUB_TEXT": "Update your inbox settings", "AUTO_ASSIGNMENT_SUB_TEXT": "പുതിയ സംഭാഷണങ്ങളിൽ ലഭ്യമായ ഏജന്റുമാരുടെ ഓട്ടോമാറ്റിക് അസൈൻമെന്റ് പ്രാപ്തമാക്കുകയോ അപ്രാപ്തമാക്കുകയോ ചെയ്യുക", "HMAC_VERIFICATION": "User Identity Validation", "HMAC_DESCRIPTION": "In order to validate the user's identity, you can pass an `identifier_hash` for each user. You can generate a HMAC sha256 hash using the `identifier` with the key shown here.", "HMAC_LINK_TO_DOCS": "You can read more here.", "HMAC_MANDATORY_VERIFICATION": "Enforce User Identity Validation", "HMAC_MANDATORY_DESCRIPTION": "If enabled, requests missing the `identifier_hash` will be rejected.", "INBOX_IDENTIFIER": "Inbox Identifier", "INBOX_IDENTIFIER_SUB_TEXT": "Use the `inbox_identifier` token shown here to authentication your API clients.", "FORWARD_EMAIL_TITLE": "Forward to Email", "FORWARD_EMAIL_SUB_TEXT": "Start forwarding your emails to the following email address.", "ALLOW_MESSAGES_AFTER_RESOLVED": "സംഭാഷണം പരിഹരിച്ചതിന് ശേഷം സന്ദേശങ്ങൾ അനുവദിക്കുക", "ALLOW_MESSAGES_AFTER_RESOLVED_SUB_TEXT": "സംഭാഷണം പരിഹരിച്ചതിന് ശേഷവും സന്ദേശങ്ങൾ അയയ്ക്കാൻ അന്തിമ ഉപയോക്താക്കളെ അനുവദിക്കുക.", "WHATSAPP_SECTION_SUBHEADER": "This API Key is used for the integration with the WhatsApp APIs.", "WHATSAPP_SECTION_UPDATE_SUBHEADER": "Enter the new API key to be used for the integration with the WhatsApp APIs.", "WHATSAPP_SECTION_TITLE": "API Key", "WHATSAPP_SECTION_UPDATE_TITLE": "Update API Key", "WHATSAPP_SECTION_UPDATE_PLACEHOLDER": "Enter the new API Key here", "WHATSAPP_SECTION_UPDATE_BUTTON": "അപ്‌ഡേറ്റ്", "WHATSAPP_WEBHOOK_TITLE": "Webhook Verification Token", "WHATSAPP_WEBHOOK_SUBHEADER": "This token is used to verify the authenticity of the webhook endpoint.", "UPDATE_PRE_CHAT_FORM_SETTINGS": "Update Pre Chat Form Settings"}, "HELP_CENTER": {"LABEL": "Help Center", "PLACEHOLDER": "Select Help Center", "SELECT_PLACEHOLDER": "Select Help Center", "REMOVE": "Remove Help Center", "SUB_TEXT": "Attach a Help Center with the inbox"}, "AUTO_ASSIGNMENT": {"MAX_ASSIGNMENT_LIMIT": "Auto assignment limit", "MAX_ASSIGNMENT_LIMIT_RANGE_ERROR": "Please enter a value greater than 0", "MAX_ASSIGNMENT_LIMIT_SUB_TEXT": "Limit the maximum number of conversations from this inbox that can be auto assigned to an agent"}, "FACEBOOK_REAUTHORIZE": {"TITLE": "വീണ്ടും അംഗീകാരം നൽകുക", "SUBTITLE": "Your Facebook connection has expired, please reconnect your Facebook page to continue services", "MESSAGE_SUCCESS": "Reconnection successful", "MESSAGE_ERROR": "ഒരു പിശക് ഉണ്ടായിരുന്നു, ദയവായി വീണ്ടും ശ്രമിക്കുക"}, "PRE_CHAT_FORM": {"DESCRIPTION": "Pre chat forms enable you to capture user information before they start conversation with you.", "SET_FIELDS": "Pre chat form fields", "SET_FIELDS_HEADER": {"FIELDS": "Fields", "LABEL": "Label", "PLACE_HOLDER": "Placeholder", "KEY": "കീ", "TYPE": "തരം", "REQUIRED": "Required"}, "ENABLE": {"LABEL": "Enable pre chat form", "OPTIONS": {"ENABLED": "Yes", "DISABLED": "No"}}, "PRE_CHAT_MESSAGE": {"LABEL": "Pre chat message", "PLACEHOLDER": "This message would be visible to the users along with the form"}, "REQUIRE_EMAIL": {"LABEL": "Visitors should provide their name and email address before starting the chat"}}, "CSAT": {"TITLE": "Enable CSAT", "SUBTITLE": "Automatically trigger CSAT surveys at the end of conversations to understand how customers feel about their support experience. Track satisfaction trends and identify areas for improvement over time.", "DISPLAY_TYPE": {"LABEL": "Display type"}, "MESSAGE": {"LABEL": "സന്ദേശം", "PLACEHOLDER": "Please enter a message to show users with the form"}, "SURVEY_RULE": {"LABEL": "Survey rule", "DESCRIPTION_PREFIX": "Send the survey if the conversation", "DESCRIPTION_SUFFIX": "any of the labels", "OPERATOR": {"CONTAINS": "അടങ്ങിയിരിക്കുന്നു", "DOES_NOT_CONTAINS": "ഉൾപ്പെട്ടിട്ടില്ല"}, "SELECT_PLACEHOLDER": "select labels"}, "NOTE": "Note: CSAT surveys are sent only once per conversation", "API": {"SUCCESS_MESSAGE": "CSAT settings updated successfully", "ERROR_MESSAGE": "We couldn't update CSAT settings. Please try again later."}}, "BUSINESS_HOURS": {"TITLE": "Set your availability", "SUBTITLE": "Set your availability on your livechat widget", "WEEKLY_TITLE": "Set your weekly hours", "TIMEZONE_LABEL": "Select timezone", "UPDATE": "Update business hours settings", "TOGGLE_AVAILABILITY": "Enable business availability for this inbox", "UNAVAILABLE_MESSAGE_LABEL": "Unavailable message for visitors", "TOGGLE_HELP": "Enabling business availability will show the available hours on live chat widget even if all the agents are offline. Outside available hours visitors can be warned with a message and a pre-chat form.", "DAY": {"ENABLE": "Enable availability for this day", "UNAVAILABLE": "Unavailable", "HOURS": "hours", "VALIDATION_ERROR": "Starting time should be before closing time.", "CHOOSE": "<PERSON><PERSON>"}, "ALL_DAY": "All-Day"}, "IMAP": {"TITLE": "IMAP", "SUBTITLE": "Set your IMAP details", "NOTE_TEXT": "To enable SMTP, please configure IMAP.", "UPDATE": "Update IMAP settings", "TOGGLE_AVAILABILITY": "Enable IMAP configuration for this inbox", "TOGGLE_HELP": "Enabling IMAP will help the user to receive email", "EDIT": {"SUCCESS_MESSAGE": "IMAP settings updated successfully", "ERROR_MESSAGE": "Unable to update IMAP settings"}, "ADDRESS": {"LABEL": "Address", "PLACE_HOLDER": "Address (Eg: imap.gmail.com)"}, "PORT": {"LABEL": "Port", "PLACE_HOLDER": "Port"}, "LOGIN": {"LABEL": "സൈൻ ഇൻ", "PLACE_HOLDER": "സൈൻ ഇൻ"}, "PASSWORD": {"LABEL": "പാസ്‌വേഡ്", "PLACE_HOLDER": "പാസ്‌വേഡ്"}, "ENABLE_SSL": "Enable SSL"}, "MICROSOFT": {"TITLE": "Microsoft", "SUBTITLE": "Reauthorize your MICROSOFT account"}, "SMTP": {"TITLE": "SMTP", "SUBTITLE": "Set your SMTP details", "UPDATE": "Update SMTP settings", "TOGGLE_AVAILABILITY": "Enable SMTP configuration for this inbox", "TOGGLE_HELP": "Enabling SMTP will help the user to send email", "EDIT": {"SUCCESS_MESSAGE": "SMTP settings updated successfully", "ERROR_MESSAGE": "Unable to update SMTP settings"}, "ADDRESS": {"LABEL": "Address", "PLACE_HOLDER": "Address (Eg: smtp.gmail.com)"}, "PORT": {"LABEL": "Port", "PLACE_HOLDER": "Port"}, "LOGIN": {"LABEL": "സൈൻ ഇൻ", "PLACE_HOLDER": "സൈൻ ഇൻ"}, "PASSWORD": {"LABEL": "പാസ്‌വേഡ്", "PLACE_HOLDER": "പാസ്‌വേഡ്"}, "DOMAIN": {"LABEL": "Domain", "PLACE_HOLDER": "Domain"}, "ENCRYPTION": "Encryption", "SSL_TLS": "SSL/TLS", "START_TLS": "STARTTLS", "OPEN_SSL_VERIFY_MODE": "Open SSL Verify Mode", "AUTH_MECHANISM": "Authentication"}, "NOTE": "Note: ", "WIDGET_BUILDER": {"WIDGET_OPTIONS": {"AVATAR": {"LABEL": "Website Avatar", "DELETE": {"API": {"SUCCESS_MESSAGE": "Avatar deleted successfully", "ERROR_MESSAGE": "ഒരു പിശക് ഉണ്ടായിരുന്നു, ദയവായി വീണ്ടും ശ്രമിക്കുക"}}}, "WEBSITE_NAME": {"LABEL": "വെബ്‌സൈറ്റിന്റെ പേര്", "PLACE_HOLDER": "നിങ്ങളുടെ വെബ്‌സൈറ്റിന്റെ പേര് നൽകുക (ഉദാ: പുണ്ണ്യാളൻ അഗർബത്തീസ്)", "ERROR": "Please enter a valid website name"}, "WELCOME_HEADING": {"LABEL": "സ്വാഗത തലക്കെട്ട്", "PLACE_HOLDER": "Hi there!"}, "WELCOME_TAGLINE": {"LABEL": "ടാഗ്‌ലൈൻ സ്വാഗതം", "PLACE_HOLDER": "ഞങ്ങളുമായി കണക്റ്റുചെയ്യുന്നത് ഞങ്ങൾ ലളിതമാക്കുന്നു. ഞങ്ങളോട് എന്തും ചോദിക്കുക, അല്ലെങ്കിൽ നിങ്ങളുടെ ഫീഡ്‌ബാക്ക് പങ്കിടുക."}, "REPLY_TIME": {"LABEL": "Reply Time", "IN_A_FEW_MINUTES": "In a few minutes", "IN_A_FEW_HOURS": "In a few hours", "IN_A_DAY": "In a day"}, "WIDGET_COLOR_LABEL": "വിജറ്റ് നിറം", "WIDGET_BUBBLE_POSITION_LABEL": "Widget Bubble Position", "WIDGET_BUBBLE_TYPE_LABEL": "Widget Bubble Type", "WIDGET_BUBBLE_LAUNCHER_TITLE": {"DEFAULT": "ഞങ്ങളുമായി ചാറ്റുചെയ്യുക", "LABEL": "Widget Bubble Launcher Title", "PLACE_HOLDER": "ഞങ്ങളുമായി ചാറ്റുചെയ്യുക"}, "UPDATE": {"BUTTON_TEXT": "Update Widget Settings", "API": {"SUCCESS_MESSAGE": "Widget settings updated successfully", "ERROR_MESSAGE": "Unable to update widget settings"}}, "WIDGET_VIEW_OPTION": {"PREVIEW": "Preview", "SCRIPT": "<PERSON><PERSON><PERSON>"}, "WIDGET_BUBBLE_POSITION": {"LEFT": "Left", "RIGHT": "Right"}, "WIDGET_BUBBLE_TYPE": {"STANDARD": "Standard", "EXPANDED_BUBBLE": "Expanded Bubble"}}, "WIDGET_SCREEN": {"DEFAULT": "<PERSON><PERSON><PERSON>", "CHAT": "Cha<PERSON>"}, "REPLY_TIME": {"IN_A_FEW_MINUTES": "സാധാരണയായി കുറച്ച് മിനിറ്റിനുള്ളിൽ മറുപടി നൽകുന്നു", "IN_A_FEW_HOURS": "സാധാരണയായി കുറച്ച് മണിക്കൂറിനുള്ളിൽ മറുപടി നൽകുന്നു", "IN_A_DAY": "സാധാരണയായി ഒരു ദിവസത്തിൽ മറുപടി നൽകുന്നു"}, "FOOTER": {"START_CONVERSATION_BUTTON_TEXT": "സംഭാഷണം ആരംഭിക്കുക", "CHAT_INPUT_PLACEHOLDER": "നിങ്ങളുടെ സന്ദേശം ടൈപ്പുചെയ്യുക"}, "BODY": {"TEAM_AVAILABILITY": {"ONLINE": "We are Online", "OFFLINE": "ഞങ്ങൾ ഇപ്പോൾ അകലെയാണ്"}, "USER_MESSAGE": "Hi", "AGENT_MESSAGE": "Hello"}, "BRANDING_TEXT": "പ്രായോജകർ Chatwoot", "SCRIPT_SETTINGS": "\n      window.chatwootSettings = {options};"}, "EMAIL_PROVIDERS": {"MICROSOFT": "Microsoft", "GOOGLE": "Google", "OTHER_PROVIDERS": "Other Providers"}, "CHANNELS": {"MESSENGER": "<PERSON>", "WEB_WIDGET": "Website", "TWITTER_PROFILE": "Twitter", "TWILIO_SMS": "<PERSON><PERSON><PERSON>", "WHATSAPP": "WhatsApp", "SMS": "SMS", "EMAIL": "ഇമെയിൽ", "TELEGRAM": "Telegram", "LINE": "Line", "API": "API Channel", "INSTAGRAM": "Instagram"}}}