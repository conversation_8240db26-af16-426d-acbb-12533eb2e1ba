import { allAgentsData } from 'dashboard/helper/specs/fixtures/agentFixtures';

export { allAgentsData };
export const formattedAgentsData = [
  {
    account_id: 0,
    confirmed: true,
    email: 'None',
    id: 0,
    name: 'None',
    role: 'agent',
  },
  {
    account_id: 1,
    availability_status: 'online',
    available_name: '<PERSON>',
    confirmed: true,
    email: '<EMAIL>',
    id: 5,
    name: '<PERSON>',
    role: 'agent',
  },
  {
    account_id: 1,
    availability_status: 'online',
    available_name: '<PERSON>',
    confirmed: true,
    email: '<EMAIL>',
    id: 1,
    name: '<PERSON>',
    role: 'administrator',
  },
  {
    account_id: 1,
    availability_status: 'busy',
    available_name: '<PERSON>',
    confirmed: true,
    email: '<EMAIL>',
    id: 4,
    name: '<PERSON>',
    role: 'agent',
  },
  {
    account_id: 1,
    availability_status: 'busy',
    available_name: '<PERSON>',
    confirmed: true,
    email: '<EMAIL>',
    id: 2,
    name: '<PERSON>',
    role: 'agent',
  },
  {
    account_id: 1,
    availability_status: 'offline',
    available_name: '<PERSON>',
    confirmed: true,
    email: '<EMAIL>',
    id: 3,
    name: '<PERSON>',
    role: 'agent',
  },
];
