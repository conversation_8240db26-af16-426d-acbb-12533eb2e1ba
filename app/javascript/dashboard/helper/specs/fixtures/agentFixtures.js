export const allAgentsData = [
  {
    account_id: 1,
    availability_status: 'online',
    available_name: '<PERSON>',
    confirmed: true,
    email: '<EMAIL>',
    id: 1,
    name: '<PERSON>',
    role: 'administrator',
  },
  {
    account_id: 1,
    availability_status: 'busy',
    available_name: '<PERSON>',
    confirmed: true,
    email: 'sa<PERSON><PERSON>@chatwoot.com',
    id: 2,
    name: '<PERSON>',
    role: 'agent',
  },
  {
    account_id: 1,
    availability_status: 'offline',
    available_name: '<PERSON>',
    confirmed: true,
    email: '<EMAIL>',
    id: 3,
    name: '<PERSON>',
    role: 'agent',
  },
  {
    account_id: 1,
    availability_status: 'busy',
    available_name: '<PERSON>',
    confirmed: true,
    email: '<EMAIL>',
    id: 4,
    name: '<PERSON>',
    role: 'agent',
  },
  {
    account_id: 1,
    availability_status: 'online',
    available_name: '<PERSON>',
    confirmed: true,
    email: '<EMAIL>',
    id: 5,
    name: '<PERSON>',
    role: 'agent',
  },
];
export const onlineAgentsData = [
  {
    account_id: 1,
    availability_status: 'online',
    available_name: '<PERSON>',
    confirmed: true,
    email: '<EMAIL>',
    id: 5,
    name: '<PERSON>',
    role: 'agent',
  },
  {
    account_id: 1,
    availability_status: 'online',
    available_name: '<PERSON>',
    confirmed: true,
    email: '<EMAIL>',
    id: 1,
    name: '<PERSON> <PERSON>nady',
    role: 'administrator',
  },
];
export const busyAgentsData = [
  {
    account_id: 1,
    availability_status: 'busy',
    available_name: 'Honey',
    confirmed: true,
    email: '<EMAIL>',
    id: 4,
    name: 'Honey Bee',
    role: 'agent',
  },
  {
    account_id: 1,
    availability_status: 'busy',
    available_name: 'Samuel K',
    confirmed: true,
    email: '<EMAIL>',
    id: 2,
    name: 'Samuel Keta',
    role: 'agent',
  },
];
export const offlineAgentsData = [
  {
    account_id: 1,
    availability_status: 'offline',
    available_name: 'James K',
    confirmed: true,
    email: '<EMAIL>',
    id: 3,
    name: 'James Koti',
    role: 'agent',
  },
];
export const sortedByAvailability = [
  {
    account_id: 1,
    availability_status: 'online',
    available_name: 'Abraham',
    confirmed: true,
    email: '<EMAIL>',
    id: 5,
    name: 'Abraham Keta',
    role: 'agent',
  },
  {
    account_id: 1,
    availability_status: 'online',
    available_name: 'John K',
    confirmed: true,
    email: '<EMAIL>',
    id: 1,
    name: 'John Kennady',
    role: 'administrator',
  },
  {
    account_id: 1,
    availability_status: 'busy',
    available_name: 'Honey',
    confirmed: true,
    email: '<EMAIL>',
    id: 4,
    name: 'Honey Bee',
    role: 'agent',
  },
  {
    account_id: 1,
    availability_status: 'busy',
    available_name: 'Samuel K',
    confirmed: true,
    email: '<EMAIL>',
    id: 2,
    name: 'Samuel Keta',
    role: 'agent',
  },
  {
    account_id: 1,
    availability_status: 'offline',
    available_name: 'James K',
    confirmed: true,
    email: '<EMAIL>',
    id: 3,
    name: 'James Koti',
    role: 'agent',
  },
];
export const formattedAgentsByPresenceOnline = [
  {
    account_id: 1,
    availability_status: 'online',
    available_name: 'Abraham',
    confirmed: true,
    email: '<EMAIL>',
    id: 1,
    name: 'Abraham Keta',
    role: 'agent',
  },
];
export const formattedAgentsByPresenceOffline = [
  {
    account_id: 1,
    availability_status: 'offline',
    available_name: 'Abraham',
    confirmed: true,
    email: '<EMAIL>',
    id: 1,
    name: 'Abraham Keta',
    role: 'agent',
  },
];
