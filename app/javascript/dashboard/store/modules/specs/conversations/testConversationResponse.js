export const dataReceived = {
  meta: {
    mine_count: 3,
    unassigned_count: 0,
    all_count: 4,
  },
  payload: [
    {
      meta: {
        sender: {
          additional_attributes: {},
          availability_status: 'offline',
          email: null,
          id: 40,
          name: 'damp-field-834',
          phone_number: null,
          identifier: null,
          thumbnail: '',
          custom_attributes: {},
          last_activity_at: **********,
        },
        channel: 'Channel::WebWidget',
        assignee: {
          id: 1,
          account_id: 1,
          availability_status: 'online',
          auto_offline: true,
          confirmed: true,
          email: '<EMAIL>',
          available_name: '<PERSON>',
          name: '<PERSON>',
          role: 'administrator',
          thumbnail:
            'http://0.0.0.0:3000/rails/active_storage/representations/redirect/eyJfcmFpbHMiOnsibWVzc2FnZSI6IkJBaHBCdz09IiwiZXhwIjpudWxsLCJwdXIiOiJibG9iX2lkIn19--318d40b3d34e02760df9f4ea0c5c89d1f590dda4/eyJfcmFpbHMiOnsibWVzc2FnZSI6IkJBaDdCem9MWm05eWJXRjBTU0lJY0c1bkJqb0dSVlE2QzNKbGMybDZaVWtpRERJMU1IZ3lOVEFHT3daVSIsImV4cCI6bnVsbCwicHVyIjoidmFyaWF0aW9uIn19--e0e35266e8ed66e90c51be02408be8a022aca545/profile-pic.png',
        },
      },
      id: 10,
      messages: [
        {
          id: 85,
          content: 'Ok',
          account_id: 1,
          inbox_id: 6,
          conversation_id: 10,
          message_type: 1,
          created_at: **********,
          updated_at: '2021-11-01T10:57:45.790Z',
          private: false,
          status: 'sent',
          source_id: null,
          content_type: 'text',
          content_attributes: {},
          sender_type: 'User',
          sender_id: 1,
          external_source_ids: {},
          sender: {
            id: 1,
            name: 'John',
            available_name: 'John',
            avatar_url:
              'http://0.0.0.0:3000/rails/active_storage/representations/redirect/eyJfcmFpbHMiOnsibWVzc2FnZSI6IkJBaHBCdz09IiwiZXhwIjpudWxsLCJwdXIiOiJibG9iX2lkIn19--318d40b3d34e02760df9f4ea0c5c89d1f590dda4/eyJfcmFpbHMiOnsibWVzc2FnZSI6IkJBaDdCem9MWm05eWJXRjBTU0lJY0c1bkJqb0dSVlE2QzNKbGMybDZaVWtpRERJMU1IZ3lOVEFHT3daVSIsImV4cCI6bnVsbCwicHVyIjoidmFyaWF0aW9uIn19--e0e35266e8ed66e90c51be02408be8a022aca545/profile-pic.png',
            type: 'user',
            availability_status: 'online',
            thumbnail:
              'http://0.0.0.0:3000/rails/active_storage/representations/redirect/eyJfcmFpbHMiOnsibWVzc2FnZSI6IkJBaHBCdz09IiwiZXhwIjpudWxsLCJwdXIiOiJibG9iX2lkIn19--318d40b3d34e02760df9f4ea0c5c89d1f590dda4/eyJfcmFpbHMiOnsibWVzc2FnZSI6IkJBaDdCem9MWm05eWJXRjBTU0lJY0c1bkJqb0dSVlE2QzNKbGMybDZaVWtpRERJMU1IZ3lOVEFHT3daVSIsImV4cCI6bnVsbCwicHVyIjoidmFyaWF0aW9uIn19--e0e35266e8ed66e90c51be02408be8a022aca545/profile-pic.png',
          },
        },
      ],
      account_id: 1,
      additional_attributes: {
        browser: {
          device_name: 'Unknown',
          browser_name: 'Chrome',
          platform_name: 'macOS',
          browser_version: '95.0.4638.54',
          platform_version: '10.15.7',
        },
        referer: 'http://localhost:3000/widget_tests',
        initiated_at: {
          timestamp: 'Mon Nov 01 2021 16:25:06 GMT+0530 (India Standard Time)',
        },
      },
      agent_last_seen_at: **********,
      assignee_last_seen_at: **********,
      can_reply: true,
      contact_last_seen_at: **********,
      custom_attributes: {},
      inbox_id: 6,
      labels: [],
      muted: false,
      snoozed_until: null,
      status: 'open',
      timestamp: **********,
      unread_count: 0,
    },
  ],
};
