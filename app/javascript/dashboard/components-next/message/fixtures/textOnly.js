import camelcaseKeys from 'camelcase-keys';

export default camelcaseKeys(
  [
    {
      id: 5272,
      content: 'Hey, how are ya, I had a few questions about <PERSON><PERSON>woot?',
      inbox_id: 475,
      conversation_id: 43,
      message_type: 0,
      content_type: 'text',
      status: 'sent',
      content_attributes: {
        in_reply_to: null,
      },
      created_at: **********,
      private: false,
      source_id: null,
      sender: {
        additional_attributes: {},
        custom_attributes: {},
        email: '<EMAIL>',
        id: 597,
        identifier: null,
        name: 'hey',
        phone_number: null,
        thumbnail: '',
        type: 'contact',
      },
    },
    {
      id: 5273,
      content: 'Give the team a way to reach you.',
      inbox_id: 475,
      conversation_id: 43,
      message_type: 3,
      content_type: 'text',
      status: 'read',
      content_attributes: {},
      created_at: **********,
      private: false,
      source_id: null,
    },
    {
      id: 5274,
      content: 'Get notified by email',
      account_id: 1,
      inbox_id: 475,
      conversation_id: 43,
      message_type: 3,
      created_at: **********,
      updated_at: '2024-11-21T13:27:53.612Z',
      private: false,
      status: 'read',
      source_id: null,
      content_type: 'input_email',
      content_attributes: {
        submitted_email: '<EMAIL>',
      },
      sender_type: null,
      sender_id: null,
      external_source_ids: {},
      additional_attributes: {},
      processed_message_content: 'Get notified by email',
      sentiment: {},
      conversation: {
        assignee_id: null,
        unread_count: 1,
        last_activity_at: **********,
        contact_inbox: {
          source_id: 'b018c554-8e17-4102-8a0b-f6d20d021017',
        },
      },
    },
    {
      id: 5275,
      content:
        'Does the Startup plan include the two users from the Free plan, or do I have to buy those separately?',
      account_id: 1,
      inbox_id: 475,
      conversation_id: 43,
      message_type: 0,
      created_at: **********,
      updated_at: '2024-11-21T13:28:55.508Z',
      private: false,
      status: 'sent',
      source_id: null,
      content_type: 'text',
      content_attributes: {
        in_reply_to: null,
      },
      sender_type: 'Contact',
      sender_id: 597,
      external_source_ids: {},
      additional_attributes: {},
      processed_message_content:
        'Does the Startup plan include the two users from the Free plan, or do I have to buy those separately?',
      sentiment: {},
      conversation: {
        assignee_id: null,
        unread_count: 1,
        last_activity_at: **********,
        contact_inbox: {
          source_id: 'b018c554-8e17-4102-8a0b-f6d20d021017',
        },
      },
      sender: {
        additional_attributes: {},
        custom_attributes: {},
        email: '<EMAIL>',
        id: 597,
        identifier: null,
        name: 'hey',
        phone_number: null,
        thumbnail: '',
        type: 'contact',
      },
    },
    {
      conversation_id: 43,
      status: 'read',
      content_type: 'text',
      processed_message_content: 'John self-assigned this conversation',
      id: 5276,
      content: 'John self-assigned this conversation',
      account_id: 1,
      inbox_id: 475,
      message_type: 2,
      created_at: **********,
      updated_at: '2024-11-21T13:30:26.788Z',
      private: false,
      source_id: null,
      content_attributes: {},
      sender_type: null,
      sender_id: null,
      external_source_ids: {},
      additional_attributes: {},
      sentiment: {},
      conversation: {
        assignee_id: 1,
        unread_count: 0,
        last_activity_at: **********,
        contact_inbox: {
          source_id: 'b018c554-8e17-4102-8a0b-f6d20d021017',
        },
      },
      previous_changes: {
        updated_at: ['2024-11-21T13:29:01.570Z', '2024-11-21T13:30:26.788Z'],
        status: ['sent', 'read'],
      },
    },
    {
      conversation_id: 43,
      status: 'read',
      content_type: 'text',
      processed_message_content:
        'Hey thanks for your interest in upgrading, no, the seats are not included, you will have to purchase them alongside the rest. How many seats are you planning to upgrade to?',
      id: 5277,
      content:
        'Hey thanks for your interest in upgrading, no, the seats are not included, you will have to purchase them alongside the rest. How many seats are you planning to upgrade to?',
      account_id: 1,
      inbox_id: 475,
      message_type: 1,
      created_at: **********,
      updated_at: '2024-11-21T13:30:26.837Z',
      private: false,
      source_id: null,
      content_attributes: {},
      sender_type: 'User',
      sender_id: 1,
      external_source_ids: {},
      additional_attributes: {},
      sentiment: {},
      conversation: {
        assignee_id: 1,
        unread_count: 0,
        last_activity_at: **********,
        contact_inbox: {
          source_id: 'b018c554-8e17-4102-8a0b-f6d20d021017',
        },
      },
      sender: {
        id: 1,
        name: 'John',
        available_name: 'John',
        avatar_url:
          'http://localhost:3000/rails/active_storage/representations/redirect/eyJfcmFpbHMiOnsibWVzc2FnZSI6IkJBaHBBaDBLIiwiZXhwIjpudWxsLCJwdXIiOiJibG9iX2lkIn19--4e625d80e7ef2dc41354392bc214832fbe640840/eyJfcmFpbHMiOnsibWVzc2FnZSI6IkJBaDdCem9MWm05eWJXRjBTU0lJY0c1bkJqb0dSVlE2RTNKbGMybDZaVjkwYjE5bWFXeHNXd2RwQWZvdyIsImV4cCI6bnVsbCwicHVyIjoidmFyaWF0aW9uIn19--ebe60765d222d11ade39165eae49cc4b2de18d89/picologo.png',
        type: 'user',
        availability_status: null,
        thumbnail:
          'http://localhost:3000/rails/active_storage/representations/redirect/eyJfcmFpbHMiOnsibWVzc2FnZSI6IkJBaHBBaDBLIiwiZXhwIjpudWxsLCJwdXIiOiJibG9iX2lkIn19--4e625d80e7ef2dc41354392bc214832fbe640840/eyJfcmFpbHMiOnsibWVzc2FnZSI6IkJBaDdCem9MWm05eWJXRjBTU0lJY0c1bkJqb0dSVlE2RTNKbGMybDZaVjkwYjE5bWFXeHNXd2RwQWZvdyIsImV4cCI6bnVsbCwicHVyIjoidmFyaWF0aW9uIn19--ebe60765d222d11ade39165eae49cc4b2de18d89/picologo.png',
      },
      previous_changes: {
        updated_at: ['2024-11-21T13:30:26.149Z', '2024-11-21T13:30:26.837Z'],
        status: ['sent', 'read'],
      },
    },
    {
      id: 5278,
      content: "Oh, that's unfortunate",
      account_id: 1,
      inbox_id: 475,
      conversation_id: 43,
      message_type: 0,
      created_at: **********,
      updated_at: '2024-11-21T13:30:38.070Z',
      private: false,
      status: 'sent',
      source_id: null,
      content_type: 'text',
      content_attributes: {
        in_reply_to: null,
      },
      sender_type: 'Contact',
      sender_id: 597,
      external_source_ids: {},
      additional_attributes: {},
      processed_message_content: "Oh, that's unfortunate",
      sentiment: {},
      conversation: {
        assignee_id: 1,
        unread_count: 1,
        last_activity_at: **********,
        contact_inbox: {
          source_id: 'b018c554-8e17-4102-8a0b-f6d20d021017',
        },
      },
      sender: {
        additional_attributes: {},
        custom_attributes: {},
        email: '<EMAIL>',
        id: 597,
        identifier: null,
        name: 'hey',
        phone_number: null,
        thumbnail: '',
        type: 'contact',
      },
    },
    {
      id: 5279,
      content:
        'I plan to upgrade to 4 agents for now, but will grow to 6 in the next three months. ',
      account_id: 1,
      inbox_id: 475,
      conversation_id: 43,
      message_type: 0,
      created_at: **********,
      updated_at: '2024-11-21T13:31:05.284Z',
      private: false,
      status: 'sent',
      source_id: null,
      content_type: 'text',
      content_attributes: {
        in_reply_to: null,
      },
      sender_type: 'Contact',
      sender_id: 597,
      external_source_ids: {},
      additional_attributes: {},
      processed_message_content:
        'I plan to upgrade to 4 agents for now, but will grow to 6 in the next three months. ',
      sentiment: {},
      conversation: {
        assignee_id: 1,
        unread_count: 1,
        last_activity_at: **********,
        contact_inbox: {
          source_id: 'b018c554-8e17-4102-8a0b-f6d20d021017',
        },
      },
      sender: {
        additional_attributes: {},
        custom_attributes: {},
        email: '<EMAIL>',
        id: 597,
        identifier: null,
        name: 'hey',
        phone_number: null,
        thumbnail: '',
        type: 'contact',
      },
    },

    {
      id: 5280,
      content: 'Is it possible to get a discount?',
      account_id: 1,
      inbox_id: 475,
      conversation_id: 43,
      message_type: 0,
      created_at: **********,
      updated_at: '2024-11-21T13:31:12.545Z',
      private: false,
      status: 'sent',
      source_id: null,
      content_type: 'text',
      content_attributes: {
        in_reply_to: null,
      },
      sender_type: 'Contact',
      sender_id: 597,
      external_source_ids: {},
      additional_attributes: {},
      processed_message_content: 'Is it possible to get a discount?',
      sentiment: {},
      conversation: {
        assignee_id: 1,
        unread_count: 1,
        last_activity_at: **********,
        contact_inbox: {
          source_id: 'b018c554-8e17-4102-8a0b-f6d20d021017',
        },
      },
      sender: {
        additional_attributes: {},
        custom_attributes: {},
        email: '<EMAIL>',
        id: 597,
        identifier: null,
        name: 'hey',
        phone_number: null,
        thumbnail: '',
        type: 'contact',
      },
    },
    {
      conversation_id: 43,
      status: 'read',
      content_type: 'text',
      processed_message_content:
        '[@Bruce](mention://user/30/Bruce) should we offer them a discount',
      id: 5281,
      content:
        '[@Bruce](mention://user/30/Bruce) should we offer them a discount',
      account_id: 1,
      inbox_id: 475,
      message_type: 1,
      created_at: **********,
      updated_at: '2024-11-21T13:32:59.863Z',
      private: true,
      source_id: null,
      content_attributes: {},
      sender_type: 'User',
      sender_id: 1,
      external_source_ids: {},
      additional_attributes: {},
      sentiment: {},
      conversation: {
        assignee_id: 1,
        unread_count: 0,
        last_activity_at: **********,
        contact_inbox: {
          source_id: 'b018c554-8e17-4102-8a0b-f6d20d021017',
        },
      },
      sender: {
        id: 1,
        name: 'John',
        available_name: 'John',
        avatar_url:
          'http://localhost:3000/rails/active_storage/representations/redirect/eyJfcmFpbHMiOnsibWVzc2FnZSI6IkJBaHBBaDBLIiwiZXhwIjpudWxsLCJwdXIiOiJibG9iX2lkIn19--4e625d80e7ef2dc41354392bc214832fbe640840/eyJfcmFpbHMiOnsibWVzc2FnZSI6IkJBaDdCem9MWm05eWJXRjBTU0lJY0c1bkJqb0dSVlE2RTNKbGMybDZaVjkwYjE5bWFXeHNXd2RwQWZvdyIsImV4cCI6bnVsbCwicHVyIjoidmFyaWF0aW9uIn19--ebe60765d222d11ade39165eae49cc4b2de18d89/picologo.png',
        type: 'user',
        availability_status: null,
        thumbnail:
          'http://localhost:3000/rails/active_storage/representations/redirect/eyJfcmFpbHMiOnsibWVzc2FnZSI6IkJBaHBBaDBLIiwiZXhwIjpudWxsLCJwdXIiOiJibG9iX2lkIn19--4e625d80e7ef2dc41354392bc214832fbe640840/eyJfcmFpbHMiOnsibWVzc2FnZSI6IkJBaDdCem9MWm05eWJXRjBTU0lJY0c1bkJqb0dSVlE2RTNKbGMybDZaVjkwYjE5bWFXeHNXd2RwQWZvdyIsImV4cCI6bnVsbCwicHVyIjoidmFyaWF0aW9uIn19--ebe60765d222d11ade39165eae49cc4b2de18d89/picologo.png',
      },
      previous_changes: {
        updated_at: ['2024-11-21T13:31:27.914Z', '2024-11-21T13:32:59.863Z'],
        status: ['sent', 'read'],
      },
    },
    {
      conversation_id: 43,
      status: 'read',
      content_type: 'text',
      processed_message_content:
        'Sure, you can use the discount code KQS3242A at the checkout to get 30% off on your yearly subscription. This coupon only applies for a year, I hope this helps',
      id: 5282,
      content:
        'Sure, you can use the discount code KQS3242A at the checkout to get 30% off on your yearly subscription. This coupon only applies for a year, I hope this helps',
      account_id: 1,
      inbox_id: 475,
      message_type: 1,
      created_at: **********,
      updated_at: '2024-11-21T13:32:59.902Z',
      private: false,
      source_id: null,
      content_attributes: {},
      sender_type: 'User',
      sender_id: 1,
      external_source_ids: {},
      additional_attributes: {},
      sentiment: {},
      conversation: {
        assignee_id: 1,
        unread_count: 0,
        last_activity_at: **********,
        contact_inbox: {
          source_id: 'b018c554-8e17-4102-8a0b-f6d20d021017',
        },
      },
      sender: {
        id: 1,
        name: 'John',
        available_name: 'John',
        avatar_url:
          'http://localhost:3000/rails/active_storage/representations/redirect/eyJfcmFpbHMiOnsibWVzc2FnZSI6IkJBaHBBaDBLIiwiZXhwIjpudWxsLCJwdXIiOiJibG9iX2lkIn19--4e625d80e7ef2dc41354392bc214832fbe640840/eyJfcmFpbHMiOnsibWVzc2FnZSI6IkJBaDdCem9MWm05eWJXRjBTU0lJY0c1bkJqb0dSVlE2RTNKbGMybDZaVjkwYjE5bWFXeHNXd2RwQWZvdyIsImV4cCI6bnVsbCwicHVyIjoidmFyaWF0aW9uIn19--ebe60765d222d11ade39165eae49cc4b2de18d89/picologo.png',
        type: 'user',
        availability_status: null,
        thumbnail:
          'http://localhost:3000/rails/active_storage/representations/redirect/eyJfcmFpbHMiOnsibWVzc2FnZSI6IkJBaHBBaDBLIiwiZXhwIjpudWxsLCJwdXIiOiJibG9iX2lkIn19--4e625d80e7ef2dc41354392bc214832fbe640840/eyJfcmFpbHMiOnsibWVzc2FnZSI6IkJBaDdCem9MWm05eWJXRjBTU0lJY0c1bkJqb0dSVlE2RTNKbGMybDZaVjkwYjE5bWFXeHNXd2RwQWZvdyIsImV4cCI6bnVsbCwicHVyIjoidmFyaWF0aW9uIn19--ebe60765d222d11ade39165eae49cc4b2de18d89/picologo.png',
      },
      previous_changes: {
        updated_at: ['2024-11-21T13:32:52.722Z', '2024-11-21T13:32:59.902Z'],
        status: ['sent', 'read'],
      },
    },
    {
      id: 5283,
      content: 'Great, thanks',
      account_id: 1,
      inbox_id: 475,
      conversation_id: 43,
      message_type: 0,
      created_at: **********,
      updated_at: '2024-11-21T13:33:02.142Z',
      private: false,
      status: 'sent',
      source_id: null,
      content_type: 'text',
      content_attributes: {
        in_reply_to: null,
      },
      sender_type: 'Contact',
      sender_id: 597,
      external_source_ids: {},
      additional_attributes: {},
      processed_message_content: 'Great, thanks',
      sentiment: {},
      conversation: {
        assignee_id: 1,
        unread_count: 1,
        last_activity_at: **********,
        contact_inbox: {
          source_id: 'b018c554-8e17-4102-8a0b-f6d20d021017',
        },
      },
      sender: {
        additional_attributes: {},
        custom_attributes: {},
        email: '<EMAIL>',
        id: 597,
        identifier: null,
        name: 'hey',
        phone_number: null,
        thumbnail: '',
        type: 'contact',
      },
    },

    {
      id: 5284,
      content: 'Really appreciate it',
      account_id: 1,
      inbox_id: 475,
      conversation_id: 43,
      message_type: 0,
      created_at: **********,
      updated_at: '2024-11-21T13:33:04.856Z',
      private: false,
      status: 'sent',
      source_id: null,
      content_type: 'text',
      content_attributes: {
        in_reply_to: null,
      },
      sender_type: 'Contact',
      sender_id: 597,
      external_source_ids: {},
      additional_attributes: {},
      processed_message_content: 'Really appreciate it',
      sentiment: {},
      conversation: {
        assignee_id: 1,
        unread_count: 1,
        last_activity_at: **********,
        contact_inbox: {
          source_id: 'b018c554-8e17-4102-8a0b-f6d20d021017',
        },
      },
      sender: {
        additional_attributes: {},
        custom_attributes: {},
        email: '<EMAIL>',
        id: 597,
        identifier: null,
        name: 'hey',
        phone_number: null,
        thumbnail: '',
        type: 'contact',
      },
    },
    {
      conversation_id: 43,
      status: 'progress',
      content_type: 'text',
      processed_message_content: ' Happy to help :)',
      id: 5285,
      content: ' Happy to help :)',
      account_id: 1,
      inbox_id: 475,
      message_type: 1,
      created_at: **********,
      updated_at: '2024-11-21T13:33:12.229Z',
      private: false,
      source_id: null,
      content_attributes: {},
      sender_type: 'User',
      sender_id: 1,
      external_source_ids: {},
      additional_attributes: {},
      sentiment: {},
      conversation: {
        assignee_id: 1,
        unread_count: 0,
        last_activity_at: **********,
        contact_inbox: {
          source_id: 'b018c554-8e17-4102-8a0b-f6d20d021017',
        },
      },
      sender: {
        id: 1,
        name: 'John',
        available_name: 'John',
        avatar_url:
          'http://localhost:3000/rails/active_storage/representations/redirect/eyJfcmFpbHMiOnsibWVzc2FnZSI6IkJBaHBBaDBLIiwiZXhwIjpudWxsLCJwdXIiOiJibG9iX2lkIn19--4e625d80e7ef2dc41354392bc214832fbe640840/eyJfcmFpbHMiOnsibWVzc2FnZSI6IkJBaDdCem9MWm05eWJXRjBTU0lJY0c1bkJqb0dSVlE2RTNKbGMybDZaVjkwYjE5bWFXeHNXd2RwQWZvdyIsImV4cCI6bnVsbCwicHVyIjoidmFyaWF0aW9uIn19--ebe60765d222d11ade39165eae49cc4b2de18d89/picologo.png',
        type: 'user',
        availability_status: null,
        thumbnail:
          'http://localhost:3000/rails/active_storage/representations/redirect/eyJfcmFpbHMiOnsibWVzc2FnZSI6IkJBaHBBaDBLIiwiZXhwIjpudWxsLCJwdXIiOiJibG9iX2lkIn19--4e625d80e7ef2dc41354392bc214832fbe640840/eyJfcmFpbHMiOnsibWVzc2FnZSI6IkJBaDdCem9MWm05eWJXRjBTU0lJY0c1bkJqb0dSVlE2RTNKbGMybDZaVjkwYjE5bWFXeHNXd2RwQWZvdyIsImV4cCI6bnVsbCwicHVyIjoidmFyaWF0aW9uIn19--ebe60765d222d11ade39165eae49cc4b2de18d89/picologo.png',
      },
      previous_changes: {
        updated_at: ['2024-11-21T13:33:11.667Z', '2024-11-21T13:33:12.229Z'],
        status: ['sent', 'read'],
      },
    },
    {
      conversation_id: 43,
      status: 'failed',
      content_type: 'text',
      processed_message_content:
        "Let us know if you have any questions, I'll close this conversation for now",
      id: 5286,
      content:
        "Let us know if you have any questions, I'll close this conversation for now",
      account_id: 1,
      inbox_id: 475,
      message_type: 1,
      created_at: **********,
      updated_at: '2024-11-21T13:33:33.879Z',
      private: false,
      source_id: null,
      content_attributes: {
        external_error:
          'Business account is restricted from messaging users in this country.',
      },
      sender_type: 'User',
      sender_id: 1,
      external_source_ids: {},
      additional_attributes: {},
      sentiment: {},
      conversation: {
        assignee_id: 1,
        unread_count: 0,
        last_activity_at: **********,
        contact_inbox: {
          source_id: 'b018c554-8e17-4102-8a0b-f6d20d021017',
        },
      },
      sender: {
        id: 1,
        name: 'John',
        available_name: 'John',
        avatar_url:
          'http://localhost:3000/rails/active_storage/representations/redirect/eyJfcmFpbHMiOnsibWVzc2FnZSI6IkJBaHBBaDBLIiwiZXhwIjpudWxsLCJwdXIiOiJibG9iX2lkIn19--4e625d80e7ef2dc41354392bc214832fbe640840/eyJfcmFpbHMiOnsibWVzc2FnZSI6IkJBaDdCem9MWm05eWJXRjBTU0lJY0c1bkJqb0dSVlE2RTNKbGMybDZaVjkwYjE5bWFXeHNXd2RwQWZvdyIsImV4cCI6bnVsbCwicHVyIjoidmFyaWF0aW9uIn19--ebe60765d222d11ade39165eae49cc4b2de18d89/picologo.png',
        type: 'user',
        availability_status: null,
        thumbnail:
          'http://localhost:3000/rails/active_storage/representations/redirect/eyJfcmFpbHMiOnsibWVzc2FnZSI6IkJBaHBBaDBLIiwiZXhwIjpudWxsLCJwdXIiOiJibG9iX2lkIn19--4e625d80e7ef2dc41354392bc214832fbe640840/eyJfcmFpbHMiOnsibWVzc2FnZSI6IkJBaDdCem9MWm05eWJXRjBTU0lJY0c1bkJqb0dSVlE2RTNKbGMybDZaVjkwYjE5bWFXeHNXd2RwQWZvdyIsImV4cCI6bnVsbCwicHVyIjoidmFyaWF0aW9uIn19--ebe60765d222d11ade39165eae49cc4b2de18d89/picologo.png',
      },
      previous_changes: {
        updated_at: ['2024-11-21T13:33:33.511Z', '2024-11-21T13:33:33.879Z'],
        status: ['sent', 'read'],
      },
    },
    {
      id: 5287,
      content: 'John set the priority to urgent',
      account_id: 1,
      inbox_id: 475,
      conversation_id: 43,
      message_type: 2,
      created_at: **********,
      updated_at: '2024-11-21T13:33:37.569Z',
      private: false,
      status: 'sent',
      source_id: null,
      content_type: 'text',
      content_attributes: {},
      sender_type: null,
      sender_id: null,
      external_source_ids: {},
      additional_attributes: {},
      processed_message_content: 'John set the priority to urgent',
      sentiment: {},
      conversation: {
        assignee_id: 1,
        unread_count: 0,
        last_activity_at: **********,
        contact_inbox: {
          source_id: 'b018c554-8e17-4102-8a0b-f6d20d021017',
        },
      },
    },
    {
      id: 5288,
      content: 'John added billing',
      account_id: 1,
      inbox_id: 475,
      conversation_id: 43,
      message_type: 2,
      created_at: **********,
      updated_at: '2024-11-21T13:33:40.207Z',
      private: false,
      status: 'sent',
      source_id: null,
      content_type: 'text',
      content_attributes: {},
      sender_type: null,
      sender_id: null,
      external_source_ids: {},
      additional_attributes: {},
      processed_message_content: 'John added billing',
      sentiment: {},
      conversation: {
        assignee_id: 1,
        unread_count: 0,
        last_activity_at: **********,
        contact_inbox: {
          source_id: 'b018c554-8e17-4102-8a0b-f6d20d021017',
        },
      },
    },
    {
      id: 5289,
      content: 'John added delivery',
      account_id: 1,
      inbox_id: 475,
      conversation_id: 43,
      message_type: 2,
      created_at: **********,
      updated_at: '2024-11-21T13:33:40.822Z',
      private: false,
      status: 'sent',
      source_id: null,
      content_type: 'text',
      content_attributes: {},
      sender_type: null,
      sender_id: null,
      external_source_ids: {},
      additional_attributes: {},
      processed_message_content: 'John added delivery',
      sentiment: {},
      conversation: {
        assignee_id: 1,
        unread_count: 0,
        last_activity_at: **********,
        contact_inbox: {
          source_id: 'b018c554-8e17-4102-8a0b-f6d20d021017',
        },
      },
    },
    {
      id: 5290,
      content: 'Conversation was marked resolved by John',
      account_id: 1,
      inbox_id: 475,
      conversation_id: 43,
      message_type: 2,
      created_at: **********,
      updated_at: '2024-11-21T13:33:49.059Z',
      private: false,
      status: 'sent',
      source_id: null,
      content_type: 'text',
      content_attributes: {},
      sender_type: null,
      sender_id: null,
      external_source_ids: {},
      additional_attributes: {},
      processed_message_content: 'Conversation was marked resolved by John',
      sentiment: {},
      conversation: {
        assignee_id: 1,
        unread_count: 0,
        last_activity_at: **********,
        contact_inbox: {
          source_id: 'b018c554-8e17-4102-8a0b-f6d20d021017',
        },
      },
    },
  ],
  { deep: true }
);
