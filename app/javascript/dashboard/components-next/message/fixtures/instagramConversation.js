import camelcaseKeys from 'camelcase-keys';

export default camelcaseKeys(
  [
    {
      id: ********,
      content: 'cwtesting<PERSON><PERSON> mentioned you in the story: ',
      inbox_id: 27355,
      conversation_id: 23731,
      message_type: 0,
      content_type: 'text',
      status: 'sent',
      content_attributes: {
        story_sender: 'cwtestinglocal',
        story_id: '*****************',
        image_type: 'story_mention',
      },
      created_at: **********,
      private: false,
      source_id:
        'aWdfZAG1faXRlbToxOklHTWVzc2FnZAUlEOjE3ODQxNDQ3NTU5OTYxMzk4OjM0MDI4MjM2Njg0MTcxMDMwMDk0OTEyODIyODExNDI0MDczNjkxMTozMTE5MjcwODk3OTU5MTM2NDg4NTg2OTU1NTEzOTczOTY0OAZDZD',
      sender: {
        additional_attributes: {
          social_profiles: {
            instagram: 'cwtestinglocal',
          },
        },
        custom_attributes: {},
        email: null,
        id: ********,
        identifier: null,
        name: '<PERSON>',
        phone_number: null,
        thumbnail: '',
        type: 'contact',
      },
      attachments: [
        {
          id: 2053726,
          message_id: ********,
          file_type: 'image',
          account_id: 1,
          extension: null,
          data_url:
            'https://videos.pexels.com/video-files/7722989/7722989-uhd_1440_2560_25fps.mp4',
          thumb_url:
            'https://videos.pexels.com/video-files/7722989/7722989-uhd_1440_2560_25fps.mp4',
          file_size: 376973,
          width: 1179,
          height: 2096,
        },
      ],
    },
    {
      id: ********,
      content: 'cwtestinglocal mentioned you in the story: ',
      inbox_id: 27355,
      conversation_id: 23731,
      message_type: 0,
      content_type: 'text',
      status: 'sent',
      content_attributes: {
        story_sender: 'cwtestinglocal',
        story_id: '*****************',
        image_type: 'story_mention',
      },
      created_at: **********,
      private: false,
      source_id:
        'aWdfZAG1faXRlbToxOklHTWVzc2FnZAUlEOjE3ODQxNDQ3NTU5OTYxMzk4OjM0MDI4MjM2Njg0MTcxMDMwMDk0OTEyODIyODExNDI0MDczNjkxMTozMTE5MjcwODk3OTU5MTM2NDg4NTg2OTU1NTEzOTczOTY0OAZDZD',
      sender: {
        additional_attributes: {
          social_profiles: {
            instagram: 'cwtestinglocal',
          },
        },
        custom_attributes: {},
        email: null,
        id: ********,
        identifier: null,
        name: 'Jane Doe',
        phone_number: null,
        thumbnail: '',
        type: 'contact',
      },
      attachments: [
        {
          id: 2053726,
          message_id: ********,
          file_type: 'image',
          account_id: 1,
          extension: null,
          data_url:
            'https://images.pexels.com/photos/2587370/pexels-photo-2587370.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
          thumb_url:
            'https://images.pexels.com/photos/2587370/pexels-photo-2587370.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
          file_size: 376973,
          width: 1179,
          height: 2096,
        },
      ],
    },
    {
      id: ********,
      content: 'cwtestinglocal mentioned you in the story: ',
      inbox_id: 27355,
      conversation_id: 23731,
      message_type: 0,
      content_type: 'text',
      status: 'sent',
      content_attributes: {
        story_sender: 'cwtestinglocal',
        story_id: '*****************',
        image_type: 'story_mention',
      },
      created_at: **********,
      private: false,
      source_id:
        'aWdfZAG1faXRlbToxOklHTWVzc2FnZAUlEOjE3ODQxNDQ3NTU5OTYxMzk4OjM0MDI4MjM2Njg0MTcxMDMwMDk0OTEyODIyODExNDI0MDczNjkxMTozMTE5MjcwODk3OTU5MTM2NDg4NTg2OTU1NTEzOTczOTY0OAZDZD',
      sender: {
        additional_attributes: {
          social_profiles: {
            instagram: 'cwtestinglocal',
          },
        },
        custom_attributes: {},
        email: null,
        id: ********,
        identifier: null,
        name: 'Jane Doe',
        phone_number: null,
        thumbnail: '',
        type: 'contact',
      },
      attachments: [
        {
          id: 2053726,
          message_id: ********,
          file_type: 'image',
          account_id: 1,
          extension: null,
          data_url: 'https://chatwoot.dev/definitely-broken-image',
          thumb_url: 'https://chatwoot.dev/definitely-broken-image',
          file_size: 376973,
          width: 1179,
          height: 2096,
        },
      ],
    },
    {
      id: ********,
      content: 'Hi, Jane here (testing)',
      inbox_id: 27355,
      conversation_id: 26942,
      message_type: 0,
      content_type: 'text',
      status: 'sent',
      content_attributes: {},
      created_at: **********,
      private: false,
      source_id:
        'aWdfZAG1faXRlbToxOklHTWVzc2FnZAUlEOjE3ODQxNDQ3NTU5OTYxMzk4OjM0MDI4MjM2Njg0MTcxMDMwMTI0NDI1OTM2Nzg4MDM2MjUyNjUwNjozMTIyNjAxOTk2NDIwNjc5NDQ5NzE1NDMxNzMzMTQ2NDE5MgZDZD',
      sender: {
        additional_attributes: {
          social_profiles: {
            instagram: 'cwtestinglocal',
          },
        },
        custom_attributes: {},
        email: null,
        id: ********,
        identifier: null,
        name: 'Jane Doe',
        phone_number: null,
        thumbnail: '',
        type: 'contact',
      },
    },
    {
      id: 26433214,
      content: 'Hey Jane, I am Jane',
      inbox_id: 27355,
      conversation_id: 26942,
      message_type: 1,
      content_type: 'text',
      status: 'sent',
      content_attributes: {},
      created_at: 1692765959,
      private: false,
      source_id:
        'aWdfZAG1faXRlbToxOklHTWVzc2FnZAUlEOjE3ODQxNDQ3NTU5OTYxMzk4OjM0MDI4MjM2Njg0MTcxMDMwMTI0NDI1OTM2Nzg4MDM2MjUyNjUwNjozMTIyNjAyMDQzOTQ5ODg4NzY4NTUxMTc5Mzk5MjUzMTk2OAZDZD',
      sender: {
        id: 1,
        name: 'Jane Doe',
        available_name: 'Jane Doe',
        avatar_url:
          'https://xsgames.co/randomusers/assets/avatars/pixel/22.jpg',
        type: 'user',
        availability_status: 'online',
        thumbnail: 'https://xsgames.co/randomusers/assets/avatars/pixel/22.jpg',
      },
    },
    {
      id: 26433277,
      content: 'Conversation was marked resolved by Jane Doe',
      inbox_id: 27355,
      conversation_id: 26942,
      message_type: 2,
      content_type: 'text',
      status: 'sent',
      content_attributes: {},
      created_at: 1692766051,
      private: false,
      source_id: null,
    },
    {
      id: 26434177,
      content: 'Hi, it’s me again',
      inbox_id: 27355,
      conversation_id: 26942,
      message_type: 0,
      content_type: 'text',
      status: 'sent',
      content_attributes: {},
      created_at: 1692767416,
      private: false,
      source_id:
        'aWdfZAG1faXRlbToxOklHTWVzc2FnZAUlEOjE3ODQxNDQ3NTU5OTYxMzk4OjM0MDI4MjM2Njg0MTcxMDMwMTI0NDI1OTM2Nzg4MDM2MjUyNjUwNjozMTIyNjA0NzI5NjY0ODY1MDg1Nzg1MDAxMDk4MTEwNTY2NAZDZD',
      sender: {
        additional_attributes: {
          social_profiles: {
            instagram: 'cwtestinglocal',
          },
        },
        custom_attributes: {},
        email: null,
        id: ********,
        identifier: null,
        name: 'Jane Doe',
        phone_number: null,
        thumbnail: '',
        type: 'contact',
      },
    },
    {
      id: 26434185,
      content: 'Hi',
      inbox_id: 27355,
      conversation_id: 26942,
      message_type: 0,
      content_type: 'text',
      status: 'sent',
      content_attributes: {},
      created_at: 1692767429,
      private: false,
      source_id:
        'aWdfZAG1faXRlbToxOklHTWVzc2FnZAUlEOjE3ODQxNDQ3NTU5OTYxMzk4OjM0MDI4MjM2Njg0MTcxMDMwMTI0NDI1OTM2Nzg4MDM2MjUyNjUwNjozMTIyNjA0NzUxNjQ1NDAzOTYwMTYwNDk5ODcyMzUzNDg0OAZDZD',
      sender: {
        additional_attributes: {
          social_profiles: {
            instagram: 'cwtestinglocal',
          },
        },
        custom_attributes: {},
        email: null,
        id: ********,
        identifier: null,
        name: 'Jane Doe',
        phone_number: null,
        thumbnail: '',
        type: 'contact',
      },
    },
    {
      id: 26434188,
      content: 'It',
      inbox_id: 27355,
      conversation_id: 26942,
      message_type: 0,
      content_type: 'text',
      status: 'sent',
      content_attributes: {},
      created_at: 1692767431,
      private: false,
      source_id:
        'aWdfZAG1faXRlbToxOklHTWVzc2FnZAUlEOjE3ODQxNDQ3NTU5OTYxMzk4OjM0MDI4MjM2Njg0MTcxMDMwMTI0NDI1OTM2Nzg4MDM2MjUyNjUwNjozMTIyNjA0NzUyODEzODkyNjcyMDkxMTUwMzM4OTYyMjI3MgZDZD',
      sender: {
        additional_attributes: {
          social_profiles: {
            instagram: 'cwtestinglocal',
          },
        },
        custom_attributes: {},
        email: null,
        id: ********,
        identifier: null,
        name: 'Jane Doe',
        phone_number: null,
        thumbnail: '',
        type: 'contact',
      },
    },
    {
      id: 26434190,
      content: 'Is',
      inbox_id: 27355,
      conversation_id: 26942,
      message_type: 0,
      content_type: 'text',
      status: 'sent',
      content_attributes: {},
      created_at: 1692767432,
      private: false,
      source_id:
        'aWdfZAG1faXRlbToxOklHTWVzc2FnZAUlEOjE3ODQxNDQ3NTU5OTYxMzk4OjM0MDI4MjM2Njg0MTcxMDMwMTI0NDI1OTM2Nzg4MDM2MjUyNjUwNjozMTIyNjA0NzU0MzY1ODQzMDc2NDU0MDM4MTA5Nzg4NTY5NgZDZD',
      sender: {
        additional_attributes: {
          social_profiles: {
            instagram: 'cwtestinglocal',
          },
        },
        custom_attributes: {},
        email: null,
        id: ********,
        identifier: null,
        name: 'Jane Doe',
        phone_number: null,
        thumbnail: '',
        type: 'contact',
      },
    },
    {
      id: 26434193,
      content: 'Me',
      inbox_id: 27355,
      conversation_id: 26942,
      message_type: 0,
      content_type: 'text',
      status: 'sent',
      content_attributes: {},
      created_at: 1692767434,
      private: false,
      source_id:
        'aWdfZAG1faXRlbToxOklHTWVzc2FnZAUlEOjE3ODQxNDQ3NTU5OTYxMzk4OjM0MDI4MjM2Njg0MTcxMDMwMTI0NDI1OTM2Nzg4MDM2MjUyNjUwNjozMTIyNjA0NzU1OTg2MjEyNDU0NTg2MzE0NjA3NTU4NjU2MAZDZD',
      sender: {
        additional_attributes: {
          social_profiles: {
            instagram: 'cwtestinglocal',
          },
        },
        custom_attributes: {},
        email: null,
        id: ********,
        identifier: null,
        name: 'Jane Doe',
        phone_number: null,
        thumbnail: '',
        type: 'contact',
      },
    },
    {
      id: 26434195,
      content: 'Again',
      inbox_id: 27355,
      conversation_id: 26942,
      message_type: 0,
      content_type: 'text',
      status: 'sent',
      content_attributes: {},
      created_at: 1692767435,
      private: false,
      source_id:
        'aWdfZAG1faXRlbToxOklHTWVzc2FnZAUlEOjE3ODQxNDQ3NTU5OTYxMzk4OjM0MDI4MjM2Njg0MTcxMDMwMTI0NDI1OTM2Nzg4MDM2MjUyNjUwNjozMTIyNjA0NzU3Nzk3MzE1NjMyNDE3NTI1NzU2MTcyNjk3NgZDZD',
      sender: {
        additional_attributes: {
          social_profiles: {
            instagram: 'cwtestinglocal',
          },
        },
        custom_attributes: {},
        email: null,
        id: ********,
        identifier: null,
        name: 'Jane Doe',
        phone_number: null,
        thumbnail: '',
        type: 'contact',
      },
    },
    {
      id: 26434914,
      content: 'Conversation was marked resolved by Jane Doe',
      inbox_id: 27355,
      conversation_id: 26942,
      message_type: 2,
      content_type: 'text',
      status: 'sent',
      content_attributes: {},
      created_at: 1692768629,
      private: false,
      source_id: null,
    },
    {
      id: 26436726,
      content: '😁',
      inbox_id: 27355,
      conversation_id: 26942,
      message_type: 1,
      content_type: 'text',
      status: 'sent',
      content_attributes: {},
      created_at: 1692770672,
      private: false,
      source_id:
        'aWdfZAG1faXRlbToxOklHTWVzc2FnZAUlEOjE3ODQxNDQ3NTU5OTYxMzk4OjM0MDI4MjM2Njg0MTcxMDMwMTI0NDI1OTM2Nzg4MDM2MjUyNjUwNjozMTIyNjEwNzM4NDA4NTgwMzUxNzY3OTYxNTM4MDI5MTU4NAZDZD',
      sender: {
        id: 10,
        name: 'Jane',
        available_name: 'Jane',
        avatar_url:
          'https://app.chatwoot.com/rails/active_storage/representations/redirect/eyJfcmFpbHMiOnsibWVzc2FnZSI6IkJBaHBDZz09IiwiZXhwIjpudWxsLCJwdXIiOiJibG9iX2lkIn19--4a2c0c8e787a381c0a809905e7cb172d5a40f589/eyJfcmFpbHMiOnsibWVzc2FnZSI6IkJBaDdCem9MWm05eWJXRjBTU0lKYW5CbFp3WTZCa1ZVT2hOeVpYTnBlbVZmZEc5ZlptbHNiRnNIYVFINk1BPT0iLCJleHAiOm51bGwsInB1ciI6InZhcmlhdGlvbiJ9fQ==--533c3ad7218e24c4b0e8f8959dc1953ce1d279b9/73185.jpeg',
        type: 'user',
        availability_status: 'offline',
        thumbnail:
          'https://app.chatwoot.com/rails/active_storage/representations/redirect/eyJfcmFpbHMiOnsibWVzc2FnZSI6IkJBaHBDZz09IiwiZXhwIjpudWxsLCJwdXIiOiJibG9iX2lkIn19--4a2c0c8e787a381c0a809905e7cb172d5a40f589/eyJfcmFpbHMiOnsibWVzc2FnZSI6IkJBaDdCem9MWm05eWJXRjBTU0lKYW5CbFp3WTZCa1ZVT2hOeVpYTnBlbVZmZEc5ZlptbHNiRnNIYVFINk1BPT0iLCJleHAiOm51bGwsInB1ciI6InZhcmlhdGlvbiJ9fQ==--533c3ad7218e24c4b0e8f8959dc1953ce1d279b9/73185.jpeg',
      },
    },
    {
      id: 27405598,
      content: 'Guess what? I am back!',
      inbox_id: 27355,
      conversation_id: 26942,
      message_type: 0,
      content_type: 'text',
      status: 'sent',
      content_attributes: {},
      created_at: 1694016247,
      private: false,
      source_id:
        'aWdfZAG1faXRlbToxOklHTWVzc2FnZAUlEOjE3ODQxNDQ3NTU5OTYxMzk4OjM0MDI4MjM2Njg0MTcxMDMwMTI0NDI1OTM2Nzg4MDM2MjUyNjUwNjozMTI0OTA4NDE2Mjk1NDQ4MTc3NzQyMDMxNjE3NjI4NTY5NgZDZD',
      sender: {
        additional_attributes: {
          social_profiles: {
            instagram: 'cwtestinglocal',
          },
        },
        custom_attributes: {},
        email: null,
        id: ********,
        identifier: null,
        name: 'Jane Doe',
        phone_number: null,
        thumbnail: '',
        type: 'contact',
      },
    },
    {
      id: 27405678,
      content: null,
      inbox_id: 27355,
      conversation_id: 26942,
      message_type: 0,
      content_type: 'text',
      status: 'sent',
      content_attributes: {},
      created_at: 1694016327,
      private: false,
      source_id:
        'aWdfZAG1faXRlbToxOklHTWVzc2FnZAUlEOjE3ODQxNDQ3NTU5OTYxMzk4OjM0MDI4MjM2Njg0MTcxMDMwMTI0NDI1OTM2Nzg4MDM2MjUyNjUwNjozMTI0OTA4NTYwNDk1OTA2MzI4MDc4NjI2MDE5OTczNTI5NgZDZD',
      sender: {
        additional_attributes: {
          social_profiles: {
            instagram: 'cwtestinglocal',
          },
        },
        custom_attributes: {},
        email: null,
        id: ********,
        identifier: null,
        name: 'Jane Doe',
        phone_number: null,
        thumbnail: '',
        type: 'contact',
      },
    },
    {
      id: 27405690,
      content: 'Some pictures from Thailand',
      inbox_id: 27355,
      conversation_id: 26942,
      message_type: 0,
      content_type: 'text',
      status: 'sent',
      content_attributes: {},
      created_at: 1694016341,
      private: false,
      source_id:
        'aWdfZAG1faXRlbToxOklHTWVzc2FnZAUlEOjE3ODQxNDQ3NTU5OTYxMzk4OjM0MDI4MjM2Njg0MTcxMDMwMTI0NDI1OTM2Nzg4MDM2MjUyNjUwNjozMTI0OTA4NTg3MTk4NzkzMjM4NTc5Njk2Mjk4MzYwODMyMAZDZD',
      sender: {
        additional_attributes: {
          social_profiles: {
            instagram: 'cwtestinglocal',
          },
        },
        custom_attributes: {},
        email: null,
        id: ********,
        identifier: null,
        name: 'Jane Doe',
        phone_number: null,
        thumbnail: '',
        type: 'contact',
      },
    },
    {
      id: 27406085,
      content: 'byeeee',
      inbox_id: 27355,
      conversation_id: 26942,
      message_type: 1,
      content_type: 'text',
      status: 'sent',
      content_attributes: {},
      created_at: 1694016698,
      private: false,
      source_id:
        'aWdfZAG1faXRlbToxOklHTWVzc2FnZAUlEOjE3ODQxNDQ3NTU5OTYxMzk4OjM0MDI4MjM2Njg0MTcxMDMwMTI0NDI1OTM2Nzg4MDM2MjUyNjUwNjozMTI0OTA5MjUxODA3NzE2MDgxOTE5NDkwOTM0NTk3MjIyNAZDZD',
      sender: {
        id: 1,
        name: 'Jane Doe',
        available_name: 'Jane Doe',
        avatar_url:
          'https://xsgames.co/randomusers/assets/avatars/pixel/22.jpg',
        type: 'user',
        availability_status: 'online',
        thumbnail: 'https://xsgames.co/randomusers/assets/avatars/pixel/22.jpg',
      },
    },
    {
      id: 27406088,
      content: 'Conversation was marked resolved by Jane Doe',
      inbox_id: 27355,
      conversation_id: 26942,
      message_type: 2,
      content_type: 'text',
      status: 'sent',
      content_attributes: {},
      created_at: 1694016701,
      private: false,
      source_id: null,
    },
    {
      id: ********,
      content: null,
      inbox_id: 27355,
      conversation_id: 26942,
      message_type: 0,
      content_type: 'text',
      status: 'sent',
      content_attributes: {},
      created_at: 1694017101,
      private: false,
      source_id:
        'aWdfZAG1faXRlbToxOklHTWVzc2FnZAUlEOjE3ODQxNDQ3NTU5OTYxMzk4OjM0MDI4MjM2Njg0MTcxMDMwMTI0NDI1OTM2Nzg4MDM2MjUyNjUwNjozMTI0OTA5OTkxNDU3Mzc0MDA2NTA4ODY0OTAzNzg3MzE1MgZDZD',
      sender: {
        additional_attributes: {
          social_profiles: {
            instagram: 'cwtestinglocal',
          },
        },
        custom_attributes: {},
        email: null,
        id: ********,
        identifier: null,
        name: 'Jane Doe',
        phone_number: null,
        thumbnail: '',
        type: 'contact',
      },
      attachments: [
        {
          id: 2201627,
          message_id: ********,
          file_type: 'image',
          account_id: 1,
          extension: null,
          data_url: 'https://random.imagecdn.app/500/500',
          thumb_url: 'https://random.imagecdn.app/500/500',
          file_size: 141072,
          width: 1170,
          height: 1560,
        },
      ],
    },
    {
      id: ********,
      content: 'Conversation was marked resolved by Jane Doe',
      inbox_id: 27355,
      conversation_id: 26942,
      message_type: 2,
      content_type: 'text',
      status: 'sent',
      content_attributes: {},
      created_at: **********,
      private: false,
      source_id: null,
    },
    {
      id: ********,
      content: 'Me again',
      inbox_id: 27355,
      conversation_id: 26942,
      message_type: 0,
      content_type: 'text',
      status: 'sent',
      content_attributes: {},
      created_at: **********,
      private: false,
      source_id:
        'aWdfZAG1faXRlbToxOklHTWVzc2FnZAUlEOjE3ODQxNDQ3NTU5OTYxMzk4OjM0MDI4MjM2Njg0MTcxMDMwMTI0NDI1OTM2Nzg4MDM2MjUyNjUwNjozMTI1ODM2Njg4MTgxMDMyNzQxODM4OTc5NjEzMDY1MjE2MAZDZD',
      sender: {
        additional_attributes: {
          social_profiles: {
            instagram: 'cwtestinglocal',
          },
        },
        custom_attributes: {},
        email: null,
        id: ********,
        identifier: null,
        name: 'Jane Doe',
        phone_number: null,
        thumbnail: '',
        type: 'contact',
      },
    },
    {
      id: 27788409,
      content: "Yeah, it's you again",
      inbox_id: 27355,
      conversation_id: 26942,
      message_type: 1,
      content_type: 'text',
      status: 'sent',
      content_attributes: {},
      created_at: 1694519479,
      private: false,
      source_id:
        'aWdfZAG1faXRlbToxOklHTWVzc2FnZAUlEOjE3ODQxNDQ3NTU5OTYxMzk4OjM0MDI4MjM2Njg0MTcxMDMwMTI0NDI1OTM2Nzg4MDM2MjUyNjUwNjozMTI1ODM2NzE5MzQ2NTQxMjIxMjU2NjA1NjUwNzUzOTQ1NgZDZD',
      sender: {
        id: 1,
        name: 'Jane Doe',
        available_name: 'Jane Doe',
        avatar_url:
          'https://xsgames.co/randomusers/assets/avatars/pixel/22.jpg',
        type: 'user',
        availability_status: 'online',
        thumbnail: 'https://xsgames.co/randomusers/assets/avatars/pixel/22.jpg',
      },
    },
    {
      id: ********,
      content: null,
      inbox_id: 27355,
      conversation_id: 26942,
      message_type: 0,
      content_type: 'text',
      status: 'sent',
      content_attributes: {},
      created_at: 1694519516,
      private: false,
      source_id:
        'aWdfZAG1faXRlbToxOklHTWVzc2FnZAUlEOjE3ODQxNDQ3NTU5OTYxMzk4OjM0MDI4MjM2Njg0MTcxMDMwMTI0NDI1OTM2Nzg4MDM2MjUyNjUwNjozMTI1ODM2NzgxMjMzMjI3NzY2MjM5NjAxMDE0NDQ2NDg5NgZDZD',
      sender: {
        additional_attributes: {
          social_profiles: {
            instagram: 'cwtestinglocal',
          },
        },
        custom_attributes: {},
        email: null,
        id: ********,
        identifier: null,
        name: 'Jane Doe',
        phone_number: null,
        thumbnail: '',
        type: 'contact',
      },
      attachments: [
        {
          id: 2224875,
          message_id: ********,
          file_type: 'image',
          account_id: 1,
          extension: null,
          data_url: 'https://random.imagecdn.app/500/500',
          thumb_url: 'https://random.imagecdn.app/500/500',
          file_size: 236151,
          width: 1170,
          height: 1560,
        },
      ],
    },
    {
      id: ********,
      content: 'Try this',
      inbox_id: 27355,
      conversation_id: 26942,
      message_type: 1,
      content_type: 'text',
      status: 'sent',
      content_attributes: {},
      created_at: **********,
      private: false,
      source_id:
        'aWdfZAG1faXRlbToxOklHTWVzc2FnZAUlEOjE3ODQxNDQ3NTU5OTYxMzk4OjM0MDI4MjM2Njg0MTcxMDMwMTI0NDI1OTM2Nzg4MDM2MjUyNjUwNjozMTI1ODM2ODQ3ODA4Mzg0OTAxODU2ODAwMjkwNzQwNjMzNgZDZD',
      sender: {
        id: 1,
        name: 'Jane Doe',
        available_name: 'Jane Doe',
        avatar_url:
          'https://xsgames.co/randomusers/assets/avatars/pixel/22.jpg',
        type: 'user',
        availability_status: 'online',
        thumbnail: 'https://xsgames.co/randomusers/assets/avatars/pixel/22.jpg',
      },
      attachments: [
        {
          id: 2224876,
          message_id: ********,
          file_type: 'image',
          account_id: 1,
          extension: null,
          data_url:
            'https://app.chatwoot.com/rails/active_storage/blobs/redirect/eyJfcmFpbHMiOnsibWVzc2FnZSI6IkJBaHBBL2lvYnc9PSIsImV4cCI6bnVsbCwicHVyIjoiYmxvYl9pZCJ9fQ==--c7278d793ca8de35ea7a824cfbb82ffb630b410a/facebook.png',
          thumb_url:
            'https://app.chatwoot.com/rails/active_storage/representations/redirect/eyJfcmFpbHMiOnsibWVzc2FnZSI6IkJBaHBBL2lvYnc9PSIsImV4cCI6bnVsbCwicHVyIjoiYmxvYl9pZCJ9fQ==--c7278d793ca8de35ea7a824cfbb82ffb630b410a/eyJfcmFpbHMiOnsibWVzc2FnZSI6IkJBaDdCem9MWm05eWJXRjBTU0lJY0c1bkJqb0dSVlE2RTNKbGMybDZaVjkwYjE5bWFXeHNXd2RwQWZvdyIsImV4cCI6bnVsbCwicHVyIjoidmFyaWF0aW9uIn19--624b3ceb3fdf42c4b07c7818563fe60603b6095b/facebook.png',
          file_size: 256,
          width: 24,
          height: 24,
        },
      ],
    },
    {
      id: ********,
      content: null,
      inbox_id: 27355,
      conversation_id: 26942,
      message_type: 1,
      content_type: 'text',
      status: 'sent',
      content_attributes: {},
      created_at: **********,
      private: false,
      source_id:
        'aWdfZAG1faXRlbToxOklHTWVzc2FnZAUlEOjE3ODQxNDQ3NTU5OTYxMzk4OjM0MDI4MjM2Njg0MTcxMDMwMTI0NDI1OTM2Nzg4MDM2MjUyNjUwNjozMTI1ODM2ODQ2ODAxNjAwMzQ4NDc2Mjg3NzUzNTk3NzQ3MgZDZD',
      attachments: [
        {
          id: 2224877,
          message_id: ********,
          file_type: 'image',
          account_id: 1,
          extension: null,
          data_url:
            'https://app.chatwoot.com/rails/active_storage/blobs/redirect/eyJfcmFpbHMiOnsibWVzc2FnZSI6IkJBaHBBL21vYnc9PSIsImV4cCI6bnVsbCwicHVyIjoiYmxvYl9pZCJ9fQ==--1ca7bc279638b97f4eec6eb86452d5a982a84c04/image-****************',
          thumb_url:
            'https://app.chatwoot.com/rails/active_storage/representations/redirect/eyJfcmFpbHMiOnsibWVzc2FnZSI6IkJBaHBBL21vYnc9PSIsImV4cCI6bnVsbCwicHVyIjoiYmxvYl9pZCJ9fQ==--1ca7bc279638b97f4eec6eb86452d5a982a84c04/eyJfcmFpbHMiOnsibWVzc2FnZSI6IkJBaDdCem9MWm05eWJXRjBTU0lJY0c1bkJqb0dSVlE2RTNKbGMybDZaVjkwYjE5bWFXeHNXd2RwQWZvdyIsImV4cCI6bnVsbCwicHVyIjoidmFyaWF0aW9uIn19--624b3ceb3fdf42c4b07c7818563fe60603b6095b/image-****************',
          file_size: 209,
          width: 24,
          height: 24,
        },
      ],
    },
    {
      id: ********,
      content: 'Assigned to Jane Doe by Pranav Raj',
      inbox_id: 27355,
      conversation_id: 26942,
      message_type: 2,
      content_type: 'text',
      status: 'sent',
      content_attributes: {},
      created_at: **********,
      private: false,
      source_id: null,
    },
    {
      id: ********,
      content: 'Conversation was marked resolved by Jane Doe',
      account_id: 1,
      inbox_id: 27355,
      conversation_id: 26942,
      message_type: 2,
      created_at: **********,
      updated_at: '2023-09-19T07:42:06.172Z',
      private: false,
      status: 'sent',
      source_id: null,
      content_type: 'text',
      content_attributes: {},
      sender_type: null,
      sender_id: null,
      external_source_ids: {
        slack: 'cw-origin-**********.110599',
      },
      additional_attributes: {},
      processed_message_content: 'Conversation was marked resolved by Jane Doe',
      sentiment: {},
      conversation: {
        assignee_id: 1,
        unread_count: 0,
        last_activity_at: **********,
        contact_inbox: {
          source_id: '****************',
        },
      },
    },
    {
      message_type: 0,
      content_type: 'text',
      processed_message_content: 'Hi',
      id: ********,
      content: 'Hi',
      account_id: 1,
      inbox_id: 27355,
      conversation_id: 26942,
      created_at: **********,
      updated_at: '2024-12-05T08:34:42.693Z',
      private: false,
      status: 'sent',
      source_id:
        'aWdfZAG1faXRlbToxOklHTWVzc2FnZAUlEOjE3ODQxNDQ3NTU5OTYxMzk4OjM0MDI4MjM2Njg0MTcxMDMwMTI0NDI1OTM2Nzg4MDM2MjUyNjUwNjozMTk3NTM1ODkwMDgwNTE5MTc3ODA4Nzg2Njg0Mzc5MTM2MAZDZD',
      content_attributes: {
        in_reply_to_external_id: null,
      },
      sender_type: 'Contact',
      sender_id: ********,
      external_source_ids: {
        slack: 'cw-origin-**********.631519',
      },
      additional_attributes: {},
      sentiment: {},
      conversation: {
        assignee_id: 1,
        unread_count: 0,
        last_activity_at: **********,
        contact_inbox: {
          source_id: '****************',
        },
      },
      sender: {
        additional_attributes: {
          social_profiles: {
            instagram: 'cwtestinglocal',
          },
        },
        custom_attributes: {},
        email: null,
        id: ********,
        identifier: null,
        name: 'Jane Doe',
        phone_number: null,
        thumbnail: '',
        type: 'contact',
      },
      previous_changes: {
        updated_at: ['2024-12-05T08:34:41.463Z', '2024-12-05T08:34:42.693Z'],
        external_source_ids: [
          {},
          {
            slack: 'cw-origin-**********.631519',
          },
        ],
      },
    },
    {
      message_type: 1,
      content_type: 'text',
      processed_message_content: 'Just testing here, ignore',
      id: ********,
      content: 'Just testing here, ignore',
      account_id: 1,
      inbox_id: 27355,
      conversation_id: 26942,
      created_at: **********,
      updated_at: '2024-12-05T08:35:32.207Z',
      private: true,
      status: 'sent',
      source_id: null,
      content_attributes: {},
      sender_type: 'User',
      sender_id: 1,
      external_source_ids: {
        slack: 'cw-origin-**********.139069',
      },
      additional_attributes: {},
      sentiment: {},
      conversation: {
        assignee_id: 1,
        unread_count: 0,
        last_activity_at: **********,
        contact_inbox: {
          source_id: '****************',
        },
      },
      sender: {
        id: 1,
        name: 'Jane Doe',
        available_name: 'Jane Doe',
        avatar_url:
          'https://xsgames.co/randomusers/assets/avatars/pixel/22.jpg',
        type: 'user',
        availability_status: null,
        thumbnail: 'https://xsgames.co/randomusers/assets/avatars/pixel/22.jpg',
      },
      previous_changes: {
        updated_at: ['2024-12-05T08:35:31.809Z', '2024-12-05T08:35:32.207Z'],
        external_source_ids: [
          {},
          {
            slack: 'cw-origin-**********.139069',
          },
        ],
      },
    },
    {
      id: ********,
      content: null,
      account_id: 1,
      inbox_id: 27355,
      conversation_id: 26942,
      message_type: 0,
      created_at: **********,
      updated_at: '2024-12-05T08:35:40.796Z',
      private: false,
      status: 'sent',
      source_id:
        'aWdfZAG1faXRlbToxOklHTWVzc2FnZAUlEOjE3ODQxNDQ3NTU5OTYxMzk4OjM0MDI4MjM2Njg0MTcxMDMwMTI0NDI1OTM2Nzg4MDM2MjUyNjUwNjozMTk3NTM2MDAyMjg0NTc1ODQ0NzY0NDcwNDU3NTA2MjAxNgZDZD',
      content_type: 'text',
      content_attributes: {
        in_reply_to_external_id: null,
      },
      sender_type: 'Contact',
      sender_id: ********,
      external_source_ids: {},
      additional_attributes: {},
      processed_message_content: null,
      sentiment: {},
      conversation: {
        assignee_id: 1,
        unread_count: 1,
        last_activity_at: **********,
        contact_inbox: {
          source_id: '****************',
        },
      },
      attachments: [
        {
          id: 7141485,
          message_id: ********,
          file_type: 'image',
          account_id: 1,
          extension: null,
          data_url: 'https://random.imagecdn.app/500/500',
          thumb_url: 'https://random.imagecdn.app/500/500',
          file_size: 63957,
          width: null,
          height: null,
        },
        {
          id: 7141486,
          message_id: ********,
          file_type: 'image',
          account_id: 1,
          extension: null,
          data_url: 'https://random.imagecdn.app/500/500',
          thumb_url: 'https://random.imagecdn.app/500/500',
          file_size: 43486,
          width: null,
          height: null,
        },
      ],
      sender: {
        additional_attributes: {
          social_profiles: {
            instagram: 'cwtestinglocal',
          },
        },
        custom_attributes: {},
        email: null,
        id: ********,
        identifier: null,
        name: 'Jane Doe',
        phone_number: null,
        thumbnail: '',
        type: 'contact',
      },
    },
    {
      id: ********,
      content: null,
      account_id: 1,
      inbox_id: 27355,
      conversation_id: 26942,
      message_type: 0,
      created_at: **********,
      updated_at: '2024-12-05T08:36:43.200Z',
      private: false,
      status: 'sent',
      source_id:
        'aWdfZAG1faXRlbToxOklHTWVzc2FnZAUlEOjE3ODQxNDQ3NTU5OTYxMzk4OjM0MDI4MjM2Njg0MTcxMDMwMTI0NDI1OTM2Nzg4MDM2MjUyNjUwNjozMTk3NTM2MTE2ODI4MTc0MDMzMDA3NzAwNzkxNTI1Mzc2MAZDZD',
      content_type: 'text',
      content_attributes: {
        in_reply_to_external_id: null,
      },
      sender_type: 'Contact',
      sender_id: ********,
      external_source_ids: {},
      additional_attributes: {},
      processed_message_content: null,
      sentiment: {},
      conversation: {
        assignee_id: 1,
        unread_count: 1,
        last_activity_at: **********,
        contact_inbox: {
          source_id: '****************',
        },
      },
      attachments: [
        {
          id: 7141507,
          message_id: ********,
          file_type: 'ig_reel',
          account_id: 1,
          extension: null,
          data_url:
            'https://videos.pexels.com/video-files/2023708/2023708-hd_720_1280_30fps.mp4',
          thumb_url:
            'https://videos.pexels.com/video-files/2023708/2023708-hd_720_1280_30fps.mp4',
          file_size: 2464867,
          width: null,
          height: null,
        },
      ],
      sender: {
        additional_attributes: {
          social_profiles: {
            instagram: 'cwtestinglocal',
          },
        },
        custom_attributes: {},
        email: null,
        id: ********,
        identifier: null,
        name: 'Jane Doe',
        phone_number: null,
        thumbnail: '',
        type: 'contact',
      },
    },
    {
      id: ********,
      content: null,
      inbox_id: 27355,
      conversation_id: 26942,
      message_type: 0,
      content_type: 'text',
      status: 'sent',
      content_attributes: {
        in_reply_to_external_id: null,
        is_unsupported: true,
      },
      created_at: 1733399171,
      private: false,
      source_id:
        'aWdfZAG1faXRlbToxOklHTWVzc2FnZAUlEOjE3ODQxNDQ3NTU5OTYxMzk4OjM0MDI4MjM2Njg0MTcxMDMwMTI0NDI1OTM2Nzg4MDM2MjUyNjUwNjozMTk3NTU3MDg2NDAzNzQ0ODY5NjA0NDc2MzA5MjA5MDg4MAZDZD',
      sender: {
        additional_attributes: {
          social_profiles: {
            instagram: 'cwtestinglocal',
          },
        },
        custom_attributes: {},
        email: null,
        id: ********,
        identifier: null,
        name: 'Jane Doe',
        phone_number: null,
        thumbnail: '',
        type: 'contact',
      },
    },
  ],
  { deep: true }
);
