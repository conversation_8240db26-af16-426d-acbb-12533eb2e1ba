import camelcaseKeys from 'camelcase-keys';

export default camelcaseKeys(
  [
    {
      id: 60703,
      content:
        'News, tips, and behind the scenes technical mumbo jumbo\n\n ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏\n ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏\n ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­\n­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­\n­ ­ ­ ­ ­ ­ ­ ­ ­ ­  \n\nHello! Here we are again, ready to share some scintillating updates from Cloud Provider Land. All the real humans behind this newsletter hope you\'re doing well, and ask that you holler if we can help with something. Onward!\n\nProduct updates\n---------------\n\nYou can always check out the Fresh Produce ( https://community.fly.io/c/fresh-produce/27?utm_campaign=November+%2724+Newsletter&utm_content=Email&utm_medium=email_action&utm_source=customer.io ) page to see what we\'ve been fiddling with lately. Here are some of the highlights since we last checked in:\n\n* You can auto-deploy your apps ( https://community.fly.io/t/auto-deploy-on-push-to-github/22306?utm_campaign=November+%2724+Newsletter&utm_content=Email&utm_medium=email_action&utm_source=customer.io ) on Fly.io when you push your code to GitHub, and see the checks and deployments ( https://community.fly.io/t/github-checks-and-deployments/22669?utm_campaign=November+%2724+Newsletter&utm_content=Email&utm_medium=email_action&utm_source=customer.io ) on the Github side.\n* If you want to update machine size ( https://community.fly.io/t/update-machine-size-from-the-dashboard/22600?utm_campaign=November+%2724+Newsletter&utm_content=Email&utm_medium=email_action&utm_source=customer.io ), update the size of your builder ( https://community.fly.io/t/you-can-now-resize-your-depot-builders/22578?utm_campaign=November+%2724+Newsletter&utm_content=Email&utm_medium=email_action&utm_source=customer.io ), or clone machines ( https://community.fly.io/t/clone-machines-from-the-web-ui/22542?utm_campaign=November+%2724+Newsletter&utm_content=Email&utm_medium=email_action&utm_source=customer.io ), you can do it all from the dashboard!\n\nInside Flyball\n--------------\n\nA big internal focus right now is making our Support as…supportive (sorry) as it can possibly be. So, we\'ve made some improvements to the Support portal ( https://community.fly.io/t/redesigned-support-portal/22534?utm_campaign=November+%2724+Newsletter&utm_content=Email&utm_medium=email_action&utm_source=customer.io ) that are available to anyone with paid support. We\'ve also just shipped "impact levels ( https://community.fly.io/t/support-impact-levels/22538?utm_campaign=November+%2724+Newsletter&utm_content=Email&utm_medium=email_action&utm_source=customer.io )" on Support tickets, so you can indicate the urgency and severity of your support request or escalate an existing ticket. As always, we\'re curious to know what you think, so hit us with your feedback.\n\nFeature story: why we got rid of plans\n--------------------------------------\n\nWhen you\'re looking for a platform to host your app, one of the first questions you\'ll ask is: "how much is this gonna cost me?" Oh, how we wish we could boil things down to one number to rule them all, but Legal refuses to let us put a "42" on the pricing page and leave it at that.\n\nUsers are doing all kinds of things on Fly.io—like sorting through the US Patent system ( https://fly.io/customers/amplified?utm_campaign=November+%2724+Newsletter&utm_content=Email&utm_medium=email_action&utm_source=customer.io ) and helping teenagers in New Zealand learn outside of school ( https://fly.io/customers/mymahi?utm_campaign=November+%2724+Newsletter&utm_content=Email&utm_medium=email_action&utm_source=customer.io )—and therefore using wildly varying kinds and amounts of resources. That means the (infuriating) answer to cost questions is always: "it depends." Plus, there are a few different ways to structure pricing from the company side: you can either group certain resources together based on what you think different groups of customers might want (like a prix fix menu), or you can price each individual resource and let folks order á la carte.\n\nIn the beginning, there was usage based pricing. Fly.io actually launched on the "á la carte" side of things, charging just for usage. At that point, we were just inflating Frankie the hot air balloon, and Support did not yet exist. Users told us they wanted Support, so we said "You bet!" But instead of just building paid Support options, we thought about other elements that users would want (like BAAs, SOC2 reports, etc) and packaged it all together.\n\nWe ended up with plans we called Hobby, Launch, Scale, and Enterprise. Each one had some built-in "allowances" (free resources), some had Support, and all of them were anchored to different types of users. For example, the Scale plan was for users who needed to run multi-region, high-availability workloads, had specific compliance requirements (like needing to be in line with HIPAA), and wanted priority email support.\n\nThis worked for a while. But over time, we started getting feedback from prospective users (and comments on Reddit, we won\'t lie) about how our pricing felt unclear. Folks were thinking about using us, but couldn\'t get a sense for how much it would cost.\n\nWe also realized that the groupings we came up with might not actually make sense for some of Fly.io\'s users. For example: some folks might want ultra-available Support, but not care at all about compliance. And once we started digging into it, we started asking: why build a company around primitives that are really simple and swappable, and then make a payment structure that requires picking Option A, B, or C? "There must be consistency!" we said. (We were getting pretty fired up at this point.)\n\nSo, we got rid of Plans. Instead of grouping resources together, we\'ve gone pure "pay as you go." We want you to be able to take a glance at our compute costs, do some back of the envelope math, and rest easy that you know what you\'re in for. You don\'t even have to do the math! We made this calculator ( https://fly.io/calculator?utm_campaign=November+%2724+Newsletter&utm_content=Email&utm_medium=email_action&utm_source=customer.io ) so you can just plug in what you need and have it spit out an approximate total.\n\nIn this brave new world, your invoice will be:\n\n* whatever compute/product resources you use, pro-rated for the time you use them, plus\n* whichever add-ons you pick, like Support and/or Compliance\n\nTo be super clear: this new á la carte set-up is just for new accounts. Anyone who was already on a plan still has it, no sweat.\n\nOur aim in getting rid of plans here was to make pricing simpler. Some folks have pointed out ( https://community.fly.io/t/were-making-pricing-simpler/22168?utm_campaign=November+%2724+Newsletter&utm_content=Email&utm_medium=email_action&utm_source=customer.io ) that separating out Support and Compliance as "add-ons" means that after this change (which went live October 7th) people who sign up for new accounts could end up paying more than their historical counterparts, depending on what they choose. For example, our previous "Launch" plan was $29/month, and it came with Standard Support and some free compute allowances. Today, Standard Support is $29/month by itself.\n\nGiven that, was this the right call, on balance? We think so, since we figure:\n\n* Users who are already on a plan with us probably understand our pricing well enough, so we haven\'t touched anything about their set-up.\n* For prospective users, being able to easily and quickly answer the "how much is it gonna cost me" question is crucial.\n* With this new set-up, we\'re able to price each element at what it actually costs us to provide it to you, plus the margin that allows us to make this a viable business.\n\nUnsubscribe ( http://track.customer.io/unsubscribe/= ) ・\nManage Subscriptions ( http://track.customer.io/manage_subscription_preferences/= ) ・\nView in Browser ( http://track.customer.io/deliveries/= )',
      account_id: 51,
      inbox_id: 991,
      conversation_id: 33,
      message_type: 0,
      created_at: **********,
      updated_at: '2024-12-02T12:03:42.467Z',
      private: false,
      status: 'sent',
      source_id: '<EMAIL>',
      content_type: 'incoming_email',
      content_attributes: {
        email: {
          bcc: null,
          cc: null,
          content_type:
            'multipart/alternative; boundary=b57cedbb0790b6a0fe63723dd18c9d5416068481beced4287232b787e1bb',
          date: '2024-11-30T21:58:47+00:00',
          from: ['<EMAIL>'],
          html_content: {
            full: '<!DOCTYPE html><!-- Change the lang and dir attributes to match the language of your email and the direction of that language. --><html lang="und" dir="auto" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office" style="color-scheme: light dark; supported-color-schemes: light dark;"><head>\r\n    <meta charset="utf-8"/>\r\n    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>\r\n    <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=yes"/>\r\n    <meta name="format-detection" content="telephone=no, date=no, address=no, email=no, url=no"/>\r\n    <meta name="x-apple-disable-message-reformatting"/>\r\n    <meta name="color-scheme" content="light dark"/>\r\n    <meta name="supported-color-schemes" content="light dark"/>\r\n\r\n    <title>Fly.io November &#39;24 Newsletter</title>\r\n\r\n    <link rel="preconnect" href="https://fonts.googleapis.com" media="screen"/>\r\n    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin="" media="screen"/>\r\n    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@100..900&amp;family=Ovo&amp;display=swap" rel="stylesheet" media="screen"/>\r\n\r\n    <!--[if mso]>\r\n    <noscript>\r\n      <xml>\r\n        <o:OfficeDocumentSettings>\r\n          <o:PixelsPerInch>96</o:PixelsPerInch>\r\n        </o:OfficeDocumentSettings>\r\n      </xml>\r\n    </noscript>\r\n    <![endif]-->\r\n    \r\n    <style type="text/css">\r\na:hover {\r\n  text-decoration: none;\r\n}\r\n</style>\r\n    <style type="text/css" data-name="media">\r\n@media screen and (max-width:600px) {\r\n  h1 {\r\n    font-size: 2.25em;\r\n  }\r\n}\r\n@media (prefers-color-scheme: dark) {\r\n  body {\r\n    background-color: #262524 !important;\r\n  }\r\n\r\n  .container {\r\n    background-color: #262524 !important;\r\n    color: #EDEEEF !important;\r\n  }\r\n\r\n  a {\r\n    color: #7EABCE !important;\r\n  }\r\n\r\n  code {\r\n    background-color: #4c4a47 !important;\r\n  }\r\n}\r\n</style>\r\n    <style type="text/css" data-name="interactive">\r\ninput:checked + .tabs .tab:hover {\r\n  background-color: #1185d7;\r\n}\r\ninput:focus-visible + .tab {\r\n  outline: 2px solid #10618B;\r\n}\r\ninput:checked + .tabs .interactive-heading {\r\n  display: inline-block !important;\r\n}\r\n</style>\r\n  </head>\r\n  <body class="body" style="margin: 0; padding: 0; word-spacing: normal;"><div style="display: none; max-height: 0px; overflow: hidden; mso-hide:all;">Episode #5: why we got rid of plans ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­  </div>\r\n    \r\n    <div role="article" aria-label="Fly.io Newsletter - June 2024" lang="und" dir="auto" class="wrapper" style="-webkit-font-smoothing: antialiased; -moz-osx-font-smoothing: grayscale; font-feature-settings: &#39;cv02&#39;, &#39;cv03&#39;, &#39;cv04&#39;, &#39;cv11&#39;; line-height: 1.5; -webkit-text-size-adjust: 100%; font-weight: 375; letter-spacing: -.005em; font-size: medium; font-size: max(16px, 1rem); font-family: &#39;Inter&#39;, ui-sans-serif, system-ui, sans-serif, &#39;Apple Color Emoji&#39;, &#39;Segoe UI Emoji&#39;, &#39;Segoe UI Symbol&#39;, &#39;Noto Color Emoji&#39; !important;">\r\n      <!-- Change the preheader text to something you want to be seen at in the inbox -->\r\n      <div style="display: none;">News, tips, and behind the scenes technical mumbo jumbo\r\n      <!-- the below code adds spacing after the preheader text -->\r\n       ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏\r\n ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏\r\n ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­\r\n­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­\r\n­ ­ ­ ­ ­ ­ ­ ­ ­ ­  \r\n      </div>\r\n      <!--[if true]>\r\n      <table role="presentation" width="100%" align="center" cellpadding="16" border="0" style="background:#4f4f4f;">\r\n        <tr><td></td>\r\n          <td style="width:37.5em;background:#fffffe">\r\n      <![endif]-->\r\n      <div style="max-width:37.5em; margin:0 auto;background:#fffffe; padding:2em;overflow: auto;" class="container">\r\n        <img style="display: block; margin: auto; width: 8em;" src="https://userimg-assets.customeriomail.com/images/client-env-161421/1718249384403_logo-landscape-cookies2_01J07T9TJ2YJ616YPND9M0M3P8.png"/>\r\n        \r\n        <br/>\r\n<p>Hello! Here we are again, ready to share some scintillating updates from Cloud Provider Land. All the real humans behind this newsletter hope you&#39;re doing well, and ask that you holler if we can help with something. Onward!</p>\r\n\r\n<h3 style="margin: 1em 0; font-size: 1.6em; font-weight: 400; font-style: normal; line-height: 1.2; letter-spacing: -0.015em; word-spacing: 0.015em; font-family: &#39;Ovo&#39;, serif !important;">Product updates</h3>\r\n\r\n<p>You can always check out the <a href="https://go.news.fly.io/e/c/eyJlbWFpbF9pZCI6ImRnU043UWtEQVBiTEdQWExHQUdUZnhWZW45eHZGUWRwOEdDNjgyQT0iLCJocmVmIjoiaHR0cHM6Ly9jb21tdW5pdHkuZmx5LmlvL2MvZnJlc2gtcHJvZHVjZS8yNz91dG1fY2FtcGFpZ249Tm92ZW1iZXIrJTI3MjQrTmV3c2xldHRlclx1MDAyNnV0bV9jb250ZW50PUVtYWlsXHUwMDI2dXRtX21lZGl1bT1lbWFpbF9hY3Rpb25cdTAwMjZ1dG1fc291cmNlPWN1c3RvbWVyLmlvIiwiaW50ZXJuYWwiOiI4ZGVkMDkwZDg1NjVmNmNiMTgiLCJsaW5rX2lkIjo2MTN9/c370aeeb5186be56bff342368bba9168095da891eddca9e3a5f4bdb9e771d03f">Fresh Produce</a> page to see what we&#39;ve been fiddling with lately. Here are some of the highlights since we last checked in:</p>\r\n\r\n<ul>\r\n  <li>You can <a href="https://go.news.fly.io/e/c/eyJlbWFpbF9pZCI6ImRnU043UWtEQVBiTEdQWExHQUdUZnhWZW45eHZGUWRwOEdDNjgyQT0iLCJocmVmIjoiaHR0cHM6Ly9jb21tdW5pdHkuZmx5LmlvL3QvYXV0by1kZXBsb3ktb24tcHVzaC10by1naXRodWIvMjIzMDY_dXRtX2NhbXBhaWduPU5vdmVtYmVyKyUyNzI0K05ld3NsZXR0ZXJcdTAwMjZ1dG1fY29udGVudD1FbWFpbFx1MDAyNnV0bV9tZWRpdW09ZW1haWxfYWN0aW9uXHUwMDI2dXRtX3NvdXJjZT1jdXN0b21lci5pbyIsImludGVybmFsIjoiOGRlZDA5MGQ4NTY1ZjZjYjE4IiwibGlua19pZCI6NDYxMH0/164b95c7adefbeb39a47c3ec466a0721fe20b834cc9cdfd634cdeb64f5e1e3c6">auto-deploy your apps</a> on Fly.io when you push your code to GitHub, and see the <a href="https://go.news.fly.io/e/c/eyJlbWFpbF9pZCI6ImRnU043UWtEQVBiTEdQWExHQUdUZnhWZW45eHZGUWRwOEdDNjgyQT0iLCJocmVmIjoiaHR0cHM6Ly9jb21tdW5pdHkuZmx5LmlvL3QvZ2l0aHViLWNoZWNrcy1hbmQtZGVwbG95bWVudHMvMjI2Njk_dXRtX2NhbXBhaWduPU5vdmVtYmVyKyUyNzI0K05ld3NsZXR0ZXJcdTAwMjZ1dG1fY29udGVudD1FbWFpbFx1MDAyNnV0bV9tZWRpdW09ZW1haWxfYWN0aW9uXHUwMDI2dXRtX3NvdXJjZT1jdXN0b21lci5pbyIsImludGVybmFsIjoiOGRlZDA5MGQ4NTY1ZjZjYjE4IiwibGlua19pZCI6NDYxMX0/8a09b38b182b26b413b655ff54755f73efac8f2ede1627f266acbc20e54efd39">checks and deployments</a> on the Github side.</li>\r\n  <li>If you want to <a href="https://go.news.fly.io/e/c/eyJlbWFpbF9pZCI6ImRnU043UWtEQVBiTEdQWExHQUdUZnhWZW45eHZGUWRwOEdDNjgyQT0iLCJocmVmIjoiaHR0cHM6Ly9jb21tdW5pdHkuZmx5LmlvL3QvdXBkYXRlLW1hY2hpbmUtc2l6ZS1mcm9tLXRoZS1kYXNoYm9hcmQvMjI2MDA_dXRtX2NhbXBhaWduPU5vdmVtYmVyKyUyNzI0K05ld3NsZXR0ZXJcdTAwMjZ1dG1fY29udGVudD1FbWFpbFx1MDAyNnV0bV9tZWRpdW09ZW1haWxfYWN0aW9uXHUwMDI2dXRtX3NvdXJjZT1jdXN0b21lci5pbyIsImludGVybmFsIjoiOGRlZDA5MGQ4NTY1ZjZjYjE4IiwibGlua19pZCI6NDYxMn0/174640eda7158337140cfd76b8c45cf0ce6853b29f51a9023f2205cf5a3129cd">update machine size</a>, <a href="https://go.news.fly.io/e/c/eyJlbWFpbF9pZCI6ImRnU043UWtEQVBiTEdQWExHQUdUZnhWZW45eHZGUWRwOEdDNjgyQT0iLCJocmVmIjoiaHR0cHM6Ly9jb21tdW5pdHkuZmx5LmlvL3QveW91LWNhbi1ub3ctcmVzaXplLXlvdXItZGVwb3QtYnVpbGRlcnMvMjI1Nzg_dXRtX2NhbXBhaWduPU5vdmVtYmVyKyUyNzI0K05ld3NsZXR0ZXJcdTAwMjZ1dG1fY29udGVudD1FbWFpbFx1MDAyNnV0bV9tZWRpdW09ZW1haWxfYWN0aW9uXHUwMDI2dXRtX3NvdXJjZT1jdXN0b21lci5pbyIsImludGVybmFsIjoiOGRlZDA5MGQ4NTY1ZjZjYjE4IiwibGlua19pZCI6NDYxM30/9c28dcdb486734994cc46aaeedd22db61a58514b61243600b4ae4eacba33cc6a">update the size of your builder</a>, or <a href="https://go.news.fly.io/e/c/eyJlbWFpbF9pZCI6ImRnU043UWtEQVBiTEdQWExHQUdUZnhWZW45eHZGUWRwOEdDNjgyQT0iLCJocmVmIjoiaHR0cHM6Ly9jb21tdW5pdHkuZmx5LmlvL3QvY2xvbmUtbWFjaGluZXMtZnJvbS10aGUtd2ViLXVpLzIyNTQyP3V0bV9jYW1wYWlnbj1Ob3ZlbWJlcislMjcyNCtOZXdzbGV0dGVyXHUwMDI2dXRtX2NvbnRlbnQ9RW1haWxcdTAwMjZ1dG1fbWVkaXVtPWVtYWlsX2FjdGlvblx1MDAyNnV0bV9zb3VyY2U9Y3VzdG9tZXIuaW8iLCJpbnRlcm5hbCI6IjhkZWQwOTBkODU2NWY2Y2IxOCIsImxpbmtfaWQiOjQ2MTR9/3462f8770e26c5e8db70a7b8b5d5e03484cf8ab4f2cff175d9eea8f875e5c0f4">clone machines</a>, you can do it all from the dashboard!</li>\r\n</ul>\r\n\r\n<h3 style="margin: 1em 0; font-size: 1.6em; font-weight: 400; font-style: normal; line-height: 1.2; letter-spacing: -0.015em; word-spacing: 0.015em; font-family: &#39;Ovo&#39;, serif !important;">Inside Flyball</h3>\r\n\r\n<p>A big internal focus right now is making our Support as…supportive (sorry) as it can possibly be. So, we&#39;ve made some <a href="https://go.news.fly.io/e/c/eyJlbWFpbF9pZCI6ImRnU043UWtEQVBiTEdQWExHQUdUZnhWZW45eHZGUWRwOEdDNjgyQT0iLCJocmVmIjoiaHR0cHM6Ly9jb21tdW5pdHkuZmx5LmlvL3QvcmVkZXNpZ25lZC1zdXBwb3J0LXBvcnRhbC8yMjUzND91dG1fY2FtcGFpZ249Tm92ZW1iZXIrJTI3MjQrTmV3c2xldHRlclx1MDAyNnV0bV9jb250ZW50PUVtYWlsXHUwMDI2dXRtX21lZGl1bT1lbWFpbF9hY3Rpb25cdTAwMjZ1dG1fc291cmNlPWN1c3RvbWVyLmlvIiwiaW50ZXJuYWwiOiI4ZGVkMDkwZDg1NjVmNmNiMTgiLCJsaW5rX2lkIjo0NjE1fQ/ab18015af8390f888d015ed2727951cff23109011f9681da0f8e479570aacc98">improvements to the Support portal</a> that are available to anyone with paid support. We&#39;ve also just shipped &#34;<a href="https://go.news.fly.io/e/c/eyJlbWFpbF9pZCI6ImRnU043UWtEQVBiTEdQWExHQUdUZnhWZW45eHZGUWRwOEdDNjgyQT0iLCJocmVmIjoiaHR0cHM6Ly9jb21tdW5pdHkuZmx5LmlvL3Qvc3VwcG9ydC1pbXBhY3QtbGV2ZWxzLzIyNTM4P3V0bV9jYW1wYWlnbj1Ob3ZlbWJlcislMjcyNCtOZXdzbGV0dGVyXHUwMDI2dXRtX2NvbnRlbnQ9RW1haWxcdTAwMjZ1dG1fbWVkaXVtPWVtYWlsX2FjdGlvblx1MDAyNnV0bV9zb3VyY2U9Y3VzdG9tZXIuaW8iLCJpbnRlcm5hbCI6IjhkZWQwOTBkODU2NWY2Y2IxOCIsImxpbmtfaWQiOjQ2MTZ9/c2c82ebd2dccf18b2a760f4ef42ccb26eeaa2b5c0bc547d1de94d049dfa4df88">impact levels</a>&#34; on Support tickets, so you can indicate the urgency and severity of your support request or escalate an existing ticket. As always, we&#39;re curious to know what you think, so hit us with your feedback.</p>\r\n\r\n<h3 style="margin: 1em 0; font-size: 1.6em; font-weight: 400; font-style: normal; line-height: 1.2; letter-spacing: -0.015em; word-spacing: 0.015em; font-family: &#39;Ovo&#39;, serif !important;">Feature story: why we got rid of plans</h3>\r\n\r\n<p>When you&#39;re looking for a platform to host your app, one of the first questions you&#39;ll ask is: &#34;how much is this gonna cost me?&#34; Oh, how we wish we could boil things down to one number to rule them all, but Legal refuses to let us put a &#34;42&#34; on the pricing page and leave it at that.</p>\r\n\r\n<p>Users are doing all kinds of things on Fly.io—like <a href="https://go.news.fly.io/e/c/eyJlbWFpbF9pZCI6ImRnU043UWtEQVBiTEdQWExHQUdUZnhWZW45eHZGUWRwOEdDNjgyQT0iLCJocmVmIjoiaHR0cHM6Ly9mbHkuaW8vY3VzdG9tZXJzL2FtcGxpZmllZD91dG1fY2FtcGFpZ249Tm92ZW1iZXIrJTI3MjQrTmV3c2xldHRlclx1MDAyNnV0bV9jb250ZW50PUVtYWlsXHUwMDI2dXRtX21lZGl1bT1lbWFpbF9hY3Rpb25cdTAwMjZ1dG1fc291cmNlPWN1c3RvbWVyLmlvIiwiaW50ZXJuYWwiOiI4ZGVkMDkwZDg1NjVmNmNiMTgiLCJsaW5rX2lkIjo0NjE3fQ/c2999a79411d9262699016bfdb21574eb0bacd8974a5921b398d8813436d00e7">sorting through the US Patent system</a> and <a href="https://go.news.fly.io/e/c/eyJlbWFpbF9pZCI6ImRnU043UWtEQVBiTEdQWExHQUdUZnhWZW45eHZGUWRwOEdDNjgyQT0iLCJocmVmIjoiaHR0cHM6Ly9mbHkuaW8vY3VzdG9tZXJzL215bWFoaT91dG1fY2FtcGFpZ249Tm92ZW1iZXIrJTI3MjQrTmV3c2xldHRlclx1MDAyNnV0bV9jb250ZW50PUVtYWlsXHUwMDI2dXRtX21lZGl1bT1lbWFpbF9hY3Rpb25cdTAwMjZ1dG1fc291cmNlPWN1c3RvbWVyLmlvIiwiaW50ZXJuYWwiOiI4ZGVkMDkwZDg1NjVmNmNiMTgiLCJsaW5rX2lkIjo0NjE4fQ/dd55e313d1cbca385eaaf4e3510b2d79cdc383d2ba539c7df9ad525166e83852">helping teenagers in New Zealand learn outside of school</a>—and therefore using wildly varying kinds and amounts of resources. That means the (infuriating) answer to cost questions is always: &#34;it depends.&#34; Plus, there are a few different ways to structure pricing from the company side: you can either group certain resources together based on what you <em>think</em> different groups of customers might want (like a prix fix menu), or you can price each individual resource and let folks order á la carte.</p>\r\n\r\n<p>In the beginning, there was usage based pricing. Fly.io actually launched on the &#34;á la carte&#34; side of things, charging just for usage. At that point, we were just inflating Frankie the hot air balloon, and Support did not yet exist. Users told us they wanted Support, so we said &#34;You bet!&#34; But instead of just building paid Support options, we thought about other elements that users would want (like BAAs, SOC2 reports, etc) and packaged it all together.</p>\r\n\r\n<p>We ended up with plans we called Hobby, Launch, Scale, and Enterprise. Each one had some built-in &#34;allowances&#34; (free resources), some had Support, and all of them were anchored to different types of users. For example, the Scale plan was for users who needed to run multi-region, high-availability workloads, had specific compliance requirements (like needing to be in line with HIPAA), and wanted priority email support.</p>\r\n\r\n<p>This worked for a while. But over time, we started getting feedback from prospective users (and comments on Reddit, we won&#39;t lie) about how our pricing felt unclear. Folks were thinking about using us, but couldn&#39;t get a sense for how much it would cost.</p>\r\n\r\n<p>We also realized that the groupings we came up with might not actually make sense for some of Fly.io&#39;s users. For example: some folks might want ultra-available Support, but not care at all about compliance. And once we started digging into it, we started asking: why build a company around primitives that are really simple and swappable, and then make a payment structure that requires picking Option A, B, or C? &#34;There must be consistency!&#34; we said. (We were getting pretty fired up at this point.)</p>\r\n\r\n<p>So, we got rid of Plans. Instead of grouping resources together, we&#39;ve gone pure &#34;pay as you go.&#34; We want you to be able to take a glance at our compute costs, do some back of the envelope math, and rest easy that you know what you&#39;re in for. You don&#39;t even have to do the math! We made <a href="https://go.news.fly.io/e/c/eyJlbWFpbF9pZCI6ImRnU043UWtEQVBiTEdQWExHQUdUZnhWZW45eHZGUWRwOEdDNjgyQT0iLCJocmVmIjoiaHR0cHM6Ly9mbHkuaW8vY2FsY3VsYXRvcj91dG1fY2FtcGFpZ249Tm92ZW1iZXIrJTI3MjQrTmV3c2xldHRlclx1MDAyNnV0bV9jb250ZW50PUVtYWlsXHUwMDI2dXRtX21lZGl1bT1lbWFpbF9hY3Rpb25cdTAwMjZ1dG1fc291cmNlPWN1c3RvbWVyLmlvIiwiaW50ZXJuYWwiOiI4ZGVkMDkwZDg1NjVmNmNiMTgiLCJsaW5rX2lkIjo0NjE5fQ/393875c8cc124b8c05a7d3c389233b405fd2231406358e71cff2b248830fc7e0">this calculator</a> so you can just plug in what you need and have it spit out an approximate total.</p>\r\n\r\n<p>In this brave new world, your invoice will be:</p>\r\n\r\n<ul>\r\n  <li>whatever compute/product resources you use, pro-rated for the time you use them, plus</li>\r\n  <li>whichever add-ons you pick, like Support and/or Compliance</li>\r\n</ul>\r\n\r\n<p>To be super clear: this new á la carte set-up is just for new accounts. Anyone who was already on a plan still has it, no sweat.</p>\r\n\r\n<p>Our aim in getting rid of plans here was to make pricing simpler. <a href="https://go.news.fly.io/e/c/eyJlbWFpbF9pZCI6ImRnU043UWtEQVBiTEdQWExHQUdUZnhWZW45eHZGUWRwOEdDNjgyQT0iLCJocmVmIjoiaHR0cHM6Ly9jb21tdW5pdHkuZmx5LmlvL3Qvd2VyZS1tYWtpbmctcHJpY2luZy1zaW1wbGVyLzIyMTY4P3V0bV9jYW1wYWlnbj1Ob3ZlbWJlcislMjcyNCtOZXdzbGV0dGVyXHUwMDI2dXRtX2NvbnRlbnQ9RW1haWxcdTAwMjZ1dG1fbWVkaXVtPWVtYWlsX2FjdGlvblx1MDAyNnV0bV9zb3VyY2U9Y3VzdG9tZXIuaW8iLCJpbnRlcm5hbCI6IjhkZWQwOTBkODU2NWY2Y2IxOCIsImxpbmtfaWQiOjQ2MjB9/983935cc539a8f00dd7dfdc6df9ed20fa014b352a7803f53f2739410fc90480c">Some folks have pointed out</a> that separating out Support and Compliance as &#34;add-ons&#34; means that after this change (which went live October 7th) people who sign up for new accounts could end up paying more than their historical counterparts, depending on what they choose. For example, our previous &#34;Launch&#34; plan was $29/month, and it came with Standard Support and some free compute allowances. Today, Standard Support is $29/month by itself.</p>\r\n\r\n<p>Given that, was this the right call, on balance? We think so, since we figure:</p>\r\n\r\n<ul>\r\n  <li>Users who are already on a plan with us probably understand our pricing well enough, so we haven&#39;t touched anything about their set-up.</li>\r\n  <li>For prospective users, being able to easily and quickly answer the &#34;how much is it gonna cost me&#34; question is crucial.</li>\r\n  <li>With this new set-up, we&#39;re able to price each element at what it <em>actually</em> costs us to provide it to you, plus the margin that allows us to make this a viable business.</li>\r\n</ul>\r\n      </div>\r\n      <div style="text-align:center; margin: 1.5em auto;">\r\n        <hr/>\r\n            <a href="https://go.news.fly.io/unsubscribe/=" class="untracked">Unsubscribe</a> ・\r\n            <a href="https://go.news.fly.io/manage_subscription_preferences/=">Manage Subscriptions</a> ・ \r\n            <a href="https://go.news.fly.io/deliveries/=">View in Browser</a>\r\n      </div>\r\n      <!--[if true]>\r\n      </td>\r\n      <td></td>\r\n      </tr>\r\n      </table>\r\n      <![endif]-->\r\n    </div>\r\n  \r\n<img src="https://go.news.fly.io/e/o/eyJlbWFpbF9pZCI6ImRnU043UWtEQVBiTEdQWExHQUdUZnhWZW45eHZGUWRwOEdDNjgyQT0ifQ==" style="height: 1px !important; max-height: 1px !important; max-width: 1px !important; width: 1px !important; display: none !important;" alt=""/></body></html><!-- Change the lang and dir attributes to match the language of your email and the direction of that language. Change the aria-label to match the title of your email-->',
            reply:
              'Episode #5: why we got rid of plans ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­\n\nNews, tips, and behind the scenes technical mumbo jumbo   ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­\n\nHello! Here we are again, ready to share some scintillating updates from Cloud Provider Land. All the real humans behind this newsletter hope you\'re doing well, and ask that you holler if we can help with something. Onward!\n\nProduct updates\n\nYou can always check out the [Fresh Produce](https://go.news.fly.io/e/c/eyJlbWFpbF9pZCI6ImRnU043UWtEQVBiTEdQWExHQUdUZnhWZW45eHZGUWRwOEdDNjgyQT0iLCJocmVmIjoiaHR0cHM6Ly9jb21tdW5pdHkuZmx5LmlvL2MvZnJlc2gtcHJvZHVjZS8yNz91dG1fY2FtcGFpZ249Tm92ZW1iZXIrJTI3MjQrTmV3c2xldHRlclx1MDAyNnV0bV9jb250ZW50PUVtYWlsXHUwMDI2dXRtX21lZGl1bT1lbWFpbF9hY3Rpb25cdTAwMjZ1dG1fc291cmNlPWN1c3RvbWVyLmlvIiwiaW50ZXJuYWwiOiI4ZGVkMDkwZDg1NjVmNmNiMTgiLCJsaW5rX2lkIjo2MTN9/c370aeeb5186be56bff342368bba9168095da891eddca9e3a5f4bdb9e771d03f) page to see what we\'ve been fiddling with lately. Here are some of the highlights since we last checked in:\n\n- You can [auto-deploy your apps](https://go.news.fly.io/e/c/eyJlbWFpbF9pZCI6ImRnU043UWtEQVBiTEdQWExHQUdUZnhWZW45eHZGUWRwOEdDNjgyQT0iLCJocmVmIjoiaHR0cHM6Ly9jb21tdW5pdHkuZmx5LmlvL3QvYXV0by1kZXBsb3ktb24tcHVzaC10by1naXRodWIvMjIzMDY_dXRtX2NhbXBhaWduPU5vdmVtYmVyKyUyNzI0K05ld3NsZXR0ZXJcdTAwMjZ1dG1fY29udGVudD1FbWFpbFx1MDAyNnV0bV9tZWRpdW09ZW1haWxfYWN0aW9uXHUwMDI2dXRtX3NvdXJjZT1jdXN0b21lci5pbyIsImludGVybmFsIjoiOGRlZDA5MGQ4NTY1ZjZjYjE4IiwibGlua19pZCI6NDYxMH0/164b95c7adefbeb39a47c3ec466a0721fe20b834cc9cdfd634cdeb64f5e1e3c6) on Fly.io when you push your code to GitHub, and see the [checks and deployments](https://go.news.fly.io/e/c/eyJlbWFpbF9pZCI6ImRnU043UWtEQVBiTEdQWExHQUdUZnhWZW45eHZGUWRwOEdDNjgyQT0iLCJocmVmIjoiaHR0cHM6Ly9jb21tdW5pdHkuZmx5LmlvL3QvZ2l0aHViLWNoZWNrcy1hbmQtZGVwbG95bWVudHMvMjI2Njk_dXRtX2NhbXBhaWduPU5vdmVtYmVyKyUyNzI0K05ld3NsZXR0ZXJcdTAwMjZ1dG1fY29udGVudD1FbWFpbFx1MDAyNnV0bV9tZWRpdW09ZW1haWxfYWN0aW9uXHUwMDI2dXRtX3NvdXJjZT1jdXN0b21lci5pbyIsImludGVybmFsIjoiOGRlZDA5MGQ4NTY1ZjZjYjE4IiwibGlua19pZCI6NDYxMX0/8a09b38b182b26b413b655ff54755f73efac8f2ede1627f266acbc20e54efd39) on the Github side.\n- If you want to [update machine size](https://go.news.fly.io/e/c/eyJlbWFpbF9pZCI6ImRnU043UWtEQVBiTEdQWExHQUdUZnhWZW45eHZGUWRwOEdDNjgyQT0iLCJocmVmIjoiaHR0cHM6Ly9jb21tdW5pdHkuZmx5LmlvL3QvdXBkYXRlLW1hY2hpbmUtc2l6ZS1mcm9tLXRoZS1kYXNoYm9hcmQvMjI2MDA_dXRtX2NhbXBhaWduPU5vdmVtYmVyKyUyNzI0K05ld3NsZXR0ZXJcdTAwMjZ1dG1fY29udGVudD1FbWFpbFx1MDAyNnV0bV9tZWRpdW09ZW1haWxfYWN0aW9uXHUwMDI2dXRtX3NvdXJjZT1jdXN0b21lci5pbyIsImludGVybmFsIjoiOGRlZDA5MGQ4NTY1ZjZjYjE4IiwibGlua19pZCI6NDYxMn0/174640eda7158337140cfd76b8c45cf0ce6853b29f51a9023f2205cf5a3129cd), [update the size of your builder](https://go.news.fly.io/e/c/eyJlbWFpbF9pZCI6ImRnU043UWtEQVBiTEdQWExHQUdUZnhWZW45eHZGUWRwOEdDNjgyQT0iLCJocmVmIjoiaHR0cHM6Ly9jb21tdW5pdHkuZmx5LmlvL3QveW91LWNhbi1ub3ctcmVzaXplLXlvdXItZGVwb3QtYnVpbGRlcnMvMjI1Nzg_dXRtX2NhbXBhaWduPU5vdmVtYmVyKyUyNzI0K05ld3NsZXR0ZXJcdTAwMjZ1dG1fY29udGVudD1FbWFpbFx1MDAyNnV0bV9tZWRpdW09ZW1haWxfYWN0aW9uXHUwMDI2dXRtX3NvdXJjZT1jdXN0b21lci5pbyIsImludGVybmFsIjoiOGRlZDA5MGQ4NTY1ZjZjYjE4IiwibGlua19pZCI6NDYxM30/9c28dcdb486734994cc46aaeedd22db61a58514b61243600b4ae4eacba33cc6a), or [clone machines](https://go.news.fly.io/e/c/eyJlbWFpbF9pZCI6ImRnU043UWtEQVBiTEdQWExHQUdUZnhWZW45eHZGUWRwOEdDNjgyQT0iLCJocmVmIjoiaHR0cHM6Ly9jb21tdW5pdHkuZmx5LmlvL3QvY2xvbmUtbWFjaGluZXMtZnJvbS10aGUtd2ViLXVpLzIyNTQyP3V0bV9jYW1wYWlnbj1Ob3ZlbWJlcislMjcyNCtOZXdzbGV0dGVyXHUwMDI2dXRtX2NvbnRlbnQ9RW1haWxcdTAwMjZ1dG1fbWVkaXVtPWVtYWlsX2FjdGlvblx1MDAyNnV0bV9zb3VyY2U9Y3VzdG9tZXIuaW8iLCJpbnRlcm5hbCI6IjhkZWQwOTBkODU2NWY2Y2IxOCIsImxpbmtfaWQiOjQ2MTR9/3462f8770e26c5e8db70a7b8b5d5e03484cf8ab4f2cff175d9eea8f875e5c0f4), you can do it all from the dashboard!\n\nInside Flyball\n\nA big internal focus right now is making our Support as…supportive (sorry) as it can possibly be. So, we\'ve made some [improvements to the Support portal](https://go.news.fly.io/e/c/eyJlbWFpbF9pZCI6ImRnU043UWtEQVBiTEdQWExHQUdUZnhWZW45eHZGUWRwOEdDNjgyQT0iLCJocmVmIjoiaHR0cHM6Ly9jb21tdW5pdHkuZmx5LmlvL3QvcmVkZXNpZ25lZC1zdXBwb3J0LXBvcnRhbC8yMjUzND91dG1fY2FtcGFpZ249Tm92ZW1iZXIrJTI3MjQrTmV3c2xldHRlclx1MDAyNnV0bV9jb250ZW50PUVtYWlsXHUwMDI2dXRtX21lZGl1bT1lbWFpbF9hY3Rpb25cdTAwMjZ1dG1fc291cmNlPWN1c3RvbWVyLmlvIiwiaW50ZXJuYWwiOiI4ZGVkMDkwZDg1NjVmNmNiMTgiLCJsaW5rX2lkIjo0NjE1fQ/ab18015af8390f888d015ed2727951cff23109011f9681da0f8e479570aacc98) that are available to anyone with paid support. We\'ve also just shipped "[impact levels](https://go.news.fly.io/e/c/eyJlbWFpbF9pZCI6ImRnU043UWtEQVBiTEdQWExHQUdUZnhWZW45eHZGUWRwOEdDNjgyQT0iLCJocmVmIjoiaHR0cHM6Ly9jb21tdW5pdHkuZmx5LmlvL3Qvc3VwcG9ydC1pbXBhY3QtbGV2ZWxzLzIyNTM4P3V0bV9jYW1wYWlnbj1Ob3ZlbWJlcislMjcyNCtOZXdzbGV0dGVyXHUwMDI2dXRtX2NvbnRlbnQ9RW1haWxcdTAwMjZ1dG1fbWVkaXVtPWVtYWlsX2FjdGlvblx1MDAyNnV0bV9zb3VyY2U9Y3VzdG9tZXIuaW8iLCJpbnRlcm5hbCI6IjhkZWQwOTBkODU2NWY2Y2IxOCIsImxpbmtfaWQiOjQ2MTZ9/c2c82ebd2dccf18b2a760f4ef42ccb26eeaa2b5c0bc547d1de94d049dfa4df88)" on Support tickets, so you can indicate the urgency and severity of your support request or escalate an existing ticket. As always, we\'re curious to know what you think, so hit us with your feedback.\n\nFeature story: why we got rid of plans\n\nWhen you\'re looking for a platform to host your app, one of the first questions you\'ll ask is: "how much is this gonna cost me?" Oh, how we wish we could boil things down to one number to rule them all, but Legal refuses to let us put a "42" on the pricing page and leave it at that.\n\nUsers are doing all kinds of things on Fly.io—like [sorting through the US Patent system](https://go.news.fly.io/e/c/eyJlbWFpbF9pZCI6ImRnU043UWtEQVBiTEdQWExHQUdUZnhWZW45eHZGUWRwOEdDNjgyQT0iLCJocmVmIjoiaHR0cHM6Ly9mbHkuaW8vY3VzdG9tZXJzL2FtcGxpZmllZD91dG1fY2FtcGFpZ249Tm92ZW1iZXIrJTI3MjQrTmV3c2xldHRlclx1MDAyNnV0bV9jb250ZW50PUVtYWlsXHUwMDI2dXRtX21lZGl1bT1lbWFpbF9hY3Rpb25cdTAwMjZ1dG1fc291cmNlPWN1c3RvbWVyLmlvIiwiaW50ZXJuYWwiOiI4ZGVkMDkwZDg1NjVmNmNiMTgiLCJsaW5rX2lkIjo0NjE3fQ/c2999a79411d9262699016bfdb21574eb0bacd8974a5921b398d8813436d00e7) and [helping teenagers in New Zealand learn outside of school](https://go.news.fly.io/e/c/eyJlbWFpbF9pZCI6ImRnU043UWtEQVBiTEdQWExHQUdUZnhWZW45eHZGUWRwOEdDNjgyQT0iLCJocmVmIjoiaHR0cHM6Ly9mbHkuaW8vY3VzdG9tZXJzL215bWFoaT91dG1fY2FtcGFpZ249Tm92ZW1iZXIrJTI3MjQrTmV3c2xldHRlclx1MDAyNnV0bV9jb250ZW50PUVtYWlsXHUwMDI2dXRtX21lZGl1bT1lbWFpbF9hY3Rpb25cdTAwMjZ1dG1fc291cmNlPWN1c3RvbWVyLmlvIiwiaW50ZXJuYWwiOiI4ZGVkMDkwZDg1NjVmNmNiMTgiLCJsaW5rX2lkIjo0NjE4fQ/dd55e313d1cbca385eaaf4e3510b2d79cdc383d2ba539c7df9ad525166e83852)—and therefore using wildly varying kinds and amounts of resources. That means the (infuriating) answer to cost questions is always: "it depends." Plus, there are a few different ways to structure pricing from the company side: you can either group certain resources together based on what you think different groups of customers might want (like a prix fix menu), or you can price each individual resource and let folks order á la carte.\n\nIn the beginning, there was usage based pricing. Fly.io actually launched on the "á la carte" side of things, charging just for usage. At that point, we were just inflating Frankie the hot air balloon, and Support did not yet exist. Users told us they wanted Support, so we said "You bet!" But instead of just building paid Support options, we thought about other elements that users would want (like BAAs, SOC2 reports, etc) and packaged it all together.\n\nWe ended up with plans we called Hobby, Launch, Scale, and Enterprise. Each one had some built-in "allowances" (free resources), some had Support, and all of them were anchored to different types of users. For example, the Scale plan was for users who needed to run multi-region, high-availability workloads, had specific compliance requirements (like needing to be in line with HIPAA), and wanted priority email support.\n\nThis worked for a while. But over time, we started getting feedback from prospective users (and comments on Reddit, we won\'t lie) about how our pricing felt unclear. Folks were thinking about using us, but couldn\'t get a sense for how much it would cost.\n\nWe also realized that the groupings we came up with might not actually make sense for some of Fly.io\'s users. For example: some folks might want ultra-available Support, but not care at all about compliance. And once we started digging into it, we started asking: why build a company around primitives that are really simple and swappable, and then make a payment structure that requires picking Option A, B, or C? "There must be consistency!" we said. (We were getting pretty fired up at this point.)\n\nSo, we got rid of Plans. Instead of grouping resources together, we\'ve gone pure "pay as you go." We want you to be able to take a glance at our compute costs, do some back of the envelope math, and rest easy that you know what you\'re in for. You don\'t even have to do the math! We made [this calculator](https://go.news.fly.io/e/c/eyJlbWFpbF9pZCI6ImRnU043UWtEQVBiTEdQWExHQUdUZnhWZW45eHZGUWRwOEdDNjgyQT0iLCJocmVmIjoiaHR0cHM6Ly9mbHkuaW8vY2FsY3VsYXRvcj91dG1fY2FtcGFpZ249Tm92ZW1iZXIrJTI3MjQrTmV3c2xldHRlclx1MDAyNnV0bV9jb250ZW50PUVtYWlsXHUwMDI2dXRtX21lZGl1bT1lbWFpbF9hY3Rpb25cdTAwMjZ1dG1fc291cmNlPWN1c3RvbWVyLmlvIiwiaW50ZXJuYWwiOiI4ZGVkMDkwZDg1NjVmNmNiMTgiLCJsaW5rX2lkIjo0NjE5fQ/393875c8cc124b8c05a7d3c389233b405fd2231406358e71cff2b248830fc7e0) so you can just plug in what you need and have it spit out an approximate total.\n\nIn this brave new world, your invoice will be:\n\n- whatever compute/product resources you use, pro-rated for the time you use them, plus\n- whichever add-ons you pick, like Support and/or Compliance\n\nTo be super clear: this new á la carte set-up is just for new accounts. Anyone who was already on a plan still has it, no sweat.\n\nOur aim in getting rid of plans here was to make pricing simpler. [Some folks have pointed out](https://go.news.fly.io/e/c/eyJlbWFpbF9pZCI6ImRnU043UWtEQVBiTEdQWExHQUdUZnhWZW45eHZGUWRwOEdDNjgyQT0iLCJocmVmIjoiaHR0cHM6Ly9jb21tdW5pdHkuZmx5LmlvL3Qvd2VyZS1tYWtpbmctcHJpY2luZy1zaW1wbGVyLzIyMTY4P3V0bV9jYW1wYWlnbj1Ob3ZlbWJlcislMjcyNCtOZXdzbGV0dGVyXHUwMDI2dXRtX2NvbnRlbnQ9RW1haWxcdTAwMjZ1dG1fbWVkaXVtPWVtYWlsX2FjdGlvblx1MDAyNnV0bV9zb3VyY2U9Y3VzdG9tZXIuaW8iLCJpbnRlcm5hbCI6IjhkZWQwOTBkODU2NWY2Y2IxOCIsImxpbmtfaWQiOjQ2MjB9/983935cc539a8f00dd7dfdc6df9ed20fa014b352a7803f53f2739410fc90480c) that separating out Support and Compliance as "add-ons" means that after this change (which went live October 7th) people who sign up for new accounts could end up paying more than their historical counterparts, depending on what they choose. For example, our previous "Launch" plan was $29/month, and it came with Standard Support and some free compute allowances. Today, Standard Support is $29/month by itself.\n\nGiven that, was this the right call, on balance? We think so, since we figure:\n\n- Users who are already on a plan with us probably understand our pricing well enough, so we haven\'t touched anything about their set-up.\n- For prospective users, being able to easily and quickly answer the "how much is it gonna cost me" question is crucial.\n- With this new set-up, we\'re able to price each element at what it actually costs us to provide it to you, plus the margin that allows us to make this a viable business.\n\n---------------------------------------------------------------\n[Unsubscribe](https://go.news.fly.io/unsubscribe/=) ・ [Manage Subscriptions](https://go.news.fly.io/manage_subscription_preferences/=) ・ [View in Browser](https://go.news.fly.io/deliveries/=)\n[]',
            quoted:
              'Episode #5: why we got rid of plans ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­\n\nNews, tips, and behind the scenes technical mumbo jumbo   ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­\n\nHello! Here we are again, ready to share some scintillating updates from Cloud Provider Land. All the real humans behind this newsletter hope you\'re doing well, and ask that you holler if we can help with something. Onward!\n\nProduct updates\n\nYou can always check out the [Fresh Produce](https://go.news.fly.io/e/c/eyJlbWFpbF9pZCI6ImRnU043UWtEQVBiTEdQWExHQUdUZnhWZW45eHZGUWRwOEdDNjgyQT0iLCJocmVmIjoiaHR0cHM6Ly9jb21tdW5pdHkuZmx5LmlvL2MvZnJlc2gtcHJvZHVjZS8yNz91dG1fY2FtcGFpZ249Tm92ZW1iZXIrJTI3MjQrTmV3c2xldHRlclx1MDAyNnV0bV9jb250ZW50PUVtYWlsXHUwMDI2dXRtX21lZGl1bT1lbWFpbF9hY3Rpb25cdTAwMjZ1dG1fc291cmNlPWN1c3RvbWVyLmlvIiwiaW50ZXJuYWwiOiI4ZGVkMDkwZDg1NjVmNmNiMTgiLCJsaW5rX2lkIjo2MTN9/c370aeeb5186be56bff342368bba9168095da891eddca9e3a5f4bdb9e771d03f) page to see what we\'ve been fiddling with lately. Here are some of the highlights since we last checked in:\n\n- You can [auto-deploy your apps](https://go.news.fly.io/e/c/eyJlbWFpbF9pZCI6ImRnU043UWtEQVBiTEdQWExHQUdUZnhWZW45eHZGUWRwOEdDNjgyQT0iLCJocmVmIjoiaHR0cHM6Ly9jb21tdW5pdHkuZmx5LmlvL3QvYXV0by1kZXBsb3ktb24tcHVzaC10by1naXRodWIvMjIzMDY_dXRtX2NhbXBhaWduPU5vdmVtYmVyKyUyNzI0K05ld3NsZXR0ZXJcdTAwMjZ1dG1fY29udGVudD1FbWFpbFx1MDAyNnV0bV9tZWRpdW09ZW1haWxfYWN0aW9uXHUwMDI2dXRtX3NvdXJjZT1jdXN0b21lci5pbyIsImludGVybmFsIjoiOGRlZDA5MGQ4NTY1ZjZjYjE4IiwibGlua19pZCI6NDYxMH0/164b95c7adefbeb39a47c3ec466a0721fe20b834cc9cdfd634cdeb64f5e1e3c6) on Fly.io when you push your code to GitHub, and see the [checks and deployments](https://go.news.fly.io/e/c/eyJlbWFpbF9pZCI6ImRnU043UWtEQVBiTEdQWExHQUdUZnhWZW45eHZGUWRwOEdDNjgyQT0iLCJocmVmIjoiaHR0cHM6Ly9jb21tdW5pdHkuZmx5LmlvL3QvZ2l0aHViLWNoZWNrcy1hbmQtZGVwbG95bWVudHMvMjI2Njk_dXRtX2NhbXBhaWduPU5vdmVtYmVyKyUyNzI0K05ld3NsZXR0ZXJcdTAwMjZ1dG1fY29udGVudD1FbWFpbFx1MDAyNnV0bV9tZWRpdW09ZW1haWxfYWN0aW9uXHUwMDI2dXRtX3NvdXJjZT1jdXN0b21lci5pbyIsImludGVybmFsIjoiOGRlZDA5MGQ4NTY1ZjZjYjE4IiwibGlua19pZCI6NDYxMX0/8a09b38b182b26b413b655ff54755f73efac8f2ede1627f266acbc20e54efd39) on the Github side.\n- If you want to [update machine size](https://go.news.fly.io/e/c/eyJlbWFpbF9pZCI6ImRnU043UWtEQVBiTEdQWExHQUdUZnhWZW45eHZGUWRwOEdDNjgyQT0iLCJocmVmIjoiaHR0cHM6Ly9jb21tdW5pdHkuZmx5LmlvL3QvdXBkYXRlLW1hY2hpbmUtc2l6ZS1mcm9tLXRoZS1kYXNoYm9hcmQvMjI2MDA_dXRtX2NhbXBhaWduPU5vdmVtYmVyKyUyNzI0K05ld3NsZXR0ZXJcdTAwMjZ1dG1fY29udGVudD1FbWFpbFx1MDAyNnV0bV9tZWRpdW09ZW1haWxfYWN0aW9uXHUwMDI2dXRtX3NvdXJjZT1jdXN0b21lci5pbyIsImludGVybmFsIjoiOGRlZDA5MGQ4NTY1ZjZjYjE4IiwibGlua19pZCI6NDYxMn0/174640eda7158337140cfd76b8c45cf0ce6853b29f51a9023f2205cf5a3129cd), [update the size of your builder](https://go.news.fly.io/e/c/eyJlbWFpbF9pZCI6ImRnU043UWtEQVBiTEdQWExHQUdUZnhWZW45eHZGUWRwOEdDNjgyQT0iLCJocmVmIjoiaHR0cHM6Ly9jb21tdW5pdHkuZmx5LmlvL3QveW91LWNhbi1ub3ctcmVzaXplLXlvdXItZGVwb3QtYnVpbGRlcnMvMjI1Nzg_dXRtX2NhbXBhaWduPU5vdmVtYmVyKyUyNzI0K05ld3NsZXR0ZXJcdTAwMjZ1dG1fY29udGVudD1FbWFpbFx1MDAyNnV0bV9tZWRpdW09ZW1haWxfYWN0aW9uXHUwMDI2dXRtX3NvdXJjZT1jdXN0b21lci5pbyIsImludGVybmFsIjoiOGRlZDA5MGQ4NTY1ZjZjYjE4IiwibGlua19pZCI6NDYxM30/9c28dcdb486734994cc46aaeedd22db61a58514b61243600b4ae4eacba33cc6a), or [clone machines](https://go.news.fly.io/e/c/eyJlbWFpbF9pZCI6ImRnU043UWtEQVBiTEdQWExHQUdUZnhWZW45eHZGUWRwOEdDNjgyQT0iLCJocmVmIjoiaHR0cHM6Ly9jb21tdW5pdHkuZmx5LmlvL3QvY2xvbmUtbWFjaGluZXMtZnJvbS10aGUtd2ViLXVpLzIyNTQyP3V0bV9jYW1wYWlnbj1Ob3ZlbWJlcislMjcyNCtOZXdzbGV0dGVyXHUwMDI2dXRtX2NvbnRlbnQ9RW1haWxcdTAwMjZ1dG1fbWVkaXVtPWVtYWlsX2FjdGlvblx1MDAyNnV0bV9zb3VyY2U9Y3VzdG9tZXIuaW8iLCJpbnRlcm5hbCI6IjhkZWQwOTBkODU2NWY2Y2IxOCIsImxpbmtfaWQiOjQ2MTR9/3462f8770e26c5e8db70a7b8b5d5e03484cf8ab4f2cff175d9eea8f875e5c0f4), you can do it all from the dashboard!\n\nInside Flyball\n\nA big internal focus right now is making our Support as…supportive (sorry) as it can possibly be. So, we\'ve made some [improvements to the Support portal](https://go.news.fly.io/e/c/eyJlbWFpbF9pZCI6ImRnU043UWtEQVBiTEdQWExHQUdUZnhWZW45eHZGUWRwOEdDNjgyQT0iLCJocmVmIjoiaHR0cHM6Ly9jb21tdW5pdHkuZmx5LmlvL3QvcmVkZXNpZ25lZC1zdXBwb3J0LXBvcnRhbC8yMjUzND91dG1fY2FtcGFpZ249Tm92ZW1iZXIrJTI3MjQrTmV3c2xldHRlclx1MDAyNnV0bV9jb250ZW50PUVtYWlsXHUwMDI2dXRtX21lZGl1bT1lbWFpbF9hY3Rpb25cdTAwMjZ1dG1fc291cmNlPWN1c3RvbWVyLmlvIiwiaW50ZXJuYWwiOiI4ZGVkMDkwZDg1NjVmNmNiMTgiLCJsaW5rX2lkIjo0NjE1fQ/ab18015af8390f888d015ed2727951cff23109011f9681da0f8e479570aacc98) that are available to anyone with paid support. We\'ve also just shipped "[impact levels](https://go.news.fly.io/e/c/eyJlbWFpbF9pZCI6ImRnU043UWtEQVBiTEdQWExHQUdUZnhWZW45eHZGUWRwOEdDNjgyQT0iLCJocmVmIjoiaHR0cHM6Ly9jb21tdW5pdHkuZmx5LmlvL3Qvc3VwcG9ydC1pbXBhY3QtbGV2ZWxzLzIyNTM4P3V0bV9jYW1wYWlnbj1Ob3ZlbWJlcislMjcyNCtOZXdzbGV0dGVyXHUwMDI2dXRtX2NvbnRlbnQ9RW1haWxcdTAwMjZ1dG1fbWVkaXVtPWVtYWlsX2FjdGlvblx1MDAyNnV0bV9zb3VyY2U9Y3VzdG9tZXIuaW8iLCJpbnRlcm5hbCI6IjhkZWQwOTBkODU2NWY2Y2IxOCIsImxpbmtfaWQiOjQ2MTZ9/c2c82ebd2dccf18b2a760f4ef42ccb26eeaa2b5c0bc547d1de94d049dfa4df88)" on Support tickets, so you can indicate the urgency and severity of your support request or escalate an existing ticket. As always, we\'re curious to know what you think, so hit us with your feedback.\n\nFeature story: why we got rid of plans\n\nWhen you\'re looking for a platform to host your app, one of the first questions you\'ll ask is: "how much is this gonna cost me?" Oh, how we wish we could boil things down to one number to rule them all, but Legal refuses to let us put a "42" on the pricing page and leave it at that.\n\nUsers are doing all kinds of things on Fly.io—like [sorting through the US Patent system](https://go.news.fly.io/e/c/eyJlbWFpbF9pZCI6ImRnU043UWtEQVBiTEdQWExHQUdUZnhWZW45eHZGUWRwOEdDNjgyQT0iLCJocmVmIjoiaHR0cHM6Ly9mbHkuaW8vY3VzdG9tZXJzL2FtcGxpZmllZD91dG1fY2FtcGFpZ249Tm92ZW1iZXIrJTI3MjQrTmV3c2xldHRlclx1MDAyNnV0bV9jb250ZW50PUVtYWlsXHUwMDI2dXRtX21lZGl1bT1lbWFpbF9hY3Rpb25cdTAwMjZ1dG1fc291cmNlPWN1c3RvbWVyLmlvIiwiaW50ZXJuYWwiOiI4ZGVkMDkwZDg1NjVmNmNiMTgiLCJsaW5rX2lkIjo0NjE3fQ/c2999a79411d9262699016bfdb21574eb0bacd8974a5921b398d8813436d00e7) and [helping teenagers in New Zealand learn outside of school](https://go.news.fly.io/e/c/eyJlbWFpbF9pZCI6ImRnU043UWtEQVBiTEdQWExHQUdUZnhWZW45eHZGUWRwOEdDNjgyQT0iLCJocmVmIjoiaHR0cHM6Ly9mbHkuaW8vY3VzdG9tZXJzL215bWFoaT91dG1fY2FtcGFpZ249Tm92ZW1iZXIrJTI3MjQrTmV3c2xldHRlclx1MDAyNnV0bV9jb250ZW50PUVtYWlsXHUwMDI2dXRtX21lZGl1bT1lbWFpbF9hY3Rpb25cdTAwMjZ1dG1fc291cmNlPWN1c3RvbWVyLmlvIiwiaW50ZXJuYWwiOiI4ZGVkMDkwZDg1NjVmNmNiMTgiLCJsaW5rX2lkIjo0NjE4fQ/dd55e313d1cbca385eaaf4e3510b2d79cdc383d2ba539c7df9ad525166e83852)—and therefore using wildly varying kinds and amounts of resources. That means the (infuriating) answer to cost questions is always: "it depends." Plus, there are a few different ways to structure pricing from the company side: you can either group certain resources together based on what you think different groups of customers might want (like a prix fix menu), or you can price each individual resource and let folks order á la carte.\n\nIn the beginning, there was usage based pricing. Fly.io actually launched on the "á la carte" side of things, charging just for usage. At that point, we were just inflating Frankie the hot air balloon, and Support did not yet exist. Users told us they wanted Support, so we said "You bet!" But instead of just building paid Support options, we thought about other elements that users would want (like BAAs, SOC2 reports, etc) and packaged it all together.\n\nWe ended up with plans we called Hobby, Launch, Scale, and Enterprise. Each one had some built-in "allowances" (free resources), some had Support, and all of them were anchored to different types of users. For example, the Scale plan was for users who needed to run multi-region, high-availability workloads, had specific compliance requirements (like needing to be in line with HIPAA), and wanted priority email support.\n\nThis worked for a while. But over time, we started getting feedback from prospective users (and comments on Reddit, we won\'t lie) about how our pricing felt unclear. Folks were thinking about using us, but couldn\'t get a sense for how much it would cost.\n\nWe also realized that the groupings we came up with might not actually make sense for some of Fly.io\'s users. For example: some folks might want ultra-available Support, but not care at all about compliance. And once we started digging into it, we started asking: why build a company around primitives that are really simple and swappable, and then make a payment structure that requires picking Option A, B, or C? "There must be consistency!" we said. (We were getting pretty fired up at this point.)\n\nSo, we got rid of Plans. Instead of grouping resources together, we\'ve gone pure "pay as you go." We want you to be able to take a glance at our compute costs, do some back of the envelope math, and rest easy that you know what you\'re in for. You don\'t even have to do the math! We made [this calculator](https://go.news.fly.io/e/c/eyJlbWFpbF9pZCI6ImRnU043UWtEQVBiTEdQWExHQUdUZnhWZW45eHZGUWRwOEdDNjgyQT0iLCJocmVmIjoiaHR0cHM6Ly9mbHkuaW8vY2FsY3VsYXRvcj91dG1fY2FtcGFpZ249Tm92ZW1iZXIrJTI3MjQrTmV3c2xldHRlclx1MDAyNnV0bV9jb250ZW50PUVtYWlsXHUwMDI2dXRtX21lZGl1bT1lbWFpbF9hY3Rpb25cdTAwMjZ1dG1fc291cmNlPWN1c3RvbWVyLmlvIiwiaW50ZXJuYWwiOiI4ZGVkMDkwZDg1NjVmNmNiMTgiLCJsaW5rX2lkIjo0NjE5fQ/393875c8cc124b8c05a7d3c389233b405fd2231406358e71cff2b248830fc7e0) so you can just plug in what you need and have it spit out an approximate total.\n\nIn this brave new world, your invoice will be:\n\n- whatever compute/product resources you use, pro-rated for the time you use them, plus\n- whichever add-ons you pick, like Support and/or Compliance\n\nTo be super clear: this new á la carte set-up is just for new accounts. Anyone who was already on a plan still has it, no sweat.\n\nOur aim in getting rid of plans here was to make pricing simpler. [Some folks have pointed out](https://go.news.fly.io/e/c/eyJlbWFpbF9pZCI6ImRnU043UWtEQVBiTEdQWExHQUdUZnhWZW45eHZGUWRwOEdDNjgyQT0iLCJocmVmIjoiaHR0cHM6Ly9jb21tdW5pdHkuZmx5LmlvL3Qvd2VyZS1tYWtpbmctcHJpY2luZy1zaW1wbGVyLzIyMTY4P3V0bV9jYW1wYWlnbj1Ob3ZlbWJlcislMjcyNCtOZXdzbGV0dGVyXHUwMDI2dXRtX2NvbnRlbnQ9RW1haWxcdTAwMjZ1dG1fbWVkaXVtPWVtYWlsX2FjdGlvblx1MDAyNnV0bV9zb3VyY2U9Y3VzdG9tZXIuaW8iLCJpbnRlcm5hbCI6IjhkZWQwOTBkODU2NWY2Y2IxOCIsImxpbmtfaWQiOjQ2MjB9/983935cc539a8f00dd7dfdc6df9ed20fa014b352a7803f53f2739410fc90480c) that separating out Support and Compliance as "add-ons" means that after this change (which went live October 7th) people who sign up for new accounts could end up paying more than their historical counterparts, depending on what they choose. For example, our previous "Launch" plan was $29/month, and it came with Standard Support and some free compute allowances. Today, Standard Support is $29/month by itself.\n\nGiven that, was this the right call, on balance? We think so, since we figure:\n\n- Users who are already on a plan with us probably understand our pricing well enough, so we haven\'t touched anything about their set-up.\n- For prospective users, being able to easily and quickly answer the "how much is it gonna cost me" question is crucial.\n- With this new set-up, we\'re able to price each element at what it actually costs us to provide it to you, plus the margin that allows us to make this a viable business.',
          },
          in_reply_to: null,
          message_id: '<EMAIL>',
          multipart: true,
          number_of_attachments: 0,
          subject: "Fly.io November '24 Newsletter",
          text_content: {
            full: 'News, tips, and behind the scenes technical mumbo jumbo\r\n\r\n ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏\r\n ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏\r\n ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­\r\n­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­\r\n­ ­ ­ ­ ­ ­ ­ ­ ­ ­  \r\n\r\nHello! Here we are again, ready to share some scintillating updates from Cloud Provider Land. All the real humans behind this newsletter hope you\'re doing well, and ask that you holler if we can help with something. Onward!\r\n\r\nProduct updates\r\n---------------\r\n\r\nYou can always check out the Fresh Produce ( https://community.fly.io/c/fresh-produce/27?utm_campaign=November+%2724+Newsletter&utm_content=Email&utm_medium=email_action&utm_source=customer.io ) page to see what we\'ve been fiddling with lately. Here are some of the highlights since we last checked in:\r\n\r\n* You can auto-deploy your apps ( https://community.fly.io/t/auto-deploy-on-push-to-github/22306?utm_campaign=November+%2724+Newsletter&utm_content=Email&utm_medium=email_action&utm_source=customer.io ) on Fly.io when you push your code to GitHub, and see the checks and deployments ( https://community.fly.io/t/github-checks-and-deployments/22669?utm_campaign=November+%2724+Newsletter&utm_content=Email&utm_medium=email_action&utm_source=customer.io ) on the Github side.\r\n* If you want to update machine size ( https://community.fly.io/t/update-machine-size-from-the-dashboard/22600?utm_campaign=November+%2724+Newsletter&utm_content=Email&utm_medium=email_action&utm_source=customer.io ), update the size of your builder ( https://community.fly.io/t/you-can-now-resize-your-depot-builders/22578?utm_campaign=November+%2724+Newsletter&utm_content=Email&utm_medium=email_action&utm_source=customer.io ), or clone machines ( https://community.fly.io/t/clone-machines-from-the-web-ui/22542?utm_campaign=November+%2724+Newsletter&utm_content=Email&utm_medium=email_action&utm_source=customer.io ), you can do it all from the dashboard!\r\n\r\nInside Flyball\r\n--------------\r\n\r\nA big internal focus right now is making our Support as…supportive (sorry) as it can possibly be. So, we\'ve made some improvements to the Support portal ( https://community.fly.io/t/redesigned-support-portal/22534?utm_campaign=November+%2724+Newsletter&utm_content=Email&utm_medium=email_action&utm_source=customer.io ) that are available to anyone with paid support. We\'ve also just shipped "impact levels ( https://community.fly.io/t/support-impact-levels/22538?utm_campaign=November+%2724+Newsletter&utm_content=Email&utm_medium=email_action&utm_source=customer.io )" on Support tickets, so you can indicate the urgency and severity of your support request or escalate an existing ticket. As always, we\'re curious to know what you think, so hit us with your feedback.\r\n\r\nFeature story: why we got rid of plans\r\n--------------------------------------\r\n\r\nWhen you\'re looking for a platform to host your app, one of the first questions you\'ll ask is: "how much is this gonna cost me?" Oh, how we wish we could boil things down to one number to rule them all, but Legal refuses to let us put a "42" on the pricing page and leave it at that.\r\n\r\nUsers are doing all kinds of things on Fly.io—like sorting through the US Patent system ( https://fly.io/customers/amplified?utm_campaign=November+%2724+Newsletter&utm_content=Email&utm_medium=email_action&utm_source=customer.io ) and helping teenagers in New Zealand learn outside of school ( https://fly.io/customers/mymahi?utm_campaign=November+%2724+Newsletter&utm_content=Email&utm_medium=email_action&utm_source=customer.io )—and therefore using wildly varying kinds and amounts of resources. That means the (infuriating) answer to cost questions is always: "it depends." Plus, there are a few different ways to structure pricing from the company side: you can either group certain resources together based on what you think different groups of customers might want (like a prix fix menu), or you can price each individual resource and let folks order á la carte.\r\n\r\nIn the beginning, there was usage based pricing. Fly.io actually launched on the "á la carte" side of things, charging just for usage. At that point, we were just inflating Frankie the hot air balloon, and Support did not yet exist. Users told us they wanted Support, so we said "You bet!" But instead of just building paid Support options, we thought about other elements that users would want (like BAAs, SOC2 reports, etc) and packaged it all together.\r\n\r\nWe ended up with plans we called Hobby, Launch, Scale, and Enterprise. Each one had some built-in "allowances" (free resources), some had Support, and all of them were anchored to different types of users. For example, the Scale plan was for users who needed to run multi-region, high-availability workloads, had specific compliance requirements (like needing to be in line with HIPAA), and wanted priority email support.\r\n\r\nThis worked for a while. But over time, we started getting feedback from prospective users (and comments on Reddit, we won\'t lie) about how our pricing felt unclear. Folks were thinking about using us, but couldn\'t get a sense for how much it would cost.\r\n\r\nWe also realized that the groupings we came up with might not actually make sense for some of Fly.io\'s users. For example: some folks might want ultra-available Support, but not care at all about compliance. And once we started digging into it, we started asking: why build a company around primitives that are really simple and swappable, and then make a payment structure that requires picking Option A, B, or C? "There must be consistency!" we said. (We were getting pretty fired up at this point.)\r\n\r\nSo, we got rid of Plans. Instead of grouping resources together, we\'ve gone pure "pay as you go." We want you to be able to take a glance at our compute costs, do some back of the envelope math, and rest easy that you know what you\'re in for. You don\'t even have to do the math! We made this calculator ( https://fly.io/calculator?utm_campaign=November+%2724+Newsletter&utm_content=Email&utm_medium=email_action&utm_source=customer.io ) so you can just plug in what you need and have it spit out an approximate total.\r\n\r\nIn this brave new world, your invoice will be:\r\n\r\n* whatever compute/product resources you use, pro-rated for the time you use them, plus\r\n* whichever add-ons you pick, like Support and/or Compliance\r\n\r\nTo be super clear: this new á la carte set-up is just for new accounts. Anyone who was already on a plan still has it, no sweat.\r\n\r\nOur aim in getting rid of plans here was to make pricing simpler. Some folks have pointed out ( https://community.fly.io/t/were-making-pricing-simpler/22168?utm_campaign=November+%2724+Newsletter&utm_content=Email&utm_medium=email_action&utm_source=customer.io ) that separating out Support and Compliance as "add-ons" means that after this change (which went live October 7th) people who sign up for new accounts could end up paying more than their historical counterparts, depending on what they choose. For example, our previous "Launch" plan was $29/month, and it came with Standard Support and some free compute allowances. Today, Standard Support is $29/month by itself.\r\n\r\nGiven that, was this the right call, on balance? We think so, since we figure:\r\n\r\n* Users who are already on a plan with us probably understand our pricing well enough, so we haven\'t touched anything about their set-up.\r\n* For prospective users, being able to easily and quickly answer the "how much is it gonna cost me" question is crucial.\r\n* With this new set-up, we\'re able to price each element at what it actually costs us to provide it to you, plus the margin that allows us to make this a viable business.\r\n\r\nUnsubscribe ( http://track.customer.io/unsubscribe/= ) ・\r\nManage Subscriptions ( http://track.customer.io/manage_subscription_preferences/= ) ・\r\nView in Browser ( http://track.customer.io/deliveries/= )',
            reply:
              'News, tips, and behind the scenes technical mumbo jumbo\n\n ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏\n ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏\n ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­\n­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­\n­ ­ ­ ­ ­ ­ ­ ­ ­ ­  \n\nHello! Here we are again, ready to share some scintillating updates from Cloud Provider Land. All the real humans behind this newsletter hope you\'re doing well, and ask that you holler if we can help with something. Onward!\n\nProduct updates\n---------------\n\nYou can always check out the Fresh Produce ( https://community.fly.io/c/fresh-produce/27?utm_campaign=November+%2724+Newsletter&utm_content=Email&utm_medium=email_action&utm_source=customer.io ) page to see what we\'ve been fiddling with lately. Here are some of the highlights since we last checked in:\n\n* You can auto-deploy your apps ( https://community.fly.io/t/auto-deploy-on-push-to-github/22306?utm_campaign=November+%2724+Newsletter&utm_content=Email&utm_medium=email_action&utm_source=customer.io ) on Fly.io when you push your code to GitHub, and see the checks and deployments ( https://community.fly.io/t/github-checks-and-deployments/22669?utm_campaign=November+%2724+Newsletter&utm_content=Email&utm_medium=email_action&utm_source=customer.io ) on the Github side.\n* If you want to update machine size ( https://community.fly.io/t/update-machine-size-from-the-dashboard/22600?utm_campaign=November+%2724+Newsletter&utm_content=Email&utm_medium=email_action&utm_source=customer.io ), update the size of your builder ( https://community.fly.io/t/you-can-now-resize-your-depot-builders/22578?utm_campaign=November+%2724+Newsletter&utm_content=Email&utm_medium=email_action&utm_source=customer.io ), or clone machines ( https://community.fly.io/t/clone-machines-from-the-web-ui/22542?utm_campaign=November+%2724+Newsletter&utm_content=Email&utm_medium=email_action&utm_source=customer.io ), you can do it all from the dashboard!\n\nInside Flyball\n--------------\n\nA big internal focus right now is making our Support as…supportive (sorry) as it can possibly be. So, we\'ve made some improvements to the Support portal ( https://community.fly.io/t/redesigned-support-portal/22534?utm_campaign=November+%2724+Newsletter&utm_content=Email&utm_medium=email_action&utm_source=customer.io ) that are available to anyone with paid support. We\'ve also just shipped "impact levels ( https://community.fly.io/t/support-impact-levels/22538?utm_campaign=November+%2724+Newsletter&utm_content=Email&utm_medium=email_action&utm_source=customer.io )" on Support tickets, so you can indicate the urgency and severity of your support request or escalate an existing ticket. As always, we\'re curious to know what you think, so hit us with your feedback.\n\nFeature story: why we got rid of plans\n--------------------------------------\n\nWhen you\'re looking for a platform to host your app, one of the first questions you\'ll ask is: "how much is this gonna cost me?" Oh, how we wish we could boil things down to one number to rule them all, but Legal refuses to let us put a "42" on the pricing page and leave it at that.\n\nUsers are doing all kinds of things on Fly.io—like sorting through the US Patent system ( https://fly.io/customers/amplified?utm_campaign=November+%2724+Newsletter&utm_content=Email&utm_medium=email_action&utm_source=customer.io ) and helping teenagers in New Zealand learn outside of school ( https://fly.io/customers/mymahi?utm_campaign=November+%2724+Newsletter&utm_content=Email&utm_medium=email_action&utm_source=customer.io )—and therefore using wildly varying kinds and amounts of resources. That means the (infuriating) answer to cost questions is always: "it depends." Plus, there are a few different ways to structure pricing from the company side: you can either group certain resources together based on what you think different groups of customers might want (like a prix fix menu), or you can price each individual resource and let folks order á la carte.\n\nIn the beginning, there was usage based pricing. Fly.io actually launched on the "á la carte" side of things, charging just for usage. At that point, we were just inflating Frankie the hot air balloon, and Support did not yet exist. Users told us they wanted Support, so we said "You bet!" But instead of just building paid Support options, we thought about other elements that users would want (like BAAs, SOC2 reports, etc) and packaged it all together.\n\nWe ended up with plans we called Hobby, Launch, Scale, and Enterprise. Each one had some built-in "allowances" (free resources), some had Support, and all of them were anchored to different types of users. For example, the Scale plan was for users who needed to run multi-region, high-availability workloads, had specific compliance requirements (like needing to be in line with HIPAA), and wanted priority email support.\n\nThis worked for a while. But over time, we started getting feedback from prospective users (and comments on Reddit, we won\'t lie) about how our pricing felt unclear. Folks were thinking about using us, but couldn\'t get a sense for how much it would cost.\n\nWe also realized that the groupings we came up with might not actually make sense for some of Fly.io\'s users. For example: some folks might want ultra-available Support, but not care at all about compliance. And once we started digging into it, we started asking: why build a company around primitives that are really simple and swappable, and then make a payment structure that requires picking Option A, B, or C? "There must be consistency!" we said. (We were getting pretty fired up at this point.)\n\nSo, we got rid of Plans. Instead of grouping resources together, we\'ve gone pure "pay as you go." We want you to be able to take a glance at our compute costs, do some back of the envelope math, and rest easy that you know what you\'re in for. You don\'t even have to do the math! We made this calculator ( https://fly.io/calculator?utm_campaign=November+%2724+Newsletter&utm_content=Email&utm_medium=email_action&utm_source=customer.io ) so you can just plug in what you need and have it spit out an approximate total.\n\nIn this brave new world, your invoice will be:\n\n* whatever compute/product resources you use, pro-rated for the time you use them, plus\n* whichever add-ons you pick, like Support and/or Compliance\n\nTo be super clear: this new á la carte set-up is just for new accounts. Anyone who was already on a plan still has it, no sweat.\n\nOur aim in getting rid of plans here was to make pricing simpler. Some folks have pointed out ( https://community.fly.io/t/were-making-pricing-simpler/22168?utm_campaign=November+%2724+Newsletter&utm_content=Email&utm_medium=email_action&utm_source=customer.io ) that separating out Support and Compliance as "add-ons" means that after this change (which went live October 7th) people who sign up for new accounts could end up paying more than their historical counterparts, depending on what they choose. For example, our previous "Launch" plan was $29/month, and it came with Standard Support and some free compute allowances. Today, Standard Support is $29/month by itself.\n\nGiven that, was this the right call, on balance? We think so, since we figure:\n\n* Users who are already on a plan with us probably understand our pricing well enough, so we haven\'t touched anything about their set-up.\n* For prospective users, being able to easily and quickly answer the "how much is it gonna cost me" question is crucial.\n* With this new set-up, we\'re able to price each element at what it actually costs us to provide it to you, plus the margin that allows us to make this a viable business.\n\nUnsubscribe ( http://track.customer.io/unsubscribe/= ) ・\nManage Subscriptions ( http://track.customer.io/manage_subscription_preferences/= ) ・\nView in Browser ( http://track.customer.io/deliveries/= )',
            quoted:
              "News, tips, and behind the scenes technical mumbo jumbo\n\n ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏\n ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏\n ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏  ͏ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­\n­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­ ­\n­ ­ ­ ­ ­ ­ ­ ­ ­ ­  \n\nHello! Here we are again, ready to share some scintillating updates from Cloud Provider Land. All the real humans behind this newsletter hope you're doing well, and ask that you holler if we can help with something. Onward!\n\nProduct updates",
          },
          to: ['<EMAIL>'],
        },
        cc_email: null,
        bcc_email: null,
      },
      sender_type: 'Contact',
      sender_id: 111249,
      external_source_ids: {},
      additional_attributes: {},
      processed_message_content:
        "News, tips, and behind the scenes technical mumbo jumbo\n\n\n\nHello! Here we are again, ready to share some scintillating updates from Cloud Provider Land. All the real humans behind this newsletter hope you're doing well, and ask that you holler if we can help with something. Onward!\n\nProduct updates",
      sentiment: {},
      conversation: {
        assignee_id: 110,
        unread_count: 0,
        last_activity_at: **********,
        contact_inbox: {
          source_id: '<EMAIL>',
        },
      },
      sender: {
        additional_attributes: {
          source_id:
            'email:<EMAIL>',
        },
        custom_attributes: {},
        email: '<EMAIL>',
        id: 111249,
        identifier: null,
        name: 'Fly.io Team',
        phone_number: null,
        thumbnail: '',
        type: 'contact',
      },
    },
    {
      id: 60714,
      content:
        'Get *more productive days* at a better price\n\nHey there,\n\nYour 5 day bundle is coming to an end.\n\nTo get access to more productive days at WeWork, upgrade to 10 day pass bundles and save flat 30%.\n\nBook day pass bundles of 10 at *30% off*\nUse code: *BUNDLE30* **\n\nBook now ( http://url492.myhq.in/ls/click?upn= bundle_upsell_CTA )\n\nWhy book a day pass bundle?\n\nExplore deep focus zones, fresh micro-brewed coffee, uninterrupted Wi-Fi and paid printing & on-site parking for multiple productive days in a row.\n\nBook now ( http://url492.myhq.in/ls/click?upn= bundle_upsell_CTA )\n\n*T&Cs apply\n\n* Offer applicable at all locations across India\n* Offer only applicable on 5 and 10 day pass bundles of WeWork on-demand\n* Offer valid for one time use only\n\nUnsubscribe ( <%asm_group_unsubscribe_raw_url%> )',
      account_id: 51,
      inbox_id: 991,
      conversation_id: 44,
      message_type: 0,
      created_at: **********,
      updated_at: '2024-12-02T12:03:45.272Z',
      private: false,
      status: 'sent',
      source_id: '7baNGWOeR1CfiWUWYlMXow@geopod-ismtpd-2',
      content_type: 'incoming_email',
      content_attributes: {
        email: {
          bcc: null,
          cc: null,
          content_type:
            'multipart/alternative; boundary=1e7683c70dae8b1a431814bc5779c7c187eb712e01a6f20c47756c722d87',
          date: '2024-12-02T05:36:05+00:00',
          from: ['<EMAIL>'],
          html_content: {
            full: `<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">\r\n<html data-editor-version="2" class="sg-campaigns" xmlns="http://www.w3.org/1999/xhtml">\r\n    <head>\r\n      <meta http-equiv="Content-Type" content="text/html; charset=utf-8">\r\n      <meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1">\r\n      <!--[if !mso]><!-->\r\n      <meta http-equiv="X-UA-Compatible" content="IE=Edge">\r\n      <!--<![endif]-->\r\n      <!--[if (gte mso 9)|(IE)]>\r\n      <xml>\r\n        <o:OfficeDocumentSettings>\r\n          <o:AllowPNG/>\r\n          <o:PixelsPerInch>96</o:PixelsPerInch>\r\n        </o:OfficeDocumentSettings>\r\n      </xml>\r\n      <![endif]-->\r\n      <!--[if (gte mso 9)|(IE)]>\r\n  <style type="text/css">\r\n    body {width: 600px;margin: 0 auto;}\r\n    table {border-collapse: collapse;}\r\n    table, td {mso-table-lspace: 0pt;mso-table-rspace: 0pt;}\r\n    img {-ms-interpolation-mode: bicubic;}\r\n  </style>\r\n<![endif]-->\r\n      <style type="text/css">\r\n    body, p, div {\r\n      font-family: arial,helvetica,sans-serif;\r\n      font-size: 14px;\r\n    }\r\n    body {\r\n      color: #000000;\r\n    }\r\n    body a {\r\n      color: #1188E6;\r\n      text-decoration: none;\r\n    }\r\n    p { margin: 0; padding: 0; }\r\n    table.wrapper {\r\n      width:100% !important;\r\n      table-layout: fixed;\r\n      -webkit-font-smoothing: antialiased;\r\n      -webkit-text-size-adjust: 100%;\r\n      -moz-text-size-adjust: 100%;\r\n      -ms-text-size-adjust: 100%;\r\n    }\r\n    img.max-width {\r\n      max-width: 100% !important;\r\n    }\r\n    .column.of-2 {\r\n      width: 50%;\r\n    }\r\n    .column.of-3 {\r\n      width: 33.333%;\r\n    }\r\n    .column.of-4 {\r\n      width: 25%;\r\n    }\r\n    ul ul ul ul  {\r\n      list-style-type: disc !important;\r\n    }\r\n    ol ol {\r\n      list-style-type: lower-roman !important;\r\n    }\r\n    ol ol ol {\r\n      list-style-type: lower-latin !important;\r\n    }\r\n    ol ol ol ol {\r\n      list-style-type: decimal !important;\r\n    }\r\n    @media screen and (max-width:480px) {\r\n      .preheader .rightColumnContent,\r\n      .footer .rightColumnContent {\r\n        text-align: left !important;\r\n      }\r\n      .preheader .rightColumnContent div,\r\n      .preheader .rightColumnContent span,\r\n      .footer .rightColumnContent div,\r\n      .footer .rightColumnContent span {\r\n        text-align: left !important;\r\n      }\r\n      .preheader .rightColumnContent,\r\n      .preheader .leftColumnContent {\r\n        font-size: 80% !important;\r\n        padding: 5px 0;\r\n      }\r\n      table.wrapper-mobile {\r\n        width: 100% !important;\r\n        table-layout: fixed;\r\n      }\r\n      img.max-width {\r\n        height: auto !important;\r\n        max-width: 100% !important;\r\n      }\r\n      a.bulletproof-button {\r\n        display: block !important;\r\n        width: auto !important;\r\n        font-size: 80%;\r\n        padding-left: 0 !important;\r\n        padding-right: 0 !important;\r\n      }\r\n      .columns {\r\n        width: 100% !important;\r\n      }\r\n      .column {\r\n        display: block !important;\r\n        width: 100% !important;\r\n        padding-left: 0 !important;\r\n        padding-right: 0 !important;\r\n        margin-left: 0 !important;\r\n        margin-right: 0 !important;\r\n      }\r\n      .social-icon-column {\r\n        display: inline-block !important;\r\n      }\r\n    }\r\n  </style>\r\n      <!--user entered Head Start--><!--End Head user entered-->\r\n    </head>\r\n    <body>\r\n      <center class="wrapper" data-link-color="#1188E6" data-body-style="font-size:14px; font-family:arial,helvetica,sans-serif; color:#000000; background-color:#FFFFFF;">\r\n        <div class="webkit">\r\n          <table cellpadding="0" cellspacing="0" border="0" width="100%" class="wrapper" bgcolor="#FFFFFF">\r\n            <tr>\r\n              <td valign="top" bgcolor="#FFFFFF" width="100%">\r\n                <table width="100%" role="content-container" class="outer" align="center" cellpadding="0" cellspacing="0" border="0">\r\n                  <tr>\r\n                    <td width="100%">\r\n                      <table width="100%" cellpadding="0" cellspacing="0" border="0">\r\n                        <tr>\r\n                          <td>\r\n                            <!--[if mso]>\r\n    <center>\r\n    <table><tr><td width="600">\r\n  <![endif]-->\r\n                                    <table width="100%" cellpadding="0" cellspacing="0" border="0" style="width:100%; max-width:600px;" align="center">\r\n                                      <tr>\r\n                                        <td role="modules-container" style="padding:0px 0px 0px 0px; color:#000000; text-align:left;" bgcolor="#FFFFFF" width="100%" align="left"><table class="module preheader preheader-hide" role="module" data-type="preheader" border="0" cellpadding="0" cellspacing="0" width="100%" style="display: none !important; mso-hide: all; visibility: hidden; opacity: 0; color: transparent; height: 0; width: 0;">\r\n    <tr>\r\n      <td role="module-content">\r\n        <p></p>\r\n      </td>\r\n    </tr>\r\n  </table><table class="wrapper" role="module" data-type="image" border="0" cellpadding="0" cellspacing="0" width="100%" style="table-layout: fixed;" data-muid="eacb393a-0436-46cd-bd8e-30e8f600abb3">\r\n    <tbody>\r\n      <tr>\r\n        <td style="font-size:6px; line-height:10px; padding:0px 0px 0px 0px;" valign="top" align="center">\r\n          <img class="max-width" border="0" style="display:block; color:#000000; text-decoration:none; font-family:Helvetica, arial, sans-serif; font-size:16px; max-width:100% !important; width:100%; height:auto !important;" width="600" alt="" data-proportionally-constrained="true" data-responsive="true" src="http://cdn.mcauto-images-production.sendgrid.net/04dab37225190aa7/ca1c1b30-6e2b-4a9a-abf3-1893193cc4dd/1200x99.png">\r\n        </td>\r\n      </tr>\r\n    </tbody>\r\n  </table><table class="wrapper" role="module" data-type="image" border="0" cellpadding="0" cellspacing="0" width="100%" style="table-layout: fixed;" data-muid="d9ddc2d7-5162-43df-8e6c-2215c715763e">\r\n    <tbody>\r\n      <tr>\r\n        <td style="font-size:6px; line-height:10px; padding:0px 0px 0px 0px;" valign="top" align="center">\r\n          <img class="max-width" border="0" style="display:block; color:#000000; text-decoration:none; font-family:Helvetica, arial, sans-serif; font-size:16px; max-width:100% !important; width:100%; height:auto !important;" width="600" alt="" data-proportionally-constrained="true" data-responsive="true" src="http://cdn.mcauto-images-production.sendgrid.net/04dab37225190aa7/e13b2ded-0ded-4789-9920-49d64f9f3f0d/1200x678.png">\r\n        </td>\r\n      </tr>\r\n    </tbody>\r\n  </table><table class="module" role="module" data-type="text" border="0" cellpadding="0" cellspacing="0" width="100%" style="table-layout: fixed;" data-muid="778f29eb-eec9-4844-93ed-19f1faec3161" data-mc-module-version="2019-10-22">\r\n    <tbody>\r\n      <tr>\r\n        <td style="padding:18px 0px 18px 10px; line-height:22px; text-align:inherit; background-color:#f7f7f7;" height="100%" valign="top" bgcolor="#f7f7f7" role="module-content"><div><div style="font-family: inherit; text-align: inherit"><span style="white-space-collapse: preserve; text-wrap: wrap; font-family: helvetica, sans-serif; font-size: 24px">Get </span><span style="white-space-collapse: preserve; text-wrap: wrap; font-family: helvetica, sans-serif; font-size: 24px"><strong>more productive days</strong></span><span style="white-space-collapse: preserve; text-wrap: wrap; font-family: helvetica, sans-serif; font-size: 24px"> at a better price</span><span style="font-family: helvetica, sans-serif; font-size: 24px">&nbsp;</span></div><div></div></div></td>\r\n      </tr>\r\n    </tbody>\r\n  </table><table class="module" role="module" data-type="text" border="0" cellpadding="0" cellspacing="0" width="100%" style="table-layout: fixed;" data-muid="782348f7-234d-431f-851a-908af773b520" data-mc-module-version="2019-10-22">\r\n    <tbody>\r\n      <tr>\r\n        <td style="padding:18px 0px 18px 10px; line-height:22px; text-align:inherit; background-color:#f7f7f7;" height="100%" valign="top" bgcolor="#f7f7f7" role="module-content"><div><div style="font-family: inherit; text-align: inherit"><span style="white-space-collapse: preserve; text-wrap: wrap; font-family: helvetica, sans-serif; font-size: 16px">Hey there,</span><span style="font-family: helvetica, sans-serif; font-size: 16px"><br>\r\n<br>\r\n</span><span style="white-space-collapse: preserve; text-wrap: wrap; font-family: helvetica, sans-serif; font-size: 16px">Your 5 day bundle is coming to an end.</span></div>\r\n<div style="font-family: inherit; text-align: inherit"><span style="font-family: helvetica, sans-serif; font-size: 16px"><br>\r\n</span><span style="white-space-collapse: preserve; text-wrap: wrap; font-family: helvetica, sans-serif; font-size: 16px">To get access to more productive days at WeWork, upgrade to 10 day pass bundles and save flat 30%.</span><span style="font-family: helvetica, sans-serif; font-size: 16px"><br>\r\n<br>\r\n</span><span style="white-space-collapse: preserve; text-wrap: wrap; font-family: helvetica, sans-serif; font-size: 16px">Book day pass bundles of 10 at </span><span style="white-space-collapse: preserve; text-wrap: wrap; font-family: helvetica, sans-serif; font-size: 16px"><strong>30% off</strong></span><span style="font-family: helvetica, sans-serif; font-size: 16px"><br>\r\n</span><span style="white-space-collapse: preserve; text-wrap: wrap; font-family: helvetica, sans-serif; font-size: 16px">Use code: </span><span style="white-space-collapse: preserve; text-wrap: wrap; font-family: helvetica, sans-serif; font-size: 16px"><strong>BUNDLE30</strong></span><span style="font-family: helvetica, sans-serif; font-size: 16px"><strong>&nbsp;</strong></span></div><div></div></div></td>\r\n      </tr>\r\n    </tbody>\r\n  </table><table border="0" cellpadding="0" cellspacing="0" class="module" data-role="module-button" data-type="button" role="module" style="table-layout:fixed;" width="100%" data-muid="d5de0ef7-db7f-4eea-acc9-6a79b6e42e46">\r\n      <tbody>\r\n        <tr>\r\n          <td align="left" bgcolor="f7f7f7" class="outer-td" style="padding:0px 0px 0px 10px; background-color:f7f7f7;">\r\n            <table border="0" cellpadding="0" cellspacing="0" class="wrapper-mobile" style="text-align:center;">\r\n              <tbody>\r\n                <tr>\r\n                <td align="center" bgcolor="#1e22aa" class="inner-td" style="border-radius:6px; font-size:16px; text-align:left; background-color:inherit;">\r\n                  <a href="http://url492.myhq.in/ls/click?upn=" style="background-color:#1e22aa; border:1px solid #333333; border-color:#333333; border-radius:6px; border-width:1px; color:#ffffff; display:inline-block; font-size:16px; font-weight:normal; letter-spacing:0px; line-height:normal; padding:12px 18px 12px 18px; text-align:center; text-decoration:none; border-style:solid;" target="https://wework.co.in/workspaces/on-demand/day-pass-bundles?utm_source=sendgrid_email&utm_medium=sendgrid_email&utm_campaign=myHQ_sendgrid_bundle_upsell&utm_term=sendgrid_email_bundle_upsell&utm_content=sendgrid_email_%20bundle_upsell_CTA">Book now</a>\r\n                </td>\r\n                </tr>\r\n              </tbody>\r\n            </table>\r\n          </td>\r\n        </tr>\r\n      </tbody>\r\n    </table><table class="module" role="module" data-type="text" border="0" cellpadding="0" cellspacing="0" width="100%" style="table-layout: fixed;" data-muid="5c4e7155-d9cf-4262-9d03-958d03c22f1f" data-mc-module-version="2019-10-22">\r\n    <tbody>\r\n      <tr>\r\n        <td style="padding:18px 0px 18px 10px; line-height:22px; text-align:inherit; background-color:#f7f7f7;" height="100%" valign="top" bgcolor="#f7f7f7" role="module-content"><div><div style="font-family: inherit; text-align: start"><span style="box-sizing: border-box; padding-top: 0px; padding-right: 0px; padding-bottom: 0px; padding-left: 0px; margin-top: 0px; margin-right: 0px; margin-bottom: 0px; margin-left: 0px; font-style: inherit; font-variant-ligatures: inherit; font-variant-caps: inherit; font-variant-numeric: inherit; font-variant-east-asian: inherit; font-variant-alternates: inherit; font-variant-position: inherit; font-weight: bold; font-stretch: inherit; line-height: inherit; font-family: helvetica, sans-serif; font-optical-sizing: inherit; font-kerning: inherit; font-feature-settings: inherit; font-variation-settings: inherit; font-size: 16px; vertical-align: baseline; border-top-width: 0px; border-right-width: 0px; border-bottom-width: 0px; border-left-width: 0px; border-top-style: initial; border-right-style: initial; border-bottom-style: initial; border-left-style: initial; border-top-color: initial; border-right-color: initial; border-bottom-color: initial; border-left-color: initial; border-image-source: initial; border-image-slice: initial; border-image-width: initial; border-image-outset: initial; border-image-repeat: initial; letter-spacing: normal; orphans: 2; text-align: start; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space-collapse: preserve; text-wrap: wrap; text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; background-color: rgb(247, 247, 247)">Why book a day pass bundle?</span></div>\r\n<div style="font-family: inherit; text-align: start"><br></div>\r\n<div style="font-family: inherit; text-align: inherit; margin-left: 0px"><span style="box-sizing: border-box; padding-top: 0px; padding-right: 0px; padding-bottom: 0px; padding-left: 0px; margin-top: 0px; margin-right: 0px; margin-bottom: 0px; margin-left: 0px; font-style: inherit; font-variant-ligatures: inherit; font-variant-caps: inherit; font-variant-numeric: inherit; font-variant-east-asian: inherit; font-variant-alternates: inherit; font-variant-position: inherit; font-weight: 700; font-stretch: inherit; line-height: inherit; font-family: helvetica, sans-serif; font-optical-sizing: inherit; font-kerning: inherit; font-feature-settings: inherit; font-variation-settings: inherit; font-size: 16px; vertical-align: baseline; border-top-width: 0px; border-right-width: 0px; border-bottom-width: 0px; border-left-width: 0px; border-top-style: initial; border-right-style: initial; border-bottom-style: initial; border-left-style: initial; border-top-color: initial; border-right-color: initial; border-bottom-color: initial; border-left-color: initial; border-image-source: initial; border-image-slice: initial; border-image-width: initial; border-image-outset: initial; border-image-repeat: initial; letter-spacing: normal; orphans: 2; text-align: start; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space-collapse: preserve; text-wrap: wrap; text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; float: none; display: inline; background-color: rgb(247, 247, 247)">Explore deep focus zones, fresh micro-brewed coffee, uninterrupted Wi-Fi and paid printing &amp; on-site parking for multiple productive days in a row.</span><span style="background-color: rgb(247, 247, 247)">&nbsp;</span></div><div></div></div></td>\r\n      </tr>\r\n    </tbody>\r\n  </table><table class="wrapper" role="module" data-type="image" border="0" cellpadding="0" cellspacing="0" width="100%" style="table-layout: fixed;" data-muid="6e1adfd1-27a0-418a-8982-ff353d88ce13">\r\n    <tbody>\r\n      <tr>\r\n        <td style="font-size:6px; line-height:10px; padding:0px 0px 0px 0px;" valign="top" align="center">\r\n          <img class="max-width" border="0" style="display:block; color:#000000; text-decoration:none; font-family:Helvetica, arial, sans-serif; font-size:16px; max-width:100% !important; width:100%; height:auto !important;" width="600" alt="" data-proportionally-constrained="true" data-responsive="true" src="http://cdn.mcauto-images-production.sendgrid.net/04dab37225190aa7/46db5fd4-9558-402a-ac47-93a618a1f37a/569x358.png">\r\n        </td>\r\n      </tr>\r\n    </tbody>\r\n  </table><table border="0" cellpadding="0" cellspacing="0" class="module" data-role="module-button" data-type="button" role="module" style="table-layout:fixed;" width="100%" data-muid="d5de0ef7-db7f-4eea-acc9-6a79b6e42e46.1">\r\n      <tbody>\r\n        <tr>\r\n          <td align="left" bgcolor="#F7F7F7" class="outer-td" style="padding:0px 0px 0px 10px; background-color:#F7F7F7;">\r\n            <table border="0" cellpadding="0" cellspacing="0" class="wrapper-mobile" style="text-align:center;">\r\n              <tbody>\r\n                <tr>\r\n                <td align="center" bgcolor="#1e22aa" class="inner-td" style="border-radius:6px; font-size:16px; text-align:left; background-color:inherit;">\r\n                  <a href="http://url492.myhq.in/ls/click?upn=" style="background-color:#1e22aa; border:1px solid #333333; border-color:#333333; border-radius:6px; border-width:1px; color:#ffffff; display:inline-block; font-size:16px; font-weight:normal; letter-spacing:0px; line-height:normal; padding:12px 18px 12px 18px; text-align:center; text-decoration:none; border-style:solid;" target="https://wework.co.in/workspaces/on-demand/day-pass-bundles?utm_source=sendgrid_email&utm_medium=sendgrid_email&utm_campaign=myHQ_sendgrid_bundle_upsell&utm_term=sendgrid_email_bundle_upsell&utm_content=sendgrid_email_%20bundle_upsell_CTA">Book now</a>\r\n                </td>\r\n                </tr>\r\n              </tbody>\r\n            </table>\r\n          </td>\r\n        </tr>\r\n      </tbody>\r\n    </table><table class="module" role="module" data-type="text" border="0" cellpadding="0" cellspacing="0" width="100%" style="table-layout: fixed;" data-muid="5bdd1e34-b004-465c-b9cf-59adce68649c" data-mc-module-version="2019-10-22">\r\n    <tbody>\r\n      <tr>\r\n        <td style="padding:18px 0px 18px 10px; line-height:11px; text-align:inherit; background-color:#f7f7f7;" height="100%" valign="top" bgcolor="#f7f7f7" role="module-content"><div><div style="font-family: inherit; text-align: inherit"><span style="white-space-collapse: preserve; text-wrap: wrap; font-size: 9px">*T&amp;Cs apply </span></div>\r\n<ul>\r\n  <li style="text-align: inherit; font-size: 9px; font-size: 9px"><span style="white-space-collapse: preserve; text-wrap: wrap; font-size: 9px">Offer applicable at all locations across India</span></li>\r\n  <li style="text-align: inherit; font-size: 9px; font-size: 9px"><span style="white-space-collapse: preserve; text-wrap: wrap; font-size: 9px">Offer only applicable on 5 and 10 day pass bundles of WeWork on-demand</span></li>\r\n  <li style="text-align: inherit; font-size: 9px; font-size: 9px"><span style="white-space-collapse: preserve; text-wrap: wrap; font-size: 9px">Offer valid for one time use only</span><span style="font-size: 9px">&nbsp;</span></li>\r\n</ul><div></div></div></td>\r\n      </tr>\r\n    </tbody>\r\n  </table><table class="wrapper" role="module" data-type="image" border="0" cellpadding="0" cellspacing="0" width="100%" style="table-layout: fixed;" data-muid="f01742d8-f6cd-4903-9bda-016d66baa598">\r\n    <tbody>\r\n      <tr>\r\n        <td style="font-size:6px; line-height:10px; padding:0px 0px 0px 0px;" valign="top" align="center">\r\n          <img class="max-width" border="0" style="display:block; color:#000000; text-decoration:none; font-family:Helvetica, arial, sans-serif; font-size:16px; max-width:100% !important; width:100%; height:auto !important;" width="600" alt="" data-proportionally-constrained="true" data-responsive="true" src="http://cdn.mcauto-images-production.sendgrid.net/04dab37225190aa7/2d67bc44-55bc-4354-badc-044734a955d7/1200x268.png">\r\n        </td>\r\n      </tr>\r\n    </tbody>\r\n  </table><div data-role="module-unsubscribe" class="module" role="module" data-type="unsubscribe" style="color:#1e22aa; font-size:12px; line-height:20px; padding:16px 16px 16px 16px; text-align:Center;" data-muid="4e838cf3-9892-4a6d-94d6-170e474d21e5"><p style="font-family:helvetica,sans-serif; font-size:12px; line-height:20px;"><a class="Unsubscribe--unsubscribeLink" href="<%asm_group_unsubscribe_raw_url%>" target="_blank" style="">Unsubscribe</a></p></div></td>\r\n                                      </tr>\r\n                                    </table>\r\n                                    <!--[if mso]>\r\n                                  </td>\r\n                                </tr>\r\n                              </table>\r\n                            </center>\r\n                            <![endif]-->\r\n                          </td>\r\n                        </tr>\r\n                      </table>\r\n                    </td>\r\n                  </tr>\r\n                </table>\r\n              </td>\r\n            </tr>\r\n          </table>\r\n        </div>\r\n      </center>\r\n    <p>If you&nbsp;unsubscribe, you will not receive any email communication on your WeWork transactions and benefits from the myHQ team&nbsp;<a href="http://url492.myhq.in/wf/unsubscribe?upn="> click here </a>.</p>\r\n<img src="http://url492.myhq.in/wf/open?upn=" alt="" width="1" height="1" border="0" style="height:1px !important;width:1px !important;border-width:0 !important;margin-top:0 !important;margin-bottom:0 !important;margin-right:0 !important;margin-left:0 !important;padding-top:0 !important;padding-bottom:0 !important;padding-right:0 !important;padding-left:0 !important;"/></body>\r\n  </html>`,
            reply: `[]\n[]\n\nGet more productive days at a better price\n\nHey there,\nYour 5 day bundle is coming to an end.\nTo get access to more productive days at WeWork, upgrade to 10 day pass bundles and save flat 30%.\nBook day pass bundles of 10 at 30% off Use code: BUNDLE30\n\n[Book now](http://url492.myhq.in/ls/click?upn=)\n\nWhy book a day pass bundle?\n\nExplore deep focus zones, fresh micro-brewed coffee, uninterrupted Wi-Fi and paid printing & on-site parking for multiple productive days in a row.\n\n[]\n\n[Book now](http://url492.myhq.in/ls/click?upn=)\n\n*T&Cs apply \n\n- Offer applicable at all locations across India\n- Offer only applicable on 5 and 10 day pass bundles of WeWork on-demand\n-  Offer valid for one time use only\n\n[]\n\n[Unsubscribe](<%asm_group_unsubscribe_raw_url%>)\n\nIf you unsubscribe, you will not receive any email communication on your WeWork transactions and benefits from the myHQ team [click here](http://url492.myhq.in/wf/unsubscribe?upn=).\n\n[]`,
            quoted: `[]\n[]\n\nGet more productive days at a better price\n\nHey there,\nYour 5 day bundle is coming to an end.\nTo get access to more productive days at WeWork, upgrade to 10 day pass bundles and save flat 30%.\nBook day pass bundles of 10 at 30% off Use code: BUNDLE30\n\n[Book now](http://url492.myhq.in/ls/click?upn=)\n\nWhy book a day pass bundle?\n\nExplore deep focus zones, fresh micro-brewed coffee, uninterrupted Wi-Fi and paid printing & on-site parking for multiple productive days in a row.\n\n[]\n\n[Book now](http://url492.myhq.in/ls/click?upn=)\n\n*T&Cs apply \n\n- Offer applicable at all locations across India\n- Offer only applicable on 5 and 10 day pass bundles of WeWork on-demand\n-  Offer valid for one time use only\n\n[]\n\n[Unsubscribe](<%asm_group_unsubscribe_raw_url%>)\n\nIf you unsubscribe, you will not receive any email communication on your WeWork transactions and benefits from the myHQ team [click here](http://url492.myhq.in/wf/unsubscribe?upn=).\n\n[]`,
          },
          in_reply_to: null,
          message_id: '7baNGWOeR1CfiWUWYlMXow@geopod-ismtpd-2',
          multipart: true,
          number_of_attachments: 0,
          subject: 'Book your favourite workspaces at discounted prices',
          text_content: {
            full: 'Get *more productive days* at a better price\n\nHey there,\n\nYour 5 day bundle is coming to an end.\n\nTo get access to more productive days at WeWork, upgrade to 10 day pass bundles and save flat 30%.\n\nBook day pass bundles of 10 at *30% off*\nUse code: *BUNDLE30* **\n\nBook now ( http://url492.myhq.in/ls/click?upn= bundle_upsell_CTA )\n\nWhy book a day pass bundle?\n\nExplore deep focus zones, fresh micro-brewed coffee, uninterrupted Wi-Fi and paid printing & on-site parking for multiple productive days in a row.\n\nBook now ( http://url492.myhq.in/ls/click?upn= bundle_upsell_CTA )\n\n*T&Cs apply\n\n* Offer applicable at all locations across India\n* Offer only applicable on 5 and 10 day pass bundles of WeWork on-demand\n* Offer valid for one time use only\n\nUnsubscribe ( <%asm_group_unsubscribe_raw_url%> )',
            reply:
              'Get *more productive days* at a better price\n\nHey there,\n\nYour 5 day bundle is coming to an end.\n\nTo get access to more productive days at WeWork, upgrade to 10 day pass bundles and save flat 30%.\n\nBook day pass bundles of 10 at *30% off*\nUse code: *BUNDLE30* **\n\nBook now ( http://url492.myhq.in/ls/click?upn= bundle_upsell_CTA )\n\nWhy book a day pass bundle?\n\nExplore deep focus zones, fresh micro-brewed coffee, uninterrupted Wi-Fi and paid printing & on-site parking for multiple productive days in a row.\n\nBook now ( http://url492.myhq.in/ls/click?upn= bundle_upsell_CTA )\n\n*T&Cs apply\n\n* Offer applicable at all locations across India\n* Offer only applicable on 5 and 10 day pass bundles of WeWork on-demand\n* Offer valid for one time use only\n\nUnsubscribe ( <%asm_group_unsubscribe_raw_url%> )',
            quoted:
              'Get *more productive days* at a better price\n\nHey there,\n\nYour 5 day bundle is coming to an end.\n\nTo get access to more productive days at WeWork, upgrade to 10 day pass bundles and save flat 30%.\n\nBook day pass bundles of 10 at *30% off*\nUse code: *BUNDLE30* **\n\nBook now ( http://url492.myhq.in/ls/click?upn= bundle_upsell_CTA )\n\nWhy book a day pass bundle?\n\nExplore deep focus zones, fresh micro-brewed coffee, uninterrupted Wi-Fi and paid printing & on-site parking for multiple productive days in a row.\n\nBook now ( http://url492.myhq.in/ls/click?upn= bundle_upsell_CTA )\n\n*T&Cs apply\n\n* Offer applicable at all locations across India\n* Offer only applicable on 5 and 10 day pass bundles of WeWork on-demand\n* Offer valid for one time use only\n\nUnsubscribe ( <%asm_group_unsubscribe_raw_url%> )',
          },
          to: ['<EMAIL>'],
        },
        cc_email: null,
        bcc_email: null,
      },
      sender_type: 'Contact',
      sender_id: 111254,
      external_source_ids: {},
      additional_attributes: {},
      processed_message_content:
        'Get *more productive days* at a better price\n\nHey there,\n\nYour 5 day bundle is coming to an end.\n\nTo get access to more productive days at WeWork, upgrade to 10 day pass bundles and save flat 30%.\n\nBook day pass bundles of 10 at *30% off*\nUse code: *BUNDLE30* **\n\nBook now ( http://url492.myhq.in/ls/click?upn= bundle_upsell_CTA )\n\nWhy book a day pass bundle?\n\nExplore deep focus zones, fresh micro-brewed coffee, uninterrupted Wi-Fi and paid printing & on-site parking for multiple productive days in a row.\n\nBook now ( http://url492.myhq.in/ls/click?upn= bundle_upsell_CTA )\n\n*T&Cs apply\n\n* Offer applicable at all locations across India\n* Offer only applicable on 5 and 10 day pass bundles of WeWork on-demand\n* Offer valid for one time use only\n\nUnsubscribe ( <%asm_group_unsubscribe_raw_url%> )',
      sentiment: {},
      conversation: {
        assignee_id: null,
        unread_count: 0,
        last_activity_at: **********,
        contact_inbox: {
          source_id: '<EMAIL>',
        },
      },
      sender: {
        additional_attributes: {
          source_id: 'email:7baNGWOeR1CfiWUWYlMXow@geopod-ismtpd-2',
        },
        custom_attributes: {},
        email: '<EMAIL>',
        id: 111254,
        identifier: null,
        name: 'WeWork India',
        phone_number: null,
        thumbnail: '',
        type: 'contact',
      },
    },
  ],
  { deep: true }
);
