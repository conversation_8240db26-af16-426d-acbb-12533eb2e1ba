<script setup>
import ArticlesPage from './ArticlesPage.vue';

const articles = [
  {
    title: "How to get an SSL certificate for your Help Center's custom domain",
    status: 'draft',
    updatedAt: '2 days ago',
    author: '<PERSON>',
    category: '⚡️ Marketing',
    views: 3400,
  },
  {
    title: 'Setting up your first Help Center portal',
    status: '',
    updatedAt: '1 week ago',
    author: '<PERSON>',
    category: '🛠️ Development',
    views: 400,
  },
  {
    title: 'Best practices for organizing your Help Center content',
    status: 'archived',
    updatedAt: '3 days ago',
    author: '<PERSON>',
    category: '💰 Finance',
    views: 400,
  },
  {
    title: 'Customizing the appearance of your Help Center',
    status: '',
    updatedAt: '5 days ago',
    author: '<PERSON>',
    category: '💰 Finance',
    views: 400,
  },
  {
    title: 'Best practices for organizing your Help Center content',
    status: 'archived',
    updatedAt: '3 days ago',
    author: '<PERSON>',
    category: '💰 Finance',
    views: 400,
  },
  {
    title: 'Customizing the appearance of your Help Center',
    status: '',
    updatedAt: '5 days ago',
    author: '<PERSON>',
    category: '💰 Finance',
    views: 400,
  },
  {
    title: 'Best practices for organizing your Help Center content',
    status: 'archived',
    updatedAt: '3 days ago',
    author: '<PERSON>',
    category: '💰 Finance',
    views: 400,
  },
];
</script>

<template>
  <Story title="Pages/HelpCenter/ArticlesPage" :layout="{ type: 'single' }">
    <Variant title="All Articles">
      <div class="w-full min-h-screen bg-white dark:bg-slate-900">
        <ArticlesPage :articles="articles" />
      </div>
    </Variant>
  </Story>
</template>
