module SsoAuthenticatable
  extend ActiveSupport::Concern

  def generate_sso_auth_token
    token = SecureRandom.hex(32)
    ::Redis::<PERSON>.setex(sso_token_key(token), true, 5.minutes)
    token
  end

  def invalidate_sso_auth_token(token)
    ::Redis::<PERSON>.delete(sso_token_key(token))
  end

  def valid_sso_auth_token?(token)
    ::Redis::Alfred.get(sso_token_key(token)).present?
  end

  def generate_sso_link
    encoded_email = ERB::Util.url_encode(email)
    "#{ENV.fetch('FRONTEND_URL', nil)}/app/login?email=#{encoded_email}&sso_auth_token=#{generate_sso_auth_token}"
  end

  def generate_sso_link_with_impersonation
    "#{generate_sso_link}&impersonation=true"
  end

  private

  def sso_token_key(token)
    format(::Redis::RedisKeys::USER_SSO_AUTH_TOKEN, user_id: id, token: token)
  end
end
