<!DOCTYPE html>
<html>
  <head>
    <title>SuperAdmin | Chatwoot</title>
    <%= vite_client_tag %>
    <%= vite_javascript_tag 'superadmin' %>
  </head>
  <body>
    <div class="h-full w-full antialiased">
      <main class="flex flex-col bg-woot-25 min-h-screen w-full py-20 sm:px-6 lg:px-8 dark:bg-slate-900">
        <section class="max-w-5xl mx-auto">
          <img src="/brand-assets/logo.svg" alt="Chatwoot" class="mx-auto h-8 w-auto block dark:hidden">
          <img src="/brand-assets/logo_dark.svg" alt="Chatwoot" class="mx-auto h-8 w-auto hidden dark:block">
          <h2 class="mt-6 text-center text-3xl font-medium text-slate-900 dark:text-woot-50">
            Howdy, Welcome to Chatwoot 👋
          </h2>
        </section>
        <section class="bg-white shadow sm:mx-auto mt-11 sm:w-full sm:max-w-lg dark:bg-slate-800 p-11 sm:shadow-lg sm:rounded-lg mb-8 mt-15">
          <div>
            <%= form_tag('/installation/onboarding') do %>
            <div class="space-y-5">
              <% if flash[:error].present? %>
                <div class="rounded-md bg-red-500 p-4 text-white text-sm mb-5"><%= flash[:error] %></div>
              <% end %>

              <div>
                <label for="name" class="flex justify-between text-sm font-medium leading-6 text-slate-900 dark:text-white">
                  Name
                </label>
                <div class="mt-1">
                  <%= text_field :user, :name, placeholder: "Enter your full name. eg: Bruce Wayne", required: true, class: "block w-full rounded-md border-0 px-3 py-3 appearance-none shadow-sm ring-1 ring-inset text-slate-900 dark:text-slate-100 placeholder:text-slate-400 focus:ring-2 focus:ring-inset focus:ring-woot-500 sm:text-sm sm:leading-6 outline-none dark:bg-slate-700 dark:ring-slate-600 dark:focus:ring-woot-500 ring-slate-200" %>
                </div>
              </div>

              <div>
                <label for="name" class="flex justify-between text-sm font-medium leading-6 text-slate-900 dark:text-white">
                  Company Name
                </label>
                <div class="mt-1">
                  <%= text_field :user, :company, placeholder: "Enter your company name. eg: Wayne Enterprises", required: true, class: "block w-full rounded-md border-0 px-3 py-3 appearance-none shadow-sm ring-1 ring-inset text-slate-900 dark:text-slate-100 placeholder:text-slate-400 focus:ring-2 focus:ring-inset focus:ring-woot-500 sm:text-sm sm:leading-6 outline-none dark:bg-slate-700 dark:ring-slate-600 dark:focus:ring-woot-500 ring-slate-200" %>
                </div>
              </div>

              <div>
                <label for="name" class="flex justify-between text-sm font-medium leading-6 text-slate-900 dark:text-white">
                  Work Email
                </label>
                <div class="mt-1">
                  <%= email_field :user, :email, placeholder: "Enter your work email address. eg: <EMAIL>", required: true, class: "block w-full rounded-md border-0 px-3 py-3 appearance-none shadow-sm ring-1 ring-inset text-slate-900 dark:text-slate-100 placeholder:text-slate-400 focus:ring-2 focus:ring-inset focus:ring-woot-500 sm:text-sm sm:leading-6 outline-none dark:bg-slate-700 dark:ring-slate-600 dark:focus:ring-woot-500 ring-slate-200" %>
                </div>
              </div>

              <div>
                <label for="name" class="flex justify-between text-sm font-medium leading-6 text-slate-900 dark:text-white">
                  Password
                </label>
                <div class="mt-1">
                  <%= password_field :user, :password, placeholder: "Enter a password with 6 characters or more.", required: true, class: "block w-full rounded-md border-0 px-3 py-3 appearance-none shadow-sm ring-1 ring-inset text-slate-900 dark:text-slate-100 placeholder:text-slate-400 focus:ring-2 focus:ring-inset focus:ring-woot-500 sm:text-sm sm:leading-6 outline-none dark:bg-slate-700 dark:ring-slate-600 dark:focus:ring-woot-500 ring-slate-200" %>
                </div>
              </div>

              <div class="text-sm text-slate-900">
                <%= check_box_tag "subscribe_to_updates", 'true', true %>
                <label class="ml-2" for="subscribe_to_updates">
                  Subscribe to release notes, newsletters & product feedback surveys.
                </label>
              </div>

              <button type="submit" class="flex items-center w-full justify-center rounded-md bg-woot-500 py-3 px-3 text-base font-medium text-white shadow-sm hover:bg-woot-600 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-woot-500 cursor-pointer">
                Finish Setup
              </button>
            </div>
            <% end %>
          </div>
        </section>
      </main>
    </div>
  </body>
</html>
