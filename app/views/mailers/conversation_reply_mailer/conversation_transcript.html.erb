<% @messages.each do |message| %>
  <tr>
    <td>
      <b><%= message.sender&.try(:available_name) || message.sender&.name || '' %></b>
      <% if message.conversation.inbox&.inbox_type == 'Email' %>
        <div style="font-size: 90%; color: #899096; line-height: 16px">
          <% if message.content_attributes.dig(:email, :from).present? %>
            <div>From: <%= message.content_attributes.dig(:email, :from).join(", ") %></div>
          <% else %>
            <div>From: <%= message.sender&.try(:email) %></div>
          <% end %>

          <% if message.content_attributes.dig(:email, :subject).present? %>
            <div>Subject: <%= message.content_attributes.dig(:email, :subject) %></div>
          <% end %>

          <% if message.content_attributes[:to_emails].present? %>
            <div>To: <%= message.content_attributes[:to_emails].join(", ") %></div>
          <% end %>
          <% if message.content_attributes[:cc_emails].present? %>
            <div>CC: <%= message.content_attributes[:cc_emails].join(", ") %></div>
          <% end %>
          <% if message.content_attributes[:bcc_emails].present? %>
            <div>BCC: <%= message.content_attributes[:bcc_emails].join(", ") %></div>
          <% end %>
        </div>
      <% end %>
    </td>
  </tr>
  <tr>
    <td style="padding-bottom: 32px;">
      <% if message.content %>
        <%= ChatwootMarkdownRenderer.new(message.content).render_message %>
      <% end %>
      <% if message.attachments.present? %>
        <p>Attachments:</p>
        <% message.attachments.each do |attachment| %>
          <p><a href="<%= attachment.file_url %>" target="_blank"><%= attachment.file.filename.to_s %></a></p>
        <% end %>
      <% end %>
      <p style="font-size: 90%; font-size: 90%;color: #899096;margin-top: -8px; margin-bottom: 0px;">
      <% if @inbox.timezone.present? %>
        <%= message.created_at.in_time_zone(@inbox.timezone).strftime('%b %d, %I:%M %p %Z') %>
      <% else %>
        <%= message.created_at.strftime('%b %d, %I:%M %p %Z') %>
      <% end %>
      </p>
    </td>
  </tr>
<% end %>
