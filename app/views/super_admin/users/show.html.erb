<%#
# Show

This view is the template for the show page.
It renders the attributes of a resource,
as well as a link to its edit page.

## Local variables:

- `page`:
  An instance of [Administrate::Page::Show][1].
  Contains methods for accessing the resource to be displayed on the page,
  as well as helpers for describing how each attribute of the resource
  should be displayed.

[1]: http://www.rubydoc.info/gems/administrate/Administrate/Page/Show
%>

<% content_for(:title) { t("administrate.actions.show_resource", name: page.page_title) } %>

<header class="main-content__header" role="banner">
  <h1 class="main-content__page-title">
    <%= content_for(:title) %>
  </h1>

  <div>
    <%= link_to(
            t("administrate.actions.edit"),
            [:edit, namespace,  page.resource.becomes(User)],
            class: "button",
          ) if authorized_action? page.resource, :edit %>
  </div>
</header>

<section class="main-content__body">
  <dl>
    <% page.attributes.each do |title, attributes| %>
      <fieldset class="<%= "field-unit--nested" if title.present? %>">
        <% if title.present? %>
          <legend><%= t "helpers.label.#{page.resource_name}.#{title}", default: title %></legend>
        <% end %>

        <% attributes.each do |attribute| %>
          <dt class="attribute-label" id="<%= attribute.name %>">
          <%= t(
            "helpers.label.#{resource_name}.#{attribute.name}",
            default: page.resource.class.human_attribute_name(attribute.name),
          ) %>
          </dt>

          <dd class="attribute-data attribute-data--<%=attribute.html_class%>"
              ><%= render_field attribute, page: page %></dd>
        <% end %>
      </fieldset>
    <% end %>
  </dl>
</section>

<section class="main-content__body">
  <% account_user_page = Administrate::Page::Form.new(AccountUserDashboard.new, AccountUser.new) %>
  <%= form_for([namespace, account_user_page.resource], html: { class: "form" }) do |f| %>
  <% if account_user_page.resource.errors.any? %>
    <div id="error_explanation">
      <h2>
        <%= t(
          "administrate.form.errors",
          pluralized_errors: pluralize(account_user_page.resource.errors.count, t("administrate.form.error")),
          resource_name: display_resource_name(account_user_page.resource_name)
        ) %>
      </h2>

      <ul>
        <% account_user_page.resource.errors.full_messages.each do |message| %>
          <li class="flash-error"><%= message %></li>
        <% end %>
      </ul>
    </div>
  <% end %>

  <% account_user_page.attributes.each do |title, attributes| -%>
    <% attributes.each do |attribute| %>
      <% if attribute.name == "user" %>
        <%= f.hidden_field('user_id', value: page.resource.id) %>
      <% else %>
      <div class="field-unit field-unit--<%= attribute.html_class %> field-unit--<%= requireness(attribute) %>">
        <%= render_field attribute, f: f %>
      </div>
      <% end %>
    <% end %>  
  <% end -%>

  <div class="form-actions">
    <%= f.submit %>
  </div>
<% end %>

</section>

<%= render partial: "impersonate", locals: {page: page} %>
