<%#
# Index

This view is the template for the index page.
It is responsible for rendering the search bar, header and pagination.
It renders the `_table` partial to display details about the resources.

## Local variables:

- `page`:
  An instance of [Administrate::Page::Collection][1].
  Contains helper methods to help display a table,
  and knows which attributes should be displayed in the resource's table.
- `resources`:
  An instance of `ActiveRecord::Relation` containing the resources
  that match the user's search criteria.
  By default, these resources are passed to the table partial to be displayed.
- `search_term`:
  A string containing the term the user has searched for, if any.
- `show_search_bar`:
  A boolean that determines if the search bar should be shown.

[1]: http://www.rubydoc.info/gems/administrate/Administrate/Page/Collection
%>

<% content_for(:title) do %>
  <%= display_resource_name(page.resource_name) %>
<% end %>

<header class="main-content__header" role="banner">
  <div class="flex items-center justify-between w-full">
    <h1 class="main-content__page-title m-0 mr-6" id="page-title">
      <%= content_for(:title) %>
    </h1>

    <div class="flex items-center">
      <% if show_search_bar %>
        <div class="flex items-center">
          <%= render("filters", page: page) %>
          <div class="ml-3">
            <%= render(
              "search",
              search_term: search_term,
              resource_name: display_resource_name(page.resource_name)
            ) %>
          </div>
        </div>
      <% end %>

      <div class="whitespace-nowrap ml-4">
        <%= link_to(
          t(
            "administrate.actions.new_resource",
            name: page.resource_name.titleize.downcase
          ),
          [:new, namespace, page.resource_path.to_sym],
          class: "button",
        ) if accessible_action?(new_resource, :new) %>
      </div>
    </div>
  </div>
</header>

<section class="main-content__body main-content__body--flush">
  <%= render(
    "collection",
    collection_presenter: page,
    collection_field_name: resource_name,
    page: page,
    resources: resources,
    table_title: "page-title"
  ) %>

  <%= paginate resources, param_name: '_page'%>
</section>
