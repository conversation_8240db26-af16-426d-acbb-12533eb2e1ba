<% content_for(:title) do %>
  Configure Settings - <%= @config.titleize %>
<% end %>

<header class="main-content__header" role="banner">
  <h1 class="main-content__page-title" id="page-title">
    <%= content_for(:title) %>
  </h1>
</header>

<style>
.eye-icon.eye-hide path {
  d: path('M3 3l18 18M10.5 10.677a2 2 0 002.823 2.823M7.362 7.561C5.68 8.74 4.279 10.42 3 12c1.889 2.991 5.282 6 9 6 1.55 0 3.043-.523 4.395-1.35M12 6c4.008 0 6.701 3.009 9 6a15.66 15.66 0 01-1.078 1.5');
}
</style>

<section class="main-content__body">
  <%= form_with url: super_admin_app_config_url(config: @config) , method: :post do |form| %>
    <% @allowed_configs.each do |key| %>
      <div class="flex mb-8">
        <div class="field-unit__label">
          <%= form.label "app_config[#{key}]", @installation_configs[key]&.dig('display_title') || key %>
        </div>
        <div class="-mt-2 field-unit__field ">
          <% if @installation_configs[key]&.dig('type') == 'boolean' %>
            <%= form.select "app_config[#{key}]",
              [["True", true], ["False", false]],
              { selected: ActiveModel::Type::Boolean.new.cast(@app_config[key]) },
              class: "mt-2 border border-slate-100 p-1 rounded-md"
            %>
          <% elsif @installation_configs[key]&.dig('type') == 'code' %>
            <%= form.text_area "app_config[#{key}]",
                value: @app_config[key],
                rows: 12,
                wrap: 'off',
                class: "mt-2 border font-mono text-xs border-slate-100 p-1 rounded-md overflow-scroll"
            %>
          <% elsif @installation_configs[key]&.dig('type') == 'secret' %>
            <div class="relative">
              <%= form.password_field "app_config[#{key}]",
                  id: "app_config_#{key}",
                  value: @app_config[key],
                  class: "mt-2 border border-slate-100 p-1.5 pr-8 rounded-md w-full"
              %>
              <button
                type="button"
                class="absolute reset-base !bg-white top-1/2 !outline-0 !text-n-slate-11 -translate-y-1/2 right-2 p-1 hover:!bg-n-slate-5 rounded-sm toggle-password"
                data-target="app_config_<%= key %>"
              >
                <svg class="eye-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M12 5C5.63636 5 2 12 2 12C2 12 5.63636 19 12 19C18.3636 19 22 12 22 12C22 12 18.3636 5 12 5Z"/>
                  <path d="M12 15C13.6569 15 15 13.6569 15 12C15 10.3431 13.6569 9 12 9C10.3431 9 9 10.3431 9 12C9 13.6569 10.3431 15 12 15Z"/>
                </svg>
              </button>
            </div>
          <% else %>
            <%= form.text_field "app_config[#{key}]", value: @app_config[key] %>
          <% end %>
          <%if @installation_configs[key]&.dig('description').present? %>
            <p class="pt-2 text-xs italic text-slate-400">
              <%= @installation_configs[key]&.dig('description') %>
            </p>
          <% end %>
        </div>
      </div>
    <% end %>
    <div class="form-actions">
      <%= form.submit "Submit" %>
    </div>
  <% end %>
</section>

<% content_for :javascript do %>
  <script>
  document.addEventListener('DOMContentLoaded', () => {
    document.querySelectorAll('.toggle-password').forEach(button => {
      button.addEventListener('click', () => {
        const targetId = button.dataset.target;
        const input = document.getElementById(targetId);
        const type = input.type === 'password' ? 'text' : 'password';
        input.type = type;

        // Toggle icon
        const svg = button.querySelector('.eye-icon');
        if (type === 'password') {
          svg.classList.remove('eye-hide')
        } else {
          svg.classList.add('eye-hide')
        }
      });
    });
  });
  </script>
<% end %>
