<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif; box-sizing: border-box; font-size: 14px; margin: 0;">
  <head>
    <meta name="viewport" content="width=device-width" />
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <title></title>
    <style type="text/css">
      img {
        max-width: 100%;
      }

      body {
        -webkit-font-smoothing: antialiased;
        -webkit-text-size-adjust: none;
        height: 100%;
        line-height: 1.6em;
        width: 100% !important;
      }

      body {
        background-color: #F8FAFC;
      }

      @media only screen and (max-width: 640px) {
        body {
          padding: 0 !important;
        }
        h1 {
          font-size: 22px !important;
          font-weight: 800 !important;
          margin: 20px 0 5px !important;
        }
        h2 {
          font-size: 18px !important;
          font-weight: 800 !important;
          margin: 20px 0 5px !important;
        }
        h3 {
          font-size: 16px !important;
          font-weight: 800 !important;
          margin: 20px 0 5px !important;
        }
        h4 {
          font-weight: 800 !important;
          margin: 20px 0 5px !important;
        }
        .container {
          padding: 0 !important;
          width: 100% !important;
        }
        .content {
          padding: 0 !important;
        }
        .content-wrap {
          padding: 10px !important;
        }
      }
    </style>
  </head>

  <body itemscope itemtype="http://schema.org/EmailMessage" style="font-size: 14px; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,Helvetica,Arial,sans-serif; box-sizing: border-box; font-size: 14px; -webkit-font-smoothing: antialiased; -webkit-text-size-adjust: none; width: 100% !important; height: 100%; line-height: 1.6em; background-color: #F8FAFC; margin: 0;" bgcolor="#F8FAFC">
    <table class="body-wrap" style="width: 100%; background-color: #F8FAFC; margin: 0;" bgcolor="#F8FAFC">
      <tr style="margin: 0;">
        <td class="container" width="600" style="display: block !important; max-width: 600px !important; clear: both !important; margin: 0 auto;" valign="top">
          <div class="content" style="display: block; margin: 0 auto; padding: 20px; text-align:center;">
            <table class="main" width="100%" cellpadding="0" cellspacing="0" itemprop="action" itemscope itemtype="http://schema.org/ConfirmAction" style="border-radius: 6px;  background-color: #fff; text-align:left; margin: 0; border: 1px solid #e9e9e9; border-top:3px solid #0080f8;" bgcolor="#fff">
              <tr style="margin: 0;">
                <td class="content-wrap" style="vertical-align: top; margin: 0; padding: 20px; font-family: -apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,Helvetica,Arial,sans-serif;" valign="top">
                  <table width="100%" cellpadding="0" cellspacing="0" style="margin: 0;">
                    {{ content_for_layout }}
                  </table>
                </td>
              </tr>
            </table>
          </div>
          <div class="footer" style="color: #93AFC8; margin: 0; padding: 0 20px 40px; text-align: center">
            <table width="100%" style="margin: 0;">
              <tr style="margin: 0;">
                {% if global_config['BRAND_NAME'] == '' || global_config['BRAND_NAME'] == 'Mooly.vn' %}
                  <td class="content-block" style="font-family: -apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,Helvetica,Arial,sans-serif; vertical-align: top; margin: 0; padding: 0 0 20px;" valign="top">
                    Powered by
                    <a href="{{ global_config['BRAND_URL'] }}" style="vertical-align: top; color: #93AFC8; text-align: center; margin: 0; padding: 0 0 20px;" align="center" valign="top">
                      Mooly.vn
                    </a>
                  </td>
                {% endif %} 
              </tr>
            </table>
          </div>
        </td>
      </tr>
    </table>
  </body>
</html>
