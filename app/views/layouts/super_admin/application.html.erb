<%#
# Application Layout

This view template is used as the layout
for every page that <PERSON><PERSON><PERSON><PERSON> generates.

By default, it renders:
- Navigation
- Content for a search bar
  (if provided by a `content_for` block in a nested page)
- Flashes
- Links to stylesheets and JavaScripts
%>

<!DOCTYPE html>
<html lang="<%= I18n.locale %>" class="w-full h-full">
<head>
  <meta charset="utf-8">
  <meta name="ROBOTS" content="NOODP">
  <meta name="viewport" content="initial-scale=1">
  <title>
    <%= content_for(:title) %> - <%= GlobalConfig.get_value('INSTALLATION_NAME') || 'Mooly.vn' %>
  </title>
  <%= render "stylesheet" %>
  <%= vite_client_tag %>
  <%= vite_javascript_tag 'superadmin' %>
  <%= csrf_meta_tags %>
  <link rel="icon" type="image/png" href="/mooly-icon.png">
</head>
<body class="antialiased w-full h-full">
  <%= render "icons" %>
  <div class="flex w-full h-full">
    <%= render "navigation" -%>
    <main class="w-full overflow-auto" role="main">
      <%= render "flashes" -%>
      <%= yield %>
    </main>
  </div>
  <%= render "javascript" %>
</body>
</html>
