<%= CSVSafe.generate_line [I18n.t('reports.period', since: Date.strptime(params[:since], '%s'), until: Date.strptime(params[:until], '%s'))] %>

<% headers = [
    I18n.t('reports.inbox_csv.inbox_name'),
    I18n.t('reports.inbox_csv.inbox_type'),
    I18n.t('reports.inbox_csv.conversations_count'),
    I18n.t('reports.inbox_csv.avg_first_response_time'),
    I18n.t('reports.inbox_csv.avg_resolution_time')
  ]
%>
<%= CSVSafe.generate_line headers -%>
<% @report_data.each do |row| %>
<%= CSVSafe.generate_line row -%>
<% end %>
