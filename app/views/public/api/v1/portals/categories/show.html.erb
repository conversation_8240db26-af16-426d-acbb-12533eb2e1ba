<% content_for :head do %>
  <title><%= @category.name %> | <%= @portal.name %></title>
  <% if @category.description.present? %>
    <meta name="description" content="<%= @category.description %>">
  <% end %>
<% end %>

<% if !@is_plain_layout_enabled %>
<div id="portal-bg" class="bg-white dark:bg-slate-900">
  <div id="portal-bg-gradient" class="pt-8 pb-8 md:pt-14 md:pb-6">
    <%= render 'public/api/v1/portals/categories/category-hero', category: @category, portal: @portal %>
  </div>
</div>
<% else %>
  <%= render 'public/api/v1/portals/categories/category-hero', category: @category, portal: @portal %>
<% end %>
<section class="max-w-5xl w-full mx-auto px-4 md:px-8 py-6 flex flex-col items-center justify-center flex-grow">
  <div class="w-full flex flex-col gap-6 flex-grow">
    <% if @category.articles.published.size == 0 %>
    <div class="h-full flex items-center justify-center bg-slate-50 dark:bg-slate-800 rounded-xl py-6">
      <p class="text-sm text-slate-500"><%= I18n.t('public_portal.common.no_articles') %></p>
    </div>
    <% else %>
    <% @category.articles.published.order(:position).each do |article| %>
    <div id="<%= !@is_plain_layout_enabled ? 'category-block' : '' %>" class="<%= !@is_plain_layout_enabled ? 'border border-solid border-slate-100 dark:border-slate-800 rounded-lg' : 'group' %>">
      <a
        class="<%= !@is_plain_layout_enabled ? 'p-4' : 'px-0 py-1' %> text-slate-800 dark:text-slate-50 flex justify-between content-center hover:cursor-pointer"
        href="<%= generate_article_link(@portal.slug, article.slug, @theme_from_params, @is_plain_layout_enabled) %>"
      >
        <div class="flex flex-col gap-5">
          <div class="flex flex-col gap-1">
            <h3 id="<%= !@is_plain_layout_enabled ? 'category-name' : '' %>" class="text-lg text-slate-900 tracking-[0.28px] dark:text-slate-50 font-semibold <%= @is_plain_layout_enabled ? 'group-hover:underline' : '' %>"><%= article.title %></h3>
            <p class="text-base font-normal text-slate-600 dark:text-slate-200 line-clamp-1 break-all"><%= render_category_content(article.content) %></p>
          </div>
          <span class="text-sm text-slate-600 dark:text-slate-400 font-medium flex items-center"><%= I18n.t('public_portal.common.last_updated_on', last_updated_on: article.updated_at.strftime("%b %d, %Y")) %></span>
        </div>
      </a>
    </div>  
    <% end %>
    <% end %>
  </div>
</section>
