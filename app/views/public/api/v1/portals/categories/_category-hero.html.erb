<div class="flex flex-col px-4 md:px-8 max-w-5xl w-full mx-auto gap-6 <%= @is_plain_layout_enabled && 'py-4' %>">
  <div class="flex items-center flex-row">
    <a
      class="text-slate-500 dark:text-slate-200 text-sm gap-1 <%= @is_plain_layout_enabled && 'hover:underline' %> hover:cursor-pointer leading-8 font-semibold"
      href="<%= generate_home_link(portal.slug, category.locale, @theme_from_params, @is_plain_layout_enabled) %>"
    >
      <%= I18n.t('public_portal.common.home') %>
    </a>
    <span class="w-4 h-4 [&>svg]:w-3 [&>svg]:h-3 flex items-center justify-center text-xs text-slate-500 dark:text-slate-300"><%= render partial: 'icons/chevron-right' %></span>
    <span class="text-sm text-slate-800 dark:text-slate-100 font-semibold"><%= category.name %></span>
  </div>
  <div class="flex justify-start flex-col items-start w-full gap-2">
    <div class="flex flex-col gap-2">
      <% if category.icon.present? %>
      <span class="text-4xl"><%= category.icon %></span>
      <% end %>
      <h1 class="text-3xl font-bold tracking-wide leading-[52.5px] text-slate-900 dark:text-white">
        <%= category.name %>
      </h1>
      <% if category.description.present? %>
      <span class="font-medium text-slate-700 dark:text-slate-200 text-base leading-5"><%= category.description %></span>
      <% end %>
    </div>
    <div class="flex flex-row items-center gap-1">
      <%= render "public/api/v1/portals/authors", category: category, show_expanded: true %>
      <span class="text-slate-600 dark:text-slate-400">•</span>
      <span class="flex items-center text-base text-slate-600 dark:text-slate-400 font-medium"><%= render 'public/api/v1/portals/article_count', article_count: category.articles.published.size %></span>
    </div>
  </div>
</div>
