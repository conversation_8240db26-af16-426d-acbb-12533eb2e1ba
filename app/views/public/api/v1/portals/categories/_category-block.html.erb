
<% category_link_params = {
      portal_slug: portal.slug,
      category_locale: category.locale,
      category_slug: category.slug,
      theme: @theme_from_params,
      is_plain_layout_enabled: @is_plain_layout_enabled
    }
%>

<section class="flex flex-col w-full h-full px-4 py-6 lg:container">
  <div class="flex items-center justify-between w-full">
    <h3 class="text-xl font-semibold leading-relaxed text-slate-900 dark:text-white hover:underline">
     <a href="<%= generate_category_link(category_link_params) %>">
      <%= category.name %>
    </a>
    </h3>
    <span class="text-slate-500">
      <%= render 'public/api/v1/portals/article_count', article_count: category.articles.published.order(position: :asc).size %>
    </span>
  </div>
  <div class="flex-grow w-full py-4 mt-2">
    <% if category.articles.published.size == 0 %>
      <div class="flex items-center justify-center h-full mb-4 bg-slate-50 dark:bg-slate-800 rounded-xl">
        <p class="text-sm text-slate-500"><%= I18n.t('public_portal.common.no_articles') %></p>
      </div>
    <% else %>
      <% category.articles.published.order(position: :asc).take(5).each do |article| %>
        <div class="flex content-center justify-between h-8 my-1">
          <a
            class="leading-8 text-slate-800 dark:text-slate-50 hover:underline"
            href="<%= generate_article_link(portal.slug, article.slug, @theme_from_params, @is_plain_layout_enabled) %>"
          >
            <%= article.title %>
          </a>
          <span class="flex items-center">
            <svg
              class="w-4 h-4 fill-current text-slate-700 dark:text-slate-200"
              width="24"
              height="24"
              fill="none"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M8.47 4.22a.75.75 0 0 0 0 1.06L15.19 12l-6.72 6.72a.75.75 0 1 0 1.06 1.06l7.25-7.25a.75.75 0 0 0 0-1.06L9.53 4.22a.75.75 0 0 0-1.06 0Z"
              />
            </svg>
          </span>
        </div>
      <% end %>
    <% end %>

  </div>
  <div>
    <a
      href="<%= generate_category_link(category_link_params) %>"
      class="flex flex-row items-center mt-4 text-base font-medium text-woot-600 dark:text-woot-500 hover:text-slate-900 dark:hover:text-white hover:underline"
    >
      <%= I18n.t('public_portal.common.view_all_articles') %>
      <span class="ml-2">
        <svg
          class="w-4 h-4 fill-current text-woot-500"
          width="24"
          height="24"
          fill="none"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M13.267 4.209a.75.75 0 0 0-1.034 1.086l6.251 5.955H3.75a.75.75 0 0 0 0 1.5h14.734l-6.251 5.954a.75.75 0 0 0 1.034 1.087l7.42-7.067a.996.996 0 0 0 .3-.58.758.758 0 0 0-.001-.29.995.995 0 0 0-.3-.578l-7.419-7.067Z"
          />
        </svg>
      </span>
    </a>
  </div>
</section>
