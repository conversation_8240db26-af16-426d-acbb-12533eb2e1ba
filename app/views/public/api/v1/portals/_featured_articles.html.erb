<% featured_articles = articles.where(category_id: categories).search_by_status(:published).order_by_views.limit(6) %>
<% if featured_articles.count >= 6 %>
<section class="flex flex-col w-full h-full lg:container">
   <div class="flex flex-col gap-5 px-3 py-5 border border-solid rounded-lg border-slate-100 dark:border-slate-800">
      <div class="flex items-center justify-between w-full"> 
         <div class="flex flex-col items-start gap-1">
            <div class="flex flex-row items-center gap-2 px-2">
               <h3 class="text-xl font-semibold leading-relaxed text-slate-800 dark:text-slate-50">
                  <%= I18n.t('public_portal.header.featured_articles') %>
               </h3>
            </div>
         </div>
      </div>
      <div class="grid grid-cols-1 gap-2 md:grid-cols-2 gap-x-2 gap-y-2">
         <% featured_articles.each do |article| %>
         <a class="leading-7 text-slate-700 dark:text-slate-100" href="<%= generate_article_link(portal.slug, article.slug, @theme_from_params, @is_plain_layout_enabled) %>">
            <div  id="category-item" class="flex items-start justify-between gap-6 px-2 py-1 rounded-lg">
               <%= article.title %>
               <span class="flex items-center font-normal mt-1.5">
                  <%= render partial: 'icons/chevron-right' %>
               </span>
            </div>
         </a>
         <% end %>
      </div>
   </div>
</section>
<% end %>
